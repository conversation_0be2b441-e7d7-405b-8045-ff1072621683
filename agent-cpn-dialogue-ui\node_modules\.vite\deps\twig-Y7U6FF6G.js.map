{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/twig.mjs"], "sourcesContent": ["import css from './css.mjs';\nimport javascript from './javascript.mjs';\nimport scss from './scss.mjs';\nimport php from './php.mjs';\nimport python from './python.mjs';\nimport ruby from './ruby.mjs';\nimport './html.mjs';\nimport './xml.mjs';\nimport './java.mjs';\nimport './sql.mjs';\nimport './json.mjs';\nimport './c.mjs';\nimport './shellscript.mjs';\nimport './lua.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Twig\", \"fileTypes\": [\"twig\", \"html.twig\"], \"firstLineMatch\": \"<!(?i:DOCTYPE)|<(?i:html)|<\\\\?(?i:php)|\\\\{\\\\{|\\\\{%|\\\\{#\", \"foldingStartMarker\": \"(?x)\\n        (<(?i:body|div|dl|fieldset|form|head|li|ol|script|select|style|table|tbody|tfoot|thead|tr|ul)\\\\b.*?>\\n        |<!--(?!.*--\\\\s*>)\\n        |^<!--\\\\ \\\\#tminclude\\\\ (?>.*?-->)$\\n        |\\\\{%\\\\s+(autoescape|block|embed|filter|for|if|macro|raw|sandbox|set|spaceless|trans|verbatim)\\n        )\", \"foldingStopMarker\": \"(?x)\\n        (</(?i:body|div|dl|fieldset|form|head|li|ol|script|select|style|table|tbody|tfoot|thead|tr|ul)>\\n        |^(?!.*?<!--).*?--\\\\s*>\\n        |^<!--\\\\ end\\\\ tminclude\\\\ -->$\\n        |\\\\{%\\\\s+end(autoescape|block|embed|filter|for|if|macro|raw|sandbox|set|spaceless|trans|verbatim)\\n        )\", \"name\": \"twig\", \"patterns\": [{ \"begin\": \"(<)([a-zA-Z0-9:]++)(?=[^>]*></\\\\2>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \"(>(<)/)(\\\\2)(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"meta.scope.between-tag-pair.html\" }, \"3\": { \"name\": \"entity.name.tag.html\" }, \"4\": { \"name\": \"punctuation.definition.tag.html\" } }, \"name\": \"meta.tag.any.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }] }, { \"begin\": \"(<\\\\?)(xml)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.xml.html\" } }, \"end\": \"(\\\\?>)\", \"name\": \"meta.tag.preprocessor.xml.html\", \"patterns\": [{ \"include\": \"#tag-generic-attribute\" }, { \"include\": \"#string-double-quoted\" }, { \"include\": \"#string-single-quoted\" }] }, { \"begin\": \"<!--\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.html\" } }, \"end\": \"--\\\\s*>\", \"name\": \"comment.block.html\", \"patterns\": [{ \"match\": \"--\", \"name\": \"invalid.illegal.bad-comments-or-CDATA.html\" }, { \"include\": \"#embedded-code\" }] }, { \"begin\": \"<!\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag.html\" } }, \"end\": \">\", \"name\": \"meta.tag.sgml.html\", \"patterns\": [{ \"begin\": \"(?i:DOCTYPE)\", \"captures\": { \"1\": { \"name\": \"entity.name.tag.doctype.html\" } }, \"end\": \"(?=>)\", \"name\": \"meta.tag.sgml.doctype.html\", \"patterns\": [{ \"match\": '\"[^\">]*\"', \"name\": \"string.quoted.double.doctype.identifiers-and-DTDs.html\" }] }, { \"begin\": \"\\\\[CDATA\\\\[\", \"end\": \"]](?=>)\", \"name\": \"constant.other.inline-data.html\" }, { \"match\": \"(\\\\s*)(?!--|>)\\\\S(\\\\s*)\", \"name\": \"invalid.illegal.bad-comments-or-CDATA.html\" }] }, { \"include\": \"#embedded-code\" }, { \"begin\": \"(?:^\\\\s+)?(<)((?i:style))\\\\b(?![^>]*/>)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.style.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.html\" } }, \"end\": \"(</)((?i:style))(>)(?:\\\\s*\\\\n)?\", \"name\": \"source.css.embedded.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" } }, \"end\": \"(?=</(?i:style))\", \"patterns\": [{ \"include\": \"#embedded-code\" }, { \"include\": \"source.css\" }] }] }, { \"begin\": \"(?:^\\\\s+)?(<)((?i:script))\\\\b(?![^>]*/>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.script.html\" } }, \"end\": \"(?<=</(script|SCRIPT))(>)(?:\\\\s*\\\\n)?\", \"endCaptures\": { \"2\": { \"name\": \"punctuation.definition.tag.html\" } }, \"name\": \"source.js.embedded.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<!</(?:script|SCRIPT))(>)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.script.html\" } }, \"end\": \"(</)((?i:script))\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.js\" } }, \"match\": \"(//).*?((?=<\\/script)|$\\\\n?)\", \"name\": \"comment.line.double-slash.js\" }, { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.js\" } }, \"end\": \"\\\\*/|(?=<\\/script)\", \"name\": \"comment.block.js\" }, { \"include\": \"#php\" }, { \"include\": \"#twig-print-tag\" }, { \"include\": \"#twig-statement-tag\" }, { \"include\": \"#twig-comment-tag\" }, { \"include\": \"source.js\" }] }] }, { \"begin\": \"(?ix)\\n\\n(?<=\\\\{\\\\%\\\\sjs\\\\s\\\\%\\\\}|\\\\{\\\\%\\\\sincludejs\\\\s\\\\%\\\\})\\n\", \"comment\": 'Add JS support to set tags that use the pattern \"css\" in their name', \"end\": \"(?ix)(?=\\\\{\\\\%\\\\sendjs\\\\s\\\\%\\\\}|\\\\{\\\\%\\\\sendincludejs\\\\s\\\\%\\\\})\", \"name\": \"source.js.embedded.twig\", \"patterns\": [{ \"include\": \"source.js\" }] }, { \"begin\": \"(?ix)\\n(?<=\\\\{\\\\%\\\\scss\\\\s\\\\%\\\\}|\\\\{\\\\%\\\\sincludecss\\\\s\\\\%\\\\}|\\\\{\\\\%\\\\sincludehirescss\\\\s\\\\%\\\\})\\n\", \"comment\": 'Add CSS support to set tags that use the pattern \"css\" in their name', \"end\": \"(?ix)(?=\\\\{\\\\%\\\\sendcss\\\\s\\\\%\\\\}|\\\\{\\\\%\\\\sendincludecss\\\\s\\\\%\\\\}|\\\\{\\\\%\\\\sendincludehirescss\\\\s\\\\%\\\\})\", \"name\": \"source.css.embedded.twig\", \"patterns\": [{ \"include\": \"source.css\" }] }, { \"begin\": \"(?ix)\\n(?<=\\\\{\\\\%\\\\sscss\\\\s\\\\%\\\\}|\\\\{\\\\%\\\\sincludescss\\\\s\\\\%\\\\}|\\\\{\\\\%\\\\sincludehiresscss\\\\s\\\\%\\\\})\\n\", \"comment\": 'Add SCSS support to set tags that use the pattern \"scss\" in their name', \"end\": \"(?ix)(?=\\\\{\\\\%\\\\sendscss\\\\s\\\\%\\\\}|\\\\{\\\\%\\\\sendincludescss\\\\s\\\\%\\\\}|\\\\{\\\\%\\\\sendincludehiresscss\\\\s\\\\%\\\\})\", \"name\": \"source.css.scss.embedded.twig\", \"patterns\": [{ \"include\": \"source.css.scss\" }] }, { \"begin\": \"(</?)((?i:body|head|html)\\\\b)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.structure.any.html\" } }, \"end\": \"(>)\", \"name\": \"meta.tag.structure.any.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }] }, { \"begin\": \"(</?)((?i:address|blockquote|dd|div|dl|dt|fieldset|form|frame|frameset|h1|h2|h3|h4|h5|h6|iframe|noframes|object|ol|p|ul|applet|center|dir|hr|menu|pre)\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.block.any.html\" } }, \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.block.any.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }] }, { \"begin\": \"(</?)((?i:a|abbr|acronym|area|b|base|basefont|bdo|big|br|button|caption|cite|code|col|colgroup|del|dfn|em|font|head|html|i|img|input|ins|isindex|kbd|label|legend|li|link|map|meta|noscript|optgroup|option|param|q|s|samp|script|select|small|span|strike|strong|style|sub|sup|table|tbody|td|textarea|tfoot|th|thead|title|tr|tt|u|var)\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.inline.any.html\" } }, \"end\": \"((?: ?/)?>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.inline.any.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }] }, { \"begin\": \"(</?)([a-zA-Z0-9:]+)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.other.html\" } }, \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.other.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }] }, { \"include\": \"#entities\" }, { \"match\": \"<>\", \"name\": \"invalid.illegal.incomplete.html\" }, { \"match\": \"<\", \"name\": \"invalid.illegal.bad-angle-bracket.html\" }, { \"include\": \"#twig-print-tag\" }, { \"include\": \"#twig-statement-tag\" }, { \"include\": \"#twig-comment-tag\" }], \"repository\": { \"embedded-code\": { \"patterns\": [{ \"include\": \"#ruby\" }, { \"include\": \"#php\" }, { \"include\": \"#twig-print-tag\" }, { \"include\": \"#twig-statement-tag\" }, { \"include\": \"#twig-comment-tag\" }, { \"include\": \"#python\" }] }, \"entities\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.html\" }, \"3\": { \"name\": \"punctuation.definition.entity.html\" } }, \"match\": \"(&)([a-zA-Z0-9]+|#[0-9]+|#x[0-9a-fA-F]+)(;)\", \"name\": \"constant.character.entity.html\" }, { \"match\": \"&\", \"name\": \"invalid.illegal.bad-ampersand.html\" }] }, \"php\": { \"begin\": \"(?=(^\\\\s*)?<\\\\?)\", \"end\": \"(?!(^\\\\s*)?<\\\\?)\", \"patterns\": [{ \"include\": \"source.php\" }] }, \"python\": { \"begin\": \"(?:^\\\\s*)<\\\\?python(?!.*\\\\?>)\", \"end\": \"\\\\?>(?:\\\\s*$\\\\n)?\", \"name\": \"source.python.embedded.html\", \"patterns\": [{ \"include\": \"source.python\" }] }, \"ruby\": { \"patterns\": [{ \"begin\": \"<%+#\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.erb\" } }, \"end\": \"%>\", \"name\": \"comment.block.erb\" }, { \"begin\": \"<%+(?!>)=?\", \"captures\": { \"0\": { \"name\": \"punctuation.section.embedded.ruby\" } }, \"end\": \"-?%>\", \"name\": \"source.ruby.embedded.html\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.ruby\" } }, \"match\": \"(#).*?(?=-?%>)\", \"name\": \"comment.line.number-sign.ruby\" }, { \"include\": \"source.ruby\" }] }, { \"begin\": \"<\\\\?r(?!>)=?\", \"captures\": { \"0\": { \"name\": \"punctuation.section.embedded.ruby.nitro\" } }, \"end\": \"-?\\\\?>\", \"name\": \"source.ruby.nitro.embedded.html\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.ruby.nitro\" } }, \"match\": \"(#).*?(?=-?\\\\?>)\", \"name\": \"comment.line.number-sign.ruby.nitro\" }, { \"include\": \"source.ruby\" }] }] }, \"string-double-quoted\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" } }, \"name\": \"string.quoted.double.html\", \"patterns\": [{ \"include\": \"#embedded-code\" }, { \"include\": \"#entities\" }] }, \"string-single-quoted\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" } }, \"name\": \"string.quoted.single.html\", \"patterns\": [{ \"include\": \"#embedded-code\" }, { \"include\": \"#entities\" }] }, \"tag-generic-attribute\": { \"match\": \"\\\\b([a-zA-Z\\\\-:]+)\", \"name\": \"entity.other.attribute-name.html\" }, \"tag-id-attribute\": { \"begin\": \"\\\\b(id)\\\\b\\\\s*(=)\", \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.id.html\" }, \"2\": { \"name\": \"punctuation.separator.key-value.html\" } }, \"end\": `(?<='|\")`, \"name\": \"meta.attribute-with-value.id.html\", \"patterns\": [{ \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"contentName\": \"meta.toc-list.id.html\", \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" } }, \"name\": \"string.quoted.double.html\", \"patterns\": [{ \"include\": \"#embedded-code\" }, { \"include\": \"#entities\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"contentName\": \"meta.toc-list.id.html\", \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" } }, \"name\": \"string.quoted.single.html\", \"patterns\": [{ \"include\": \"#embedded-code\" }, { \"include\": \"#entities\" }] }] }, \"tag-stuff\": { \"patterns\": [{ \"include\": \"#tag-id-attribute\" }, { \"include\": \"#tag-generic-attribute\" }, { \"include\": \"#string-double-quoted\" }, { \"include\": \"#string-single-quoted\" }, { \"include\": \"#embedded-code\" }] }, \"twig-arrays\": { \"begin\": \"(?<=[\\\\s\\\\(\\\\{\\\\[:,])\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.array.begin.twig\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.array.end.twig\" } }, \"name\": \"meta.array.twig\", \"patterns\": [{ \"include\": \"#twig-arrays\" }, { \"include\": \"#twig-hashes\" }, { \"include\": \"#twig-constants\" }, { \"include\": \"#twig-operators\" }, { \"include\": \"#twig-strings\" }, { \"include\": \"#twig-functions-warg\" }, { \"include\": \"#twig-functions\" }, { \"include\": \"#twig-macros\" }, { \"include\": \"#twig-objects\" }, { \"include\": \"#twig-properties\" }, { \"include\": \"#twig-filters-warg\" }, { \"include\": \"#twig-filters\" }, { \"include\": \"#twig-filters-warg-ud\" }, { \"include\": \"#twig-filters-ud\" }, { \"match\": \",\", \"name\": \"punctuation.separator.object.twig\" }] }, \"twig-comment-tag\": { \"begin\": \"\\\\{#-?\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.twig\" } }, \"end\": \"-?#\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.twig\" } }, \"name\": \"comment.block.twig\" }, \"twig-constants\": { \"patterns\": [{ \"match\": \"(?i)(?<=[\\\\s\\\\[\\\\(\\\\{:,])(?:true|false|null|none)(?=[\\\\s\\\\)\\\\]\\\\}\\\\,])\", \"name\": \"constant.language.twig\" }, { \"match\": \"(?<=[\\\\s\\\\[\\\\(\\\\{:,]|\\\\.\\\\.|\\\\*\\\\*)[0-9]+(?:\\\\.[0-9]+)?(?=[\\\\s\\\\)\\\\]\\\\}\\\\,]|\\\\.\\\\.|\\\\*\\\\*)\", \"name\": \"constant.numeric.twig\" }] }, \"twig-filters\": { \"captures\": { \"1\": { \"name\": \"support.function.twig\" } }, \"match\": `(?<=(?:[a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}\\\\]\\\\)\\\\'\\\\\"]\\\\|)|\\\\{%\\\\sfilter\\\\s)(abs|capitalize|e(?:scape)?|first|join|(?:json|url)_encode|keys|last|length|lower|nl2br|number_format|raw|reverse|round|sort|striptags|title|trim|upper)(?=[\\\\s\\\\|\\\\]\\\\}\\\\):,]|\\\\.\\\\.|\\\\*\\\\*)` }, \"twig-filters-ud\": { \"captures\": { \"1\": { \"name\": \"meta.function-call.other.twig\" } }, \"match\": `(?<=(?:[a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}\\\\]\\\\)\\\\'\\\\\"]\\\\|)|\\\\{%\\\\sfilter\\\\s)([a-zA-Z_\\\\x{7f}-\\\\x{ff}][a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}]*)` }, \"twig-filters-warg\": { \"begin\": `(?<=(?:[a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}\\\\]\\\\)\\\\'\\\\\"]\\\\|)|\\\\{%\\\\sfilter\\\\s)(batch|convert_encoding|date|date_modify|default|e(?:scape)?|format|join|merge|number_format|replace|round|slice|split|trim)(\\\\()`, \"beginCaptures\": { \"1\": { \"name\": \"support.function.twig\" }, \"2\": { \"name\": \"punctuation.definition.parameters.begin.twig\" } }, \"contentName\": \"meta.function.arguments.twig\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.twig\" } }, \"patterns\": [{ \"include\": \"#twig-constants\" }, { \"include\": \"#twig-operators\" }, { \"include\": \"#twig-functions-warg\" }, { \"include\": \"#twig-functions\" }, { \"include\": \"#twig-macros\" }, { \"include\": \"#twig-objects\" }, { \"include\": \"#twig-properties\" }, { \"include\": \"#twig-filters-warg\" }, { \"include\": \"#twig-filters\" }, { \"include\": \"#twig-filters-warg-ud\" }, { \"include\": \"#twig-filters-ud\" }, { \"include\": \"#twig-strings\" }, { \"include\": \"#twig-arrays\" }, { \"include\": \"#twig-hashes\" }] }, \"twig-filters-warg-ud\": { \"begin\": `(?<=(?:[a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}\\\\]\\\\)\\\\'\\\\\"]\\\\|)|\\\\{%\\\\sfilter\\\\s)([a-zA-Z_\\\\x{7f}-\\\\x{ff}][a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}]*)(\\\\()`, \"beginCaptures\": { \"1\": { \"name\": \"meta.function-call.other.twig\" }, \"2\": { \"name\": \"punctuation.definition.parameters.begin.twig\" } }, \"contentName\": \"meta.function.arguments.twig\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.twig\" } }, \"patterns\": [{ \"include\": \"#twig-constants\" }, { \"include\": \"#twig-functions-warg\" }, { \"include\": \"#twig-functions\" }, { \"include\": \"#twig-macros\" }, { \"include\": \"#twig-objects\" }, { \"include\": \"#twig-properties\" }, { \"include\": \"#twig-filters-warg\" }, { \"include\": \"#twig-filters\" }, { \"include\": \"#twig-filters-warg-ud\" }, { \"include\": \"#twig-filters-ud\" }, { \"include\": \"#twig-strings\" }, { \"include\": \"#twig-arrays\" }, { \"include\": \"#twig-hashes\" }] }, \"twig-functions\": { \"captures\": { \"1\": { \"name\": \"support.function.twig\" } }, \"match\": \"(?<=is\\\\s)(defined|empty|even|iterable|odd)\" }, \"twig-functions-warg\": { \"begin\": \"(?<=[\\\\s\\\\(\\\\[\\\\{:,])(attribute|block|constant|cycle|date|divisible by|dump|include|max|min|parent|random|range|same as|source|template_from_string)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.twig\" }, \"2\": { \"name\": \"punctuation.definition.parameters.begin.twig\" } }, \"contentName\": \"meta.function.arguments.twig\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.twig\" } }, \"patterns\": [{ \"include\": \"#twig-constants\" }, { \"include\": \"#twig-functions-warg\" }, { \"include\": \"#twig-functions\" }, { \"include\": \"#twig-macros\" }, { \"include\": \"#twig-objects\" }, { \"include\": \"#twig-properties\" }, { \"include\": \"#twig-filters-warg\" }, { \"include\": \"#twig-filters\" }, { \"include\": \"#twig-filters-warg-ud\" }, { \"include\": \"#twig-filters-ud\" }, { \"include\": \"#twig-strings\" }, { \"include\": \"#twig-arrays\" }] }, \"twig-hashes\": { \"begin\": \"(?<=[\\\\s\\\\(\\\\{\\\\[:,])\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.hash.begin.twig\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.hash.end.twig\" } }, \"name\": \"meta.hash.twig\", \"patterns\": [{ \"include\": \"#twig-hashes\" }, { \"include\": \"#twig-arrays\" }, { \"include\": \"#twig-constants\" }, { \"include\": \"#twig-operators\" }, { \"include\": \"#twig-strings\" }, { \"include\": \"#twig-functions-warg\" }, { \"include\": \"#twig-functions\" }, { \"include\": \"#twig-macros\" }, { \"include\": \"#twig-objects\" }, { \"include\": \"#twig-properties\" }, { \"include\": \"#twig-filters-warg\" }, { \"include\": \"#twig-filters\" }, { \"include\": \"#twig-filters-warg-ud\" }, { \"include\": \"#twig-filters-ud\" }, { \"match\": \":\", \"name\": \"punctuation.separator.key-value.twig\" }, { \"match\": \",\", \"name\": \"punctuation.separator.object.twig\" }] }, \"twig-keywords\": { \"match\": \"(?<=\\\\s)((?:end)?(?:autoescape|block|embed|filter|for|if|macro|raw|sandbox|set|spaceless|trans|verbatim)|as|do|else|elseif|extends|flush|from|ignore missing|import|include|only|use|with)(?=\\\\s)\", \"name\": \"keyword.control.twig\" }, \"twig-macros\": { \"begin\": \"(?x)\\n(?<=[\\\\s\\\\(\\\\[\\\\{:,])\\n([a-zA-Z_\\\\x{7f}-\\\\x{ff}][a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}]*)\\n(?:\\n(\\\\.)([a-zA-Z_\\\\x{7f}-\\\\x{ff}][a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}]*)\\n)?\\n(\\\\()\\n\", \"beginCaptures\": { \"1\": { \"name\": \"meta.function-call.twig\" }, \"2\": { \"name\": \"punctuation.separator.property.twig\" }, \"3\": { \"name\": \"variable.other.property.twig\" }, \"4\": { \"name\": \"punctuation.definition.parameters.begin.twig\" } }, \"contentName\": \"meta.function.arguments.twig\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.twig\" } }, \"patterns\": [{ \"include\": \"#twig-constants\" }, { \"include\": \"#twig-operators\" }, { \"include\": \"#twig-functions-warg\" }, { \"include\": \"#twig-functions\" }, { \"include\": \"#twig-macros\" }, { \"include\": \"#twig-objects\" }, { \"include\": \"#twig-properties\" }, { \"include\": \"#twig-filters-warg\" }, { \"include\": \"#twig-filters\" }, { \"include\": \"#twig-filters-warg-ud\" }, { \"include\": \"#twig-filters-ud\" }, { \"include\": \"#twig-strings\" }, { \"include\": \"#twig-arrays\" }, { \"include\": \"#twig-hashes\" }] }, \"twig-objects\": { \"captures\": { \"1\": { \"name\": \"variable.other.twig\" } }, \"match\": \"(?<=[\\\\s\\\\{\\\\[\\\\(:,])([a-zA-Z_\\\\x{7f}-\\\\x{ff}][a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}]*)(?=[\\\\s\\\\}\\\\[\\\\]\\\\(\\\\)\\\\.\\\\|,:])\" }, \"twig-operators\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.operator.arithmetic.twig\" } }, \"match\": \"(?<=\\\\s)(\\\\+|-|//?|%|\\\\*\\\\*?)(?=\\\\s)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.assignment.twig\" } }, \"match\": \"(?<=\\\\s)(=|~)(?=\\\\s)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.bitwise.twig\" } }, \"match\": \"(?<=\\\\s)(b-(?:and|or|xor))(?=\\\\s)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.comparison.twig\" } }, \"match\": \"(?<=\\\\s)((?:!|=)=|<=?|>=?|(?:not )?in|is(?: not)?|(?:ends|starts) with|matches)(?=\\\\s)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.logical.twig\" } }, \"match\": \"(?<=\\\\s)(\\\\?|:|\\\\?:|\\\\?\\\\?|and|not|or)(?=\\\\s)\" }, { \"captures\": { \"0\": { \"name\": \"keyword.operator.other.twig\" } }, \"match\": `(?<=[a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}\\\\]\\\\)'\"])\\\\.\\\\.(?=[a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}'\"])` }, { \"captures\": { \"0\": { \"name\": \"keyword.operator.other.twig\" } }, \"match\": `(?<=[a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}\\\\]\\\\}\\\\)'\"])\\\\|(?=[a-zA-Z_\\\\x{7f}-\\\\x{ff}])` }] }, \"twig-print-tag\": { \"begin\": \"\\\\{\\\\{-?\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.tag.twig\" } }, \"end\": \"-?\\\\}\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.tag.twig\" } }, \"name\": \"meta.tag.template.value.twig\", \"patterns\": [{ \"include\": \"#twig-constants\" }, { \"include\": \"#twig-operators\" }, { \"include\": \"#twig-functions-warg\" }, { \"include\": \"#twig-functions\" }, { \"include\": \"#twig-macros\" }, { \"include\": \"#twig-objects\" }, { \"include\": \"#twig-properties\" }, { \"include\": \"#twig-filters-warg\" }, { \"include\": \"#twig-filters\" }, { \"include\": \"#twig-filters-warg-ud\" }, { \"include\": \"#twig-filters-ud\" }, { \"include\": \"#twig-strings\" }, { \"include\": \"#twig-arrays\" }, { \"include\": \"#twig-hashes\" }] }, \"twig-properties\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.separator.property.twig\" }, \"2\": { \"name\": \"variable.other.property.twig\" } }, \"match\": \"(?x)\\n(?<=[a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}])\\n(\\\\.)([a-zA-Z_\\\\x{7f}-\\\\x{ff}][a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}]*)\\n(?=[\\\\.\\\\s\\\\|\\\\[\\\\)\\\\]\\\\}:,])\\n\" }, { \"begin\": \"(?x)\\n(?<=[a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}])\\n(\\\\.)([a-zA-Z_\\\\x{7f}-\\\\x{ff}][a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}]*)\\n(\\\\()\\n\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.property.twig\" }, \"2\": { \"name\": \"variable.other.property.twig\" }, \"3\": { \"name\": \"punctuation.definition.parameters.begin.twig\" } }, \"contentName\": \"meta.function.arguments.twig\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.twig\" } }, \"patterns\": [{ \"include\": \"#twig-constants\" }, { \"include\": \"#twig-functions-warg\" }, { \"include\": \"#twig-functions\" }, { \"include\": \"#twig-macros\" }, { \"include\": \"#twig-objects\" }, { \"include\": \"#twig-properties\" }, { \"include\": \"#twig-filters-warg\" }, { \"include\": \"#twig-filters\" }, { \"include\": \"#twig-filters-warg-ud\" }, { \"include\": \"#twig-filters-ud\" }, { \"include\": \"#twig-strings\" }, { \"include\": \"#twig-arrays\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.section.array.begin.twig\" }, \"2\": { \"name\": \"variable.other.property.twig\" }, \"3\": { \"name\": \"punctuation.section.array.end.twig\" }, \"4\": { \"name\": \"punctuation.section.array.begin.twig\" }, \"5\": { \"name\": \"variable.other.property.twig\" }, \"6\": { \"name\": \"punctuation.section.array.end.twig\" }, \"7\": { \"name\": \"punctuation.section.array.begin.twig\" }, \"8\": { \"name\": \"variable.other.property.twig\" }, \"9\": { \"name\": \"punctuation.section.array.end.twig\" } }, \"match\": `(?x)\n(?<=[a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}\\\\]])\n(?:\n(\\\\[)('[a-zA-Z_\\\\x{7f}-\\\\x{ff}][a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}]*')(\\\\])\n|(\\\\[)(\"[a-zA-Z_\\\\x{7f}-\\\\x{ff}][a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}]*\")(\\\\])\n|(\\\\[)([a-zA-Z_\\\\x{7f}-\\\\x{ff}][a-zA-Z0-9_\\\\x{7f}-\\\\x{ff}]*)(\\\\])\n)\n` }] }, \"twig-statement-tag\": { \"begin\": \"\\\\{%-?\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.tag.twig\" } }, \"end\": \"-?%\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.tag.twig\" } }, \"name\": \"meta.tag.template.block.twig\", \"patterns\": [{ \"include\": \"#twig-constants\" }, { \"include\": \"#twig-keywords\" }, { \"include\": \"#twig-operators\" }, { \"include\": \"#twig-functions-warg\" }, { \"include\": \"#twig-functions\" }, { \"include\": \"#twig-macros\" }, { \"include\": \"#twig-filters-warg\" }, { \"include\": \"#twig-filters\" }, { \"include\": \"#twig-filters-warg-ud\" }, { \"include\": \"#twig-filters-ud\" }, { \"include\": \"#twig-objects\" }, { \"include\": \"#twig-properties\" }, { \"include\": \"#twig-strings\" }, { \"include\": \"#twig-arrays\" }, { \"include\": \"#twig-hashes\" }] }, \"twig-strings\": { \"patterns\": [{ \"begin\": \"(?:(?<!\\\\\\\\)|(?<=\\\\\\\\\\\\\\\\))'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.twig\" } }, \"end\": \"(?:(?<!\\\\\\\\)|(?<=\\\\\\\\\\\\\\\\))'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.twig\" } }, \"name\": \"string.quoted.single.twig\" }, { \"begin\": '(?:(?<!\\\\\\\\)|(?<=\\\\\\\\\\\\\\\\))\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.twig\" } }, \"end\": '(?:(?<!\\\\\\\\)|(?<=\\\\\\\\\\\\\\\\))\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.twig\" } }, \"name\": \"string.quoted.double.twig\" }] } }, \"scopeName\": \"text.html.twig\", \"embeddedLangs\": [\"css\", \"javascript\", \"scss\", \"php\", \"python\", \"ruby\"] });\nvar twig = [\n  ...css,\n  ...javascript,\n  ...scss,\n  ...php,\n  ...python,\n  ...ruby,\n  lang\n];\n\nexport { twig as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,QAAQ,aAAa,CAAC,QAAQ,WAAW,GAAG,kBAAkB,2DAA2D,sBAAsB,kTAAkT,qBAAqB,iTAAiT,QAAQ,QAAQ,YAAY,CAAC,EAAE,SAAS,uCAAuC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,mBAAmB,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,eAAe,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,OAAO,UAAU,QAAQ,kCAAkC,YAAY,CAAC,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,EAAE,SAAS,QAAQ,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,WAAW,QAAQ,sBAAsB,YAAY,CAAC,EAAE,SAAS,MAAM,QAAQ,6CAA6C,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,MAAM,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,KAAK,QAAQ,sBAAsB,YAAY,CAAC,EAAE,SAAS,gBAAgB,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,SAAS,QAAQ,8BAA8B,YAAY,CAAC,EAAE,SAAS,YAAY,QAAQ,yDAAyD,CAAC,EAAE,GAAG,EAAE,SAAS,eAAe,OAAO,WAAW,QAAQ,kCAAkC,GAAG,EAAE,SAAS,2BAA2B,QAAQ,6CAA6C,CAAC,EAAE,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,SAAS,2CAA2C,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,mCAAmC,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,oBAAoB,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,4CAA4C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,yCAAyC,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,+BAA+B,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,qBAAqB,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,SAAS,gCAAgC,QAAQ,+BAA+B,GAAG,EAAE,SAAS,QAAQ,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,sBAAsB,QAAQ,mBAAmB,GAAG,EAAE,WAAW,OAAO,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,oEAAoE,WAAW,uEAAuE,OAAO,mEAAmE,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,sGAAsG,WAAW,wEAAwE,OAAO,0GAA0G,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,yGAAyG,WAAW,0EAA0E,OAAO,6GAA6G,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,iCAAiC,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,OAAO,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,8JAA8J,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,iVAAiV,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,eAAe,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,wBAAwB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,MAAM,QAAQ,kCAAkC,GAAG,EAAE,SAAS,KAAK,QAAQ,yCAAyC,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,oBAAoB,CAAC,GAAG,cAAc,EAAE,iBAAiB,EAAE,YAAY,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,OAAO,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,+CAA+C,QAAQ,iCAAiC,GAAG,EAAE,SAAS,KAAK,QAAQ,qCAAqC,CAAC,EAAE,GAAG,OAAO,EAAE,SAAS,oBAAoB,OAAO,oBAAoB,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,iCAAiC,OAAO,qBAAqB,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,QAAQ,EAAE,YAAY,CAAC,EAAE,SAAS,QAAQ,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,MAAM,QAAQ,oBAAoB,GAAG,EAAE,SAAS,cAAc,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,QAAQ,QAAQ,6BAA6B,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,kBAAkB,QAAQ,gCAAgC,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,gBAAgB,YAAY,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,UAAU,QAAQ,mCAAmC,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,SAAS,oBAAoB,QAAQ,sCAAsC,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,sBAAsB,QAAQ,mCAAmC,GAAG,oBAAoB,EAAE,SAAS,qBAAqB,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,YAAY,QAAQ,qCAAqC,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,yBAAyB,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,yBAAyB,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,eAAe,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,QAAQ,mBAAmB,YAAY,CAAC,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,SAAS,KAAK,QAAQ,oCAAoC,CAAC,EAAE,GAAG,oBAAoB,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,UAAU,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,qBAAqB,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,SAAS,0EAA0E,QAAQ,yBAAyB,GAAG,EAAE,SAAS,8FAA8F,QAAQ,wBAAwB,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,SAAS,oQAAoQ,GAAG,mBAAmB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,SAAS,6HAA6H,GAAG,qBAAqB,EAAE,SAAS,yMAAyM,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,gCAAgC,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,mIAAmI,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,gCAAgC,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,SAAS,8CAA8C,GAAG,uBAAuB,EAAE,SAAS,6JAA6J,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,gCAAgC,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,eAAe,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,SAAS,KAAK,QAAQ,uCAAuC,GAAG,EAAE,SAAS,KAAK,QAAQ,oCAAoC,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,qMAAqM,QAAQ,uBAAuB,GAAG,eAAe,EAAE,SAAS,sKAAsK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,gCAAgC,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,SAAS,8GAA8G,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,uCAAuC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,uBAAuB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,SAAS,oCAAoC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,yFAAyF,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,SAAS,gDAAgD,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,kFAAkF,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,6EAA6E,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS,uIAAuI,GAAG,EAAE,SAAS,gHAAgH,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,gCAAgC,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOh/pB,CAAC,EAAE,GAAG,sBAAsB,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,UAAU,eAAe,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,SAAS,gCAAgC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,gCAAgC,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,4BAA4B,GAAG,EAAE,SAAS,gCAAgC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,gCAAgC,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,4BAA4B,CAAC,EAAE,EAAE,GAAG,aAAa,kBAAkB,iBAAiB,CAAC,OAAO,cAAc,QAAQ,OAAO,UAAU,MAAM,EAAE,CAAC;AACl8C,IAAI,OAAO;AAAA,EACT,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}