{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-scale-chromatic/src/colors.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-scale-chromatic/src/categorical/Tableau10.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-sankey/node_modules/d3-array/src/max.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-sankey/node_modules/d3-array/src/min.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-sankey/node_modules/d3-array/src/sum.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-sankey/src/align.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-sankey/src/constant.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-sankey/src/sankey.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-sankey/node_modules/d3-path/src/path.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-sankey/node_modules/d3-shape/src/constant.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-sankey/node_modules/d3-shape/src/point.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-sankey/node_modules/d3-shape/src/array.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-sankey/node_modules/d3-shape/src/link/index.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-sankey/src/sankeyLinkHorizontal.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/mermaid/dist/chunks/mermaid.core/sankeyDiagram-4UZDY2LN.mjs"], "sourcesContent": ["export default function(specifier) {\n  var n = specifier.length / 6 | 0, colors = new Array(n), i = 0;\n  while (i < n) colors[i] = \"#\" + specifier.slice(i * 6, ++i * 6);\n  return colors;\n}\n", "import colors from \"../colors.js\";\n\nexport default colors(\"4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab\");\n", "export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n", "export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n", "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n", "import {min} from \"d3-array\";\n\nfunction targetDepth(d) {\n  return d.target.depth;\n}\n\nexport function left(node) {\n  return node.depth;\n}\n\nexport function right(node, n) {\n  return n - 1 - node.height;\n}\n\nexport function justify(node, n) {\n  return node.sourceLinks.length ? node.depth : n - 1;\n}\n\nexport function center(node) {\n  return node.targetLinks.length ? node.depth\n      : node.sourceLinks.length ? min(node.sourceLinks, targetDepth) - 1\n      : 0;\n}\n", "export default function constant(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {max, min, sum} from \"d3-array\";\nimport {justify} from \"./align.js\";\nimport constant from \"./constant.js\";\n\nfunction ascendingSourceBreadth(a, b) {\n  return ascendingBreadth(a.source, b.source) || a.index - b.index;\n}\n\nfunction ascendingTargetBreadth(a, b) {\n  return ascendingBreadth(a.target, b.target) || a.index - b.index;\n}\n\nfunction ascendingBreadth(a, b) {\n  return a.y0 - b.y0;\n}\n\nfunction value(d) {\n  return d.value;\n}\n\nfunction defaultId(d) {\n  return d.index;\n}\n\nfunction defaultNodes(graph) {\n  return graph.nodes;\n}\n\nfunction defaultLinks(graph) {\n  return graph.links;\n}\n\nfunction find(nodeById, id) {\n  const node = nodeById.get(id);\n  if (!node) throw new Error(\"missing: \" + id);\n  return node;\n}\n\nfunction computeLinkBreadths({nodes}) {\n  for (const node of nodes) {\n    let y0 = node.y0;\n    let y1 = y0;\n    for (const link of node.sourceLinks) {\n      link.y0 = y0 + link.width / 2;\n      y0 += link.width;\n    }\n    for (const link of node.targetLinks) {\n      link.y1 = y1 + link.width / 2;\n      y1 += link.width;\n    }\n  }\n}\n\nexport default function Sankey() {\n  let x0 = 0, y0 = 0, x1 = 1, y1 = 1; // extent\n  let dx = 24; // nodeWidth\n  let dy = 8, py; // nodePadding\n  let id = defaultId;\n  let align = justify;\n  let sort;\n  let linkSort;\n  let nodes = defaultNodes;\n  let links = defaultLinks;\n  let iterations = 6;\n\n  function sankey() {\n    const graph = {nodes: nodes.apply(null, arguments), links: links.apply(null, arguments)};\n    computeNodeLinks(graph);\n    computeNodeValues(graph);\n    computeNodeDepths(graph);\n    computeNodeHeights(graph);\n    computeNodeBreadths(graph);\n    computeLinkBreadths(graph);\n    return graph;\n  }\n\n  sankey.update = function(graph) {\n    computeLinkBreadths(graph);\n    return graph;\n  };\n\n  sankey.nodeId = function(_) {\n    return arguments.length ? (id = typeof _ === \"function\" ? _ : constant(_), sankey) : id;\n  };\n\n  sankey.nodeAlign = function(_) {\n    return arguments.length ? (align = typeof _ === \"function\" ? _ : constant(_), sankey) : align;\n  };\n\n  sankey.nodeSort = function(_) {\n    return arguments.length ? (sort = _, sankey) : sort;\n  };\n\n  sankey.nodeWidth = function(_) {\n    return arguments.length ? (dx = +_, sankey) : dx;\n  };\n\n  sankey.nodePadding = function(_) {\n    return arguments.length ? (dy = py = +_, sankey) : dy;\n  };\n\n  sankey.nodes = function(_) {\n    return arguments.length ? (nodes = typeof _ === \"function\" ? _ : constant(_), sankey) : nodes;\n  };\n\n  sankey.links = function(_) {\n    return arguments.length ? (links = typeof _ === \"function\" ? _ : constant(_), sankey) : links;\n  };\n\n  sankey.linkSort = function(_) {\n    return arguments.length ? (linkSort = _, sankey) : linkSort;\n  };\n\n  sankey.size = function(_) {\n    return arguments.length ? (x0 = y0 = 0, x1 = +_[0], y1 = +_[1], sankey) : [x1 - x0, y1 - y0];\n  };\n\n  sankey.extent = function(_) {\n    return arguments.length ? (x0 = +_[0][0], x1 = +_[1][0], y0 = +_[0][1], y1 = +_[1][1], sankey) : [[x0, y0], [x1, y1]];\n  };\n\n  sankey.iterations = function(_) {\n    return arguments.length ? (iterations = +_, sankey) : iterations;\n  };\n\n  function computeNodeLinks({nodes, links}) {\n    for (const [i, node] of nodes.entries()) {\n      node.index = i;\n      node.sourceLinks = [];\n      node.targetLinks = [];\n    }\n    const nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d]));\n    for (const [i, link] of links.entries()) {\n      link.index = i;\n      let {source, target} = link;\n      if (typeof source !== \"object\") source = link.source = find(nodeById, source);\n      if (typeof target !== \"object\") target = link.target = find(nodeById, target);\n      source.sourceLinks.push(link);\n      target.targetLinks.push(link);\n    }\n    if (linkSort != null) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(linkSort);\n        targetLinks.sort(linkSort);\n      }\n    }\n  }\n\n  function computeNodeValues({nodes}) {\n    for (const node of nodes) {\n      node.value = node.fixedValue === undefined\n          ? Math.max(sum(node.sourceLinks, value), sum(node.targetLinks, value))\n          : node.fixedValue;\n    }\n  }\n\n  function computeNodeDepths({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.depth = x;\n        for (const {target} of node.sourceLinks) {\n          next.add(target);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeHeights({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.height = x;\n        for (const {source} of node.targetLinks) {\n          next.add(source);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeLayers({nodes}) {\n    const x = max(nodes, d => d.depth) + 1;\n    const kx = (x1 - x0 - dx) / (x - 1);\n    const columns = new Array(x);\n    for (const node of nodes) {\n      const i = Math.max(0, Math.min(x - 1, Math.floor(align.call(null, node, x))));\n      node.layer = i;\n      node.x0 = x0 + i * kx;\n      node.x1 = node.x0 + dx;\n      if (columns[i]) columns[i].push(node);\n      else columns[i] = [node];\n    }\n    if (sort) for (const column of columns) {\n      column.sort(sort);\n    }\n    return columns;\n  }\n\n  function initializeNodeBreadths(columns) {\n    const ky = min(columns, c => (y1 - y0 - (c.length - 1) * py) / sum(c, value));\n    for (const nodes of columns) {\n      let y = y0;\n      for (const node of nodes) {\n        node.y0 = y;\n        node.y1 = y + node.value * ky;\n        y = node.y1 + py;\n        for (const link of node.sourceLinks) {\n          link.width = link.value * ky;\n        }\n      }\n      y = (y1 - y + py) / (nodes.length + 1);\n      for (let i = 0; i < nodes.length; ++i) {\n        const node = nodes[i];\n        node.y0 += y * (i + 1);\n        node.y1 += y * (i + 1);\n      }\n      reorderLinks(nodes);\n    }\n  }\n\n  function computeNodeBreadths(graph) {\n    const columns = computeNodeLayers(graph);\n    py = Math.min(dy, (y1 - y0) / (max(columns, c => c.length) - 1));\n    initializeNodeBreadths(columns);\n    for (let i = 0; i < iterations; ++i) {\n      const alpha = Math.pow(0.99, i);\n      const beta = Math.max(1 - alpha, (i + 1) / iterations);\n      relaxRightToLeft(columns, alpha, beta);\n      relaxLeftToRight(columns, alpha, beta);\n    }\n  }\n\n  // Reposition each node based on its incoming (target) links.\n  function relaxLeftToRight(columns, alpha, beta) {\n    for (let i = 1, n = columns.length; i < n; ++i) {\n      const column = columns[i];\n      for (const target of column) {\n        let y = 0;\n        let w = 0;\n        for (const {source, value} of target.targetLinks) {\n          let v = value * (target.layer - source.layer);\n          y += targetTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - target.y0) * alpha;\n        target.y0 += dy;\n        target.y1 += dy;\n        reorderNodeLinks(target);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  // Reposition each node based on its outgoing (source) links.\n  function relaxRightToLeft(columns, alpha, beta) {\n    for (let n = columns.length, i = n - 2; i >= 0; --i) {\n      const column = columns[i];\n      for (const source of column) {\n        let y = 0;\n        let w = 0;\n        for (const {target, value} of source.sourceLinks) {\n          let v = value * (target.layer - source.layer);\n          y += sourceTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - source.y0) * alpha;\n        source.y0 += dy;\n        source.y1 += dy;\n        reorderNodeLinks(source);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  function resolveCollisions(nodes, alpha) {\n    const i = nodes.length >> 1;\n    const subject = nodes[i];\n    resolveCollisionsBottomToTop(nodes, subject.y0 - py, i - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, subject.y1 + py, i + 1, alpha);\n    resolveCollisionsBottomToTop(nodes, y1, nodes.length - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, y0, 0, alpha);\n  }\n\n  // Push any overlapping nodes down.\n  function resolveCollisionsTopToBottom(nodes, y, i, alpha) {\n    for (; i < nodes.length; ++i) {\n      const node = nodes[i];\n      const dy = (y - node.y0) * alpha;\n      if (dy > 1e-6) node.y0 += dy, node.y1 += dy;\n      y = node.y1 + py;\n    }\n  }\n\n  // Push any overlapping nodes up.\n  function resolveCollisionsBottomToTop(nodes, y, i, alpha) {\n    for (; i >= 0; --i) {\n      const node = nodes[i];\n      const dy = (node.y1 - y) * alpha;\n      if (dy > 1e-6) node.y0 -= dy, node.y1 -= dy;\n      y = node.y0 - py;\n    }\n  }\n\n  function reorderNodeLinks({sourceLinks, targetLinks}) {\n    if (linkSort === undefined) {\n      for (const {source: {sourceLinks}} of targetLinks) {\n        sourceLinks.sort(ascendingTargetBreadth);\n      }\n      for (const {target: {targetLinks}} of sourceLinks) {\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  function reorderLinks(nodes) {\n    if (linkSort === undefined) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(ascendingTargetBreadth);\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  // Returns the target.y0 that would produce an ideal link from source to target.\n  function targetTop(source, target) {\n    let y = source.y0 - (source.sourceLinks.length - 1) * py / 2;\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y += width + py;\n    }\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  // Returns the source.y0 that would produce an ideal link from source to target.\n  function sourceTop(source, target) {\n    let y = target.y0 - (target.targetLinks.length - 1) * py / 2;\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y += width + py;\n    }\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  return sankey;\n}\n", "var pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction Path() {\n  this._x0 = this._y0 = // start of current subpath\n  this._x1 = this._y1 = null; // end of current subpath\n  this._ = \"\";\n}\n\nfunction path() {\n  return new Path;\n}\n\nPath.prototype = path.prototype = {\n  constructor: Path,\n  moveTo: function(x, y) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y);\n  },\n  closePath: function() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  },\n  lineTo: function(x, y) {\n    this._ += \"L\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  quadraticCurveTo: function(x1, y1, x, y) {\n    this._ += \"Q\" + (+x1) + \",\" + (+y1) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) {\n    this._ += \"C\" + (+x1) + \",\" + (+y1) + \",\" + (+x2) + \",\" + (+y2) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  arcTo: function(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n    var x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._ += \"M\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._ += \"L\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      var x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._ += \"L\" + (x1 + t01 * x01) + \",\" + (y1 + t01 * y01);\n      }\n\n      this._ += \"A\" + r + \",\" + r + \",0,0,\" + (+(y01 * x20 > x01 * y20)) + \",\" + (this._x1 = x1 + t21 * x21) + \",\" + (this._y1 = y1 + t21 * y21);\n    }\n  },\n  arc: function(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n    var dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._ += \"M\" + x0 + \",\" + y0;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._ += \"L\" + x0 + \",\" + y0;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (x - dx) + \",\" + (y - dy) + \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (this._x1 = x0) + \",\" + (this._y1 = y0);\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,\" + (+(da >= pi)) + \",\" + cw + \",\" + (this._x1 = x + r * Math.cos(a1)) + \",\" + (this._y1 = y + r * Math.sin(a1));\n    }\n  },\n  rect: function(x, y, w, h) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y) + \"h\" + (+w) + \"v\" + (+h) + \"h\" + (-w) + \"Z\";\n  },\n  toString: function() {\n    return this._;\n  }\n};\n\nexport default path;\n", "export default function(x) {\n  return function constant() {\n    return x;\n  };\n}\n", "export function x(p) {\n  return p[0];\n}\n\nexport function y(p) {\n  return p[1];\n}\n", "export var slice = Array.prototype.slice;\n", "import {path} from \"d3-path\";\nimport {slice} from \"../array.js\";\nimport constant from \"../constant.js\";\nimport {x as pointX, y as pointY} from \"../point.js\";\nimport pointRadial from \"../pointRadial.js\";\n\nfunction linkSource(d) {\n  return d.source;\n}\n\nfunction linkTarget(d) {\n  return d.target;\n}\n\nfunction link(curve) {\n  var source = linkSource,\n      target = linkTarget,\n      x = pointX,\n      y = pointY,\n      context = null;\n\n  function link() {\n    var buffer, argv = slice.call(arguments), s = source.apply(this, argv), t = target.apply(this, argv);\n    if (!context) context = buffer = path();\n    curve(context, +x.apply(this, (argv[0] = s, argv)), +y.apply(this, argv), +x.apply(this, (argv[0] = t, argv)), +y.apply(this, argv));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  link.source = function(_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n\n  link.target = function(_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n\n  link.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n\n  link.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n\n  link.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), link) : context;\n  };\n\n  return link;\n}\n\nfunction curveHorizontal(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0 = (x0 + x1) / 2, y0, x0, y1, x1, y1);\n}\n\nfunction curveVertical(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0, y0 = (y0 + y1) / 2, x1, y0, x1, y1);\n}\n\nfunction curveRadial(context, x0, y0, x1, y1) {\n  var p0 = pointRadial(x0, y0),\n      p1 = pointRadial(x0, y0 = (y0 + y1) / 2),\n      p2 = pointRadial(x1, y0),\n      p3 = pointRadial(x1, y1);\n  context.moveTo(p0[0], p0[1]);\n  context.bezierCurveTo(p1[0], p1[1], p2[0], p2[1], p3[0], p3[1]);\n}\n\nexport function linkHorizontal() {\n  return link(curveHorizontal);\n}\n\nexport function linkVertical() {\n  return link(curveVertical);\n}\n\nexport function linkRadial() {\n  var l = link(curveRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}\n", "import {linkHorizontal} from \"d3-shape\";\n\nfunction horizontalSource(d) {\n  return [d.source.x1, d.y0];\n}\n\nfunction horizontalTarget(d) {\n  return [d.target.x0, d.y1];\n}\n\nexport default function() {\n  return linkHorizontal()\n      .source(horizontalSource)\n      .target(horizontalTarget);\n}\n", "import {\n  __name,\n  clear,\n  common_default,\n  defaultConfig2 as defaultConfig,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n  setupGraphViewbox\n} from \"./chunk-3XYRH5AP.mjs\";\n\n// src/diagrams/sankey/parser/sankey.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 9], $V1 = [1, 10], $V2 = [1, 5, 10, 12];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SANKEY\": 4, \"NEWLINE\": 5, \"csv\": 6, \"opt_eof\": 7, \"record\": 8, \"csv_tail\": 9, \"EOF\": 10, \"field[source]\": 11, \"COMMA\": 12, \"field[target]\": 13, \"field[value]\": 14, \"field\": 15, \"escaped\": 16, \"non_escaped\": 17, \"DQUOTE\": 18, \"ESCAPED_TEXT\": 19, \"NON_ESCAPED_TEXT\": 20, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SANKEY\", 5: \"NEWLINE\", 10: \"EOF\", 11: \"field[source]\", 12: \"COMMA\", 13: \"field[target]\", 14: \"field[value]\", 18: \"DQUOTE\", 19: \"ESCAPED_TEXT\", 20: \"NON_ESCAPED_TEXT\" },\n    productions_: [0, [3, 4], [6, 2], [9, 2], [9, 0], [7, 1], [7, 0], [8, 5], [15, 1], [15, 1], [16, 3], [17, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 7:\n          const source = yy.findOrCreateNode($$[$0 - 4].trim().replaceAll('\"\"', '\"'));\n          const target = yy.findOrCreateNode($$[$0 - 2].trim().replaceAll('\"\"', '\"'));\n          const value = parseFloat($$[$0].trim());\n          yy.addLink(source, target, value);\n          break;\n        case 8:\n        case 9:\n        case 11:\n          this.$ = $$[$0];\n          break;\n        case 10:\n          this.$ = $$[$0 - 1];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, { 5: [1, 3] }, { 6: 4, 8: 5, 15: 6, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 1: [2, 6], 7: 11, 10: [1, 12] }, o($V1, [2, 4], { 9: 13, 5: [1, 14] }), { 12: [1, 15] }, o($V2, [2, 8]), o($V2, [2, 9]), { 19: [1, 16] }, o($V2, [2, 11]), { 1: [2, 1] }, { 1: [2, 5] }, o($V1, [2, 2]), { 6: 17, 8: 5, 15: 6, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 15: 18, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 18: [1, 19] }, o($V1, [2, 3]), { 12: [1, 20] }, o($V2, [2, 10]), { 15: 21, 16: 7, 17: 8, 18: $V0, 20: $V1 }, o([1, 5, 10], [2, 7])],\n    defaultActions: { 11: [2, 1], 12: [2, 5] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.pushState(\"csv\");\n            return 4;\n            break;\n          case 1:\n            return 10;\n            break;\n          case 2:\n            return 5;\n            break;\n          case 3:\n            return 12;\n            break;\n          case 4:\n            this.pushState(\"escaped_text\");\n            return 18;\n            break;\n          case 5:\n            return 20;\n            break;\n          case 6:\n            this.popState(\"escaped_text\");\n            return 18;\n            break;\n          case 7:\n            return 19;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:sankey-beta\\b)/i, /^(?:$)/i, /^(?:((\\u000D\\u000A)|(\\u000A)))/i, /^(?:(\\u002C))/i, /^(?:(\\u0022))/i, /^(?:([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])*)/i, /^(?:(\\u0022)(?!(\\u0022)))/i, /^(?:(([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])|(\\u002C)|(\\u000D)|(\\u000A)|(\\u0022)(\\u0022))*)/i],\n      conditions: { \"csv\": { \"rules\": [1, 2, 3, 4, 5, 6, 7], \"inclusive\": false }, \"escaped_text\": { \"rules\": [6, 7], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar sankey_default = parser;\n\n// src/diagrams/sankey/sankeyDB.ts\nvar links = [];\nvar nodes = [];\nvar nodesMap = /* @__PURE__ */ new Map();\nvar clear2 = /* @__PURE__ */ __name(() => {\n  links = [];\n  nodes = [];\n  nodesMap = /* @__PURE__ */ new Map();\n  clear();\n}, \"clear\");\nvar SankeyLink = class {\n  constructor(source, target, value = 0) {\n    this.source = source;\n    this.target = target;\n    this.value = value;\n  }\n  static {\n    __name(this, \"SankeyLink\");\n  }\n};\nvar addLink = /* @__PURE__ */ __name((source, target, value) => {\n  links.push(new SankeyLink(source, target, value));\n}, \"addLink\");\nvar SankeyNode = class {\n  constructor(ID) {\n    this.ID = ID;\n  }\n  static {\n    __name(this, \"SankeyNode\");\n  }\n};\nvar findOrCreateNode = /* @__PURE__ */ __name((ID) => {\n  ID = common_default.sanitizeText(ID, getConfig());\n  let node = nodesMap.get(ID);\n  if (node === void 0) {\n    node = new SankeyNode(ID);\n    nodesMap.set(ID, node);\n    nodes.push(node);\n  }\n  return node;\n}, \"findOrCreateNode\");\nvar getNodes = /* @__PURE__ */ __name(() => nodes, \"getNodes\");\nvar getLinks = /* @__PURE__ */ __name(() => links, \"getLinks\");\nvar getGraph = /* @__PURE__ */ __name(() => ({\n  nodes: nodes.map((node) => ({ id: node.ID })),\n  links: links.map((link) => ({\n    source: link.source.ID,\n    target: link.target.ID,\n    value: link.value\n  }))\n}), \"getGraph\");\nvar sankeyDB_default = {\n  nodesMap,\n  getConfig: /* @__PURE__ */ __name(() => getConfig().sankey, \"getConfig\"),\n  getNodes,\n  getLinks,\n  getGraph,\n  addLink,\n  findOrCreateNode,\n  getAccTitle,\n  setAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getDiagramTitle,\n  setDiagramTitle,\n  clear: clear2\n};\n\n// src/diagrams/sankey/sankeyRenderer.ts\nimport {\n  select as d3select,\n  scaleOrdinal as d3scaleOrdinal,\n  schemeTableau10 as d3schemeTableau10\n} from \"d3\";\nimport {\n  sankey as d3Sankey,\n  sankeyLinkHorizontal as d3SankeyLinkHorizontal,\n  sankeyLeft as d3SankeyLeft,\n  sankeyRight as d3SankeyRight,\n  sankeyCenter as d3SankeyCenter,\n  sankeyJustify as d3SankeyJustify\n} from \"d3-sankey\";\n\n// src/rendering-util/uid.ts\nvar Uid = class _Uid {\n  static {\n    __name(this, \"Uid\");\n  }\n  static {\n    this.count = 0;\n  }\n  static next(name) {\n    return new _Uid(name + ++_Uid.count);\n  }\n  constructor(id) {\n    this.id = id;\n    this.href = `#${id}`;\n  }\n  toString() {\n    return \"url(\" + this.href + \")\";\n  }\n};\n\n// src/diagrams/sankey/sankeyRenderer.ts\nvar alignmentsMap = {\n  left: d3SankeyLeft,\n  right: d3SankeyRight,\n  center: d3SankeyCenter,\n  justify: d3SankeyJustify\n};\nvar draw = /* @__PURE__ */ __name(function(text, id, _version, diagObj) {\n  const { securityLevel, sankey: conf } = getConfig();\n  const defaultSankeyConfig = defaultConfig.sankey;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = d3select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? d3select(sandboxElement.nodes()[0].contentDocument.body) : d3select(\"body\");\n  const svg = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : d3select(`[id=\"${id}\"]`);\n  const width = conf?.width ?? defaultSankeyConfig.width;\n  const height = conf?.height ?? defaultSankeyConfig.width;\n  const useMaxWidth = conf?.useMaxWidth ?? defaultSankeyConfig.useMaxWidth;\n  const nodeAlignment = conf?.nodeAlignment ?? defaultSankeyConfig.nodeAlignment;\n  const prefix = conf?.prefix ?? defaultSankeyConfig.prefix;\n  const suffix = conf?.suffix ?? defaultSankeyConfig.suffix;\n  const showValues = conf?.showValues ?? defaultSankeyConfig.showValues;\n  const graph = diagObj.db.getGraph();\n  const nodeAlign = alignmentsMap[nodeAlignment];\n  const nodeWidth = 10;\n  const sankey = d3Sankey().nodeId((d) => d.id).nodeWidth(nodeWidth).nodePadding(10 + (showValues ? 15 : 0)).nodeAlign(nodeAlign).extent([\n    [0, 0],\n    [width, height]\n  ]);\n  sankey(graph);\n  const colorScheme = d3scaleOrdinal(d3schemeTableau10);\n  svg.append(\"g\").attr(\"class\", \"nodes\").selectAll(\".node\").data(graph.nodes).join(\"g\").attr(\"class\", \"node\").attr(\"id\", (d) => (d.uid = Uid.next(\"node-\")).id).attr(\"transform\", function(d) {\n    return \"translate(\" + d.x0 + \",\" + d.y0 + \")\";\n  }).attr(\"x\", (d) => d.x0).attr(\"y\", (d) => d.y0).append(\"rect\").attr(\"height\", (d) => {\n    return d.y1 - d.y0;\n  }).attr(\"width\", (d) => d.x1 - d.x0).attr(\"fill\", (d) => colorScheme(d.id));\n  const getText = /* @__PURE__ */ __name(({ id: id2, value }) => {\n    if (!showValues) {\n      return id2;\n    }\n    return `${id2}\n${prefix}${Math.round(value * 100) / 100}${suffix}`;\n  }, \"getText\");\n  svg.append(\"g\").attr(\"class\", \"node-labels\").attr(\"font-size\", 14).selectAll(\"text\").data(graph.nodes).join(\"text\").attr(\"x\", (d) => d.x0 < width / 2 ? d.x1 + 6 : d.x0 - 6).attr(\"y\", (d) => (d.y1 + d.y0) / 2).attr(\"dy\", `${showValues ? \"0\" : \"0.35\"}em`).attr(\"text-anchor\", (d) => d.x0 < width / 2 ? \"start\" : \"end\").text(getText);\n  const link = svg.append(\"g\").attr(\"class\", \"links\").attr(\"fill\", \"none\").attr(\"stroke-opacity\", 0.5).selectAll(\".link\").data(graph.links).join(\"g\").attr(\"class\", \"link\").style(\"mix-blend-mode\", \"multiply\");\n  const linkColor = conf?.linkColor ?? \"gradient\";\n  if (linkColor === \"gradient\") {\n    const gradient = link.append(\"linearGradient\").attr(\"id\", (d) => (d.uid = Uid.next(\"linearGradient-\")).id).attr(\"gradientUnits\", \"userSpaceOnUse\").attr(\"x1\", (d) => d.source.x1).attr(\"x2\", (d) => d.target.x0);\n    gradient.append(\"stop\").attr(\"offset\", \"0%\").attr(\"stop-color\", (d) => colorScheme(d.source.id));\n    gradient.append(\"stop\").attr(\"offset\", \"100%\").attr(\"stop-color\", (d) => colorScheme(d.target.id));\n  }\n  let coloring;\n  switch (linkColor) {\n    case \"gradient\":\n      coloring = /* @__PURE__ */ __name((d) => d.uid, \"coloring\");\n      break;\n    case \"source\":\n      coloring = /* @__PURE__ */ __name((d) => colorScheme(d.source.id), \"coloring\");\n      break;\n    case \"target\":\n      coloring = /* @__PURE__ */ __name((d) => colorScheme(d.target.id), \"coloring\");\n      break;\n    default:\n      coloring = linkColor;\n  }\n  link.append(\"path\").attr(\"d\", d3SankeyLinkHorizontal()).attr(\"stroke\", coloring).attr(\"stroke-width\", (d) => Math.max(1, d.width));\n  setupGraphViewbox(void 0, svg, 0, useMaxWidth);\n}, \"draw\");\nvar sankeyRenderer_default = {\n  draw\n};\n\n// src/diagrams/sankey/sankeyUtils.ts\nvar prepareTextForParsing = /* @__PURE__ */ __name((text) => {\n  const textToParse = text.replaceAll(/^[^\\S\\n\\r]+|[^\\S\\n\\r]+$/g, \"\").replaceAll(/([\\n\\r])+/g, \"\\n\").trim();\n  return textToParse;\n}, \"prepareTextForParsing\");\n\n// src/diagrams/sankey/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `.label {\n      font-family: ${options.fontFamily};\n    }`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/sankey/sankeyDiagram.ts\nvar originalParse = sankey_default.parse.bind(sankey_default);\nsankey_default.parse = (text) => originalParse(prepareTextForParsing(text));\nvar diagram = {\n  styles: styles_default,\n  parser: sankey_default,\n  db: sankeyDB_default,\n  renderer: sankeyRenderer_default\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAe,SAAA,OAAS,WAAW;AACjC,MAAI,IAAI,UAAU,SAAS,IAAI,GAAGA,UAAS,IAAI,MAAM,CAAC,GAAG,IAAI;AAC7D,SAAO,IAAI;AAAGA,YAAO,CAAC,IAAI,MAAM,UAAU,MAAM,IAAI,GAAG,EAAE,IAAI,CAAC;AAC9D,SAAOA;AACT;ACFA,IAAA,oBAAe,OAAO,8DAA8D;ACFrE,SAAS,IAAI,QAAQ,SAAS;AAC3C,MAAIC;AACJ,MAAI,YAAY,QAAW;AACzB,eAAWC,UAAS,QAAQ;AAC1B,UAAIA,UAAS,SACLD,OAAMC,UAAUD,SAAQ,UAAaC,UAASA,SAAS;AAC7DD,eAAMC;MACR;IACF;EACF,OAAO;AACL,QAAI,QAAQ;AACZ,aAASA,UAAS,QAAQ;AACxB,WAAKA,SAAQ,QAAQA,QAAO,EAAE,OAAO,MAAM,MAAM,SACzCD,OAAMC,UAAUD,SAAQ,UAAaC,UAASA,SAAS;AAC7DD,eAAMC;MACR;IACF;EACF;AACA,SAAOD;AACT;ACnBe,SAAS,IAAI,QAAQ,SAAS;AAC3C,MAAIE;AACJ,MAAI,YAAY,QAAW;AACzB,eAAWD,UAAS,QAAQ;AAC1B,UAAIA,UAAS,SACLC,OAAMD,UAAUC,SAAQ,UAAaD,UAASA,SAAS;AAC7DC,eAAMD;MACR;IACF;EACF,OAAO;AACL,QAAI,QAAQ;AACZ,aAASA,UAAS,QAAQ;AACxB,WAAKA,SAAQ,QAAQA,QAAO,EAAE,OAAO,MAAM,MAAM,SACzCC,OAAMD,UAAUC,SAAQ,UAAaD,UAASA,SAAS;AAC7DC,eAAMD;MACR;IACF;EACF;AACA,SAAOC;AACT;ACnBe,SAAS,IAAI,QAAQ,SAAS;AAC3C,MAAIC,OAAM;AACV,MAAI,YAAY,QAAW;AACzB,aAASF,UAAS,QAAQ;AACxB,UAAIA,SAAQ,CAACA,QAAO;AAClBE,gBAAOF;MACT;IACF;EACF,OAAO;AACL,QAAI,QAAQ;AACZ,aAASA,UAAS,QAAQ;AACxB,UAAIA,SAAQ,CAAC,QAAQA,QAAO,EAAE,OAAO,MAAM,GAAG;AAC5CE,gBAAOF;MACT;IACF;EACF;AACA,SAAOE;AACT;ACfA,SAAS,YAAY,GAAG;AACtB,SAAO,EAAE,OAAO;AAClB;AAEO,SAAS,KAAK,MAAM;AACzB,SAAO,KAAK;AACd;AAEO,SAAS,MAAM,MAAM,GAAG;AAC7B,SAAO,IAAI,IAAI,KAAK;AACtB;AAEO,SAAS,QAAQ,MAAM,GAAG;AAC/B,SAAO,KAAK,YAAY,SAAS,KAAK,QAAQ,IAAI;AACpD;AAEO,SAAS,OAAO,MAAM;AAC3B,SAAO,KAAK,YAAY,SAAS,KAAK,QAChC,KAAK,YAAY,SAAS,IAAI,KAAK,aAAa,WAAW,IAAI,IAC/D;AACR;ACtBe,SAASC,WAASC,IAAG;AAClC,SAAO,WAAW;AAChB,WAAOA;EACT;AACF;ACAA,SAAS,uBAAuB,GAAG,GAAG;AACpC,SAAO,iBAAiB,EAAE,QAAQ,EAAE,MAAM,KAAK,EAAE,QAAQ,EAAE;AAC7D;AAEA,SAAS,uBAAuB,GAAG,GAAG;AACpC,SAAO,iBAAiB,EAAE,QAAQ,EAAE,MAAM,KAAK,EAAE,QAAQ,EAAE;AAC7D;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,SAAO,EAAE,KAAK,EAAE;AAClB;AAEA,SAAS,MAAM,GAAG;AAChB,SAAO,EAAE;AACX;AAEA,SAAS,UAAU,GAAG;AACpB,SAAO,EAAE;AACX;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM;AACf;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM;AACf;AAEA,SAAS,KAAK,UAAU,IAAI;AAC1B,QAAM,OAAO,SAAS,IAAI,EAAE;AAC5B,MAAI,CAAC;AAAM,UAAM,IAAI,MAAM,cAAc,EAAE;AAC3C,SAAO;AACT;AAEA,SAAS,oBAAoB,EAAC,OAAAC,OAAK,GAAG;AACpC,aAAW,QAAQA,QAAO;AACxB,QAAI,KAAK,KAAK;AACd,QAAI,KAAK;AACT,eAAWC,SAAQ,KAAK,aAAa;AACnCA,YAAK,KAAK,KAAKA,MAAK,QAAQ;AAC5B,YAAMA,MAAK;IACb;AACA,eAAWA,SAAQ,KAAK,aAAa;AACnCA,YAAK,KAAK,KAAKA,MAAK,QAAQ;AAC5B,YAAMA,MAAK;IACb;EACF;AACF;AAEe,SAAS,SAAS;AAC/B,MAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;AACjC,MAAI,KAAK;AACT,MAAI,KAAK,GAAG;AACZ,MAAI,KAAK;AACT,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI;AACJ,MAAID,SAAQ;AACZ,MAAIE,SAAQ;AACZ,MAAI,aAAa;AAEjB,WAAS,SAAS;AAChB,UAAM,QAAQ,EAAC,OAAOF,OAAM,MAAM,MAAM,SAAS,GAAG,OAAOE,OAAM,MAAM,MAAM,SAAS,EAAC;AACvF,qBAAiB,KAAK;AACtB,sBAAkB,KAAK;AACvB,sBAAkB,KAAK;AACvB,uBAAmB,KAAK;AACxB,wBAAoB,KAAK;AACzB,wBAAoB,KAAK;AACzB,WAAO;EACT;AAEA,SAAO,SAAS,SAAS,OAAO;AAC9B,wBAAoB,KAAK;AACzB,WAAO;EACT;AAEA,SAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,KAAK,OAAO,MAAM,aAAa,IAAIJ,WAAS,CAAC,GAAG,UAAU;EACvF;AAEA,SAAO,YAAY,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAIA,WAAS,CAAC,GAAG,UAAU;EAC1F;AAEA,SAAO,WAAW,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,OAAO,GAAG,UAAU;EACjD;AAEA,SAAO,YAAY,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,KAAK,CAAC,GAAG,UAAU;EAChD;AAEA,SAAO,cAAc,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,KAAK,KAAK,CAAC,GAAG,UAAU;EACrD;AAEA,SAAO,QAAQ,SAAS,GAAG;AACzB,WAAO,UAAU,UAAUE,SAAQ,OAAO,MAAM,aAAa,IAAIF,WAAS,CAAC,GAAG,UAAUE;EAC1F;AAEA,SAAO,QAAQ,SAAS,GAAG;AACzB,WAAO,UAAU,UAAUE,SAAQ,OAAO,MAAM,aAAa,IAAIJ,WAAS,CAAC,GAAG,UAAUI;EAC1F;AAEA,SAAO,WAAW,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,WAAW,GAAG,UAAU;EACrD;AAEA,SAAO,OAAO,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,KAAK,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,KAAK,IAAI,KAAK,EAAE;EAC7F;AAEA,SAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;EACtH;AAEA,SAAO,aAAa,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,aAAa,CAAC,GAAG,UAAU;EACxD;AAEA,WAAS,iBAAiB,EAAC,OAAAF,QAAO,OAAAE,OAAK,GAAG;AACxC,eAAW,CAAC,GAAG,IAAI,KAAKF,OAAM,QAAO,GAAI;AACvC,WAAK,QAAQ;AACb,WAAK,cAAc,CAAA;AACnB,WAAK,cAAc,CAAA;IACrB;AACA,UAAM,WAAW,IAAI,IAAIA,OAAM,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,GAAGA,MAAK,GAAG,CAAC,CAAC,CAAC;AAClE,eAAW,CAAC,GAAGC,KAAI,KAAKC,OAAM,QAAO,GAAI;AACvCD,YAAK,QAAQ;AACb,UAAI,EAAC,QAAQ,OAAM,IAAIA;AACvB,UAAI,OAAO,WAAW;AAAU,iBAASA,MAAK,SAAS,KAAK,UAAU,MAAM;AAC5E,UAAI,OAAO,WAAW;AAAU,iBAASA,MAAK,SAAS,KAAK,UAAU,MAAM;AAC5E,aAAO,YAAY,KAAKA,KAAI;AAC5B,aAAO,YAAY,KAAKA,KAAI;IAC9B;AACA,QAAI,YAAY,MAAM;AACpB,iBAAW,EAAC,aAAa,YAAW,KAAKD,QAAO;AAC9C,oBAAY,KAAK,QAAQ;AACzB,oBAAY,KAAK,QAAQ;MAC3B;IACF;EACF;AAEA,WAAS,kBAAkB,EAAC,OAAAA,OAAK,GAAG;AAClC,eAAW,QAAQA,QAAO;AACxB,WAAK,QAAQ,KAAK,eAAe,SAC3B,KAAK,IAAI,IAAI,KAAK,aAAa,KAAK,GAAG,IAAI,KAAK,aAAa,KAAK,CAAC,IACnE,KAAK;IACb;EACF;AAEA,WAAS,kBAAkB,EAAC,OAAAA,OAAK,GAAG;AAClC,UAAM,IAAIA,OAAM;AAChB,QAAI,UAAU,IAAI,IAAIA,MAAK;AAC3B,QAAI,OAAO,oBAAI,IAAA;AACf,QAAID,KAAI;AACR,WAAO,QAAQ,MAAM;AACnB,iBAAW,QAAQ,SAAS;AAC1B,aAAK,QAAQA;AACb,mBAAW,EAAC,OAAM,KAAK,KAAK,aAAa;AACvC,eAAK,IAAI,MAAM;QACjB;MACF;AACA,UAAI,EAAEA,KAAI;AAAG,cAAM,IAAI,MAAM,eAAe;AAC5C,gBAAU;AACV,aAAO,oBAAI,IAAA;IACb;EACF;AAEA,WAAS,mBAAmB,EAAC,OAAAC,OAAK,GAAG;AACnC,UAAM,IAAIA,OAAM;AAChB,QAAI,UAAU,IAAI,IAAIA,MAAK;AAC3B,QAAI,OAAO,oBAAI,IAAA;AACf,QAAID,KAAI;AACR,WAAO,QAAQ,MAAM;AACnB,iBAAW,QAAQ,SAAS;AAC1B,aAAK,SAASA;AACd,mBAAW,EAAC,OAAM,KAAK,KAAK,aAAa;AACvC,eAAK,IAAI,MAAM;QACjB;MACF;AACA,UAAI,EAAEA,KAAI;AAAG,cAAM,IAAI,MAAM,eAAe;AAC5C,gBAAU;AACV,aAAO,oBAAI,IAAA;IACb;EACF;AAEA,WAAS,kBAAkB,EAAC,OAAAC,OAAK,GAAG;AAClC,UAAMD,KAAI,IAAIC,QAAO,CAAA,MAAK,EAAE,KAAK,IAAI;AACrC,UAAM,MAAM,KAAK,KAAK,OAAOD,KAAI;AACjC,UAAM,UAAU,IAAI,MAAMA,EAAC;AAC3B,eAAW,QAAQC,QAAO;AACxB,YAAM,IAAI,KAAK,IAAI,GAAG,KAAK,IAAID,KAAI,GAAG,KAAK,MAAM,MAAM,KAAK,MAAM,MAAMA,EAAC,CAAC,CAAC,CAAC;AAC5E,WAAK,QAAQ;AACb,WAAK,KAAK,KAAK,IAAI;AACnB,WAAK,KAAK,KAAK,KAAK;AACpB,UAAI,QAAQ,CAAC;AAAG,gBAAQ,CAAC,EAAE,KAAK,IAAI;;AAC/B,gBAAQ,CAAC,IAAI,CAAC,IAAI;IACzB;AACA,QAAI;AAAM,iBAAW,UAAU,SAAS;AACtC,eAAO,KAAK,IAAI;MAClB;AACA,WAAO;EACT;AAEA,WAAS,uBAAuB,SAAS;AACvC,UAAM,KAAK,IAAI,SAAS,CAAA,OAAM,KAAK,MAAM,EAAE,SAAS,KAAK,MAAM,IAAI,GAAG,KAAK,CAAC;AAC5E,eAAWC,UAAS,SAAS;AAC3B,UAAIG,KAAI;AACR,iBAAW,QAAQH,QAAO;AACxB,aAAK,KAAKG;AACV,aAAK,KAAKA,KAAI,KAAK,QAAQ;AAC3BA,aAAI,KAAK,KAAK;AACd,mBAAWF,SAAQ,KAAK,aAAa;AACnCA,gBAAK,QAAQA,MAAK,QAAQ;QAC5B;MACF;AACAE,YAAK,KAAKA,KAAI,OAAOH,OAAM,SAAS;AACpC,eAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,EAAE,GAAG;AACrC,cAAM,OAAOA,OAAM,CAAC;AACpB,aAAK,MAAMG,MAAK,IAAI;AACpB,aAAK,MAAMA,MAAK,IAAI;MACtB;AACA,mBAAaH,MAAK;IACpB;EACF;AAEA,WAAS,oBAAoB,OAAO;AAClC,UAAM,UAAU,kBAAkB,KAAK;AACvC,SAAK,KAAK,IAAI,KAAK,KAAK,OAAO,IAAI,SAAS,CAAA,MAAK,EAAE,MAAM,IAAI,EAAE;AAC/D,2BAAuB,OAAO;AAC9B,aAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,YAAM,QAAQ,KAAK,IAAI,MAAM,CAAC;AAC9B,YAAM,OAAO,KAAK,IAAI,IAAI,QAAQ,IAAI,KAAK,UAAU;AACrD,uBAAiB,SAAS,OAAO,IAAI;AACrC,uBAAiB,SAAS,OAAO,IAAI;IACvC;EACF;AAGA,WAAS,iBAAiB,SAAS,OAAO,MAAM;AAC9C,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,YAAM,SAAS,QAAQ,CAAC;AACxB,iBAAW,UAAU,QAAQ;AAC3B,YAAIG,KAAI;AACR,YAAI,IAAI;AACR,mBAAW,EAAC,QAAQ,OAAAR,OAAK,KAAK,OAAO,aAAa;AAChD,cAAI,IAAIA,UAAS,OAAO,QAAQ,OAAO;AACvCQ,gBAAK,UAAU,QAAQ,MAAM,IAAI;AACjC,eAAK;QACP;AACA,YAAI,EAAE,IAAI;AAAI;AACd,YAAIC,OAAMD,KAAI,IAAI,OAAO,MAAM;AAC/B,eAAO,MAAMC;AACb,eAAO,MAAMA;AACb,yBAAiB,MAAM;MACzB;AACA,UAAI,SAAS;AAAW,eAAO,KAAK,gBAAgB;AACpD,wBAAkB,QAAQ,IAAI;IAChC;EACF;AAGA,WAAS,iBAAiB,SAAS,OAAO,MAAM;AAC9C,aAAS,IAAI,QAAQ,QAAQ,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACnD,YAAM,SAAS,QAAQ,CAAC;AACxB,iBAAW,UAAU,QAAQ;AAC3B,YAAID,KAAI;AACR,YAAI,IAAI;AACR,mBAAW,EAAC,QAAQ,OAAAR,OAAK,KAAK,OAAO,aAAa;AAChD,cAAI,IAAIA,UAAS,OAAO,QAAQ,OAAO;AACvCQ,gBAAK,UAAU,QAAQ,MAAM,IAAI;AACjC,eAAK;QACP;AACA,YAAI,EAAE,IAAI;AAAI;AACd,YAAIC,OAAMD,KAAI,IAAI,OAAO,MAAM;AAC/B,eAAO,MAAMC;AACb,eAAO,MAAMA;AACb,yBAAiB,MAAM;MACzB;AACA,UAAI,SAAS;AAAW,eAAO,KAAK,gBAAgB;AACpD,wBAAkB,QAAQ,IAAI;IAChC;EACF;AAEA,WAAS,kBAAkBJ,QAAO,OAAO;AACvC,UAAM,IAAIA,OAAM,UAAU;AAC1B,UAAM,UAAUA,OAAM,CAAC;AACvB,iCAA6BA,QAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,KAAK;AACjE,iCAA6BA,QAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,KAAK;AACjE,iCAA6BA,QAAO,IAAIA,OAAM,SAAS,GAAG,KAAK;AAC/D,iCAA6BA,QAAO,IAAI,GAAG,KAAK;EAClD;AAGA,WAAS,6BAA6BA,QAAOG,IAAG,GAAG,OAAO;AACxD,WAAO,IAAIH,OAAM,QAAQ,EAAE,GAAG;AAC5B,YAAM,OAAOA,OAAM,CAAC;AACpB,YAAMI,OAAMD,KAAI,KAAK,MAAM;AAC3B,UAAIC,MAAK;AAAM,aAAK,MAAMA,KAAI,KAAK,MAAMA;AACzCD,WAAI,KAAK,KAAK;IAChB;EACF;AAGA,WAAS,6BAA6BH,QAAOG,IAAG,GAAG,OAAO;AACxD,WAAO,KAAK,GAAG,EAAE,GAAG;AAClB,YAAM,OAAOH,OAAM,CAAC;AACpB,YAAMI,OAAM,KAAK,KAAKD,MAAK;AAC3B,UAAIC,MAAK;AAAM,aAAK,MAAMA,KAAI,KAAK,MAAMA;AACzCD,WAAI,KAAK,KAAK;IAChB;EACF;AAEA,WAAS,iBAAiB,EAAC,aAAa,YAAW,GAAG;AACpD,QAAI,aAAa,QAAW;AAC1B,iBAAW,EAAC,QAAQ,EAAC,aAAAE,aAAW,EAAC,KAAK,aAAa;AACjDA,qBAAY,KAAK,sBAAsB;MACzC;AACA,iBAAW,EAAC,QAAQ,EAAC,aAAAC,aAAW,EAAC,KAAK,aAAa;AACjDA,qBAAY,KAAK,sBAAsB;MACzC;IACF;EACF;AAEA,WAAS,aAAaN,QAAO;AAC3B,QAAI,aAAa,QAAW;AAC1B,iBAAW,EAAC,aAAa,YAAW,KAAKA,QAAO;AAC9C,oBAAY,KAAK,sBAAsB;AACvC,oBAAY,KAAK,sBAAsB;MACzC;IACF;EACF;AAGA,WAAS,UAAU,QAAQ,QAAQ;AACjC,QAAIG,KAAI,OAAO,MAAM,OAAO,YAAY,SAAS,KAAK,KAAK;AAC3D,eAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,UAAI,SAAS;AAAQ;AACrBA,YAAK,QAAQ;IACf;AACA,eAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,UAAI,SAAS;AAAQ;AACrBA,YAAK;IACP;AACA,WAAOA;EACT;AAGA,WAAS,UAAU,QAAQ,QAAQ;AACjC,QAAIA,KAAI,OAAO,MAAM,OAAO,YAAY,SAAS,KAAK,KAAK;AAC3D,eAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,UAAI,SAAS;AAAQ;AACrBA,YAAK,QAAQ;IACf;AACA,eAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,UAAI,SAAS;AAAQ;AACrBA,YAAK;IACP;AACA,WAAOA;EACT;AAEA,SAAO;AACT;AChXA,IAAI,KAAK,KAAK;AAAd,IACI,MAAM,IAAI;AADd,IAEI,UAAU;AAFd,IAGI,aAAa,MAAM;AAEvB,SAAS,OAAO;AACd,OAAK,MAAM,KAAK;EAChB,KAAK,MAAM,KAAK,MAAM;AACtB,OAAK,IAAI;AACX;AAEA,SAAS,OAAO;AACd,SAAO,IAAI,KAAA;AACb;AAEA,KAAK,YAAY,KAAK,YAAY;EAChC,aAAa;EACb,QAAQ,SAASJ,IAAGI,IAAG;AACrB,SAAK,KAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACJ,MAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACI;EAC7E;EACA,WAAW,WAAW;AACpB,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK;AACrC,WAAK,KAAK;IACZ;EACF;EACA,QAAQ,SAASJ,IAAGI,IAAG;AACrB,SAAK,KAAK,OAAO,KAAK,MAAM,CAACJ,MAAK,OAAO,KAAK,MAAM,CAACI;EACvD;EACA,kBAAkB,SAAS,IAAI,IAAIJ,IAAGI,IAAG;AACvC,SAAK,KAAK,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,OAAO,KAAK,MAAM,CAACJ,MAAK,OAAO,KAAK,MAAM,CAACI;EACnF;EACA,eAAe,SAAS,IAAI,IAAI,IAAI,IAAIJ,IAAGI,IAAG;AAC5C,SAAK,KAAK,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,OAAO,KAAK,MAAM,CAACJ,MAAK,OAAO,KAAK,MAAM,CAACI;EAC/G;EACA,OAAO,SAAS,IAAI,IAAI,IAAI,IAAI,GAAG;AACjC,SAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC;AAC7C,QAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACV,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM;AAG9B,QAAI,IAAI;AAAG,YAAM,IAAI,MAAM,sBAAsB,CAAC;AAGlD,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;IACtD,WAGS,EAAE,QAAQ;AAAS;aAKnB,EAAE,KAAK,IAAI,MAAM,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,GAAG;AAC3D,WAAK,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;IACtD,OAGK;AACH,UAAI,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM,KAC1B,QAAQ,MAAM,MAAM,MAAM,KAC1B,MAAM,KAAK,KAAK,KAAK,GACrB,MAAM,KAAK,KAAK,KAAK,GACrB,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,QAAQ,QAAQ,UAAU,IAAI,MAAM,IAAI,KAAK,CAAC,GAChF,MAAM,IAAI,KACV,MAAM,IAAI;AAGd,UAAI,KAAK,IAAI,MAAM,CAAC,IAAI,SAAS;AAC/B,aAAK,KAAK,OAAO,KAAK,MAAM,OAAO,OAAO,KAAK,MAAM;MACvD;AAEA,WAAK,KAAK,MAAM,IAAI,MAAM,IAAI,UAAW,EAAE,MAAM,MAAM,MAAM,OAAQ,OAAO,KAAK,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,MAAM,KAAK,MAAM;IACxI;EACF;EACA,KAAK,SAASJ,IAAGI,IAAG,GAAG,IAAI,IAAI,KAAK;AAClCJ,SAAI,CAACA,IAAGI,KAAI,CAACA,IAAG,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AAChC,QAAI,KAAK,IAAI,KAAK,IAAI,EAAE,GACpB,KAAK,IAAI,KAAK,IAAI,EAAE,GACpB,KAAKJ,KAAI,IACT,KAAKI,KAAI,IACT,KAAK,IAAI,KACT,KAAK,MAAM,KAAK,KAAK,KAAK;AAG9B,QAAI,IAAI;AAAG,YAAM,IAAI,MAAM,sBAAsB,CAAC;AAGlD,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,KAAK,MAAM,KAAK,MAAM;IAC7B,WAGS,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,WAAW,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,SAAS;AAC/E,WAAK,KAAK,MAAM,KAAK,MAAM;IAC7B;AAGA,QAAI,CAAC;AAAG;AAGR,QAAI,KAAK;AAAG,WAAK,KAAK,MAAM;AAG5B,QAAI,KAAK,YAAY;AACnB,WAAK,KAAK,MAAM,IAAI,MAAM,IAAI,UAAU,KAAK,OAAOJ,KAAI,MAAM,OAAOI,KAAI,MAAM,MAAM,IAAI,MAAM,IAAI,UAAU,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;IAC9J,WAGS,KAAK,SAAS;AACrB,WAAK,KAAK,MAAM,IAAI,MAAM,IAAI,QAAS,EAAE,MAAM,MAAO,MAAM,KAAK,OAAO,KAAK,MAAMJ,KAAI,IAAI,KAAK,IAAI,EAAE,KAAK,OAAO,KAAK,MAAMI,KAAI,IAAI,KAAK,IAAI,EAAE;IAClJ;EACF;EACA,MAAM,SAASJ,IAAGI,IAAG,GAAG,GAAG;AACzB,SAAK,KAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACJ,MAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACI,MAAK,MAAO,CAAC,IAAK,MAAO,CAAC,IAAK,MAAO,CAAC,IAAK;EACzH;EACA,UAAU,WAAW;AACnB,WAAO,KAAK;EACd;AACF;AC/He,SAAA,SAASJ,IAAG;AACzB,SAAO,SAASD,YAAW;AACzB,WAAOC;EACT;AACF;ACJO,SAAS,EAAE,GAAG;AACnB,SAAO,EAAE,CAAC;AACZ;AAEO,SAAS,EAAE,GAAG;AACnB,SAAO,EAAE,CAAC;AACZ;ACNO,IAAI,QAAQ,MAAM,UAAU;ACMnC,SAAS,WAAW,GAAG;AACrB,SAAO,EAAE;AACX;AAEA,SAAS,WAAW,GAAG;AACrB,SAAO,EAAE;AACX;AAEA,SAAS,KAAK,OAAO;AACnB,MAAI,SAAS,YACT,SAAS,YACTA,MAAIQ,GACJJ,MAAIK,GACJ,UAAU;AAEd,WAASP,QAAO;AACd,QAAI,QAAQ,OAAO,MAAM,KAAK,SAAS,GAAG,IAAI,OAAO,MAAM,MAAM,IAAI,GAAG,IAAI,OAAO,MAAM,MAAM,IAAI;AACnG,QAAI,CAAC;AAAS,gBAAU,SAAS,KAAI;AACrC,UAAM,SAAS,CAACF,IAAE,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,KAAI,GAAI,CAACI,IAAE,MAAM,MAAM,IAAI,GAAG,CAACJ,IAAE,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,KAAI,GAAI,CAACI,IAAE,MAAM,MAAM,IAAI,CAAC;AACnI,QAAI;AAAQ,aAAO,UAAU,MAAM,SAAS,MAAM;EACpD;AAEAF,QAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,SAAS,GAAGA,SAAQ;EACjD;AAEAA,QAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,SAAS,GAAGA,SAAQ;EACjD;AAEAA,QAAK,IAAI,SAAS,GAAG;AACnB,WAAO,UAAU,UAAUF,MAAI,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGE,SAAQF;EACrF;AAEAE,QAAK,IAAI,SAAS,GAAG;AACnB,WAAO,UAAU,UAAUE,MAAI,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGF,SAAQE;EACrF;AAEAF,QAAK,UAAU,SAAS,GAAG;AACzB,WAAO,UAAU,UAAW,UAAU,KAAK,OAAO,OAAO,GAAIA,SAAQ;EACvE;AAEA,SAAOA;AACT;AAEA,SAAS,gBAAgB,SAAS,IAAI,IAAI,IAAI,IAAI;AAChD,UAAQ,OAAO,IAAI,EAAE;AACrB,UAAQ,cAAc,MAAM,KAAK,MAAM,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAC9D;AAgBO,SAAS,iBAAiB;AAC/B,SAAO,KAAK,eAAe;AAC7B;ACtEA,SAAS,iBAAiB,GAAG;AAC3B,SAAO,CAAC,EAAE,OAAO,IAAI,EAAE,EAAE;AAC3B;AAEA,SAAS,iBAAiB,GAAG;AAC3B,SAAO,CAAC,EAAE,OAAO,IAAI,EAAE,EAAE;AAC3B;AAEe,SAAA,yBAAW;AACxB,SAAO,eAAc,EAChB,OAAO,gBAAgB,EACvB,OAAO,gBAAgB;AAC9B;ACEA,IAAI,SAAS,WAAW;AACtB,MAAI,IAAoB,OAAO,SAAS,GAAG,GAAG,IAAI,GAAG;AACnD,SAAK,KAAK,MAAM,CAAA,GAAI,IAAI,EAAE,QAAQ,KAAK,GAAG,EAAE,CAAC,CAAC,IAAI;AAAG;AACrD,WAAO;EACT,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,IAAI,EAAE;AACzD,MAAI,UAAU;IACZ,OAAuB,OAAO,SAAS,QAAQ;IAC/C,GAAG,OAAO;IACV,IAAI,CAAA;IACJ,UAAU,EAAE,SAAS,GAAG,SAAS,GAAG,UAAU,GAAG,WAAW,GAAG,OAAO,GAAG,WAAW,GAAG,UAAU,GAAG,YAAY,GAAG,OAAO,IAAI,iBAAiB,IAAI,SAAS,IAAI,iBAAiB,IAAI,gBAAgB,IAAI,SAAS,IAAI,WAAW,IAAI,eAAe,IAAI,UAAU,IAAI,gBAAgB,IAAI,oBAAoB,IAAI,WAAW,GAAG,QAAQ,EAAC;IACzU,YAAY,EAAE,GAAG,SAAS,GAAG,UAAU,GAAG,WAAW,IAAI,OAAO,IAAI,iBAAiB,IAAI,SAAS,IAAI,iBAAiB,IAAI,gBAAgB,IAAI,UAAU,IAAI,gBAAgB,IAAI,mBAAkB;IACnM,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5G,eAA+B,OAAO,SAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAS,IAAI,IAAI;AACtG,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAO;QACb,KAAK;AACH,gBAAM,SAAS,GAAG,iBAAiB,GAAG,KAAK,CAAC,EAAE,KAAI,EAAG,WAAW,MAAM,GAAG,CAAC;AAC1E,gBAAM,SAAS,GAAG,iBAAiB,GAAG,KAAK,CAAC,EAAE,KAAI,EAAG,WAAW,MAAM,GAAG,CAAC;AAC1E,gBAAMN,SAAQ,WAAW,GAAG,EAAE,EAAE,KAAI,CAAE;AACtC,aAAG,QAAQ,QAAQ,QAAQA,MAAK;AAChC;QACF,KAAK;QACL,KAAK;QACL,KAAK;AACH,eAAK,IAAI,GAAG,EAAE;AACd;QACF,KAAK;AACH,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;MACV;IACI,GAAG,WAAW;IACd,OAAO,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,EAAA,GAAK,EAAE,GAAG,CAAC,CAAC,EAAC,GAAI,EAAE,GAAG,CAAC,GAAG,CAAC,EAAA,GAAK,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,IAAI,IAAG,GAAI,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,EAAC,CAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAC,GAAI,EAAE,GAAG,CAAC,GAAG,CAAC,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,IAAI,IAAG,GAAI,EAAE,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,IAAI,IAAG,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,IAAI,IAAG,GAAI,EAAE,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACliB,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAC;IACxC,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,UAAI,KAAK,aAAa;AACpB,aAAK,MAAM,GAAG;MAChB,OAAO;AACL,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;MACR;IACF,GAAG,YAAY;IACf,OAAuB,OAAO,SAAS,MAAM,OAAO;AAC/C,UAAC,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAA,GAAI,SAAS,CAAC,IAAI,GAAG,SAAS,CAAA,GAAI,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAmB,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAI,SAAS,OAAO,OAAO,KAAK,KAAK;AACrC,UAAI,cAAc,EAAE,IAAI,CAAA,EAAE;AAC1B,eAAS,KAAK,KAAK,IAAI;AACrB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AACpD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;QAC/B;MACF;AACA,aAAO,SAAS,OAAO,YAAY,EAAE;AACrC,kBAAY,GAAG,QAAQ;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAO,OAAO,UAAU,aAAa;AACvC,eAAO,SAAS,CAAA;MAClB;AACA,UAAI,QAAQ,OAAO;AACnB,aAAO,KAAK,KAAK;AACjB,UAAI,SAAS,OAAO,WAAW,OAAO,QAAQ;AAC9C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACnD,aAAK,aAAa,YAAY,GAAG;MACnC,OAAO;AACL,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;MAChD;AACA,eAAS,SAAS,GAAG;AACnB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;MAClC;AACA,aAAO,UAAU,UAAU;AAC3B,eAAS,MAAM;AACb,YAAI;AACJ,gBAAQ,OAAO,IAAG,KAAM,OAAO,IAAG,KAAM;AACxC,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,iBAAiB,OAAO;AAC1B,qBAAS;AACT,oBAAQ,OAAO,IAAG;UACpB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;QAClC;AACA,eAAO;MACT;AACA,aAAO,KAAK,KAAK;AACd,UAAC,QAAwB,OAAO,QAAW,GAAG,QAAQ,CAAA,GAAI,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACX,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC9B,mBAAS,KAAK,eAAe,KAAK;QACpC,OAAO;AACL,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACnD,qBAAS,IAAG;UACd;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;QAC9C;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AACjE,cAAI,SAAS;AACb,qBAAW,CAAA;AACX,eAAK,KAAK,MAAM,KAAK,GAAG;AACtB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AACpC,uBAAS,KAAK,MAAM,KAAK,WAAW,CAAC,IAAI,GAAG;YAC9C;UACF;AACA,cAAI,OAAO,cAAc;AACvB,qBAAS,0BAA0B,WAAW,KAAK,QAAQ,OAAO,aAAY,IAAK,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAa,KAAK,WAAW,MAAM,KAAK,UAAU;UAC9K,OAAO;AACL,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAO,KAAK,WAAW,MAAM,KAAK,UAAU;UACrJ;AACA,eAAK,WAAW,QAAQ;YACtB,MAAM,OAAO;YACb,OAAO,KAAK,WAAW,MAAM,KAAK;YAClC,MAAM,OAAO;YACb,KAAK;YACL;UACZ,CAAW;QACH;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACnD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;QACpG;AACA,gBAAQ,OAAO,CAAC,GAAC;UACf,KAAK;AACH,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAK,OAAO,MAAM;AACzB,mBAAO,KAAK,OAAO,MAAM;AACzB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACY;AACnB,uBAAS,OAAO;AAChB,uBAAS,OAAO;AAChB,yBAAW,OAAO;AAClB,sBAAQ,OAAO;YAIjB;AAIA;UACF,KAAK;AACH,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;cACT,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;YACrD;AACY,gBAAI,QAAQ;AACV,oBAAM,GAAG,QAAQ;gBACf,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;cACjD;YACY;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;cAClC;cACA;cACA;cACA,YAAY;cACZ,OAAO,CAAC;cACR;cACA;YACd,EAAc,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC5B,qBAAO;YACT;AACA,gBAAI,KAAK;AACP,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;YACnC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;UACF,KAAK;AACH,mBAAO;QACnB;MACM;AACA,aAAO;IACT,GAAG,OAAO;EACd;AACE,MAAI,QAAwB,WAAW;AACrC,QAAI,SAAS;MACX,KAAK;MACL,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,YAAI,KAAK,GAAG,QAAQ;AAClB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;QACrC,OAAO;AACL,gBAAM,IAAI,MAAM,GAAG;QACrB;MACF,GAAG,YAAY;;MAEf,UAA0B,OAAO,SAAS,OAAO,IAAI;AACnD,aAAK,KAAK,MAAM,KAAK,MAAM,CAAA;AAC3B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;UACZ,YAAY;UACZ,cAAc;UACd,WAAW;UACX,aAAa;QACvB;AACQ,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,GAAG,CAAC;QAC3B;AACA,aAAK,SAAS;AACd,eAAO;MACT,GAAG,UAAU;;MAEb,OAAuB,OAAO,WAAW;AACvC,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACT,eAAK;AACL,eAAK,OAAO;QACd,OAAO;AACL,eAAK,OAAO;QACd;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,MAAM,CAAC;QACrB;AACA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;MACT,GAAG,OAAO;;MAEV,OAAuB,OAAO,SAAS,IAAI;AACzC,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AACpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAC5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAC7D,YAAI,MAAM,SAAS,GAAG;AACpB,eAAK,YAAY,MAAM,SAAS;QAClC;AACA,YAAI,IAAI,KAAK,OAAO;AACpB,aAAK,SAAS;UACZ,YAAY,KAAK,OAAO;UACxB,WAAW,KAAK,WAAW;UAC3B,cAAc,KAAK,OAAO;UAC1B,aAAa,SAAS,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAAK,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS,KAAK,OAAO,eAAe;QAClM;AACQ,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;QACrD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;MACT,GAAG,OAAO;;MAEV,MAAsB,OAAO,WAAW;AACtC,aAAK,QAAQ;AACb,eAAO;MACT,GAAG,MAAM;;MAET,QAAwB,OAAO,WAAW;AACxC,YAAI,KAAK,QAAQ,iBAAiB;AAChC,eAAK,aAAa;QACpB,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAA,GAAgB;YAChO,MAAM;YACN,OAAO;YACP,MAAM,KAAK;UACvB,CAAW;QACH;AACA,eAAO;MACT,GAAG,QAAQ;;MAEX,MAAsB,OAAO,SAAS,GAAG;AACvC,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;MAChC,GAAG,MAAM;;MAET,WAA2B,OAAO,WAAW;AAC3C,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAQ,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;MAC7E,GAAG,WAAW;;MAEd,eAA+B,OAAO,WAAW;AAC/C,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AACpB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAK,KAAK,MAAM;QAChD;AACA,gBAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;MACjF,GAAG,eAAe;;MAElB,cAA8B,OAAO,WAAW;AAC9C,YAAI,MAAM,KAAK,UAAS;AACxB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAa,IAAK,OAAO,IAAI;MACjD,GAAG,cAAc;;MAEjB,YAA4B,OAAO,SAAS,OAAO,cAAc;AAC/D,YAAI,OAAO,OAAO;AAClB,YAAI,KAAK,QAAQ,iBAAiB;AAChC,mBAAS;YACP,UAAU,KAAK;YACf,QAAQ;cACN,YAAY,KAAK,OAAO;cACxB,WAAW,KAAK;cAChB,cAAc,KAAK,OAAO;cAC1B,aAAa,KAAK,OAAO;YACvC;YACY,QAAQ,KAAK;YACb,OAAO,KAAK;YACZ,SAAS,KAAK;YACd,SAAS,KAAK;YACd,QAAQ,KAAK;YACb,QAAQ,KAAK;YACb,OAAO,KAAK;YACZ,QAAQ,KAAK;YACb,IAAI,KAAK;YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;YAC3C,MAAM,KAAK;UACvB;AACU,cAAI,KAAK,QAAQ,QAAQ;AACvB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;UACjD;QACF;AACA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACT,eAAK,YAAY,MAAM;QACzB;AACA,aAAK,SAAS;UACZ,YAAY,KAAK,OAAO;UACxB,WAAW,KAAK,WAAW;UAC3B,cAAc,KAAK,OAAO;UAC1B,aAAa,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAAS,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;QACvJ;AACQ,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;QAC9D;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC5B,eAAK,OAAO;QACd;AACA,YAAI,OAAO;AACT,iBAAO;QACT,WAAW,KAAK,YAAY;AAC1B,mBAAS,KAAK,QAAQ;AACpB,iBAAK,CAAC,IAAI,OAAO,CAAC;UACpB;AACA,iBAAO;QACT;AACA,eAAO;MACT,GAAG,YAAY;;MAEf,MAAsB,OAAO,WAAW;AACtC,YAAI,KAAK,MAAM;AACb,iBAAO,KAAK;QACd;AACA,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,OAAO;QACd;AACA,YAAI,OAAO,OAAO,WAAW;AAC7B,YAAI,CAAC,KAAK,OAAO;AACf,eAAK,SAAS;AACd,eAAK,QAAQ;QACf;AACA,YAAI,QAAQ,KAAK,cAAa;AAC9B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAClE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAChC,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACnB,uBAAO;cACT,WAAW,KAAK,YAAY;AAC1B,wBAAQ;AACR;cACF,OAAO;AACL,uBAAO;cACT;YACF,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC7B;YACF;UACF;QACF;AACA,YAAI,OAAO;AACT,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACnB,mBAAO;UACT;AACA,iBAAO;QACT;AACA,YAAI,KAAK,WAAW,IAAI;AACtB,iBAAO,KAAK;QACd,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAA,GAAgB;YACtH,MAAM;YACN,OAAO;YACP,MAAM,KAAK;UACvB,CAAW;QACH;MACF,GAAG,MAAM;;MAET,KAAqB,OAAO,SAAS,MAAM;AACzC,YAAI,IAAI,KAAK,KAAI;AACjB,YAAI,GAAG;AACL,iBAAO;QACT,OAAO;AACL,iBAAO,KAAK,IAAG;QACjB;MACF,GAAG,KAAK;;MAER,OAAuB,OAAO,SAAS,MAAM,WAAW;AACtD,aAAK,eAAe,KAAK,SAAS;MACpC,GAAG,OAAO;;MAEV,UAA0B,OAAO,SAAS,WAAW;AACnD,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACT,iBAAO,KAAK,eAAe,IAAG;QAChC,OAAO;AACL,iBAAO,KAAK,eAAe,CAAC;QAC9B;MACF,GAAG,UAAU;;MAEb,eAA+B,OAAO,SAAS,gBAAgB;AAC7D,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACrF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;QAC9E,OAAO;AACL,iBAAO,KAAK,WAAW,SAAS,EAAE;QACpC;MACF,GAAG,eAAe;;MAElB,UAA0B,OAAO,SAAS,SAAS,GAAG;AACpD,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACV,iBAAO,KAAK,eAAe,CAAC;QAC9B,OAAO;AACL,iBAAO;QACT;MACF,GAAG,UAAU;;MAEb,WAA2B,OAAO,SAAS,UAAU,WAAW;AAC9D,aAAK,MAAM,SAAS;MACtB,GAAG,WAAW;;MAEd,gBAAgC,OAAO,SAAS,iBAAiB;AAC/D,eAAO,KAAK,eAAe;MAC7B,GAAG,gBAAgB;MACnB,SAAS,EAAE,oBAAoB,KAAI;MACnC,eAA+B,OAAO,SAAS,UAAU,IAAI,KAAK,2BAA2B,UAAU;AAErG,gBAAQ,2BAAyB;UAC/B,KAAK;AACH,iBAAK,UAAU,KAAK;AACpB,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,cAAc;AAC7B,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,SAAS,cAAc;AAC5B,mBAAO;UAET,KAAK;AACH,mBAAO;QAEnB;MACM,GAAG,WAAW;MACd,OAAO,CAAC,uBAAuB,WAAW,mCAAmC,kBAAkB,kBAAkB,sDAAsD,8BAA8B,kGAAkG;MACvS,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,aAAa,MAAA,GAAS,gBAAgB,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG,aAAa,MAAK,GAAI,WAAW,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,aAAa,KAAI,EAAE;IAC/M;AACI,WAAO;EACT,EAAC;AACD,UAAQ,QAAQ;AAChB,WAAS,SAAS;AAChB,SAAK,KAAK,CAAA;EACZ;AACA,SAAO,QAAQ,QAAQ;AACvB,SAAO,YAAY;AACnB,UAAQ,SAAS;AACjB,SAAO,IAAI,OAAM;AACnB,EAAC;AACD,OAAO,SAAS;AAChB,IAAI,iBAAiB;AAGrB,IAAI,QAAQ,CAAA;AACZ,IAAI,QAAQ,CAAA;AACZ,IAAI,WAA2B,oBAAI,IAAG;AACtC,IAAI,SAAyB,OAAO,MAAM;AACxC,UAAQ,CAAA;AACR,UAAQ,CAAA;AACR,aAA2B,oBAAI,IAAG;AAClC,UAAK;AACP,GAAG,OAAO;AACV,IAAI,cAAa,KAAA,MAAM;EACrB,YAAY,QAAQ,QAAQA,SAAQ,GAAG;AACrC,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,QAAQA;EACf;AAIF,GAFI,OAAO,IAAM,YAAY,GAPZ;AAUjB,IAAI,UAA0B,OAAO,CAAC,QAAQ,QAAQA,WAAU;AAC9D,QAAM,KAAK,IAAI,WAAW,QAAQ,QAAQA,MAAK,CAAC;AAClD,GAAG,SAAS;AACZ,IAAI,cAAa,KAAA,MAAM;EACrB,YAAY,IAAI;AACd,SAAK,KAAK;EACZ;AAIF,GAFI,OAAO,IAAM,YAAY,GALZ;AAQjB,IAAI,mBAAmC,OAAO,CAAC,OAAO;AACpD,OAAK,eAAe,aAAa,IAAIc,WAAS,CAAE;AAChD,MAAI,OAAO,SAAS,IAAI,EAAE;AAC1B,MAAI,SAAS,QAAQ;AACnB,WAAO,IAAI,WAAW,EAAE;AACxB,aAAS,IAAI,IAAI,IAAI;AACrB,UAAM,KAAK,IAAI;EACjB;AACA,SAAO;AACT,GAAG,kBAAkB;AACrB,IAAI,WAA2B,OAAO,MAAM,OAAO,UAAU;AAC7D,IAAI,WAA2B,OAAO,MAAM,OAAO,UAAU;AAC7D,IAAI,WAA2B,OAAO,OAAO;EAC3C,OAAO,MAAM,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,GAAE,EAAG;EAC5C,OAAO,MAAM,IAAI,CAACR,WAAU;IAC1B,QAAQA,MAAK,OAAO;IACpB,QAAQA,MAAK,OAAO;IACpB,OAAOA,MAAK;EAChB,EAAI;AACJ,IAAI,UAAU;AACd,IAAI,mBAAmB;EACrB;EACA,WAA2B,OAAO,MAAMQ,WAAS,EAAG,QAAQ,WAAW;EACvE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAO;AACT;AAkBA,IAAI,OAAM,KAAA,MAAW;EAOnB,OAAO,KAAK,MAAM;AAChB,WAAO,IAAI,GAAK,OAAO,EAAE,GAAK,KAAK;EACrC;EACA,YAAY,IAAI;AACd,SAAK,KAAK;AACV,SAAK,OAAO,IAAI,EAAE;EACpB;EACA,WAAW;AACT,WAAO,SAAS,KAAK,OAAO;EAC9B;AACF,GAfI,OAAO,IAAM,KAAK,GAGlB,GAAK,QAAQ,GALP;AAoBV,IAAI,gBAAgB;EAClB;EACA;EACA;EACA;AACF;AACA,IAAI,OAAuB,OAAO,SAAS,MAAM,IAAI,UAAU,SAAS;AACtE,QAAM,EAAE,eAAe,QAAQ,KAAI,IAAKA,WAAS;AACjD,QAAM,sBAAsBC,eAAc;AAC1C,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiBC,OAAS,OAAO,EAAE;EACrC;AACA,QAAM,OAAO,kBAAkB,YAAYA,OAAS,eAAe,MAAA,EAAQ,CAAC,EAAE,gBAAgB,IAAI,IAAIA,OAAS,MAAM;AACrH,QAAM,MAAM,kBAAkB,YAAY,KAAK,OAAO,QAAQ,EAAE,IAAI,IAAIA,OAAS,QAAQ,EAAE,IAAI;AAC/F,QAAM,SAAQ,QAAA,OAAA,SAAA,KAAM,UAAS,oBAAoB;AACjD,QAAM,UAAS,QAAA,OAAA,SAAA,KAAM,WAAU,oBAAoB;AACnD,QAAM,eAAc,QAAA,OAAA,SAAA,KAAM,gBAAe,oBAAoB;AAC7D,QAAM,iBAAgB,QAAA,OAAA,SAAA,KAAM,kBAAiB,oBAAoB;AACjE,QAAM,UAAS,QAAA,OAAA,SAAA,KAAM,WAAU,oBAAoB;AACnD,QAAM,UAAS,QAAA,OAAA,SAAA,KAAM,WAAU,oBAAoB;AACnD,QAAM,cAAa,QAAA,OAAA,SAAA,KAAM,eAAc,oBAAoB;AAC3D,QAAM,QAAQ,QAAQ,GAAG,SAAQ;AACjC,QAAM,YAAY,cAAc,aAAa;AAC7C,QAAM,YAAY;AAClB,QAAM,SAASC,OAAQ,EAAG,OAAO,CAAC,MAAM,EAAE,EAAE,EAAE,UAAU,SAAS,EAAE,YAAY,MAAM,aAAa,KAAK,EAAE,EAAE,UAAU,SAAS,EAAE,OAAO;IACrI,CAAC,GAAG,CAAC;IACL,CAAC,OAAO,MAAM;EAClB,CAAG;AACD,SAAO,KAAK;AACZ,QAAM,cAAcC,QAAe,iBAAiB;AACpD,MAAI,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,UAAU,OAAO,EAAE,KAAK,MAAM,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,SAAS,MAAM,EAAE,KAAK,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI,KAAK,OAAO,GAAG,EAAE,EAAE,KAAK,aAAa,SAAS,GAAG;AAC1L,WAAO,eAAe,EAAE,KAAK,MAAM,EAAE,KAAK;EAC5C,CAAC,EAAE,KAAK,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,KAAK,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,OAAO,MAAM,EAAE,KAAK,UAAU,CAAC,MAAM;AACpF,WAAO,EAAE,KAAK,EAAE;EAClB,CAAC,EAAE,KAAK,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,QAAQ,CAAC,MAAM,YAAY,EAAE,EAAE,CAAC;AAC1E,QAAM,UAA0B,OAAO,CAAC,EAAE,IAAI,KAAK,OAAAlB,OAAAA,MAAY;AAC7D,QAAI,CAAC,YAAY;AACf,aAAO;IACT;AACA,WAAO,GAAG,GAAG;EACf,MAAM,GAAG,KAAK,MAAMA,SAAQ,GAAG,IAAI,GAAG,GAAG,MAAM;EAC/C,GAAG,SAAS;AACZ,MAAI,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa,EAAE,KAAK,aAAa,EAAE,EAAE,UAAU,MAAM,EAAE,KAAK,MAAM,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,KAAK,CAAC,MAAM,EAAE,KAAK,QAAQ,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,KAAK,MAAM,GAAG,aAAa,MAAM,MAAM,IAAI,EAAE,KAAK,eAAe,CAAC,MAAM,EAAE,KAAK,QAAQ,IAAI,UAAU,KAAK,EAAE,KAAK,OAAO;AACzU,QAAMM,QAAO,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,QAAQ,MAAM,EAAE,KAAK,kBAAkB,GAAG,EAAE,UAAU,OAAO,EAAE,KAAK,MAAM,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,SAAS,MAAM,EAAE,MAAM,kBAAkB,UAAU;AAC5M,QAAM,aAAY,QAAA,OAAA,SAAA,KAAM,cAAa;AACrC,MAAI,cAAc,YAAY;AAC5B,UAAM,WAAWA,MAAK,OAAO,gBAAgB,EAAE,KAAK,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI,KAAK,iBAAiB,GAAG,EAAE,EAAE,KAAK,iBAAiB,gBAAgB,EAAE,KAAK,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE;AAC/M,aAAS,OAAO,MAAM,EAAE,KAAK,UAAU,IAAI,EAAE,KAAK,cAAc,CAAC,MAAM,YAAY,EAAE,OAAO,EAAE,CAAC;AAC/F,aAAS,OAAO,MAAM,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,cAAc,CAAC,MAAM,YAAY,EAAE,OAAO,EAAE,CAAC;EACnG;AACA,MAAI;AACJ,UAAQ,WAAS;IACf,KAAK;AACH,iBAA2B,OAAO,CAAC,MAAM,EAAE,KAAK,UAAU;AAC1D;IACF,KAAK;AACH,iBAA2B,OAAO,CAAC,MAAM,YAAY,EAAE,OAAO,EAAE,GAAG,UAAU;AAC7E;IACF,KAAK;AACH,iBAA2B,OAAO,CAAC,MAAM,YAAY,EAAE,OAAO,EAAE,GAAG,UAAU;AAC7E;IACF;AACE,iBAAW;EACjB;AACEA,QAAK,OAAO,MAAM,EAAE,KAAK,KAAK,uBAAsB,CAAE,EAAE,KAAK,UAAU,QAAQ,EAAE,KAAK,gBAAgB,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,KAAK,CAAC;AACjI,oBAAkB,QAAQ,KAAK,GAAG,WAAW;AAC/C,GAAG,MAAM;AACT,IAAI,yBAAyB;EAC3B;AACF;AAGA,IAAI,wBAAwC,OAAO,CAAC,SAAS;AAC3D,QAAM,cAAc,KAAK,WAAW,4BAA4B,EAAE,EAAE,WAAW,cAAc,IAAI,EAAE,KAAI;AACvG,SAAO;AACT,GAAG,uBAAuB;AAG1B,IAAI,YAA4B,OAAO,CAAC,YAAY;qBAC/B,QAAQ,UAAU;QAC/B,WAAW;AACnB,IAAI,iBAAiB;AAGrB,IAAI,gBAAgB,eAAe,MAAM,KAAK,cAAc;AAC5D,eAAe,QAAQ,CAAC,SAAS,cAAc,sBAAsB,IAAI,CAAC;AACvE,IAAC,UAAU;EACZ,QAAQ;EACR,QAAQ;EACR,IAAI;EACJ,UAAU;AACZ;", "names": ["colors", "max", "value", "min", "sum", "constant", "x", "nodes", "link", "links", "y", "dy", "sourceLinks", "targetLinks", "pointX", "pointY", "getConfig", "defaultConfig", "d3select", "d3Sankey", "d3scaleOrdinal"]}