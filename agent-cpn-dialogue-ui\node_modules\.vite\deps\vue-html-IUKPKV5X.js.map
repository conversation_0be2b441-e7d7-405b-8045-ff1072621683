{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/vue-html.mjs"], "sourcesContent": ["import vue from './vue.mjs';\nimport javascript from './javascript.mjs';\nimport './html.mjs';\nimport './css.mjs';\nimport './markdown.mjs';\nimport './pug.mjs';\nimport './sass.mjs';\nimport './scss.mjs';\nimport './stylus.mjs';\nimport './coffee.mjs';\nimport './less.mjs';\nimport './typescript.mjs';\nimport './jsx.mjs';\nimport './tsx.mjs';\nimport './json.mjs';\nimport './jsonc.mjs';\nimport './json5.mjs';\nimport './yaml.mjs';\nimport './toml.mjs';\nimport './graphql.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Vue HTML\", \"fileTypes\": [], \"name\": \"vue-html\", \"patterns\": [{ \"include\": \"source.vue#vue-interpolations\" }, { \"begin\": \"(<)([A-Z][a-zA-Z0-9:-]*)(?=[^>]*></\\\\2>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"support.class.component.html\" } }, \"end\": \"(>)(<)(/)(\\\\2)(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" }, \"2\": { \"name\": \"punctuation.definition.tag.begin.html meta.scope.between-tag-pair.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"4\": { \"name\": \"support.class.component.html\" }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.any.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }] }, { \"begin\": \"(<)([a-z][a-zA-Z0-9:-]*)(?=[^>]*></\\\\2>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \"(>)(<)(/)(\\\\2)(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" }, \"2\": { \"name\": \"punctuation.definition.tag.begin.html meta.scope.between-tag-pair.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"4\": { \"name\": \"entity.name.tag.html\" }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.any.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }] }, { \"begin\": \"(<\\\\?)(xml)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.xml.html\" } }, \"end\": \"(\\\\?>)\", \"name\": \"meta.tag.preprocessor.xml.html\", \"patterns\": [{ \"include\": \"#tag-generic-attribute\" }, { \"include\": \"#string-double-quoted\" }, { \"include\": \"#string-single-quoted\" }] }, { \"begin\": \"<!--\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.html\" } }, \"end\": \"-->\", \"name\": \"comment.block.html\" }, { \"begin\": \"<!\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag.html\" } }, \"end\": \">\", \"name\": \"meta.tag.sgml.html\", \"patterns\": [{ \"begin\": \"(?i:DOCTYPE)\", \"captures\": { \"1\": { \"name\": \"entity.name.tag.doctype.html\" } }, \"end\": \"(?=>)\", \"name\": \"meta.tag.sgml.doctype.html\", \"patterns\": [{ \"match\": '\"[^\">]*\"', \"name\": \"string.quoted.double.doctype.identifiers-and-DTDs.html\" }] }, { \"begin\": \"\\\\[CDATA\\\\[\", \"end\": \"]](?=>)\", \"name\": \"constant.other.inline-data.html\" }, { \"match\": \"(\\\\s*)(?!--|>)\\\\S(\\\\s*)\", \"name\": \"invalid.illegal.bad-comments-or-CDATA.html\" }] }, { \"begin\": \"(</?)([A-Z][a-zA-Z0-9:-]*\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"support.class.component.html\" } }, \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.block.any.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }] }, { \"begin\": \"(</?)([a-z][a-zA-Z0-9:-]*\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.block.any.html\" } }, \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.block.any.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }] }, { \"begin\": \"(</?)((?i:body|head|html)\\\\b)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.structure.any.html\" } }, \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.structure.any.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }] }, { \"begin\": \"(</?)((?i:address|blockquote|dd|div|dl|dt|fieldset|form|frame|frameset|h1|h2|h3|h4|h5|h6|iframe|noframes|object|ol|p|ul|applet|center|dir|hr|menu|pre)(?!-)\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.block.any.html\" } }, \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.block.any.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }] }, { \"begin\": \"(</?)((?i:a|abbr|acronym|area|b|base|basefont|bdo|big|br|button|caption|cite|code|col|colgroup|del|dfn|em|font|head|html|i|img|input|ins|isindex|kbd|label|legend|li|link|map|meta|noscript|optgroup|option|param|q|s|samp|script|select|small|span|strike|strong|style|sub|sup|table|tbody|td|textarea|tfoot|th|thead|title|tr|tt|u|var)(?!-)\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.inline.any.html\" } }, \"end\": \"(/?>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.inline.any.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }] }, { \"begin\": \"(</?)([a-zA-Z0-9:-]+)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.other.html\" } }, \"end\": \"(/?>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.other.html\", \"patterns\": [{ \"include\": \"#tag-stuff\" }] }, { \"include\": \"#entities\" }, { \"match\": \"<>\", \"name\": \"invalid.illegal.incomplete.html\" }, { \"match\": \"<\", \"name\": \"invalid.illegal.bad-angle-bracket.html\" }], \"repository\": { \"entities\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.html\" }, \"3\": { \"name\": \"punctuation.definition.entity.html\" } }, \"match\": \"(&)([a-zA-Z0-9]+|#[0-9]+|#x[0-9a-fA-F]+)(;)\", \"name\": \"constant.character.entity.html\" }, { \"match\": \"&\", \"name\": \"invalid.illegal.bad-ampersand.html\" }] }, \"string-double-quoted\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" } }, \"name\": \"string.quoted.double.html\", \"patterns\": [{ \"include\": \"source.vue#vue-interpolations\" }, { \"include\": \"#entities\" }] }, \"string-single-quoted\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" } }, \"name\": \"string.quoted.single.html\", \"patterns\": [{ \"include\": \"source.vue#vue-interpolations\" }, { \"include\": \"#entities\" }] }, \"tag-generic-attribute\": { \"match\": \"(?<=[^=])\\\\b([a-zA-Z0-9:\\\\-_]+)\", \"name\": \"entity.other.attribute-name.html\" }, \"tag-id-attribute\": { \"begin\": \"\\\\b(id)\\\\b\\\\s*(=)\", \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.id.html\" }, \"2\": { \"name\": \"punctuation.separator.key-value.html\" } }, \"end\": `(?!\\\\G)(?<='|\"|[^\\\\s<>/])`, \"name\": \"meta.attribute-with-value.id.html\", \"patterns\": [{ \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"contentName\": \"meta.toc-list.id.html\", \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" } }, \"name\": \"string.quoted.double.html\", \"patterns\": [{ \"include\": \"source.vue#vue-interpolations\" }, { \"include\": \"#entities\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"contentName\": \"meta.toc-list.id.html\", \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" } }, \"name\": \"string.quoted.single.html\", \"patterns\": [{ \"include\": \"source.vue#vue-interpolations\" }, { \"include\": \"#entities\" }] }, { \"captures\": { \"0\": { \"name\": \"meta.toc-list.id.html\" } }, \"match\": `(?<==)(?:[^\\\\s<>/'\"]|/(?!>))+`, \"name\": \"string.unquoted.html\" }] }, \"tag-stuff\": { \"patterns\": [{ \"include\": \"#vue-directives\" }, { \"include\": \"#tag-id-attribute\" }, { \"include\": \"#tag-generic-attribute\" }, { \"include\": \"#string-double-quoted\" }, { \"include\": \"#string-single-quoted\" }, { \"include\": \"#unquoted-attribute\" }] }, \"unquoted-attribute\": { \"match\": `(?<==)(?:[^\\\\s<>/'\"]|/(?!>))+`, \"name\": \"string.unquoted.html\" }, \"vue-directives\": { \"begin\": \"(?:\\\\b(v-)|(:|@|#))([a-zA-Z0-9\\\\-_]+)(?:\\\\:([a-zA-Z\\\\-_]+))?(?:\\\\.([a-zA-Z\\\\-_]+))*\\\\s*(=)\", \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.html\" }, \"2\": { \"name\": \"punctuation.separator.key-value.html\" }, \"3\": { \"name\": \"entity.other.attribute-name.html\" }, \"4\": { \"name\": \"entity.other.attribute-name.html\" }, \"5\": { \"name\": \"entity.other.attribute-name.html\" }, \"6\": { \"name\": \"punctuation.separator.key-value.html\" } }, \"end\": \"(?<='|\\\")|(?=[\\\\s<>`])\", \"name\": \"meta.directive.vue\", \"patterns\": [{ \"begin\": \"`\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"end\": \"`\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" } }, \"name\": \"source.directive.vue\", \"patterns\": [{ \"include\": \"source.js#expression\" }] }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" } }, \"name\": \"source.directive.vue\", \"patterns\": [{ \"include\": \"source.js#expression\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" } }, \"name\": \"source.directive.vue\", \"patterns\": [{ \"include\": \"source.js#expression\" }] }] } }, \"scopeName\": \"text.html.vue-html\", \"embeddedLangs\": [\"vue\", \"javascript\"] });\nvar vueHtml = [\n  ...vue,\n  ...javascript,\n  lang\n];\n\nexport { vueHtml as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,YAAY,aAAa,CAAC,GAAG,QAAQ,YAAY,YAAY,CAAC,EAAE,WAAW,gCAAgC,GAAG,EAAE,SAAS,4CAA4C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,qBAAqB,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,yEAAyE,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,4CAA4C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,qBAAqB,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,yEAAyE,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,eAAe,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,OAAO,UAAU,QAAQ,kCAAkC,YAAY,CAAC,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,EAAE,SAAS,QAAQ,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,OAAO,QAAQ,qBAAqB,GAAG,EAAE,SAAS,MAAM,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,KAAK,QAAQ,sBAAsB,YAAY,CAAC,EAAE,SAAS,gBAAgB,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,SAAS,QAAQ,8BAA8B,YAAY,CAAC,EAAE,SAAS,YAAY,QAAQ,yDAAyD,CAAC,EAAE,GAAG,EAAE,SAAS,eAAe,OAAO,WAAW,QAAQ,kCAAkC,GAAG,EAAE,SAAS,2BAA2B,QAAQ,6CAA6C,CAAC,EAAE,GAAG,EAAE,SAAS,iCAAiC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,iCAAiC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,iCAAiC,YAAY,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,mKAAmK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,sVAAsV,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,MAAM,QAAQ,kCAAkC,GAAG,EAAE,SAAS,KAAK,QAAQ,yCAAyC,CAAC,GAAG,cAAc,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,+CAA+C,QAAQ,iCAAiC,GAAG,EAAE,SAAS,KAAK,QAAQ,qCAAqC,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,mCAAmC,QAAQ,mCAAmC,GAAG,oBAAoB,EAAE,SAAS,qBAAqB,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,6BAA6B,QAAQ,qCAAqC,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,yBAAyB,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,yBAAyB,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,SAAS,iCAAiC,QAAQ,uBAAuB,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,sBAAsB,EAAE,SAAS,iCAAiC,QAAQ,uBAAuB,GAAG,kBAAkB,EAAE,SAAS,8FAA8F,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,0BAA0B,QAAQ,sBAAsB,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,aAAa,sBAAsB,iBAAiB,CAAC,OAAO,YAAY,EAAE,CAAC;AAC/6R,IAAI,UAAU;AAAA,EACZ,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}