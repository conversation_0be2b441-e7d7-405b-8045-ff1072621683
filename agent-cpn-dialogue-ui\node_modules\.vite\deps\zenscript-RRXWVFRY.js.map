{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/zenscript.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"ZenScript\", \"fileTypes\": [\"zs\"], \"name\": \"zenscript\", \"patterns\": [{ \"comment\": \"numbers\", \"match\": \"\\\\b((0(x|X)[0-9a-fA-F]*)|(([0-9]+\\\\.?[0-9]*)|(\\\\.[0-9]+))((e|E)(\\\\+|-)?[0-9]+)?)([LlFfUuDd]|UL|ul)?\\\\b\", \"name\": \"constant.numeric.zenscript\" }, { \"comment\": \"prefixedNumbers\", \"match\": \"\\\\b\\\\-?(0b|0x|0o|0B|0X|0O)(0|[1-9a-fA-F][0-9a-fA-F_]*)[a-zA-Z_]*\\\\b\", \"name\": \"constant.numeric.zenscript\" }, { \"include\": \"#code\" }, { \"comment\": \"arrays\", \"match\": \"\\\\b((?:[a-z]\\\\w*\\\\.)*[A-Z]+\\\\w*)(?=\\\\[)\", \"name\": \"storage.type.object.array.zenscript\" }], \"repository\": { \"brackets\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.control.zenscript\" }, \"2\": { \"name\": \"keyword.other.zenscript\" }, \"3\": { \"name\": \"keyword.control.zenscript\" }, \"4\": { \"name\": \"variable.other.zenscript\" }, \"5\": { \"name\": \"keyword.control.zenscript\" }, \"6\": { \"name\": \"constant.numeric.zenscript\" }, \"7\": { \"name\": \"keyword.control.zenscript\" } }, \"comment\": \"items and blocks\", \"match\": \"(<)\\\\b(.*?)(:(.*?(:(\\\\*|\\\\d+)?)?)?)(>)\", \"name\": \"keyword.other.zenscript\" }] }, \"class\": { \"captures\": { \"1\": { \"name\": \"storage.type.zenscript\" }, \"2\": { \"name\": \"entity.name.type.class.zenscript\" } }, \"comment\": \"class\", \"match\": \"(zenClass)\\\\s+(\\\\w+)\", \"name\": \"meta.class.zenscript\" }, \"code\": { \"patterns\": [{ \"include\": \"#class\" }, { \"include\": \"#functions\" }, { \"include\": \"#dots\" }, { \"include\": \"#quotes\" }, { \"include\": \"#brackets\" }, { \"include\": \"#comments\" }, { \"include\": \"#var\" }, { \"include\": \"#keywords\" }, { \"include\": \"#constants\" }, { \"include\": \"#operators\" }] }, \"comments\": { \"patterns\": [{ \"comment\": \"inline comments\", \"match\": \"//[^\\n]*\", \"name\": \"comment.line.double=slash\" }, { \"begin\": \"\\\\/\\\\*\", \"beginCaptures\": { \"0\": { \"name\": \"comment.block\" } }, \"comment\": \"block comments\", \"end\": \"\\\\*\\\\/\", \"endCaptures\": { \"0\": { \"name\": \"comment.block\" } }, \"name\": \"comment.block\" }] }, \"dots\": { \"captures\": { \"1\": { \"name\": \"storage.type.zenscript\" }, \"2\": { \"name\": \"keyword.control.zenscript\" }, \"5\": { \"name\": \"keyword.control.zenscript\" } }, \"comment\": \"dots\", \"match\": \"\\\\b(\\\\w+)(\\\\.)(\\\\w+)((\\\\.)(\\\\w+))*\", \"name\": \"plain.text.zenscript\" }, \"functions\": { \"captures\": { \"0\": { \"name\": \"storage.type.function.zenscript\" }, \"1\": { \"name\": \"entity.name.function.zenscript\" } }, \"comment\": \"functions\", \"match\": \"function\\\\s+([A-Za-z_$][\\\\w$]*)\\\\s*(?=\\\\()\", \"name\": \"meta.function.zenscript\" }, \"keywords\": { \"patterns\": [{ \"comment\": \"statement keywords\", \"match\": \"\\\\b(instanceof|get|implements|set|import|function|override|const|if|else|do|while|for|throw|panic|lock|try|catch|finally|return|break|continue|switch|case|default|in|is|as|match|throws|super|new)\\\\b\", \"name\": \"keyword.control.zenscript\" }, { \"comment\": \"storage keywords\", \"match\": \"\\\\b(zenClass|zenConstructor|alias|class|interface|enum|struct|expand|variant|set|void|bool|byte|sbyte|short|ushort|int|uint|long|ulong|usize|float|double|char|string)\\\\b\", \"name\": \"storage.type.zenscript\" }, { \"comment\": \"modifier keywords\", \"match\": \"\\\\b(variant|abstract|final|private|public|export|internal|static|protected|implicit|virtual|extern|immutable)\\\\b\", \"name\": \"storage.modifier.zenscript\" }, { \"comment\": \"annotation keywords\", \"match\": \"\\\\b(Native|Precondition)\\\\b\", \"name\": \"entity.other.attribute-name\" }, { \"comment\": \"language keywords\", \"match\": \"\\\\b(null|true|false)\\\\b\", \"name\": \"constant.language\" }] }, \"operators\": { \"patterns\": [{ \"comment\": \"math operators\", \"match\": \"\\\\b(\\\\.|\\\\.\\\\.|\\\\.\\\\.\\\\.|,|\\\\+|\\\\+=|\\\\+\\\\+|-|-=|--|~|~=|\\\\*|\\\\*=|/|/=|%|%=|\\\\||\\\\|=|\\\\|\\\\||&|&=|&&|\\\\^|\\\\^=|\\\\?|\\\\?\\\\.|\\\\?\\\\?|<|<=|<<|<<=|>|>=|>>|>>=|>>>|>>>=|=>|=|==|===|!|!=|!==|\\\\$|`)\\\\b\", \"name\": \"keyword.control\" }, { \"comment\": \"colons\", \"match\": \"\\\\b(;|:)\\\\b\", \"name\": \"keyword.control\" }] }, \"quotes\": { \"patterns\": [{ \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.zenscript\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.zenscript\" } }, \"name\": \"string.quoted.double.zenscript\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.zenscript\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.zenscript\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.zenscript\" } }, \"name\": \"string.quoted.single.zenscript\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.zenscript\" }] }] }, \"var\": { \"comment\": \"var\", \"match\": \"\\\\b(val|var)\\\\b\", \"name\": \"storage.type\" } }, \"scopeName\": \"source.zenscript\" });\nvar zenscript = [\n  lang\n];\n\nexport { zenscript as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,aAAa,aAAa,CAAC,IAAI,GAAG,QAAQ,aAAa,YAAY,CAAC,EAAE,WAAW,WAAW,SAAS,0GAA0G,QAAQ,6BAA6B,GAAG,EAAE,WAAW,mBAAmB,SAAS,uEAAuE,QAAQ,6BAA6B,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,UAAU,SAAS,2CAA2C,QAAQ,sCAAsC,CAAC,GAAG,cAAc,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,WAAW,oBAAoB,SAAS,0CAA0C,QAAQ,0BAA0B,CAAC,EAAE,GAAG,SAAS,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,WAAW,SAAS,SAAS,wBAAwB,QAAQ,uBAAuB,GAAG,QAAQ,EAAE,YAAY,CAAC,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,OAAO,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,WAAW,mBAAmB,SAAS,YAAY,QAAQ,4BAA4B,GAAG,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gBAAgB,EAAE,GAAG,WAAW,kBAAkB,OAAO,UAAU,eAAe,EAAE,KAAK,EAAE,QAAQ,gBAAgB,EAAE,GAAG,QAAQ,gBAAgB,CAAC,EAAE,GAAG,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,WAAW,QAAQ,SAAS,sCAAsC,QAAQ,uBAAuB,GAAG,aAAa,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,WAAW,aAAa,SAAS,8CAA8C,QAAQ,0BAA0B,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,WAAW,sBAAsB,SAAS,0MAA0M,QAAQ,4BAA4B,GAAG,EAAE,WAAW,oBAAoB,SAAS,6KAA6K,QAAQ,yBAAyB,GAAG,EAAE,WAAW,qBAAqB,SAAS,oHAAoH,QAAQ,6BAA6B,GAAG,EAAE,WAAW,uBAAuB,SAAS,+BAA+B,QAAQ,8BAA8B,GAAG,EAAE,WAAW,qBAAqB,SAAS,2BAA2B,QAAQ,oBAAoB,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,WAAW,kBAAkB,SAAS,iMAAiM,QAAQ,kBAAkB,GAAG,EAAE,WAAW,UAAU,SAAS,eAAe,QAAQ,kBAAkB,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,QAAQ,kCAAkC,YAAY,CAAC,EAAE,SAAS,SAAS,QAAQ,sCAAsC,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,QAAQ,kCAAkC,YAAY,CAAC,EAAE,SAAS,SAAS,QAAQ,sCAAsC,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,EAAE,WAAW,OAAO,SAAS,mBAAmB,QAAQ,eAAe,EAAE,GAAG,aAAa,mBAAmB,CAAC;AACr+I,IAAI,YAAY;AAAA,EACd;AACF;", "names": []}