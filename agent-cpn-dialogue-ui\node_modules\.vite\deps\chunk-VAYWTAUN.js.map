{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/mermaid/dist/chunks/mermaid.core/chunk-SKB7J2MH.mjs"], "sourcesContent": ["import {\n  __name,\n  configureSvgSize,\n  log\n} from \"./chunk-3XYRH5AP.mjs\";\n\n// src/rendering-util/setupViewPortForSVG.ts\nvar setupViewPortForSVG = /* @__PURE__ */ __name((svg, padding, cssDiagram, useMaxWidth) => {\n  svg.attr(\"class\", cssDiagram);\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n  configureSvgSize(svg, height, width, useMaxWidth);\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr(\"viewBox\", viewBox);\n  log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n}, \"setupViewPortForSVG\");\nvar calculateDimensionsWithPadding = /* @__PURE__ */ __name((svg, padding) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y\n  };\n}, \"calculateDimensionsWithPadding\");\nvar createViewBox = /* @__PURE__ */ __name((x, y, width, height, padding) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n}, \"createViewBox\");\n\nexport {\n  setupViewPortForSVG\n};\n"], "mappings": ";;;;;;;AAOG,IAAC,sBAAsC,OAAO,CAAC,KAAK,SAAS,YAAY,gBAAgB;AAC1F,MAAI,KAAK,SAAS,UAAU;AAC5B,QAAM,EAAE,OAAO,QAAQ,GAAG,EAAC,IAAK,+BAA+B,KAAK,OAAO;AAC3E,mBAAiB,KAAK,QAAQ,OAAO,WAAW;AAChD,QAAM,UAAU,cAAc,GAAG,GAAG,OAAO,QAAQ,OAAO;AAC1D,MAAI,KAAK,WAAW,OAAO;AAC3B,MAAI,MAAM,uBAAuB,OAAO,kBAAkB,OAAO,EAAE;AACrE,GAAG,qBAAqB;AACxB,IAAI,iCAAiD,OAAO,CAAC,KAAK,YAAY;;AAC5E,QAAM,WAAS,KAAA,IAAI,KAAI,MAAR,OAAA,SAAA,GAAY,QAAA,MAAa,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,GAAG,GAAG,EAAC;AACzE,SAAO;IACL,OAAO,OAAO,QAAQ,UAAU;IAChC,QAAQ,OAAO,SAAS,UAAU;IAClC,GAAG,OAAO;IACV,GAAG,OAAO;EACd;AACA,GAAG,gCAAgC;AACnC,IAAI,gBAAgC,OAAO,CAAC,GAAG,GAAG,OAAO,QAAQ,YAAY;AAC3E,SAAO,GAAG,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,IAAI,MAAM;AACzD,GAAG,eAAe;", "names": []}