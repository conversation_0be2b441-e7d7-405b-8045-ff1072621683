{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/elixir.mjs"], "sourcesContent": ["import html from './html.mjs';\nimport './javascript.mjs';\nimport './css.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Elixir\", \"fileTypes\": [\"ex\", \"exs\"], \"firstLineMatch\": \"^#!/.*\\\\belixir\", \"foldingStartMarker\": \"(after|else|catch|rescue|\\\\-\\\\>|\\\\{|\\\\[|do)\\\\s*$\", \"foldingStopMarker\": \"^\\\\s*((\\\\}|\\\\]|after|else|catch|rescue)\\\\s*$|end\\\\b)\", \"name\": \"elixir\", \"patterns\": [{ \"begin\": \"\\\\b(fn)\\\\b(?!.*->)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.elixir\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"#core_syntax\" }] }, { \"captures\": { \"1\": { \"name\": \"entity.name.type.class.elixir\" }, \"2\": { \"name\": \"punctuation.separator.method.elixir\" }, \"3\": { \"name\": \"entity.name.function.elixir\" } }, \"match\": \"([A-Z]\\\\w+)\\\\s*(\\\\.)\\\\s*([a-z_]\\\\w*[!?]?)\" }, { \"captures\": { \"1\": { \"name\": \"constant.other.symbol.elixir\" }, \"2\": { \"name\": \"punctuation.separator.method.elixir\" }, \"3\": { \"name\": \"entity.name.function.elixir\" } }, \"match\": \"(\\\\:\\\\w+)\\\\s*(\\\\.)\\\\s*([_]?\\\\w*[!?]?)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.other.elixir\" }, \"2\": { \"name\": \"entity.name.function.elixir\" } }, \"match\": \"(\\\\|\\\\>)\\\\s*([a-z_]\\\\w*[!?]?)\" }, { \"match\": \"\\\\b[a-z_]\\\\w*[!?]?(?=\\\\s*\\\\.?\\\\s*\\\\()\", \"name\": \"entity.name.function.elixir\" }, { \"begin\": \"\\\\b(fn)\\\\b(?=.*->)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.elixir\" } }, \"end\": \"(?>(->)|(when)|(\\\\)))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.other.elixir\" }, \"2\": { \"name\": \"keyword.control.elixir\" }, \"3\": { \"name\": \"punctuation.section.function.elixir\" } }, \"patterns\": [{ \"include\": \"#core_syntax\" }] }, { \"include\": \"#core_syntax\" }, { \"begin\": `^(?=.*->)((?![^\"']*(\"|')[^\"']*->)|(?=.*->[^\"']*(\"|')[^\"']*->))((?!.*\\\\([^\\\\)]*->)|(?=[^\\\\(\\\\)]*->)|(?=\\\\s*\\\\(.*\\\\).*->))((?!.*\\\\b(fn)\\\\b)|(?=.*->.*\\\\bfn\\\\b))`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.elixir\" } }, \"end\": \"(?>(->)|(when)|(\\\\)))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.other.elixir\" }, \"2\": { \"name\": \"keyword.control.elixir\" }, \"3\": { \"name\": \"punctuation.section.function.elixir\" } }, \"patterns\": [{ \"include\": \"#core_syntax\" }] }], \"repository\": { \"core_syntax\": { \"patterns\": [{ \"begin\": \"^\\\\s*(defmodule)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.module.elixir\" } }, \"end\": \"\\\\b(do)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.module.elixir\" } }, \"name\": \"meta.module.elixir\", \"patterns\": [{ \"match\": \"\\\\b[A-Z]\\\\w*(?=\\\\.)\", \"name\": \"entity.other.inherited-class.elixir\" }, { \"match\": \"\\\\b[A-Z]\\\\w*\\\\b\", \"name\": \"entity.name.type.class.elixir\" }] }, { \"begin\": \"^\\\\s*(defprotocol)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.protocol.elixir\" } }, \"end\": \"\\\\b(do)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.protocol.elixir\" } }, \"name\": \"meta.protocol_declaration.elixir\", \"patterns\": [{ \"match\": \"\\\\b[A-Z]\\\\w*\\\\b\", \"name\": \"entity.name.type.protocol.elixir\" }] }, { \"begin\": \"^\\\\s*(defimpl)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.protocol.elixir\" } }, \"end\": \"\\\\b(do)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.protocol.elixir\" } }, \"name\": \"meta.protocol_implementation.elixir\", \"patterns\": [{ \"match\": \"\\\\b[A-Z]\\\\w*\\\\b\", \"name\": \"entity.name.type.protocol.elixir\" }] }, { \"begin\": \"^\\\\s*(def|defmacro|defdelegate|defguard)\\\\s+((?>[a-zA-Z_]\\\\w*(?>\\\\.|::))?(?>[a-zA-Z_]\\\\w*(?>[?!]|=(?!>))?|===?|>[>=]?|<=>|<[<=]?|[%&`/\\\\|]|\\\\*\\\\*?|=?~|[-+]@?|\\\\[\\\\]=?))((\\\\()|\\\\s*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.module.elixir\" }, \"2\": { \"name\": \"entity.name.function.public.elixir\" }, \"4\": { \"name\": \"punctuation.section.function.elixir\" } }, \"end\": \"(\\\\bdo:)|(\\\\bdo\\\\b)|(?=\\\\s+(def|defn|defmacro|defdelegate|defguard)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"constant.other.keywords.elixir\" }, \"2\": { \"name\": \"keyword.control.module.elixir\" } }, \"name\": \"meta.function.public.elixir\", \"patterns\": [{ \"include\": \"$self\" }, { \"begin\": \"\\\\s(\\\\\\\\\\\\\\\\)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.other.elixir\" } }, \"end\": \",|\\\\)|$\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"match\": \"\\\\b(is_atom|is_binary|is_bitstring|is_boolean|is_float|is_function|is_integer|is_list|is_map|is_nil|is_number|is_pid|is_port|is_record|is_reference|is_tuple|is_exception|abs|bit_size|byte_size|div|elem|hd|length|map_size|node|rem|round|tl|trunc|tuple_size)\\\\b\", \"name\": \"keyword.control.elixir\" }] }, { \"begin\": \"^\\\\s*(defp|defnp|defmacrop|defguardp)\\\\s+((?>[a-zA-Z_]\\\\w*(?>\\\\.|::))?(?>[a-zA-Z_]\\\\w*(?>[?!]|=(?!>))?|===?|>[>=]?|<=>|<[<=]?|[%&`/\\\\|]|\\\\*\\\\*?|=?~|[-+]@?|\\\\[\\\\]=?))((\\\\()|\\\\s*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.module.elixir\" }, \"2\": { \"name\": \"entity.name.function.private.elixir\" }, \"4\": { \"name\": \"punctuation.section.function.elixir\" } }, \"end\": \"(\\\\bdo:)|(\\\\bdo\\\\b)|(?=\\\\s+(defp|defmacrop|defguardp)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"constant.other.keywords.elixir\" }, \"2\": { \"name\": \"keyword.control.module.elixir\" } }, \"name\": \"meta.function.private.elixir\", \"patterns\": [{ \"include\": \"$self\" }, { \"begin\": \"\\\\s(\\\\\\\\\\\\\\\\)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.other.elixir\" } }, \"end\": \",|\\\\)|$\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"match\": \"\\\\b(is_atom|is_binary|is_bitstring|is_boolean|is_float|is_function|is_integer|is_list|is_map|is_nil|is_number|is_pid|is_port|is_record|is_reference|is_tuple|is_exception|abs|bit_size|byte_size|div|elem|hd|length|map_size|node|rem|round|tl|trunc|tuple_size)\\\\b\", \"name\": \"keyword.control.elixir\" }] }, { \"begin\": '\\\\s*~L\"\"\"', \"comment\": \"Leex Sigil\", \"end\": '\\\\s*\"\"\"', \"name\": \"sigil.leex\", \"patterns\": [{ \"include\": \"text.elixir\" }, { \"include\": \"text.html.basic\" }] }, { \"begin\": '\\\\s*~H\"\"\"', \"comment\": \"HEEx Sigil\", \"end\": '\\\\s*\"\"\"', \"name\": \"sigil.heex\", \"patterns\": [{ \"include\": \"text.elixir\" }, { \"include\": \"text.html.basic\" }] }, { \"begin\": '@(module|type)?doc (~[a-z])?\"\"\"', \"comment\": \"@doc with heredocs is treated as documentation\", \"end\": '\\\\s*\"\"\"', \"name\": \"comment.block.documentation.heredoc\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": '@(module|type)?doc ~[A-Z]\"\"\"', \"comment\": \"@doc with heredocs is treated as documentation\", \"end\": '\\\\s*\"\"\"', \"name\": \"comment.block.documentation.heredoc\" }, { \"begin\": \"@(module|type)?doc (~[a-z])?'''\", \"comment\": \"@doc with heredocs is treated as documentation\", \"end\": \"\\\\s*'''\", \"name\": \"comment.block.documentation.heredoc\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": \"@(module|type)?doc ~[A-Z]'''\", \"comment\": \"@doc with heredocs is treated as documentation\", \"end\": \"\\\\s*'''\", \"name\": \"comment.block.documentation.heredoc\" }, { \"comment\": \"@doc false is treated as documentation\", \"match\": \"@(module|type)?doc false\", \"name\": \"comment.block.documentation.false\" }, { \"begin\": '@(module|type)?doc \"', \"comment\": \"@doc with string is treated as documentation\", \"end\": '\"', \"name\": \"comment.block.documentation.string\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"match\": \"(?<!\\\\.)\\\\b(do|end|case|bc|lc|for|if|cond|unless|try|receive|fn|defmodule|defp?|defprotocol|defimpl|defrecord|defstruct|defnp?|defmacrop?|defguardp?|defdelegate|defexception|defoverridable|exit|after|rescue|catch|else|raise|reraise|throw|import|require|alias|use|quote|unquote|super|with)\\\\b(?![?!:])\", \"name\": \"keyword.control.elixir\" }, { \"comment\": \" as above, just doesn't need a 'end' and does a logic operation\", \"match\": \"(?<!\\\\.)\\\\b(and|not|or|when|xor|in)\\\\b\", \"name\": \"keyword.operator.elixir\" }, { \"match\": \"\\\\b[A-Z]\\\\w*\\\\b\", \"name\": \"entity.name.type.class.elixir\" }, { \"match\": \"\\\\b(nil|true|false)\\\\b(?![?!])\", \"name\": \"constant.language.elixir\" }, { \"match\": \"\\\\b(__(CALLER|ENV|MODULE|DIR|STACKTRACE)__)\\\\b(?![?!])\", \"name\": \"variable.language.elixir\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.elixir\" } }, \"match\": \"(@)[a-zA-Z_]\\\\w*\", \"name\": \"variable.other.readwrite.module.elixir\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.elixir\" } }, \"match\": \"(&)\\\\d+\", \"name\": \"variable.other.anonymous.elixir\" }, { \"match\": \"&(?![&])\", \"name\": \"variable.other.anonymous.elixir\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.elixir\" } }, \"match\": \"\\\\^[a-z_]\\\\w*\", \"name\": \"variable.other.capture.elixir\" }, { \"match\": \"\\\\b0x[0-9A-Fa-f](?>_?[0-9A-Fa-f])*\\\\b\", \"name\": \"constant.numeric.hex.elixir\" }, { \"match\": \"\\\\b\\\\d(?>_?\\\\d)*(\\\\.(?![^[:space:][:digit:]])(?>_?\\\\d)+)([eE][-+]?\\\\d(?>_?\\\\d)*)?\\\\b\", \"name\": \"constant.numeric.float.elixir\" }, { \"match\": \"\\\\b\\\\d(?>_?\\\\d)*\\\\b\", \"name\": \"constant.numeric.integer.elixir\" }, { \"match\": \"\\\\b0b[01](?>_?[01])*\\\\b\", \"name\": \"constant.numeric.binary.elixir\" }, { \"match\": \"\\\\b0o[0-7](?>_?[0-7])*\\\\b\", \"name\": \"constant.numeric.octal.elixir\" }, { \"begin\": \":'\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.constant.elixir\" } }, \"end\": \"'\", \"name\": \"constant.other.symbol.single-quoted.elixir\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": ':\"', \"captures\": { \"0\": { \"name\": \"punctuation.definition.constant.elixir\" } }, \"end\": '\"', \"name\": \"constant.other.symbol.double-quoted.elixir\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": \"(?>''')\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"Single-quoted heredocs\", \"end\": \"^\\\\s*'''\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.single.heredoc.elixir\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"single quoted string (allows for interpolation)\", \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.single.elixir\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": '(?>\"\"\")', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"Double-quoted heredocs\", \"end\": '^\\\\s*\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.double.heredoc.elixir\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"double quoted string (allows for interpolation)\", \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.double.elixir\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": '~[a-z](?>\"\"\")', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"Double-quoted heredocs sigils\", \"end\": '^\\\\s*\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.other.sigil.heredoc.elixir\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": \"~[a-z]\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"sigil (allow for interpolation)\", \"end\": \"\\\\}[a-z]*\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.other.sigil.elixir\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": \"~[a-z]\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"sigil (allow for interpolation)\", \"end\": \"\\\\][a-z]*\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.other.sigil.elixir\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": \"~[a-z]\\\\<\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"sigil (allow for interpolation)\", \"end\": \"\\\\>[a-z]*\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.other.sigil.elixir\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": \"~[a-z]\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"sigil (allow for interpolation)\", \"end\": \"\\\\)[a-z]*\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.other.sigil.elixir\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": \"~[a-z]([^\\\\w])\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"sigil (allow for interpolation)\", \"end\": \"\\\\1[a-z]*\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.other.sigil.elixir\", \"patterns\": [{ \"include\": \"#interpolated_elixir\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": '~[A-Z](?>\"\"\")', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"Double-quoted heredocs sigils\", \"end\": '^\\\\s*\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.other.sigil.heredoc.literal.elixir\" }, { \"begin\": \"~[A-Z]\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"sigil (without interpolation)\", \"end\": \"\\\\}[a-z]*\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.other.sigil.literal.elixir\" }, { \"begin\": \"~[A-Z]\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"sigil (without interpolation)\", \"end\": \"\\\\][a-z]*\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.other.sigil.literal.elixir\" }, { \"begin\": \"~[A-Z]\\\\<\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"sigil (without interpolation)\", \"end\": \"\\\\>[a-z]*\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.other.sigil.literal.elixir\" }, { \"begin\": \"~[A-Z]\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"sigil (without interpolation)\", \"end\": \"\\\\)[a-z]*\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.other.sigil.literal.elixir\" }, { \"begin\": \"~[A-Z]([^\\\\w])\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.elixir\" } }, \"comment\": \"sigil (without interpolation)\", \"end\": \"\\\\1[a-z]*\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.elixir\" } }, \"name\": \"string.quoted.other.sigil.literal.elixir\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.elixir\" } }, \"comment\": \"symbols\", \"match\": \"(?<!:)(:)(?>[a-zA-Z_][\\\\w@]*(?>[?!]|=(?![>=]))?|\\\\<\\\\>|===?|!==?|<<>>|<<<|>>>|~~~|::|<\\\\-|\\\\|>|=>|=~|=|/|\\\\\\\\\\\\\\\\|\\\\*\\\\*?|\\\\.\\\\.?\\\\.?|\\\\.\\\\.//|>=?|<=?|&&?&?|\\\\+\\\\+?|\\\\-\\\\-?|\\\\|\\\\|?\\\\|?|\\\\!|@|\\\\%?\\\\{\\\\}|%|\\\\[\\\\]|\\\\^(\\\\^\\\\^)?)\", \"name\": \"constant.other.symbol.elixir\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.elixir\" } }, \"comment\": \"symbols\", \"match\": \"(?>[a-zA-Z_][\\\\w@]*(?>[?!])?)(:)(?!:)\", \"name\": \"constant.other.keywords.elixir\" }, { \"begin\": \"(^[ \\\\t]+)?(?=##)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.elixir\" } }, \"end\": \"(?!#)\", \"patterns\": [{ \"begin\": \"##\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.elixir\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.section.elixir\" }] }, { \"begin\": \"(^[ \\\\t]+)?(?=#)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.elixir\" } }, \"end\": \"(?!#)\", \"patterns\": [{ \"begin\": \"#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.elixir\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.elixir\" }] }, { \"match\": \"\\\\b_([^_][\\\\w]+[?!]?)\", \"name\": \"comment.unused.elixir\" }, { \"match\": \"\\\\b_\\\\b\", \"name\": \"comment.wildcard.elixir\" }, { \"comment\": '\\n\t\t\tmatches questionmark-letters.\\n\\n\t\t\texamples (1st alternation = hex):\\n\t\t\t?\\\\x1     ?\\\\x61\\n\\n\t\t\texamples (2rd alternation = escaped):\\n\t\t\t?\\\\n      ?\\\\b\\n\\n\t\t\texamples (3rd alternation = normal):\\n\t\t\t?a       ?A       ?0\\n\t\t\t?*       ?\"       ?(\\n\t\t\t?.       ?#\\n\\n\t\t\tthe negative lookbehind prevents against matching\\n\t\t\tp(42.tainted?)\\n\t\t\t', \"match\": \"(?<!\\\\w)\\\\?(\\\\\\\\(x[0-9A-Fa-f]{1,2}(?![0-9A-Fa-f])\\\\b|[^xMC])|[^\\\\s\\\\\\\\])\", \"name\": \"constant.numeric.elixir\" }, { \"match\": \"\\\\+\\\\+|\\\\-\\\\-|<\\\\|>\", \"name\": \"keyword.operator.concatenation.elixir\" }, { \"match\": \"\\\\|\\\\>|<~>|<>|<<<|>>>|~>>|<<~|~>|<~|<\\\\|>\", \"name\": \"keyword.operator.sigils_1.elixir\" }, { \"match\": \"&&&|&&\", \"name\": \"keyword.operator.sigils_2.elixir\" }, { \"match\": \"<\\\\-|\\\\\\\\\\\\\\\\\", \"name\": \"keyword.operator.sigils_3.elixir\" }, { \"match\": \"===?|!==?|<=?|>=?\", \"name\": \"keyword.operator.comparison.elixir\" }, { \"match\": \"(\\\\|\\\\|\\\\||&&&|\\\\^\\\\^\\\\^|<<<|>>>|~~~)\", \"name\": \"keyword.operator.bitwise.elixir\" }, { \"match\": \"(?<=[ \\\\t])!+|\\\\bnot\\\\b|&&|\\\\band\\\\b|\\\\|\\\\||\\\\bor\\\\b|\\\\bxor\\\\b\", \"name\": \"keyword.operator.logical.elixir\" }, { \"match\": \"(\\\\*|\\\\+|\\\\-|/)\", \"name\": \"keyword.operator.arithmetic.elixir\" }, { \"match\": \"\\\\||\\\\+\\\\+|\\\\-\\\\-|\\\\*\\\\*|\\\\\\\\\\\\\\\\|\\\\<\\\\-|\\\\<\\\\>|\\\\<\\\\<|\\\\>\\\\>|\\\\:\\\\:|\\\\.\\\\.|//|\\\\|>|~|=>|&\", \"name\": \"keyword.operator.other.elixir\" }, { \"match\": \"=\", \"name\": \"keyword.operator.assignment.elixir\" }, { \"match\": \":\", \"name\": \"punctuation.separator.other.elixir\" }, { \"match\": \"\\\\;\", \"name\": \"punctuation.separator.statement.elixir\" }, { \"match\": \",\", \"name\": \"punctuation.separator.object.elixir\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.method.elixir\" }, { \"match\": \"\\\\{|\\\\}\", \"name\": \"punctuation.section.scope.elixir\" }, { \"match\": \"\\\\[|\\\\]\", \"name\": \"punctuation.section.array.elixir\" }, { \"match\": \"\\\\(|\\\\)\", \"name\": \"punctuation.section.function.elixir\" }] }, \"escaped_char\": { \"match\": \"\\\\\\\\(x[\\\\da-fA-F]{1,2}|.)\", \"name\": \"constant.character.escaped.elixir\" }, \"interpolated_elixir\": { \"begin\": \"#\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.elixir\" } }, \"contentName\": \"source.elixir\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.elixir\" } }, \"name\": \"meta.embedded.line.elixir\", \"patterns\": [{ \"include\": \"#nest_curly_and_self\" }, { \"include\": \"$self\" }] }, \"nest_curly_and_self\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"captures\": { \"0\": { \"name\": \"punctuation.section.scope.elixir\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#nest_curly_and_self\" }] }, { \"include\": \"$self\" }] } }, \"scopeName\": \"source.elixir\", \"embeddedLangs\": [\"html\"] });\nvar elixir = [\n  ...html,\n  lang\n];\n\nexport { elixir as default };\n"], "mappings": ";;;;;;;;AAIA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,UAAU,aAAa,CAAC,MAAM,KAAK,GAAG,kBAAkB,mBAAmB,sBAAsB,oDAAoD,qBAAqB,wDAAwD,QAAQ,UAAU,YAAY,CAAC,EAAE,SAAS,sBAAsB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,4CAA4C,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,wCAAwC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,gCAAgC,GAAG,EAAE,SAAS,yCAAyC,QAAQ,8BAA8B,GAAG,EAAE,SAAS,sBAAsB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,yBAAyB,eAAe,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,SAAS,iKAAiK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,yBAAyB,eAAe,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,uBAAuB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,QAAQ,sBAAsB,YAAY,CAAC,EAAE,SAAS,uBAAuB,QAAQ,sCAAsC,GAAG,EAAE,SAAS,mBAAmB,QAAQ,gCAAgC,CAAC,EAAE,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,QAAQ,oCAAoC,YAAY,CAAC,EAAE,SAAS,mBAAmB,QAAQ,mCAAmC,CAAC,EAAE,GAAG,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,QAAQ,uCAAuC,YAAY,CAAC,EAAE,SAAS,mBAAmB,QAAQ,mCAAmC,CAAC,EAAE,GAAG,EAAE,SAAS,wLAAwL,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,2EAA2E,eAAe,EAAE,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,uQAAuQ,QAAQ,yBAAyB,CAAC,EAAE,GAAG,EAAE,SAAS,qLAAqL,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,6DAA6D,eAAe,EAAE,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,uQAAuQ,QAAQ,yBAAyB,CAAC,EAAE,GAAG,EAAE,SAAS,aAAa,WAAW,cAAc,OAAO,WAAW,QAAQ,cAAc,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,aAAa,WAAW,cAAc,OAAO,WAAW,QAAQ,cAAc,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,mCAAmC,WAAW,kDAAkD,OAAO,WAAW,QAAQ,uCAAuC,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,gCAAgC,WAAW,kDAAkD,OAAO,WAAW,QAAQ,sCAAsC,GAAG,EAAE,SAAS,mCAAmC,WAAW,kDAAkD,OAAO,WAAW,QAAQ,uCAAuC,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,gCAAgC,WAAW,kDAAkD,OAAO,WAAW,QAAQ,sCAAsC,GAAG,EAAE,WAAW,0CAA0C,SAAS,4BAA4B,QAAQ,oCAAoC,GAAG,EAAE,SAAS,wBAAwB,WAAW,gDAAgD,OAAO,KAAK,QAAQ,sCAAsC,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,gTAAgT,QAAQ,yBAAyB,GAAG,EAAE,WAAW,mEAAmE,SAAS,0CAA0C,QAAQ,0BAA0B,GAAG,EAAE,SAAS,mBAAmB,QAAQ,gCAAgC,GAAG,EAAE,SAAS,kCAAkC,QAAQ,2BAA2B,GAAG,EAAE,SAAS,0DAA0D,QAAQ,2BAA2B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,SAAS,oBAAoB,QAAQ,yCAAyC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,SAAS,WAAW,QAAQ,kCAAkC,GAAG,EAAE,SAAS,YAAY,QAAQ,kCAAkC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,SAAS,iBAAiB,QAAQ,gCAAgC,GAAG,EAAE,SAAS,yCAAyC,QAAQ,8BAA8B,GAAG,EAAE,SAAS,wFAAwF,QAAQ,gCAAgC,GAAG,EAAE,SAAS,uBAAuB,QAAQ,kCAAkC,GAAG,EAAE,SAAS,2BAA2B,QAAQ,iCAAiC,GAAG,EAAE,SAAS,6BAA6B,QAAQ,gCAAgC,GAAG,EAAE,SAAS,MAAM,YAAY,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,KAAK,QAAQ,8CAA8C,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,MAAM,YAAY,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,KAAK,QAAQ,8CAA8C,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,0BAA0B,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,uCAAuC,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,mDAAmD,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,0BAA0B,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,uCAAuC,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,mDAAmD,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,iCAAiC,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,4CAA4C,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,mCAAmC,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,oCAAoC,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,mCAAmC,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,oCAAoC,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,mCAAmC,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,oCAAoC,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,mCAAmC,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,oCAAoC,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,kBAAkB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,mCAAmC,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,oCAAoC,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,iCAAiC,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,mDAAmD,GAAG,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,iCAAiC,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,2CAA2C,GAAG,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,iCAAiC,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,2CAA2C,GAAG,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,iCAAiC,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,2CAA2C,GAAG,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,iCAAiC,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,2CAA2C,GAAG,EAAE,SAAS,kBAAkB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,iCAAiC,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,2CAA2C,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,WAAW,WAAW,SAAS,oOAAoO,QAAQ,+BAA+B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,WAAW,WAAW,SAAS,yCAAyC,QAAQ,iCAAiC,GAAG,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,SAAS,YAAY,CAAC,EAAE,SAAS,MAAM,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,OAAO,QAAQ,8BAA8B,CAAC,EAAE,GAAG,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,SAAS,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,OAAO,QAAQ,kCAAkC,CAAC,EAAE,GAAG,EAAE,SAAS,yBAAyB,QAAQ,wBAAwB,GAAG,EAAE,SAAS,WAAW,QAAQ,0BAA0B,GAAG,EAAE,WAAW,+VAA+V,SAAS,4EAA4E,QAAQ,0BAA0B,GAAG,EAAE,SAAS,uBAAuB,QAAQ,wCAAwC,GAAG,EAAE,SAAS,6CAA6C,QAAQ,mCAAmC,GAAG,EAAE,SAAS,UAAU,QAAQ,mCAAmC,GAAG,EAAE,SAAS,iBAAiB,QAAQ,mCAAmC,GAAG,EAAE,SAAS,qBAAqB,QAAQ,qCAAqC,GAAG,EAAE,SAAS,yCAAyC,QAAQ,kCAAkC,GAAG,EAAE,SAAS,kEAAkE,QAAQ,kCAAkC,GAAG,EAAE,SAAS,mBAAmB,QAAQ,qCAAqC,GAAG,EAAE,SAAS,8FAA8F,QAAQ,gCAAgC,GAAG,EAAE,SAAS,KAAK,QAAQ,qCAAqC,GAAG,EAAE,SAAS,KAAK,QAAQ,qCAAqC,GAAG,EAAE,SAAS,OAAO,QAAQ,yCAAyC,GAAG,EAAE,SAAS,KAAK,QAAQ,sCAAsC,GAAG,EAAE,SAAS,OAAO,QAAQ,sCAAsC,GAAG,EAAE,SAAS,WAAW,QAAQ,mCAAmC,GAAG,EAAE,SAAS,WAAW,QAAQ,mCAAmC,GAAG,EAAE,SAAS,WAAW,QAAQ,sCAAsC,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,6BAA6B,QAAQ,oCAAoC,GAAG,uBAAuB,EAAE,SAAS,QAAQ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,eAAe,iBAAiB,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,EAAE,GAAG,aAAa,iBAAiB,iBAAiB,CAAC,MAAM,EAAE,CAAC;AACn5kB,IAAI,SAAS;AAAA,EACX,GAAG;AAAA,EACH;AACF;", "names": []}