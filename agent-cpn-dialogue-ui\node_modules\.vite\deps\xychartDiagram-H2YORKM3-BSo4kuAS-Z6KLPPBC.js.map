{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-array/src/range.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-scale/src/band.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/mermaid/dist/chunks/mermaid.core/xychartDiagram-H2YORKM3.mjs"], "sourcesContent": ["export default function range(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n\n  var i = -1,\n      n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n      range = new Array(n);\n\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n\n  return range;\n}\n", "import {range as sequence} from \"d3-array\";\nimport {initRange} from \"./init.js\";\nimport ordinal from \"./ordinal.js\";\n\nexport default function band() {\n  var scale = ordinal().unknown(undefined),\n      domain = scale.domain,\n      ordinalRange = scale.range,\n      r0 = 0,\n      r1 = 1,\n      step,\n      bandwidth,\n      round = false,\n      paddingInner = 0,\n      paddingOuter = 0,\n      align = 0.5;\n\n  delete scale.unknown;\n\n  function rescale() {\n    var n = domain().length,\n        reverse = r1 < r0,\n        start = reverse ? r1 : r0,\n        stop = reverse ? r0 : r1;\n    step = (stop - start) / Math.max(1, n - paddingInner + paddingOuter * 2);\n    if (round) step = Math.floor(step);\n    start += (stop - start - step * (n - paddingInner)) * align;\n    bandwidth = step * (1 - paddingInner);\n    if (round) start = Math.round(start), bandwidth = Math.round(bandwidth);\n    var values = sequence(n).map(function(i) { return start + step * i; });\n    return ordinalRange(reverse ? values.reverse() : values);\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? ([r0, r1] = _, r0 = +r0, r1 = +r1, rescale()) : [r0, r1];\n  };\n\n  scale.rangeRound = function(_) {\n    return [r0, r1] = _, r0 = +r0, r1 = +r1, round = true, rescale();\n  };\n\n  scale.bandwidth = function() {\n    return bandwidth;\n  };\n\n  scale.step = function() {\n    return step;\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, rescale()) : round;\n  };\n\n  scale.padding = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_), rescale()) : paddingInner;\n  };\n\n  scale.paddingInner = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, _), rescale()) : paddingInner;\n  };\n\n  scale.paddingOuter = function(_) {\n    return arguments.length ? (paddingOuter = +_, rescale()) : paddingOuter;\n  };\n\n  scale.align = function(_) {\n    return arguments.length ? (align = Math.max(0, Math.min(1, _)), rescale()) : align;\n  };\n\n  scale.copy = function() {\n    return band(domain(), [r0, r1])\n        .round(round)\n        .paddingInner(paddingInner)\n        .paddingOuter(paddingOuter)\n        .align(align);\n  };\n\n  return initRange.apply(rescale(), arguments);\n}\n\nfunction pointish(scale) {\n  var copy = scale.copy;\n\n  scale.padding = scale.paddingOuter;\n  delete scale.paddingInner;\n  delete scale.paddingOuter;\n\n  scale.copy = function() {\n    return pointish(copy());\n  };\n\n  return scale;\n}\n\nexport function point() {\n  return pointish(band.apply(null, arguments).paddingInner(1));\n}\n", "import {\n  computeDimensionOfText\n} from \"./chunk-QESNASVV.mjs\";\nimport {\n  cleanAndMerge\n} from \"./chunk-55PJQP7W.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-P3VETL53.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getDiagramTitle,\n  getThemeVariables,\n  log,\n  sanitizeText,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-3XYRH5AP.mjs\";\n\n// src/diagrams/xychart/parser/xychart.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 10, 12, 14, 16, 18, 19, 21, 23], $V1 = [2, 6], $V2 = [1, 3], $V3 = [1, 5], $V4 = [1, 6], $V5 = [1, 7], $V6 = [1, 5, 10, 12, 14, 16, 18, 19, 21, 23, 34, 35, 36], $V7 = [1, 25], $V8 = [1, 26], $V9 = [1, 28], $Va = [1, 29], $Vb = [1, 30], $Vc = [1, 31], $Vd = [1, 32], $Ve = [1, 33], $Vf = [1, 34], $Vg = [1, 35], $Vh = [1, 36], $Vi = [1, 37], $Vj = [1, 43], $Vk = [1, 42], $Vl = [1, 47], $Vm = [1, 50], $Vn = [1, 10, 12, 14, 16, 18, 19, 21, 23, 34, 35, 36], $Vo = [1, 10, 12, 14, 16, 18, 19, 21, 23, 24, 26, 27, 28, 34, 35, 36], $Vp = [1, 10, 12, 14, 16, 18, 19, 21, 23, 24, 26, 27, 28, 34, 35, 36, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50], $Vq = [1, 64];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"eol\": 4, \"XYCHART\": 5, \"chartConfig\": 6, \"document\": 7, \"CHART_ORIENTATION\": 8, \"statement\": 9, \"title\": 10, \"text\": 11, \"X_AXIS\": 12, \"parseXAxis\": 13, \"Y_AXIS\": 14, \"parseYAxis\": 15, \"LINE\": 16, \"plotData\": 17, \"BAR\": 18, \"acc_title\": 19, \"acc_title_value\": 20, \"acc_descr\": 21, \"acc_descr_value\": 22, \"acc_descr_multiline_value\": 23, \"SQUARE_BRACES_START\": 24, \"commaSeparatedNumbers\": 25, \"SQUARE_BRACES_END\": 26, \"NUMBER_WITH_DECIMAL\": 27, \"COMMA\": 28, \"xAxisData\": 29, \"bandData\": 30, \"ARROW_DELIMITER\": 31, \"commaSeparatedTexts\": 32, \"yAxisData\": 33, \"NEWLINE\": 34, \"SEMI\": 35, \"EOF\": 36, \"alphaNum\": 37, \"STR\": 38, \"MD_STR\": 39, \"alphaNumToken\": 40, \"AMP\": 41, \"NUM\": 42, \"ALPHA\": 43, \"PLUS\": 44, \"EQUALS\": 45, \"MULT\": 46, \"DOT\": 47, \"BRKT\": 48, \"MINUS\": 49, \"UNDERSCORE\": 50, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 5: \"XYCHART\", 8: \"CHART_ORIENTATION\", 10: \"title\", 12: \"X_AXIS\", 14: \"Y_AXIS\", 16: \"LINE\", 18: \"BAR\", 19: \"acc_title\", 20: \"acc_title_value\", 21: \"acc_descr\", 22: \"acc_descr_value\", 23: \"acc_descr_multiline_value\", 24: \"SQUARE_BRACES_START\", 26: \"SQUARE_BRACES_END\", 27: \"NUMBER_WITH_DECIMAL\", 28: \"COMMA\", 31: \"ARROW_DELIMITER\", 34: \"NEWLINE\", 35: \"SEMI\", 36: \"EOF\", 38: \"STR\", 39: \"MD_STR\", 41: \"AMP\", 42: \"NUM\", 43: \"ALPHA\", 44: \"PLUS\", 45: \"EQUALS\", 46: \"MULT\", 47: \"DOT\", 48: \"BRKT\", 49: \"MINUS\", 50: \"UNDERSCORE\" },\n    productions_: [0, [3, 2], [3, 3], [3, 2], [3, 1], [6, 1], [7, 0], [7, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 3], [9, 2], [9, 3], [9, 2], [9, 2], [9, 1], [17, 3], [25, 3], [25, 1], [13, 1], [13, 2], [13, 1], [29, 1], [29, 3], [30, 3], [32, 3], [32, 1], [15, 1], [15, 2], [15, 1], [33, 3], [4, 1], [4, 1], [4, 1], [11, 1], [11, 1], [11, 1], [37, 1], [37, 2], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 5:\n          yy.setOrientation($$[$0]);\n          break;\n        case 9:\n          yy.setDiagramTitle($$[$0].text.trim());\n          break;\n        case 12:\n          yy.setLineData({ text: \"\", type: \"text\" }, $$[$0]);\n          break;\n        case 13:\n          yy.setLineData($$[$0 - 1], $$[$0]);\n          break;\n        case 14:\n          yy.setBarData({ text: \"\", type: \"text\" }, $$[$0]);\n          break;\n        case 15:\n          yy.setBarData($$[$0 - 1], $$[$0]);\n          break;\n        case 16:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 17:\n        case 18:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 19:\n          this.$ = $$[$0 - 1];\n          break;\n        case 20:\n          this.$ = [Number($$[$0 - 2]), ...$$[$0]];\n          break;\n        case 21:\n          this.$ = [Number($$[$0])];\n          break;\n        case 22:\n          yy.setXAxisTitle($$[$0]);\n          break;\n        case 23:\n          yy.setXAxisTitle($$[$0 - 1]);\n          break;\n        case 24:\n          yy.setXAxisTitle({ type: \"text\", text: \"\" });\n          break;\n        case 25:\n          yy.setXAxisBand($$[$0]);\n          break;\n        case 26:\n          yy.setXAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));\n          break;\n        case 27:\n          this.$ = $$[$0 - 1];\n          break;\n        case 28:\n          this.$ = [$$[$0 - 2], ...$$[$0]];\n          break;\n        case 29:\n          this.$ = [$$[$0]];\n          break;\n        case 30:\n          yy.setYAxisTitle($$[$0]);\n          break;\n        case 31:\n          yy.setYAxisTitle($$[$0 - 1]);\n          break;\n        case 32:\n          yy.setYAxisTitle({ type: \"text\", text: \"\" });\n          break;\n        case 33:\n          yy.setYAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));\n          break;\n        case 37:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 38:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 39:\n          this.$ = { text: $$[$0], type: \"markdown\" };\n          break;\n        case 40:\n          this.$ = $$[$0];\n          break;\n        case 41:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [o($V0, $V1, { 3: 1, 4: 2, 7: 4, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), { 1: [3] }, o($V0, $V1, { 4: 2, 7: 4, 3: 8, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), o($V0, $V1, { 4: 2, 7: 4, 6: 9, 3: 10, 5: $V2, 8: [1, 11], 34: $V3, 35: $V4, 36: $V5 }), { 1: [2, 4], 9: 12, 10: [1, 13], 12: [1, 14], 14: [1, 15], 16: [1, 16], 18: [1, 17], 19: [1, 18], 21: [1, 19], 23: [1, 20] }, o($V6, [2, 34]), o($V6, [2, 35]), o($V6, [2, 36]), { 1: [2, 1] }, o($V0, $V1, { 4: 2, 7: 4, 3: 21, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), { 1: [2, 3] }, o($V6, [2, 5]), o($V0, [2, 7], { 4: 22, 34: $V3, 35: $V4, 36: $V5 }), { 11: 23, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 39, 13: 38, 24: $Vj, 27: $Vk, 29: 40, 30: 41, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 45, 15: 44, 27: $Vl, 33: 46, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 49, 17: 48, 24: $Vm, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 52, 17: 51, 24: $Vm, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 20: [1, 53] }, { 22: [1, 54] }, o($Vn, [2, 18]), { 1: [2, 2] }, o($Vn, [2, 8]), o($Vn, [2, 9]), o($Vo, [2, 37], { 40: 55, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }), o($Vo, [2, 38]), o($Vo, [2, 39]), o($Vp, [2, 40]), o($Vp, [2, 42]), o($Vp, [2, 43]), o($Vp, [2, 44]), o($Vp, [2, 45]), o($Vp, [2, 46]), o($Vp, [2, 47]), o($Vp, [2, 48]), o($Vp, [2, 49]), o($Vp, [2, 50]), o($Vp, [2, 51]), o($Vn, [2, 10]), o($Vn, [2, 22], { 30: 41, 29: 56, 24: $Vj, 27: $Vk }), o($Vn, [2, 24]), o($Vn, [2, 25]), { 31: [1, 57] }, { 11: 59, 32: 58, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, o($Vn, [2, 11]), o($Vn, [2, 30], { 33: 60, 27: $Vl }), o($Vn, [2, 32]), { 31: [1, 61] }, o($Vn, [2, 12]), { 17: 62, 24: $Vm }, { 25: 63, 27: $Vq }, o($Vn, [2, 14]), { 17: 65, 24: $Vm }, o($Vn, [2, 16]), o($Vn, [2, 17]), o($Vp, [2, 41]), o($Vn, [2, 23]), { 27: [1, 66] }, { 26: [1, 67] }, { 26: [2, 29], 28: [1, 68] }, o($Vn, [2, 31]), { 27: [1, 69] }, o($Vn, [2, 13]), { 26: [1, 70] }, { 26: [2, 21], 28: [1, 71] }, o($Vn, [2, 15]), o($Vn, [2, 26]), o($Vn, [2, 27]), { 11: 59, 32: 72, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, o($Vn, [2, 33]), o($Vn, [2, 19]), { 25: 73, 27: $Vq }, { 26: [2, 28] }, { 26: [2, 20] }],\n    defaultActions: { 8: [2, 1], 10: [2, 3], 21: [2, 2], 72: [2, 28], 73: [2, 20] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            this.popState();\n            return 34;\n            break;\n          case 3:\n            this.popState();\n            return 34;\n            break;\n          case 4:\n            return 34;\n            break;\n          case 5:\n            break;\n          case 6:\n            return 10;\n            break;\n          case 7:\n            this.pushState(\"acc_title\");\n            return 19;\n            break;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 9:\n            this.pushState(\"acc_descr\");\n            return 21;\n            break;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 11:\n            this.pushState(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 14:\n            return 5;\n            break;\n          case 15:\n            return 8;\n            break;\n          case 16:\n            this.pushState(\"axis_data\");\n            return \"X_AXIS\";\n            break;\n          case 17:\n            this.pushState(\"axis_data\");\n            return \"Y_AXIS\";\n            break;\n          case 18:\n            this.pushState(\"axis_band_data\");\n            return 24;\n            break;\n          case 19:\n            return 31;\n            break;\n          case 20:\n            this.pushState(\"data\");\n            return 16;\n            break;\n          case 21:\n            this.pushState(\"data\");\n            return 18;\n            break;\n          case 22:\n            this.pushState(\"data_inner\");\n            return 24;\n            break;\n          case 23:\n            return 27;\n            break;\n          case 24:\n            this.popState();\n            return 26;\n            break;\n          case 25:\n            this.popState();\n            break;\n          case 26:\n            this.pushState(\"string\");\n            break;\n          case 27:\n            this.popState();\n            break;\n          case 28:\n            return \"STR\";\n            break;\n          case 29:\n            return 24;\n            break;\n          case 30:\n            return 26;\n            break;\n          case 31:\n            return 43;\n            break;\n          case 32:\n            return \"COLON\";\n            break;\n          case 33:\n            return 44;\n            break;\n          case 34:\n            return 28;\n            break;\n          case 35:\n            return 45;\n            break;\n          case 36:\n            return 46;\n            break;\n          case 37:\n            return 48;\n            break;\n          case 38:\n            return 50;\n            break;\n          case 39:\n            return 47;\n            break;\n          case 40:\n            return 41;\n            break;\n          case 41:\n            return 49;\n            break;\n          case 42:\n            return 42;\n            break;\n          case 43:\n            break;\n          case 44:\n            return 35;\n            break;\n          case 45:\n            return 36;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:(\\r?\\n))/i, /^(?:(\\r?\\n))/i, /^(?:[\\n\\r]+)/i, /^(?:%%[^\\n]*)/i, /^(?:title\\b)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:\\{)/i, /^(?:[^\\}]*)/i, /^(?:xychart-beta\\b)/i, /^(?:(?:vertical|horizontal))/i, /^(?:x-axis\\b)/i, /^(?:y-axis\\b)/i, /^(?:\\[)/i, /^(?:-->)/i, /^(?:line\\b)/i, /^(?:bar\\b)/i, /^(?:\\[)/i, /^(?:[+-]?(?:\\d+(?:\\.\\d+)?|\\.\\d+))/i, /^(?:\\])/i, /^(?:(?:`\\)                                    \\{ this\\.pushState\\(md_string\\); \\}\\n<md_string>\\(\\?:\\(\\?!`\"\\)\\.\\)\\+                  \\{ return MD_STR; \\}\\n<md_string>\\(\\?:`))/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:\\[)/i, /^(?:\\])/i, /^(?:[A-Za-z]+)/i, /^(?::)/i, /^(?:\\+)/i, /^(?:,)/i, /^(?:=)/i, /^(?:\\*)/i, /^(?:#)/i, /^(?:[\\_])/i, /^(?:\\.)/i, /^(?:&)/i, /^(?:-)/i, /^(?:[0-9]+)/i, /^(?:\\s+)/i, /^(?:;)/i, /^(?:$)/i],\n      conditions: { \"data_inner\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"data\": { \"rules\": [0, 1, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 22, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"axis_band_data\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"axis_data\": { \"rules\": [0, 1, 2, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"title\": { \"rules\": [], \"inclusive\": false }, \"md_string\": { \"rules\": [], \"inclusive\": false }, \"string\": { \"rules\": [27, 28], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar xychart_default = parser;\n\n// src/diagrams/xychart/chartBuilder/interfaces.ts\nfunction isBarPlot(data) {\n  return data.type === \"bar\";\n}\n__name(isBarPlot, \"isBarPlot\");\nfunction isBandAxisData(data) {\n  return data.type === \"band\";\n}\n__name(isBandAxisData, \"isBandAxisData\");\nfunction isLinearAxisData(data) {\n  return data.type === \"linear\";\n}\n__name(isLinearAxisData, \"isLinearAxisData\");\n\n// src/diagrams/xychart/chartBuilder/textDimensionCalculator.ts\nvar TextDimensionCalculatorWithFont = class {\n  constructor(parentGroup) {\n    this.parentGroup = parentGroup;\n  }\n  static {\n    __name(this, \"TextDimensionCalculatorWithFont\");\n  }\n  getMaxDimension(texts, fontSize) {\n    if (!this.parentGroup) {\n      return {\n        width: texts.reduce((acc, cur) => Math.max(cur.length, acc), 0) * fontSize,\n        height: fontSize\n      };\n    }\n    const dimension = {\n      width: 0,\n      height: 0\n    };\n    const elem = this.parentGroup.append(\"g\").attr(\"visibility\", \"hidden\").attr(\"font-size\", fontSize);\n    for (const t of texts) {\n      const bbox = computeDimensionOfText(elem, 1, t);\n      const width = bbox ? bbox.width : t.length * fontSize;\n      const height = bbox ? bbox.height : fontSize;\n      dimension.width = Math.max(dimension.width, width);\n      dimension.height = Math.max(dimension.height, height);\n    }\n    elem.remove();\n    return dimension;\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts\nimport { scaleBand } from \"d3\";\n\n// src/diagrams/xychart/chartBuilder/components/axis/baseAxis.ts\nvar BAR_WIDTH_TO_TICK_WIDTH_RATIO = 0.7;\nvar MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL = 0.2;\nvar BaseAxis = class {\n  constructor(axisConfig, title, textDimensionCalculator, axisThemeConfig) {\n    this.axisConfig = axisConfig;\n    this.title = title;\n    this.textDimensionCalculator = textDimensionCalculator;\n    this.axisThemeConfig = axisThemeConfig;\n    this.boundingRect = { x: 0, y: 0, width: 0, height: 0 };\n    this.axisPosition = \"left\";\n    this.showTitle = false;\n    this.showLabel = false;\n    this.showTick = false;\n    this.showAxisLine = false;\n    this.outerPadding = 0;\n    this.titleTextHeight = 0;\n    this.labelTextHeight = 0;\n    this.range = [0, 10];\n    this.boundingRect = { x: 0, y: 0, width: 0, height: 0 };\n    this.axisPosition = \"left\";\n  }\n  static {\n    __name(this, \"BaseAxis\");\n  }\n  setRange(range) {\n    this.range = range;\n    if (this.axisPosition === \"left\" || this.axisPosition === \"right\") {\n      this.boundingRect.height = range[1] - range[0];\n    } else {\n      this.boundingRect.width = range[1] - range[0];\n    }\n    this.recalculateScale();\n  }\n  getRange() {\n    return [this.range[0] + this.outerPadding, this.range[1] - this.outerPadding];\n  }\n  setAxisPosition(axisPosition) {\n    this.axisPosition = axisPosition;\n    this.setRange(this.range);\n  }\n  getTickDistance() {\n    const range = this.getRange();\n    return Math.abs(range[0] - range[1]) / this.getTickValues().length;\n  }\n  getAxisOuterPadding() {\n    return this.outerPadding;\n  }\n  getLabelDimension() {\n    return this.textDimensionCalculator.getMaxDimension(\n      this.getTickValues().map((tick) => tick.toString()),\n      this.axisConfig.labelFontSize\n    );\n  }\n  recalculateOuterPaddingToDrawBar() {\n    if (BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() > this.outerPadding * 2) {\n      this.outerPadding = Math.floor(BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() / 2);\n    }\n    this.recalculateScale();\n  }\n  calculateSpaceIfDrawnHorizontally(availableSpace) {\n    let availableHeight = availableSpace.height;\n    if (this.axisConfig.showAxisLine && availableHeight > this.axisConfig.axisLineWidth) {\n      availableHeight -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.width;\n      this.outerPadding = Math.min(spaceRequired.width / 2, maxPadding);\n      const heightRequired = spaceRequired.height + this.axisConfig.labelPadding * 2;\n      this.labelTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableHeight >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableHeight -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const heightRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height - availableHeight;\n  }\n  calculateSpaceIfDrawnVertical(availableSpace) {\n    let availableWidth = availableSpace.width;\n    if (this.axisConfig.showAxisLine && availableWidth > this.axisConfig.axisLineWidth) {\n      availableWidth -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.height;\n      this.outerPadding = Math.min(spaceRequired.height / 2, maxPadding);\n      const widthRequired = spaceRequired.width + this.axisConfig.labelPadding * 2;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableWidth >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableWidth -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const widthRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width - availableWidth;\n    this.boundingRect.height = availableSpace.height;\n  }\n  calculateSpace(availableSpace) {\n    if (this.axisPosition === \"left\" || this.axisPosition === \"right\") {\n      this.calculateSpaceIfDrawnVertical(availableSpace);\n    } else {\n      this.calculateSpaceIfDrawnHorizontally(availableSpace);\n    }\n    this.recalculateScale();\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  getDrawableElementsForLeftAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const x = this.boundingRect.x + this.boundingRect.width - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"left-axis\", \"axisl-line\"],\n        data: [\n          {\n            path: `M ${x},${this.boundingRect.y} L ${x},${this.boundingRect.y + this.boundingRect.height} `,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"left-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.boundingRect.x + this.boundingRect.width - (this.showLabel ? this.axisConfig.labelPadding : 0) - (this.showTick ? this.axisConfig.tickLength : 0) - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          y: this.getScaleValue(tick),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"middle\",\n          horizontalPos: \"right\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const x = this.boundingRect.x + this.boundingRect.width - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"left-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${x},${this.getScaleValue(tick)} L ${x - this.axisConfig.tickLength},${this.getScaleValue(tick)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"left-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.axisConfig.titlePadding,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 270,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElementsForBottomAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"bottom-axis\", \"axis-line\"],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${this.boundingRect.x + this.boundingRect.width},${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"bottom-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y: this.boundingRect.y + this.axisConfig.labelPadding + (this.showTick ? this.axisConfig.tickLength : 0) + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"bottom-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${y} L ${this.getScaleValue(tick)},${y + this.axisConfig.tickLength}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"bottom-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.range[0] + (this.range[1] - this.range[0]) / 2,\n            y: this.boundingRect.y + this.boundingRect.height - this.axisConfig.titlePadding - this.titleTextHeight,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElementsForTopAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.boundingRect.height - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"top-axis\", \"axis-line\"],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${this.boundingRect.x + this.boundingRect.width},${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"top-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y: this.boundingRect.y + (this.showTitle ? this.titleTextHeight + this.axisConfig.titlePadding * 2 : 0) + this.axisConfig.labelPadding,\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"top-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${y + this.boundingRect.height - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)} L ${this.getScaleValue(tick)},${y + this.boundingRect.height - this.axisConfig.tickLength - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"top-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.axisConfig.titlePadding,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElements() {\n    if (this.axisPosition === \"left\") {\n      return this.getDrawableElementsForLeftAxis();\n    }\n    if (this.axisPosition === \"right\") {\n      throw Error(\"Drawing of right axis is not implemented\");\n    }\n    if (this.axisPosition === \"bottom\") {\n      return this.getDrawableElementsForBottomAxis();\n    }\n    if (this.axisPosition === \"top\") {\n      return this.getDrawableElementsForTopAxis();\n    }\n    return [];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts\nvar BandAxis = class extends BaseAxis {\n  static {\n    __name(this, \"BandAxis\");\n  }\n  constructor(axisConfig, axisThemeConfig, categories, title, textDimensionCalculator) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.categories = categories;\n    this.scale = scaleBand().domain(this.categories).range(this.getRange());\n  }\n  setRange(range) {\n    super.setRange(range);\n  }\n  recalculateScale() {\n    this.scale = scaleBand().domain(this.categories).range(this.getRange()).paddingInner(1).paddingOuter(0).align(0.5);\n    log.trace(\"BandAxis axis final categories, range: \", this.categories, this.getRange());\n  }\n  getTickValues() {\n    return this.categories;\n  }\n  getScaleValue(value) {\n    return this.scale(value) ?? this.getRange()[0];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/linearAxis.ts\nimport { scaleLinear } from \"d3\";\nvar LinearAxis = class extends BaseAxis {\n  static {\n    __name(this, \"LinearAxis\");\n  }\n  constructor(axisConfig, axisThemeConfig, domain, title, textDimensionCalculator) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.domain = domain;\n    this.scale = scaleLinear().domain(this.domain).range(this.getRange());\n  }\n  getTickValues() {\n    return this.scale.ticks();\n  }\n  recalculateScale() {\n    const domain = [...this.domain];\n    if (this.axisPosition === \"left\") {\n      domain.reverse();\n    }\n    this.scale = scaleLinear().domain(domain).range(this.getRange());\n  }\n  getScaleValue(value) {\n    return this.scale(value);\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/index.ts\nfunction getAxis(data, axisConfig, axisThemeConfig, tmpSVGGroup2) {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);\n  if (isBandAxisData(data)) {\n    return new BandAxis(\n      axisConfig,\n      axisThemeConfig,\n      data.categories,\n      data.title,\n      textDimensionCalculator\n    );\n  }\n  return new LinearAxis(\n    axisConfig,\n    axisThemeConfig,\n    [data.min, data.max],\n    data.title,\n    textDimensionCalculator\n  );\n}\n__name(getAxis, \"getAxis\");\n\n// src/diagrams/xychart/chartBuilder/components/chartTitle.ts\nvar ChartTitle = class {\n  constructor(textDimensionCalculator, chartConfig, chartData, chartThemeConfig) {\n    this.textDimensionCalculator = textDimensionCalculator;\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.chartThemeConfig = chartThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n    this.showChartTitle = false;\n  }\n  static {\n    __name(this, \"ChartTitle\");\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace) {\n    const titleDimension = this.textDimensionCalculator.getMaxDimension(\n      [this.chartData.title],\n      this.chartConfig.titleFontSize\n    );\n    const widthRequired = Math.max(titleDimension.width, availableSpace.width);\n    const heightRequired = titleDimension.height + 2 * this.chartConfig.titlePadding;\n    if (titleDimension.width <= widthRequired && titleDimension.height <= heightRequired && this.chartConfig.showTitle && this.chartData.title) {\n      this.boundingRect.width = widthRequired;\n      this.boundingRect.height = heightRequired;\n      this.showChartTitle = true;\n    }\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  getDrawableElements() {\n    const drawableElem = [];\n    if (this.showChartTitle) {\n      drawableElem.push({\n        groupTexts: [\"chart-title\"],\n        type: \"text\",\n        data: [\n          {\n            fontSize: this.chartConfig.titleFontSize,\n            text: this.chartData.title,\n            verticalPos: \"middle\",\n            horizontalPos: \"center\",\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.chartThemeConfig.titleColor,\n            rotation: 0\n          }\n        ]\n      });\n    }\n    return drawableElem;\n  }\n};\nfunction getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);\n  return new ChartTitle(textDimensionCalculator, chartConfig, chartData, chartThemeConfig);\n}\n__name(getChartTitleComponent, \"getChartTitleComponent\");\n\n// src/diagrams/xychart/chartBuilder/components/plot/linePlot.ts\nimport { line } from \"d3\";\nvar LinePlot = class {\n  constructor(plotData, xAxis, yAxis, orientation, plotIndex2) {\n    this.plotData = plotData;\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n    this.orientation = orientation;\n    this.plotIndex = plotIndex2;\n  }\n  static {\n    __name(this, \"LinePlot\");\n  }\n  getDrawableElement() {\n    const finalData = this.plotData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1])\n    ]);\n    let path;\n    if (this.orientation === \"horizontal\") {\n      path = line().y((d) => d[0]).x((d) => d[1])(finalData);\n    } else {\n      path = line().x((d) => d[0]).y((d) => d[1])(finalData);\n    }\n    if (!path) {\n      return [];\n    }\n    return [\n      {\n        groupTexts: [\"plot\", `line-plot-${this.plotIndex}`],\n        type: \"path\",\n        data: [\n          {\n            path,\n            strokeFill: this.plotData.strokeFill,\n            strokeWidth: this.plotData.strokeWidth\n          }\n        ]\n      }\n    ];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/plot/barPlot.ts\nvar BarPlot = class {\n  constructor(barData, boundingRect, xAxis, yAxis, orientation, plotIndex2) {\n    this.barData = barData;\n    this.boundingRect = boundingRect;\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n    this.orientation = orientation;\n    this.plotIndex = plotIndex2;\n  }\n  static {\n    __name(this, \"BarPlot\");\n  }\n  getDrawableElement() {\n    const finalData = this.barData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1])\n    ]);\n    const barPaddingPercent = 0.05;\n    const barWidth = Math.min(this.xAxis.getAxisOuterPadding() * 2, this.xAxis.getTickDistance()) * (1 - barPaddingPercent);\n    const barWidthHalf = barWidth / 2;\n    if (this.orientation === \"horizontal\") {\n      return [\n        {\n          groupTexts: [\"plot\", `bar-plot-${this.plotIndex}`],\n          type: \"rect\",\n          data: finalData.map((data) => ({\n            x: this.boundingRect.x,\n            y: data[0] - barWidthHalf,\n            height: barWidth,\n            width: data[1] - this.boundingRect.x,\n            fill: this.barData.fill,\n            strokeWidth: 0,\n            strokeFill: this.barData.fill\n          }))\n        }\n      ];\n    }\n    return [\n      {\n        groupTexts: [\"plot\", `bar-plot-${this.plotIndex}`],\n        type: \"rect\",\n        data: finalData.map((data) => ({\n          x: data[0] - barWidthHalf,\n          y: data[1],\n          width: barWidth,\n          height: this.boundingRect.y + this.boundingRect.height - data[1],\n          fill: this.barData.fill,\n          strokeWidth: 0,\n          strokeFill: this.barData.fill\n        }))\n      }\n    ];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/plot/index.ts\nvar BasePlot = class {\n  constructor(chartConfig, chartData, chartThemeConfig) {\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.chartThemeConfig = chartThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  static {\n    __name(this, \"BasePlot\");\n  }\n  setAxes(xAxis, yAxis) {\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace) {\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height;\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  getDrawableElements() {\n    if (!(this.xAxis && this.yAxis)) {\n      throw Error(\"Axes must be passed to render Plots\");\n    }\n    const drawableElem = [];\n    for (const [i, plot] of this.chartData.plots.entries()) {\n      switch (plot.type) {\n        case \"line\":\n          {\n            const linePlot = new LinePlot(\n              plot,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...linePlot.getDrawableElement());\n          }\n          break;\n        case \"bar\":\n          {\n            const barPlot = new BarPlot(\n              plot,\n              this.boundingRect,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...barPlot.getDrawableElement());\n          }\n          break;\n      }\n    }\n    return drawableElem;\n  }\n};\nfunction getPlotComponent(chartConfig, chartData, chartThemeConfig) {\n  return new BasePlot(chartConfig, chartData, chartThemeConfig);\n}\n__name(getPlotComponent, \"getPlotComponent\");\n\n// src/diagrams/xychart/chartBuilder/orchestrator.ts\nvar Orchestrator = class {\n  constructor(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.componentStore = {\n      title: getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2),\n      plot: getPlotComponent(chartConfig, chartData, chartThemeConfig),\n      xAxis: getAxis(\n        chartData.xAxis,\n        chartConfig.xAxis,\n        {\n          titleColor: chartThemeConfig.xAxisTitleColor,\n          labelColor: chartThemeConfig.xAxisLabelColor,\n          tickColor: chartThemeConfig.xAxisTickColor,\n          axisLineColor: chartThemeConfig.xAxisLineColor\n        },\n        tmpSVGGroup2\n      ),\n      yAxis: getAxis(\n        chartData.yAxis,\n        chartConfig.yAxis,\n        {\n          titleColor: chartThemeConfig.yAxisTitleColor,\n          labelColor: chartThemeConfig.yAxisLabelColor,\n          tickColor: chartThemeConfig.yAxisTickColor,\n          axisLineColor: chartThemeConfig.yAxisLineColor\n        },\n        tmpSVGGroup2\n      )\n    };\n  }\n  static {\n    __name(this, \"Orchestrator\");\n  }\n  calculateVerticalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);\n    let chartHeight = Math.floor(\n      availableHeight * this.chartConfig.plotReservedSpacePercent / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight\n    });\n    plotY = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition(\"bottom\");\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableHeight -= spaceUsed.height;\n    this.componentStore.yAxis.setAxisPosition(\"left\");\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    plotX = spaceUsed.width;\n    availableWidth -= spaceUsed.width;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.xAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: plotX, y: plotY + chartHeight });\n    this.componentStore.yAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n  calculateHorizontalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let titleYEnd = 0;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);\n    let chartHeight = Math.floor(\n      availableHeight * this.chartConfig.plotReservedSpacePercent / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight\n    });\n    titleYEnd = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition(\"left\");\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableWidth -= spaceUsed.width;\n    plotX = spaceUsed.width;\n    this.componentStore.yAxis.setAxisPosition(\"top\");\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableHeight -= spaceUsed.height;\n    plotY = titleYEnd + spaceUsed.height;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.yAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: plotX, y: titleYEnd });\n    this.componentStore.xAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n  calculateSpace() {\n    if (this.chartConfig.chartOrientation === \"horizontal\") {\n      this.calculateHorizontalSpace();\n    } else {\n      this.calculateVerticalSpace();\n    }\n  }\n  getDrawableElement() {\n    this.calculateSpace();\n    const drawableElem = [];\n    this.componentStore.plot.setAxes(this.componentStore.xAxis, this.componentStore.yAxis);\n    for (const component of Object.values(this.componentStore)) {\n      drawableElem.push(...component.getDrawableElements());\n    }\n    return drawableElem;\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/index.ts\nvar XYChartBuilder = class {\n  static {\n    __name(this, \"XYChartBuilder\");\n  }\n  static build(config, chartData, chartThemeConfig, tmpSVGGroup2) {\n    const orchestrator = new Orchestrator(config, chartData, chartThemeConfig, tmpSVGGroup2);\n    return orchestrator.getDrawableElement();\n  }\n};\n\n// src/diagrams/xychart/xychartDb.ts\nvar plotIndex = 0;\nvar tmpSVGGroup;\nvar xyChartConfig = getChartDefaultConfig();\nvar xyChartThemeConfig = getChartDefaultThemeConfig();\nvar xyChartData = getChartDefaultData();\nvar plotColorPalette = xyChartThemeConfig.plotColorPalette.split(\",\").map((color) => color.trim());\nvar hasSetXAxis = false;\nvar hasSetYAxis = false;\nfunction getChartDefaultThemeConfig() {\n  const defaultThemeVariables = getThemeVariables();\n  const config = getConfig();\n  return cleanAndMerge(defaultThemeVariables.xyChart, config.themeVariables.xyChart);\n}\n__name(getChartDefaultThemeConfig, \"getChartDefaultThemeConfig\");\nfunction getChartDefaultConfig() {\n  const config = getConfig();\n  return cleanAndMerge(\n    defaultConfig_default.xyChart,\n    config.xyChart\n  );\n}\n__name(getChartDefaultConfig, \"getChartDefaultConfig\");\nfunction getChartDefaultData() {\n  return {\n    yAxis: {\n      type: \"linear\",\n      title: \"\",\n      min: Infinity,\n      max: -Infinity\n    },\n    xAxis: {\n      type: \"band\",\n      title: \"\",\n      categories: []\n    },\n    title: \"\",\n    plots: []\n  };\n}\n__name(getChartDefaultData, \"getChartDefaultData\");\nfunction textSanitizer(text) {\n  const config = getConfig();\n  return sanitizeText(text.trim(), config);\n}\n__name(textSanitizer, \"textSanitizer\");\nfunction setTmpSVGG(SVGG) {\n  tmpSVGGroup = SVGG;\n}\n__name(setTmpSVGG, \"setTmpSVGG\");\nfunction setOrientation(orientation) {\n  if (orientation === \"horizontal\") {\n    xyChartConfig.chartOrientation = \"horizontal\";\n  } else {\n    xyChartConfig.chartOrientation = \"vertical\";\n  }\n}\n__name(setOrientation, \"setOrientation\");\nfunction setXAxisTitle(title) {\n  xyChartData.xAxis.title = textSanitizer(title.text);\n}\n__name(setXAxisTitle, \"setXAxisTitle\");\nfunction setXAxisRangeData(min, max) {\n  xyChartData.xAxis = { type: \"linear\", title: xyChartData.xAxis.title, min, max };\n  hasSetXAxis = true;\n}\n__name(setXAxisRangeData, \"setXAxisRangeData\");\nfunction setXAxisBand(categories) {\n  xyChartData.xAxis = {\n    type: \"band\",\n    title: xyChartData.xAxis.title,\n    categories: categories.map((c) => textSanitizer(c.text))\n  };\n  hasSetXAxis = true;\n}\n__name(setXAxisBand, \"setXAxisBand\");\nfunction setYAxisTitle(title) {\n  xyChartData.yAxis.title = textSanitizer(title.text);\n}\n__name(setYAxisTitle, \"setYAxisTitle\");\nfunction setYAxisRangeData(min, max) {\n  xyChartData.yAxis = { type: \"linear\", title: xyChartData.yAxis.title, min, max };\n  hasSetYAxis = true;\n}\n__name(setYAxisRangeData, \"setYAxisRangeData\");\nfunction setYAxisRangeFromPlotData(data) {\n  const minValue = Math.min(...data);\n  const maxValue = Math.max(...data);\n  const prevMinValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.min : Infinity;\n  const prevMaxValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.max : -Infinity;\n  xyChartData.yAxis = {\n    type: \"linear\",\n    title: xyChartData.yAxis.title,\n    min: Math.min(prevMinValue, minValue),\n    max: Math.max(prevMaxValue, maxValue)\n  };\n}\n__name(setYAxisRangeFromPlotData, \"setYAxisRangeFromPlotData\");\nfunction transformDataWithoutCategory(data) {\n  let retData = [];\n  if (data.length === 0) {\n    return retData;\n  }\n  if (!hasSetXAxis) {\n    const prevMinValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.min : Infinity;\n    const prevMaxValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.max : -Infinity;\n    setXAxisRangeData(Math.min(prevMinValue, 1), Math.max(prevMaxValue, data.length));\n  }\n  if (!hasSetYAxis) {\n    setYAxisRangeFromPlotData(data);\n  }\n  if (isBandAxisData(xyChartData.xAxis)) {\n    retData = xyChartData.xAxis.categories.map((c, i) => [c, data[i]]);\n  }\n  if (isLinearAxisData(xyChartData.xAxis)) {\n    const min = xyChartData.xAxis.min;\n    const max = xyChartData.xAxis.max;\n    const step = (max - min) / (data.length - 1);\n    const categories = [];\n    for (let i = min; i <= max; i += step) {\n      categories.push(`${i}`);\n    }\n    retData = categories.map((c, i) => [c, data[i]]);\n  }\n  return retData;\n}\n__name(transformDataWithoutCategory, \"transformDataWithoutCategory\");\nfunction getPlotColorFromPalette(plotIndex2) {\n  return plotColorPalette[plotIndex2 === 0 ? 0 : plotIndex2 % plotColorPalette.length];\n}\n__name(getPlotColorFromPalette, \"getPlotColorFromPalette\");\nfunction setLineData(title, data) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: \"line\",\n    strokeFill: getPlotColorFromPalette(plotIndex),\n    strokeWidth: 2,\n    data: plotData\n  });\n  plotIndex++;\n}\n__name(setLineData, \"setLineData\");\nfunction setBarData(title, data) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: \"bar\",\n    fill: getPlotColorFromPalette(plotIndex),\n    data: plotData\n  });\n  plotIndex++;\n}\n__name(setBarData, \"setBarData\");\nfunction getDrawableElem() {\n  if (xyChartData.plots.length === 0) {\n    throw Error(\"No Plot to render, please provide a plot with some data\");\n  }\n  xyChartData.title = getDiagramTitle();\n  return XYChartBuilder.build(xyChartConfig, xyChartData, xyChartThemeConfig, tmpSVGGroup);\n}\n__name(getDrawableElem, \"getDrawableElem\");\nfunction getChartThemeConfig() {\n  return xyChartThemeConfig;\n}\n__name(getChartThemeConfig, \"getChartThemeConfig\");\nfunction getChartConfig() {\n  return xyChartConfig;\n}\n__name(getChartConfig, \"getChartConfig\");\nfunction getXYChartData() {\n  return xyChartData;\n}\n__name(getXYChartData, \"getXYChartData\");\nvar clear2 = /* @__PURE__ */ __name(function() {\n  clear();\n  plotIndex = 0;\n  xyChartConfig = getChartDefaultConfig();\n  xyChartData = getChartDefaultData();\n  xyChartThemeConfig = getChartDefaultThemeConfig();\n  plotColorPalette = xyChartThemeConfig.plotColorPalette.split(\",\").map((color) => color.trim());\n  hasSetXAxis = false;\n  hasSetYAxis = false;\n}, \"clear\");\nvar xychartDb_default = {\n  getDrawableElem,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  setOrientation,\n  setXAxisTitle,\n  setXAxisRangeData,\n  setXAxisBand,\n  setYAxisTitle,\n  setYAxisRangeData,\n  setLineData,\n  setBarData,\n  setTmpSVGG,\n  getChartThemeConfig,\n  getChartConfig,\n  getXYChartData\n};\n\n// src/diagrams/xychart/xychartRenderer.ts\nvar draw = /* @__PURE__ */ __name((txt, id, _version, diagObj) => {\n  const db = diagObj.db;\n  const themeConfig = db.getChartThemeConfig();\n  const chartConfig = db.getChartConfig();\n  const labelData = db.getXYChartData().plots[0].data.map((data) => data[1]);\n  function getDominantBaseLine(horizontalPos) {\n    return horizontalPos === \"top\" ? \"text-before-edge\" : \"middle\";\n  }\n  __name(getDominantBaseLine, \"getDominantBaseLine\");\n  function getTextAnchor(verticalPos) {\n    return verticalPos === \"left\" ? \"start\" : verticalPos === \"right\" ? \"end\" : \"middle\";\n  }\n  __name(getTextAnchor, \"getTextAnchor\");\n  function getTextTransformation(data) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n  __name(getTextTransformation, \"getTextTransformation\");\n  log.debug(\"Rendering xychart chart\\n\" + txt);\n  const svg = selectSvgElement(id);\n  const group = svg.append(\"g\").attr(\"class\", \"main\");\n  const background = group.append(\"rect\").attr(\"width\", chartConfig.width).attr(\"height\", chartConfig.height).attr(\"class\", \"background\");\n  configureSvgSize(svg, chartConfig.height, chartConfig.width, true);\n  svg.attr(\"viewBox\", `0 0 ${chartConfig.width} ${chartConfig.height}`);\n  background.attr(\"fill\", themeConfig.backgroundColor);\n  db.setTmpSVGG(svg.append(\"g\").attr(\"class\", \"mermaid-tmp-group\"));\n  const shapes = db.getDrawableElem();\n  const groups = {};\n  function getGroup(gList) {\n    let elem = group;\n    let prefix = \"\";\n    for (const [i] of gList.entries()) {\n      let parent = group;\n      if (i > 0 && groups[prefix]) {\n        parent = groups[prefix];\n      }\n      prefix += gList[i];\n      elem = groups[prefix];\n      if (!elem) {\n        elem = groups[prefix] = parent.append(\"g\").attr(\"class\", gList[i]);\n      }\n    }\n    return elem;\n  }\n  __name(getGroup, \"getGroup\");\n  for (const shape of shapes) {\n    if (shape.data.length === 0) {\n      continue;\n    }\n    const shapeGroup = getGroup(shape.groupTexts);\n    switch (shape.type) {\n      case \"rect\":\n        shapeGroup.selectAll(\"rect\").data(shape.data).enter().append(\"rect\").attr(\"x\", (data) => data.x).attr(\"y\", (data) => data.y).attr(\"width\", (data) => data.width).attr(\"height\", (data) => data.height).attr(\"fill\", (data) => data.fill).attr(\"stroke\", (data) => data.strokeFill).attr(\"stroke-width\", (data) => data.strokeWidth);\n        if (chartConfig.showDataLabel) {\n          if (chartConfig.chartOrientation === \"horizontal\") {\n            let fitsHorizontally2 = function(item, fontSize) {\n              const { data, label } = item;\n              const textWidth = fontSize * label.length * charWidthFactor;\n              return textWidth <= data.width - 10;\n            };\n            var fitsHorizontally = fitsHorizontally2;\n            __name(fitsHorizontally2, \"fitsHorizontally\");\n            const charWidthFactor = 0.7;\n            const validItems = shape.data.map((d, i) => ({ data: d, label: labelData[i].toString() })).filter((item) => item.data.width > 0 && item.data.height > 0);\n            const candidateFontSizes = validItems.map((item) => {\n              const { data } = item;\n              let fontSize = data.height * 0.7;\n              while (!fitsHorizontally2(item, fontSize) && fontSize > 0) {\n                fontSize -= 1;\n              }\n              return fontSize;\n            });\n            const uniformFontSize = Math.floor(Math.min(...candidateFontSizes));\n            shapeGroup.selectAll(\"text\").data(validItems).enter().append(\"text\").attr(\"x\", (item) => item.data.x + item.data.width - 10).attr(\"y\", (item) => item.data.y + item.data.height / 2).attr(\"text-anchor\", \"end\").attr(\"dominant-baseline\", \"middle\").attr(\"fill\", \"black\").attr(\"font-size\", `${uniformFontSize}px`).text((item) => item.label);\n          } else {\n            let fitsInBar2 = function(item, fontSize, yOffset2) {\n              const { data, label } = item;\n              const charWidthFactor = 0.7;\n              const textWidth = fontSize * label.length * charWidthFactor;\n              const centerX = data.x + data.width / 2;\n              const leftEdge = centerX - textWidth / 2;\n              const rightEdge = centerX + textWidth / 2;\n              const horizontalFits = leftEdge >= data.x && rightEdge <= data.x + data.width;\n              const verticalFits = data.y + yOffset2 + fontSize <= data.y + data.height;\n              return horizontalFits && verticalFits;\n            };\n            var fitsInBar = fitsInBar2;\n            __name(fitsInBar2, \"fitsInBar\");\n            const yOffset = 10;\n            const validItems = shape.data.map((d, i) => ({ data: d, label: labelData[i].toString() })).filter((item) => item.data.width > 0 && item.data.height > 0);\n            const candidateFontSizes = validItems.map((item) => {\n              const { data, label } = item;\n              let fontSize = data.width / (label.length * 0.7);\n              while (!fitsInBar2(item, fontSize, yOffset) && fontSize > 0) {\n                fontSize -= 1;\n              }\n              return fontSize;\n            });\n            const uniformFontSize = Math.floor(Math.min(...candidateFontSizes));\n            shapeGroup.selectAll(\"text\").data(validItems).enter().append(\"text\").attr(\"x\", (item) => item.data.x + item.data.width / 2).attr(\"y\", (item) => item.data.y + yOffset).attr(\"text-anchor\", \"middle\").attr(\"dominant-baseline\", \"hanging\").attr(\"fill\", \"black\").attr(\"font-size\", `${uniformFontSize}px`).text((item) => item.label);\n          }\n        }\n        break;\n      case \"text\":\n        shapeGroup.selectAll(\"text\").data(shape.data).enter().append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", (data) => data.fill).attr(\"font-size\", (data) => data.fontSize).attr(\"dominant-baseline\", (data) => getDominantBaseLine(data.verticalPos)).attr(\"text-anchor\", (data) => getTextAnchor(data.horizontalPos)).attr(\"transform\", (data) => getTextTransformation(data)).text((data) => data.text);\n        break;\n      case \"path\":\n        shapeGroup.selectAll(\"path\").data(shape.data).enter().append(\"path\").attr(\"d\", (data) => data.path).attr(\"fill\", (data) => data.fill ? data.fill : \"none\").attr(\"stroke\", (data) => data.strokeFill).attr(\"stroke-width\", (data) => data.strokeWidth);\n        break;\n    }\n  }\n}, \"draw\");\nvar xychartRenderer_default = {\n  draw\n};\n\n// src/diagrams/xychart/xychartDiagram.ts\nvar diagram = {\n  parser: xychart_default,\n  db: xychartDb_default,\n  renderer: xychartRenderer_default\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAe,SAAS,MAAM,OAAO,MAAM,MAAM;AAC/C,UAAQ,CAAC,OAAO,OAAO,CAAC,MAAM,QAAQ,IAAI,UAAU,UAAU,KAAK,OAAO,OAAO,QAAQ,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC;AAE9G,MAAI,IAAI,IACJ,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI,GACpDA,SAAQ,IAAI,MAAM,CAAC;AAEvB,SAAO,EAAE,IAAI,GAAG;AACdA,WAAM,CAAC,IAAI,QAAQ,IAAI;EACzB;AAEA,SAAOA;AACT;ACRe,SAAS,OAAO;AAC7B,MAAI,QAAQ,QAAA,EAAU,QAAQ,MAAS,GACnC,SAAS,MAAM,QACf,eAAe,MAAM,OACrB,KAAK,GACL,KAAK,GACL,MACA,WACA,QAAQ,OACR,eAAe,GACf,eAAe,GACf,QAAQ;AAEZ,SAAO,MAAM;AAEb,WAAS,UAAU;AACjB,QAAI,IAAI,OAAM,EAAG,QACb,UAAU,KAAK,IACf,QAAQ,UAAU,KAAK,IACvB,OAAO,UAAU,KAAK;AAC1B,YAAQ,OAAO,SAAS,KAAK,IAAI,GAAG,IAAI,eAAe,eAAe,CAAC;AACvE,QAAI;AAAO,aAAO,KAAK,MAAM,IAAI;AACjC,cAAU,OAAO,QAAQ,QAAQ,IAAI,iBAAiB;AACtD,gBAAY,QAAQ,IAAI;AACxB,QAAI;AAAO,cAAQ,KAAK,MAAM,KAAK,GAAG,YAAY,KAAK,MAAM,SAAS;AACtE,QAAI,SAASC,MAAS,CAAC,EAAE,IAAI,SAAS,GAAG;AAAE,aAAO,QAAQ,OAAO;IAAG,CAAC;AACrE,WAAO,aAAa,UAAU,OAAO,QAAO,IAAK,MAAM;EACzD;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,OAAO,CAAC,GAAG,QAAO,KAAM,OAAM;EAC3D;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,QAAO,KAAM,CAAC,IAAI,EAAE;EACnF;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,WAAO,CAAC,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,QAAQ,MAAM,QAAO;EAChE;AAEA,QAAM,YAAY,WAAW;AAC3B,WAAO;EACT;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO;EACT;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,CAAC,CAAC,GAAG,QAAO,KAAM;EACvD;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,eAAe,KAAK,IAAI,GAAG,eAAe,CAAC,CAAC,GAAG,QAAO,KAAM;EACzF;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,eAAe,KAAK,IAAI,GAAG,CAAC,GAAG,QAAO,KAAM;EACzE;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,eAAe,CAAC,GAAG,QAAO,KAAM;EAC7D;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC,GAAG,QAAO,KAAM;EAC/E;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,KAAK,OAAM,GAAI,CAAC,IAAI,EAAE,CAAC,EACzB,MAAM,KAAK,EACX,aAAa,YAAY,EACzB,aAAa,YAAY,EACzB,MAAM,KAAK;EAClB;AAEA,SAAO,UAAU,MAAM,QAAO,GAAI,SAAS;AAC7C;ACvDA,IAAI,SAAS,WAAW;AACtB,MAAI,IAAoB,OAAO,SAAS,GAAG,GAAG,IAAI,GAAG;AACnD,SAAK,KAAK,MAAM,CAAA,GAAI,IAAI,EAAE,QAAQ,KAAK,GAAG,EAAE,CAAC,CAAC,IAAI;AAAG;AACrD,WAAO;EACT,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE;AAC7pB,MAAI,UAAU;IACZ,OAAuB,OAAO,SAAS,QAAQ;IAC/C,GAAG,OAAO;IACV,IAAI,CAAA;IACJ,UAAU,EAAE,SAAS,GAAG,SAAS,GAAG,OAAO,GAAG,WAAW,GAAG,eAAe,GAAG,YAAY,GAAG,qBAAqB,GAAG,aAAa,GAAG,SAAS,IAAI,QAAQ,IAAI,UAAU,IAAI,cAAc,IAAI,UAAU,IAAI,cAAc,IAAI,QAAQ,IAAI,YAAY,IAAI,OAAO,IAAI,aAAa,IAAI,mBAAmB,IAAI,aAAa,IAAI,mBAAmB,IAAI,6BAA6B,IAAI,uBAAuB,IAAI,yBAAyB,IAAI,qBAAqB,IAAI,uBAAuB,IAAI,SAAS,IAAI,aAAa,IAAI,YAAY,IAAI,mBAAmB,IAAI,uBAAuB,IAAI,aAAa,IAAI,WAAW,IAAI,QAAQ,IAAI,OAAO,IAAI,YAAY,IAAI,OAAO,IAAI,UAAU,IAAI,iBAAiB,IAAI,OAAO,IAAI,OAAO,IAAI,SAAS,IAAI,QAAQ,IAAI,UAAU,IAAI,QAAQ,IAAI,OAAO,IAAI,QAAQ,IAAI,SAAS,IAAI,cAAc,IAAI,WAAW,GAAG,QAAQ,EAAC;IAC70B,YAAY,EAAE,GAAG,SAAS,GAAG,WAAW,GAAG,qBAAqB,IAAI,SAAS,IAAI,UAAU,IAAI,UAAU,IAAI,QAAQ,IAAI,OAAO,IAAI,aAAa,IAAI,mBAAmB,IAAI,aAAa,IAAI,mBAAmB,IAAI,6BAA6B,IAAI,uBAAuB,IAAI,qBAAqB,IAAI,uBAAuB,IAAI,SAAS,IAAI,mBAAmB,IAAI,WAAW,IAAI,QAAQ,IAAI,OAAO,IAAI,OAAO,IAAI,UAAU,IAAI,OAAO,IAAI,OAAO,IAAI,SAAS,IAAI,QAAQ,IAAI,UAAU,IAAI,QAAQ,IAAI,OAAO,IAAI,QAAQ,IAAI,SAAS,IAAI,aAAY;IAChiB,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACtc,eAA+B,OAAO,SAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAS,IAAI,IAAI;AACtG,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAO;QACb,KAAK;AACH,aAAG,eAAe,GAAG,EAAE,CAAC;AACxB;QACF,KAAK;AACH,aAAG,gBAAgB,GAAG,EAAE,EAAE,KAAK,KAAA,CAAM;AACrC;QACF,KAAK;AACH,aAAG,YAAY,EAAE,MAAM,IAAI,MAAM,OAAM,GAAI,GAAG,EAAE,CAAC;AACjD;QACF,KAAK;AACH,aAAG,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;AACjC;QACF,KAAK;AACH,aAAG,WAAW,EAAE,MAAM,IAAI,MAAM,OAAM,GAAI,GAAG,EAAE,CAAC;AAChD;QACF,KAAK;AACH,aAAG,WAAW,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;AAChC;QACF,KAAK;AACH,eAAK,IAAI,GAAG,EAAE,EAAE,KAAI;AACpB,aAAG,YAAY,KAAK,CAAC;AACrB;QACF,KAAK;QACL,KAAK;AACH,eAAK,IAAI,GAAG,EAAE,EAAE,KAAI;AACpB,aAAG,kBAAkB,KAAK,CAAC;AAC3B;QACF,KAAK;AACH,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;QACF,KAAK;AACH,eAAK,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;AACvC;QACF,KAAK;AACH,eAAK,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;AACxB;QACF,KAAK;AACH,aAAG,cAAc,GAAG,EAAE,CAAC;AACvB;QACF,KAAK;AACH,aAAG,cAAc,GAAG,KAAK,CAAC,CAAC;AAC3B;QACF,KAAK;AACH,aAAG,cAAc,EAAE,MAAM,QAAQ,MAAM,GAAA,CAAI;AAC3C;QACF,KAAK;AACH,aAAG,aAAa,GAAG,EAAE,CAAC;AACtB;QACF,KAAK;AACH,aAAG,kBAAkB,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC;AACvD;QACF,KAAK;AACH,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;QACF,KAAK;AACH,eAAK,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;AAC/B;QACF,KAAK;AACH,eAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AAChB;QACF,KAAK;AACH,aAAG,cAAc,GAAG,EAAE,CAAC;AACvB;QACF,KAAK;AACH,aAAG,cAAc,GAAG,KAAK,CAAC,CAAC;AAC3B;QACF,KAAK;AACH,aAAG,cAAc,EAAE,MAAM,QAAQ,MAAM,GAAA,CAAI;AAC3C;QACF,KAAK;AACH,aAAG,kBAAkB,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC;AACvD;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,GAAG,EAAE,GAAG,MAAM,OAAM;AACrC;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,GAAG,EAAE,GAAG,MAAM,OAAM;AACrC;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,GAAG,EAAE,GAAG,MAAM,WAAU;AACzC;QACF,KAAK;AACH,eAAK,IAAI,GAAG,EAAE;AACd;QACF,KAAK;AACH,eAAK,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE;AAChC;MACV;IACI,GAAG,WAAW;IACd,OAAO,CAAC,EAAE,KAAK,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAG,CAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAA,GAAK,EAAE,KAAK,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAA,CAAK,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,IAAG,CAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAC,GAAI,EAAE,KAAK,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAG,CAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAG,CAAE,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAA,GAAO,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAA,GAAO,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAA,GAAO,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAA,GAAO,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAG,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAG,CAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAA,CAAK,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAG,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI,IAAI,IAAG,CAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,IAAA,GAAO,EAAE,IAAI,IAAI,IAAI,IAAG,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,IAAG,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAG,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,IAAG,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAA,CAAG;IAC7tF,gBAAgB,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAC;IAC7E,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,UAAI,KAAK,aAAa;AACpB,aAAK,MAAM,GAAG;MAChB,OAAO;AACL,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;MACR;IACF,GAAG,YAAY;IACf,OAAuB,OAAO,SAAS,MAAM,OAAO;AAC/C,UAAC,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAA,GAAI,SAAS,CAAC,IAAI,GAAG,SAAS,CAAA,GAAI,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAmB,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAI,SAAS,OAAO,OAAO,KAAK,KAAK;AACrC,UAAI,cAAc,EAAE,IAAI,CAAA,EAAE;AAC1B,eAAS,KAAK,KAAK,IAAI;AACrB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AACpD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;QAC/B;MACF;AACA,aAAO,SAAS,OAAO,YAAY,EAAE;AACrC,kBAAY,GAAG,QAAQ;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAO,OAAO,UAAU,aAAa;AACvC,eAAO,SAAS,CAAA;MAClB;AACA,UAAI,QAAQ,OAAO;AACnB,aAAO,KAAK,KAAK;AACjB,UAAI,SAAS,OAAO,WAAW,OAAO,QAAQ;AAC9C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACnD,aAAK,aAAa,YAAY,GAAG;MACnC,OAAO;AACL,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;MAChD;AACA,eAAS,SAAS,GAAG;AACnB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;MAClC;AACA,aAAO,UAAU,UAAU;AAC3B,eAAS,MAAM;AACb,YAAI;AACJ,gBAAQ,OAAO,IAAG,KAAM,OAAO,IAAG,KAAM;AACxC,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,iBAAiB,OAAO;AAC1B,qBAAS;AACT,oBAAQ,OAAO,IAAG;UACpB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;QAClC;AACA,eAAO;MACT;AACA,aAAO,KAAK,KAAK;AACd,UAAC,QAAwB,OAAO,QAAW,GAAG,QAAQ,CAAA,GAAI,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACX,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC9B,mBAAS,KAAK,eAAe,KAAK;QACpC,OAAO;AACL,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACnD,qBAAS,IAAG;UACd;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;QAC9C;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AACjE,cAAI,SAAS;AACb,qBAAW,CAAA;AACX,eAAK,KAAK,MAAM,KAAK,GAAG;AACtB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AACpC,uBAAS,KAAK,MAAM,KAAK,WAAW,CAAC,IAAI,GAAG;YAC9C;UACF;AACA,cAAI,OAAO,cAAc;AACvB,qBAAS,0BAA0B,WAAW,KAAK,QAAQ,OAAO,aAAY,IAAK,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAa,KAAK,WAAW,MAAM,KAAK,UAAU;UAC9K,OAAO;AACL,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAO,KAAK,WAAW,MAAM,KAAK,UAAU;UACrJ;AACA,eAAK,WAAW,QAAQ;YACtB,MAAM,OAAO;YACb,OAAO,KAAK,WAAW,MAAM,KAAK;YAClC,MAAM,OAAO;YACb,KAAK;YACL;UACZ,CAAW;QACH;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACnD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;QACpG;AACA,gBAAQ,OAAO,CAAC,GAAC;UACf,KAAK;AACH,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAK,OAAO,MAAM;AACzB,mBAAO,KAAK,OAAO,MAAM;AACzB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACY;AACnB,uBAAS,OAAO;AAChB,uBAAS,OAAO;AAChB,yBAAW,OAAO;AAClB,sBAAQ,OAAO;YAIjB;AAIA;UACF,KAAK;AACH,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;cACT,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;YACrD;AACY,gBAAI,QAAQ;AACV,oBAAM,GAAG,QAAQ;gBACf,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;cACjD;YACY;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;cAClC;cACA;cACA;cACA,YAAY;cACZ,OAAO,CAAC;cACR;cACA;YACd,EAAc,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC5B,qBAAO;YACT;AACA,gBAAI,KAAK;AACP,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;YACnC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;UACF,KAAK;AACH,mBAAO;QACnB;MACM;AACA,aAAO;IACT,GAAG,OAAO;EACd;AACE,MAAI,QAAwB,WAAW;AACrC,QAAI,SAAS;MACX,KAAK;MACL,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,YAAI,KAAK,GAAG,QAAQ;AAClB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;QACrC,OAAO;AACL,gBAAM,IAAI,MAAM,GAAG;QACrB;MACF,GAAG,YAAY;;MAEf,UAA0B,OAAO,SAAS,OAAO,IAAI;AACnD,aAAK,KAAK,MAAM,KAAK,MAAM,CAAA;AAC3B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;UACZ,YAAY;UACZ,cAAc;UACd,WAAW;UACX,aAAa;QACvB;AACQ,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,GAAG,CAAC;QAC3B;AACA,aAAK,SAAS;AACd,eAAO;MACT,GAAG,UAAU;;MAEb,OAAuB,OAAO,WAAW;AACvC,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACT,eAAK;AACL,eAAK,OAAO;QACd,OAAO;AACL,eAAK,OAAO;QACd;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,MAAM,CAAC;QACrB;AACA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;MACT,GAAG,OAAO;;MAEV,OAAuB,OAAO,SAAS,IAAI;AACzC,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AACpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAC5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAC7D,YAAI,MAAM,SAAS,GAAG;AACpB,eAAK,YAAY,MAAM,SAAS;QAClC;AACA,YAAI,IAAI,KAAK,OAAO;AACpB,aAAK,SAAS;UACZ,YAAY,KAAK,OAAO;UACxB,WAAW,KAAK,WAAW;UAC3B,cAAc,KAAK,OAAO;UAC1B,aAAa,SAAS,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAAK,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS,KAAK,OAAO,eAAe;QAClM;AACQ,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;QACrD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;MACT,GAAG,OAAO;;MAEV,MAAsB,OAAO,WAAW;AACtC,aAAK,QAAQ;AACb,eAAO;MACT,GAAG,MAAM;;MAET,QAAwB,OAAO,WAAW;AACxC,YAAI,KAAK,QAAQ,iBAAiB;AAChC,eAAK,aAAa;QACpB,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAA,GAAgB;YAChO,MAAM;YACN,OAAO;YACP,MAAM,KAAK;UACvB,CAAW;QACH;AACA,eAAO;MACT,GAAG,QAAQ;;MAEX,MAAsB,OAAO,SAAS,GAAG;AACvC,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;MAChC,GAAG,MAAM;;MAET,WAA2B,OAAO,WAAW;AAC3C,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAQ,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;MAC7E,GAAG,WAAW;;MAEd,eAA+B,OAAO,WAAW;AAC/C,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AACpB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAK,KAAK,MAAM;QAChD;AACA,gBAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;MACjF,GAAG,eAAe;;MAElB,cAA8B,OAAO,WAAW;AAC9C,YAAI,MAAM,KAAK,UAAS;AACxB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAa,IAAK,OAAO,IAAI;MACjD,GAAG,cAAc;;MAEjB,YAA4B,OAAO,SAAS,OAAO,cAAc;AAC/D,YAAI,OAAO,OAAO;AAClB,YAAI,KAAK,QAAQ,iBAAiB;AAChC,mBAAS;YACP,UAAU,KAAK;YACf,QAAQ;cACN,YAAY,KAAK,OAAO;cACxB,WAAW,KAAK;cAChB,cAAc,KAAK,OAAO;cAC1B,aAAa,KAAK,OAAO;YACvC;YACY,QAAQ,KAAK;YACb,OAAO,KAAK;YACZ,SAAS,KAAK;YACd,SAAS,KAAK;YACd,QAAQ,KAAK;YACb,QAAQ,KAAK;YACb,OAAO,KAAK;YACZ,QAAQ,KAAK;YACb,IAAI,KAAK;YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;YAC3C,MAAM,KAAK;UACvB;AACU,cAAI,KAAK,QAAQ,QAAQ;AACvB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;UACjD;QACF;AACA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACT,eAAK,YAAY,MAAM;QACzB;AACA,aAAK,SAAS;UACZ,YAAY,KAAK,OAAO;UACxB,WAAW,KAAK,WAAW;UAC3B,cAAc,KAAK,OAAO;UAC1B,aAAa,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAAS,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;QACvJ;AACQ,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;QAC9D;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC5B,eAAK,OAAO;QACd;AACA,YAAI,OAAO;AACT,iBAAO;QACT,WAAW,KAAK,YAAY;AAC1B,mBAAS,KAAK,QAAQ;AACpB,iBAAK,CAAC,IAAI,OAAO,CAAC;UACpB;AACA,iBAAO;QACT;AACA,eAAO;MACT,GAAG,YAAY;;MAEf,MAAsB,OAAO,WAAW;AACtC,YAAI,KAAK,MAAM;AACb,iBAAO,KAAK;QACd;AACA,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,OAAO;QACd;AACA,YAAI,OAAO,OAAO,WAAW;AAC7B,YAAI,CAAC,KAAK,OAAO;AACf,eAAK,SAAS;AACd,eAAK,QAAQ;QACf;AACA,YAAI,QAAQ,KAAK,cAAa;AAC9B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAClE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAChC,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACnB,uBAAO;cACT,WAAW,KAAK,YAAY;AAC1B,wBAAQ;AACR;cACF,OAAO;AACL,uBAAO;cACT;YACF,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC7B;YACF;UACF;QACF;AACA,YAAI,OAAO;AACT,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACnB,mBAAO;UACT;AACA,iBAAO;QACT;AACA,YAAI,KAAK,WAAW,IAAI;AACtB,iBAAO,KAAK;QACd,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAA,GAAgB;YACtH,MAAM;YACN,OAAO;YACP,MAAM,KAAK;UACvB,CAAW;QACH;MACF,GAAG,MAAM;;MAET,KAAqB,OAAO,SAAS,MAAM;AACzC,YAAI,IAAI,KAAK,KAAI;AACjB,YAAI,GAAG;AACL,iBAAO;QACT,OAAO;AACL,iBAAO,KAAK,IAAG;QACjB;MACF,GAAG,KAAK;;MAER,OAAuB,OAAO,SAAS,MAAM,WAAW;AACtD,aAAK,eAAe,KAAK,SAAS;MACpC,GAAG,OAAO;;MAEV,UAA0B,OAAO,SAAS,WAAW;AACnD,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACT,iBAAO,KAAK,eAAe,IAAG;QAChC,OAAO;AACL,iBAAO,KAAK,eAAe,CAAC;QAC9B;MACF,GAAG,UAAU;;MAEb,eAA+B,OAAO,SAAS,gBAAgB;AAC7D,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACrF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;QAC9E,OAAO;AACL,iBAAO,KAAK,WAAW,SAAS,EAAE;QACpC;MACF,GAAG,eAAe;;MAElB,UAA0B,OAAO,SAAS,SAAS,GAAG;AACpD,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACV,iBAAO,KAAK,eAAe,CAAC;QAC9B,OAAO;AACL,iBAAO;QACT;MACF,GAAG,UAAU;;MAEb,WAA2B,OAAO,SAAS,UAAU,WAAW;AAC9D,aAAK,MAAM,SAAS;MACtB,GAAG,WAAW;;MAEd,gBAAgC,OAAO,SAAS,iBAAiB;AAC/D,eAAO,KAAK,eAAe;MAC7B,GAAG,gBAAgB;MACnB,SAAS,EAAE,oBAAoB,KAAI;MACnC,eAA+B,OAAO,SAAS,UAAU,IAAI,KAAK,2BAA2B,UAAU;AAErG,gBAAQ,2BAAyB;UAC/B,KAAK;AACH;UACF,KAAK;AACH;UACF,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH;UACF,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,WAAW;AAC1B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,WAAW;AAC1B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,qBAAqB;AACpC;UACF,KAAK;AACH,iBAAK,SAAQ;AACb;UACF,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,WAAW;AAC1B,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,WAAW;AAC1B,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,gBAAgB;AAC/B,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,YAAY;AAC3B,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb;UACF,KAAK;AACH,iBAAK,UAAU,QAAQ;AACvB;UACF,KAAK;AACH,iBAAK,SAAQ;AACb;UACF,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH;UACF,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;QAEnB;MACM,GAAG,WAAW;MACd,OAAO,CAAC,wBAAwB,uBAAuB,iBAAiB,iBAAiB,iBAAiB,kBAAkB,iBAAiB,yBAAyB,yBAAyB,yBAAyB,yBAAyB,0BAA0B,YAAY,gBAAgB,wBAAwB,iCAAiC,kBAAkB,kBAAkB,YAAY,aAAa,gBAAgB,eAAe,YAAY,sCAAsC,YAAY,kLAAkL,aAAa,aAAa,eAAe,YAAY,YAAY,mBAAmB,WAAW,YAAY,WAAW,WAAW,YAAY,WAAW,cAAc,YAAY,WAAW,WAAW,gBAAgB,aAAa,WAAW,SAAS;MACr6B,YAAY,EAAE,cAAc,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,KAAI,GAAI,QAAQ,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,KAAI,GAAI,kBAAkB,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,KAAI,GAAI,aAAa,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,KAAI,GAAI,uBAAuB,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,aAAa,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,aAAa,EAAE,SAAS,CAAC,CAAC,GAAG,aAAa,MAAK,GAAI,SAAS,EAAE,SAAS,CAAA,GAAI,aAAa,MAAK,GAAI,aAAa,EAAE,SAAS,CAAA,GAAI,aAAa,MAAK,GAAI,UAAU,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,WAAW,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,KAAI,EAAE;IAC7sC;AACI,WAAO;EACT,EAAC;AACD,UAAQ,QAAQ;AAChB,WAAS,SAAS;AAChB,SAAK,KAAK,CAAA;EACZ;AACA,SAAO,QAAQ,QAAQ;AACvB,SAAO,YAAY;AACnB,UAAQ,SAAS;AACjB,SAAO,IAAI,OAAM;AACnB,EAAC;AACD,OAAO,SAAS;AAChB,IAAI,kBAAkB;AAGtB,SAAS,UAAU,MAAM;AACvB,SAAO,KAAK,SAAS;AACvB;AACA,OAAO,WAAW,WAAW;AAC7B,SAAS,eAAe,MAAM;AAC5B,SAAO,KAAK,SAAS;AACvB;AACA,OAAO,gBAAgB,gBAAgB;AACvC,SAAS,iBAAiB,MAAM;AAC9B,SAAO,KAAK,SAAS;AACvB;AACA,OAAO,kBAAkB,kBAAkB;AAG3C,IAAI,mCAAkC,KAAA,MAAM;EAC1C,YAAY,aAAa;AACvB,SAAK,cAAc;EACrB;EAIA,gBAAgB,OAAO,UAAU;AAC/B,QAAI,CAAC,KAAK,aAAa;AACrB,aAAO;QACL,OAAO,MAAM,OAAO,CAAC,KAAK,QAAQ,KAAK,IAAI,IAAI,QAAQ,GAAG,GAAG,CAAC,IAAI;QAClE,QAAQ;MAChB;IACI;AACA,UAAM,YAAY;MAChB,OAAO;MACP,QAAQ;IACd;AACI,UAAM,OAAO,KAAK,YAAY,OAAO,GAAG,EAAE,KAAK,cAAc,QAAQ,EAAE,KAAK,aAAa,QAAQ;AACjG,eAAW,KAAK,OAAO;AACrB,YAAM,OAAO,uBAAuB,MAAM,GAAG,CAAC;AAC9C,YAAM,QAAQ,OAAO,KAAK,QAAQ,EAAE,SAAS;AAC7C,YAAM,SAAS,OAAO,KAAK,SAAS;AACpC,gBAAU,QAAQ,KAAK,IAAI,UAAU,OAAO,KAAK;AACjD,gBAAU,SAAS,KAAK,IAAI,UAAU,QAAQ,MAAM;IACtD;AACA,SAAK,OAAM;AACX,WAAO;EACT;AACF,GAxBI,OAAO,IAAM,iCAAiC,GALZ;AAmCtC,IAAI,gCAAgC;AACpC,IAAI,0CAA0C;AAC9C,IAAI,YAAW,KAAA,MAAM;EACnB,YAAY,YAAY,OAAO,yBAAyB,iBAAiB;AACvE,SAAK,aAAa;AAClB,SAAK,QAAQ;AACb,SAAK,0BAA0B;AAC/B,SAAK,kBAAkB;AACvB,SAAK,eAAe,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,EAAC;AACrD,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,QAAQ,CAAC,GAAG,EAAE;AACnB,SAAK,eAAe,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,EAAC;AACrD,SAAK,eAAe;EACtB;EAIA,SAASD,QAAO;AACd,SAAK,QAAQA;AACb,QAAI,KAAK,iBAAiB,UAAU,KAAK,iBAAiB,SAAS;AACjE,WAAK,aAAa,SAASA,OAAM,CAAC,IAAIA,OAAM,CAAC;IAC/C,OAAO;AACL,WAAK,aAAa,QAAQA,OAAM,CAAC,IAAIA,OAAM,CAAC;IAC9C;AACA,SAAK,iBAAgB;EACvB;EACA,WAAW;AACT,WAAO,CAAC,KAAK,MAAM,CAAC,IAAI,KAAK,cAAc,KAAK,MAAM,CAAC,IAAI,KAAK,YAAY;EAC9E;EACA,gBAAgB,cAAc;AAC5B,SAAK,eAAe;AACpB,SAAK,SAAS,KAAK,KAAK;EAC1B;EACA,kBAAkB;AAChB,UAAMA,SAAQ,KAAK,SAAQ;AAC3B,WAAO,KAAK,IAAIA,OAAM,CAAC,IAAIA,OAAM,CAAC,CAAC,IAAI,KAAK,cAAa,EAAG;EAC9D;EACA,sBAAsB;AACpB,WAAO,KAAK;EACd;EACA,oBAAoB;AAClB,WAAO,KAAK,wBAAwB;MAClC,KAAK,cAAa,EAAG,IAAI,CAAC,SAAS,KAAK,SAAA,CAAU;MAClD,KAAK,WAAW;IACtB;EACE;EACA,mCAAmC;AACjC,QAAI,gCAAgC,KAAK,gBAAe,IAAK,KAAK,eAAe,GAAG;AAClF,WAAK,eAAe,KAAK,MAAM,gCAAgC,KAAK,gBAAe,IAAK,CAAC;IAC3F;AACA,SAAK,iBAAgB;EACvB;EACA,kCAAkC,gBAAgB;AAChD,QAAI,kBAAkB,eAAe;AACrC,QAAI,KAAK,WAAW,gBAAgB,kBAAkB,KAAK,WAAW,eAAe;AACnF,yBAAmB,KAAK,WAAW;AACnC,WAAK,eAAe;IACtB;AACA,QAAI,KAAK,WAAW,WAAW;AAC7B,YAAM,gBAAgB,KAAK,kBAAiB;AAC5C,YAAM,aAAa,0CAA0C,eAAe;AAC5E,WAAK,eAAe,KAAK,IAAI,cAAc,QAAQ,GAAG,UAAU;AAChE,YAAM,iBAAiB,cAAc,SAAS,KAAK,WAAW,eAAe;AAC7E,WAAK,kBAAkB,cAAc;AACrC,UAAI,kBAAkB,iBAAiB;AACrC,2BAAmB;AACnB,aAAK,YAAY;MACnB;IACF;AACA,QAAI,KAAK,WAAW,YAAY,mBAAmB,KAAK,WAAW,YAAY;AAC7E,WAAK,WAAW;AAChB,yBAAmB,KAAK,WAAW;IACrC;AACA,QAAI,KAAK,WAAW,aAAa,KAAK,OAAO;AAC3C,YAAM,gBAAgB,KAAK,wBAAwB;QACjD,CAAC,KAAK,KAAK;QACX,KAAK,WAAW;MACxB;AACM,YAAM,iBAAiB,cAAc,SAAS,KAAK,WAAW,eAAe;AAC7E,WAAK,kBAAkB,cAAc;AACrC,UAAI,kBAAkB,iBAAiB;AACrC,2BAAmB;AACnB,aAAK,YAAY;MACnB;IACF;AACA,SAAK,aAAa,QAAQ,eAAe;AACzC,SAAK,aAAa,SAAS,eAAe,SAAS;EACrD;EACA,8BAA8B,gBAAgB;AAC5C,QAAI,iBAAiB,eAAe;AACpC,QAAI,KAAK,WAAW,gBAAgB,iBAAiB,KAAK,WAAW,eAAe;AAClF,wBAAkB,KAAK,WAAW;AAClC,WAAK,eAAe;IACtB;AACA,QAAI,KAAK,WAAW,WAAW;AAC7B,YAAM,gBAAgB,KAAK,kBAAiB;AAC5C,YAAM,aAAa,0CAA0C,eAAe;AAC5E,WAAK,eAAe,KAAK,IAAI,cAAc,SAAS,GAAG,UAAU;AACjE,YAAM,gBAAgB,cAAc,QAAQ,KAAK,WAAW,eAAe;AAC3E,UAAI,iBAAiB,gBAAgB;AACnC,0BAAkB;AAClB,aAAK,YAAY;MACnB;IACF;AACA,QAAI,KAAK,WAAW,YAAY,kBAAkB,KAAK,WAAW,YAAY;AAC5E,WAAK,WAAW;AAChB,wBAAkB,KAAK,WAAW;IACpC;AACA,QAAI,KAAK,WAAW,aAAa,KAAK,OAAO;AAC3C,YAAM,gBAAgB,KAAK,wBAAwB;QACjD,CAAC,KAAK,KAAK;QACX,KAAK,WAAW;MACxB;AACM,YAAM,gBAAgB,cAAc,SAAS,KAAK,WAAW,eAAe;AAC5E,WAAK,kBAAkB,cAAc;AACrC,UAAI,iBAAiB,gBAAgB;AACnC,0BAAkB;AAClB,aAAK,YAAY;MACnB;IACF;AACA,SAAK,aAAa,QAAQ,eAAe,QAAQ;AACjD,SAAK,aAAa,SAAS,eAAe;EAC5C;EACA,eAAe,gBAAgB;AAC7B,QAAI,KAAK,iBAAiB,UAAU,KAAK,iBAAiB,SAAS;AACjE,WAAK,8BAA8B,cAAc;IACnD,OAAO;AACL,WAAK,kCAAkC,cAAc;IACvD;AACA,SAAK,iBAAgB;AACrB,WAAO;MACL,OAAO,KAAK,aAAa;MACzB,QAAQ,KAAK,aAAa;IAChC;EACE;EACA,iBAAiB,OAAO;AACtB,SAAK,aAAa,IAAI,MAAM;AAC5B,SAAK,aAAa,IAAI,MAAM;EAC9B;EACA,iCAAiC;AAC/B,UAAM,kBAAkB,CAAA;AACxB,QAAI,KAAK,cAAc;AACrB,YAAM,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,QAAQ,KAAK,WAAW,gBAAgB;AAC1F,sBAAgB,KAAK;QACnB,MAAM;QACN,YAAY,CAAC,aAAa,YAAY;QACtC,MAAM;UACJ;YACE,MAAM,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC,MAAM,CAAC,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,MAAM;YAC5F,YAAY,KAAK,gBAAgB;YACjC,aAAa,KAAK,WAAW;UACzC;QACA;MACA,CAAO;IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;QACnB,MAAM;QACN,YAAY,CAAC,aAAa,OAAO;QACjC,MAAM,KAAK,cAAa,EAAG,IAAI,CAAC,UAAU;UACxC,MAAM,KAAK,SAAQ;UACnB,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS,KAAK,YAAY,KAAK,WAAW,eAAe,MAAM,KAAK,WAAW,KAAK,WAAW,aAAa,MAAM,KAAK,eAAe,KAAK,WAAW,gBAAgB;UACjN,GAAG,KAAK,cAAc,IAAI;UAC1B,MAAM,KAAK,gBAAgB;UAC3B,UAAU,KAAK,WAAW;UAC1B,UAAU;UACV,aAAa;UACb,eAAe;QACzB,EAAU;MACV,CAAO;IACH;AACA,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS,KAAK,eAAe,KAAK,WAAW,gBAAgB;AAC/G,sBAAgB,KAAK;QACnB,MAAM;QACN,YAAY,CAAC,aAAa,OAAO;QACjC,MAAM,KAAK,cAAa,EAAG,IAAI,CAAC,UAAU;UACxC,MAAM,KAAK,CAAC,IAAI,KAAK,cAAc,IAAI,CAAC,MAAM,IAAI,KAAK,WAAW,UAAU,IAAI,KAAK,cAAc,IAAI,CAAC;UACxG,YAAY,KAAK,gBAAgB;UACjC,aAAa,KAAK,WAAW;QACvC,EAAU;MACV,CAAO;IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;QACnB,MAAM;QACN,YAAY,CAAC,aAAa,OAAO;QACjC,MAAM;UACJ;YACE,MAAM,KAAK;YACX,GAAG,KAAK,aAAa,IAAI,KAAK,WAAW;YACzC,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS;YACpD,MAAM,KAAK,gBAAgB;YAC3B,UAAU,KAAK,WAAW;YAC1B,UAAU;YACV,aAAa;YACb,eAAe;UAC3B;QACA;MACA,CAAO;IACH;AACA,WAAO;EACT;EACA,mCAAmC;AACjC,UAAM,kBAAkB,CAAA;AACxB,QAAI,KAAK,cAAc;AACrB,YAAM,IAAI,KAAK,aAAa,IAAI,KAAK,WAAW,gBAAgB;AAChE,sBAAgB,KAAK;QACnB,MAAM;QACN,YAAY,CAAC,eAAe,WAAW;QACvC,MAAM;UACJ;YACE,MAAM,KAAK,KAAK,aAAa,CAAC,IAAI,CAAC,MAAM,KAAK,aAAa,IAAI,KAAK,aAAa,KAAK,IAAI,CAAC;YAC3F,YAAY,KAAK,gBAAgB;YACjC,aAAa,KAAK,WAAW;UACzC;QACA;MACA,CAAO;IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;QACnB,MAAM;QACN,YAAY,CAAC,eAAe,OAAO;QACnC,MAAM,KAAK,cAAa,EAAG,IAAI,CAAC,UAAU;UACxC,MAAM,KAAK,SAAQ;UACnB,GAAG,KAAK,cAAc,IAAI;UAC1B,GAAG,KAAK,aAAa,IAAI,KAAK,WAAW,gBAAgB,KAAK,WAAW,KAAK,WAAW,aAAa,MAAM,KAAK,eAAe,KAAK,WAAW,gBAAgB;UAChK,MAAM,KAAK,gBAAgB;UAC3B,UAAU,KAAK,WAAW;UAC1B,UAAU;UACV,aAAa;UACb,eAAe;QACzB,EAAU;MACV,CAAO;IACH;AACA,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,KAAK,aAAa,KAAK,KAAK,eAAe,KAAK,WAAW,gBAAgB;AACrF,sBAAgB,KAAK;QACnB,MAAM;QACN,YAAY,CAAC,eAAe,OAAO;QACnC,MAAM,KAAK,cAAa,EAAG,IAAI,CAAC,UAAU;UACxC,MAAM,KAAK,KAAK,cAAc,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,cAAc,IAAI,CAAC,IAAI,IAAI,KAAK,WAAW,UAAU;UACxG,YAAY,KAAK,gBAAgB;UACjC,aAAa,KAAK,WAAW;QACvC,EAAU;MACV,CAAO;IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;QACnB,MAAM;QACN,YAAY,CAAC,eAAe,OAAO;QACnC,MAAM;UACJ;YACE,MAAM,KAAK;YACX,GAAG,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,KAAK;YACrD,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS,KAAK,WAAW,eAAe,KAAK;YACxF,MAAM,KAAK,gBAAgB;YAC3B,UAAU,KAAK,WAAW;YAC1B,UAAU;YACV,aAAa;YACb,eAAe;UAC3B;QACA;MACA,CAAO;IACH;AACA,WAAO;EACT;EACA,gCAAgC;AAC9B,UAAM,kBAAkB,CAAA;AACxB,QAAI,KAAK,cAAc;AACrB,YAAM,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS,KAAK,WAAW,gBAAgB;AAC3F,sBAAgB,KAAK;QACnB,MAAM;QACN,YAAY,CAAC,YAAY,WAAW;QACpC,MAAM;UACJ;YACE,MAAM,KAAK,KAAK,aAAa,CAAC,IAAI,CAAC,MAAM,KAAK,aAAa,IAAI,KAAK,aAAa,KAAK,IAAI,CAAC;YAC3F,YAAY,KAAK,gBAAgB;YACjC,aAAa,KAAK,WAAW;UACzC;QACA;MACA,CAAO;IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;QACnB,MAAM;QACN,YAAY,CAAC,YAAY,OAAO;QAChC,MAAM,KAAK,cAAa,EAAG,IAAI,CAAC,UAAU;UACxC,MAAM,KAAK,SAAQ;UACnB,GAAG,KAAK,cAAc,IAAI;UAC1B,GAAG,KAAK,aAAa,KAAK,KAAK,YAAY,KAAK,kBAAkB,KAAK,WAAW,eAAe,IAAI,KAAK,KAAK,WAAW;UAC1H,MAAM,KAAK,gBAAgB;UAC3B,UAAU,KAAK,WAAW;UAC1B,UAAU;UACV,aAAa;UACb,eAAe;QACzB,EAAU;MACV,CAAO;IACH;AACA,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,KAAK,aAAa;AAC5B,sBAAgB,KAAK;QACnB,MAAM;QACN,YAAY,CAAC,YAAY,OAAO;QAChC,MAAM,KAAK,cAAa,EAAG,IAAI,CAAC,UAAU;UACxC,MAAM,KAAK,KAAK,cAAc,IAAI,CAAC,IAAI,IAAI,KAAK,aAAa,UAAU,KAAK,eAAe,KAAK,WAAW,gBAAgB,EAAE,MAAM,KAAK,cAAc,IAAI,CAAC,IAAI,IAAI,KAAK,aAAa,SAAS,KAAK,WAAW,cAAc,KAAK,eAAe,KAAK,WAAW,gBAAgB,EAAE;UAClR,YAAY,KAAK,gBAAgB;UACjC,aAAa,KAAK,WAAW;QACvC,EAAU;MACV,CAAO;IACH;AACA,QAAI,KAAK,WAAW;AAClB,sBAAgB,KAAK;QACnB,MAAM;QACN,YAAY,CAAC,YAAY,OAAO;QAChC,MAAM;UACJ;YACE,MAAM,KAAK;YACX,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,QAAQ;YACnD,GAAG,KAAK,aAAa,IAAI,KAAK,WAAW;YACzC,MAAM,KAAK,gBAAgB;YAC3B,UAAU,KAAK,WAAW;YAC1B,UAAU;YACV,aAAa;YACb,eAAe;UAC3B;QACA;MACA,CAAO;IACH;AACA,WAAO;EACT;EACA,sBAAsB;AACpB,QAAI,KAAK,iBAAiB,QAAQ;AAChC,aAAO,KAAK,+BAA8B;IAC5C;AACA,QAAI,KAAK,iBAAiB,SAAS;AACjC,YAAM,MAAM,0CAA0C;IACxD;AACA,QAAI,KAAK,iBAAiB,UAAU;AAClC,aAAO,KAAK,iCAAgC;IAC9C;AACA,QAAI,KAAK,iBAAiB,OAAO;AAC/B,aAAO,KAAK,8BAA6B;IAC3C;AACA,WAAO,CAAA;EACT;AACF,GA3UI,OAAO,IAAM,UAAU,GApBZ;AAkWf,IAAI,YAAW,KAAA,cAAc,SAAS;EAIpC,YAAY,YAAY,iBAAiB,YAAY,OAAO,yBAAyB;AACnF,UAAM,YAAY,OAAO,yBAAyB,eAAe;AACjE,SAAK,aAAa;AAClB,SAAK,QAAQE,KAAS,EAAG,OAAO,KAAK,UAAU,EAAE,MAAM,KAAK,SAAA,CAAU;EACxE;EACA,SAASF,QAAO;AACd,UAAM,SAASA,MAAK;EACtB;EACA,mBAAmB;AACjB,SAAK,QAAQE,KAAS,EAAG,OAAO,KAAK,UAAU,EAAE,MAAM,KAAK,SAAA,CAAU,EAAE,aAAa,CAAC,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG;AACjH,QAAI,MAAM,2CAA2C,KAAK,YAAY,KAAK,SAAA,CAAU;EACvF;EACA,gBAAgB;AACd,WAAO,KAAK;EACd;EACA,cAAc,OAAO;AACnB,WAAO,KAAK,MAAM,KAAK,KAAK,KAAK,SAAQ,EAAG,CAAC;EAC/C;AACF,GApBI,OAAO,IAAM,UAAU,GAFZ;AA0Bf,IAAI,cAAa,KAAA,cAAc,SAAS;EAItC,YAAY,YAAY,iBAAiB,QAAQ,OAAO,yBAAyB;AAC/E,UAAM,YAAY,OAAO,yBAAyB,eAAe;AACjE,SAAK,SAAS;AACd,SAAK,QAAQC,OAAW,EAAG,OAAO,KAAK,MAAM,EAAE,MAAM,KAAK,SAAA,CAAU;EACtE;EACA,gBAAgB;AACd,WAAO,KAAK,MAAM,MAAK;EACzB;EACA,mBAAmB;AACjB,UAAM,SAAS,CAAC,GAAG,KAAK,MAAM;AAC9B,QAAI,KAAK,iBAAiB,QAAQ;AAChC,aAAO,QAAO;IAChB;AACA,SAAK,QAAQA,OAAW,EAAG,OAAO,MAAM,EAAE,MAAM,KAAK,SAAA,CAAU;EACjE;EACA,cAAc,OAAO;AACnB,WAAO,KAAK,MAAM,KAAK;EACzB;AACF,GApBI,OAAO,IAAM,YAAY,GAFZ;AAyBjB,SAAS,QAAQ,MAAM,YAAY,iBAAiB,cAAc;AAChE,QAAM,0BAA0B,IAAI,gCAAgC,YAAY;AAChF,MAAI,eAAe,IAAI,GAAG;AACxB,WAAO,IAAI;MACT;MACA;MACA,KAAK;MACL,KAAK;MACL;IACN;EACE;AACA,SAAO,IAAI;IACT;IACA;IACA,CAAC,KAAK,KAAK,KAAK,GAAG;IACnB,KAAK;IACL;EACJ;AACA;AACA,OAAO,SAAS,SAAS;AAGzB,IAAI,cAAa,KAAA,MAAM;EACrB,YAAY,yBAAyB,aAAa,WAAW,kBAAkB;AAC7E,SAAK,0BAA0B;AAC/B,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,eAAe;MAClB,GAAG;MACH,GAAG;MACH,OAAO;MACP,QAAQ;IACd;AACI,SAAK,iBAAiB;EACxB;EAIA,iBAAiB,OAAO;AACtB,SAAK,aAAa,IAAI,MAAM;AAC5B,SAAK,aAAa,IAAI,MAAM;EAC9B;EACA,eAAe,gBAAgB;AAC7B,UAAM,iBAAiB,KAAK,wBAAwB;MAClD,CAAC,KAAK,UAAU,KAAK;MACrB,KAAK,YAAY;IACvB;AACI,UAAM,gBAAgB,KAAK,IAAI,eAAe,OAAO,eAAe,KAAK;AACzE,UAAM,iBAAiB,eAAe,SAAS,IAAI,KAAK,YAAY;AACpE,QAAI,eAAe,SAAS,iBAAiB,eAAe,UAAU,kBAAkB,KAAK,YAAY,aAAa,KAAK,UAAU,OAAO;AAC1I,WAAK,aAAa,QAAQ;AAC1B,WAAK,aAAa,SAAS;AAC3B,WAAK,iBAAiB;IACxB;AACA,WAAO;MACL,OAAO,KAAK,aAAa;MACzB,QAAQ,KAAK,aAAa;IAChC;EACE;EACA,sBAAsB;AACpB,UAAM,eAAe,CAAA;AACrB,QAAI,KAAK,gBAAgB;AACvB,mBAAa,KAAK;QAChB,YAAY,CAAC,aAAa;QAC1B,MAAM;QACN,MAAM;UACJ;YACE,UAAU,KAAK,YAAY;YAC3B,MAAM,KAAK,UAAU;YACrB,aAAa;YACb,eAAe;YACf,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,QAAQ;YACnD,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS;YACpD,MAAM,KAAK,iBAAiB;YAC5B,UAAU;UACtB;QACA;MACA,CAAO;IACH;AACA,WAAO;EACT;AACF,GA7CI,OAAO,IAAM,YAAY,GAfZ;AA6DjB,SAAS,uBAAuB,aAAa,WAAW,kBAAkB,cAAc;AACtF,QAAM,0BAA0B,IAAI,gCAAgC,YAAY;AAChF,SAAO,IAAI,WAAW,yBAAyB,aAAa,WAAW,gBAAgB;AACzF;AACA,OAAO,wBAAwB,wBAAwB;AAIvD,IAAI,YAAW,KAAA,MAAM;EACnB,YAAY,UAAU,OAAO,OAAO,aAAa,YAAY;AAC3D,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,cAAc;AACnB,SAAK,YAAY;EACnB;EAIA,qBAAqB;AACnB,UAAM,YAAY,KAAK,SAAS,KAAK,IAAI,CAAC,MAAM;MAC9C,KAAK,MAAM,cAAc,EAAE,CAAC,CAAC;MAC7B,KAAK,MAAM,cAAc,EAAE,CAAC,CAAC;IACnC,CAAK;AACD,QAAI;AACJ,QAAI,KAAK,gBAAgB,cAAc;AACrC,aAAO,OAAI,EAAG,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,SAAS;IACvD,OAAO;AACL,aAAO,OAAI,EAAG,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,SAAS;IACvD;AACA,QAAI,CAAC,MAAM;AACT,aAAO,CAAA;IACT;AACA,WAAO;MACL;QACE,YAAY,CAAC,QAAQ,aAAa,KAAK,SAAS,EAAE;QAClD,MAAM;QACN,MAAM;UACJ;YACE;YACA,YAAY,KAAK,SAAS;YAC1B,aAAa,KAAK,SAAS;UACvC;QACA;MACA;IACA;EACE;AACF,GA9BI,OAAO,IAAM,UAAU,GATZ;AA0Cf,IAAI,WAAU,KAAA,MAAM;EAClB,YAAY,SAAS,cAAc,OAAO,OAAO,aAAa,YAAY;AACxE,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,cAAc;AACnB,SAAK,YAAY;EACnB;EAIA,qBAAqB;AACnB,UAAM,YAAY,KAAK,QAAQ,KAAK,IAAI,CAAC,MAAM;MAC7C,KAAK,MAAM,cAAc,EAAE,CAAC,CAAC;MAC7B,KAAK,MAAM,cAAc,EAAE,CAAC,CAAC;IACnC,CAAK;AACD,UAAM,oBAAoB;AAC1B,UAAM,WAAW,KAAK,IAAI,KAAK,MAAM,oBAAmB,IAAK,GAAG,KAAK,MAAM,gBAAe,CAAE,KAAK,IAAI;AACrG,UAAM,eAAe,WAAW;AAChC,QAAI,KAAK,gBAAgB,cAAc;AACrC,aAAO;QACL;UACE,YAAY,CAAC,QAAQ,YAAY,KAAK,SAAS,EAAE;UACjD,MAAM;UACN,MAAM,UAAU,IAAI,CAAC,UAAU;YAC7B,GAAG,KAAK,aAAa;YACrB,GAAG,KAAK,CAAC,IAAI;YACb,QAAQ;YACR,OAAO,KAAK,CAAC,IAAI,KAAK,aAAa;YACnC,MAAM,KAAK,QAAQ;YACnB,aAAa;YACb,YAAY,KAAK,QAAQ;UACrC,EAAY;QACZ;MACA;IACI;AACA,WAAO;MACL;QACE,YAAY,CAAC,QAAQ,YAAY,KAAK,SAAS,EAAE;QACjD,MAAM;QACN,MAAM,UAAU,IAAI,CAAC,UAAU;UAC7B,GAAG,KAAK,CAAC,IAAI;UACb,GAAG,KAAK,CAAC;UACT,OAAO;UACP,QAAQ,KAAK,aAAa,IAAI,KAAK,aAAa,SAAS,KAAK,CAAC;UAC/D,MAAM,KAAK,QAAQ;UACnB,aAAa;UACb,YAAY,KAAK,QAAQ;QACnC,EAAU;MACV;IACA;EACE;AACF,GA3CI,OAAO,IAAM,SAAS,GAVZ;AAwDd,IAAI,YAAW,KAAA,MAAM;EACnB,YAAY,aAAa,WAAW,kBAAkB;AACpD,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,eAAe;MAClB,GAAG;MACH,GAAG;MACH,OAAO;MACP,QAAQ;IACd;EACE;EAIA,QAAQ,OAAO,OAAO;AACpB,SAAK,QAAQ;AACb,SAAK,QAAQ;EACf;EACA,iBAAiB,OAAO;AACtB,SAAK,aAAa,IAAI,MAAM;AAC5B,SAAK,aAAa,IAAI,MAAM;EAC9B;EACA,eAAe,gBAAgB;AAC7B,SAAK,aAAa,QAAQ,eAAe;AACzC,SAAK,aAAa,SAAS,eAAe;AAC1C,WAAO;MACL,OAAO,KAAK,aAAa;MACzB,QAAQ,KAAK,aAAa;IAChC;EACE;EACA,sBAAsB;AACpB,QAAI,EAAE,KAAK,SAAS,KAAK,QAAQ;AAC/B,YAAM,MAAM,qCAAqC;IACnD;AACA,UAAM,eAAe,CAAA;AACrB,eAAW,CAAC,GAAG,IAAI,KAAK,KAAK,UAAU,MAAM,QAAA,GAAW;AACtD,cAAQ,KAAK,MAAI;QACf,KAAK;AACH;AACE,kBAAM,WAAW,IAAI;cACnB;cACA,KAAK;cACL,KAAK;cACL,KAAK,YAAY;cACjB;YACd;AACY,yBAAa,KAAK,GAAG,SAAS,mBAAkB,CAAE;UACpD;AACA;QACF,KAAK;AACH;AACE,kBAAM,UAAU,IAAI;cAClB;cACA,KAAK;cACL,KAAK;cACL,KAAK;cACL,KAAK,YAAY;cACjB;YACd;AACY,yBAAa,KAAK,GAAG,QAAQ,mBAAkB,CAAE;UACnD;AACA;MACV;IACI;AACA,WAAO;EACT;AACF,GAtDI,OAAO,IAAM,UAAU,GAbZ;AAoEf,SAAS,iBAAiB,aAAa,WAAW,kBAAkB;AAClE,SAAO,IAAI,SAAS,aAAa,WAAW,gBAAgB;AAC9D;AACA,OAAO,kBAAkB,kBAAkB;AAG3C,IAAI,gBAAe,KAAA,MAAM;EACvB,YAAY,aAAa,WAAW,kBAAkB,cAAc;AAClE,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,iBAAiB;MACpB,OAAO,uBAAuB,aAAa,WAAW,kBAAkB,YAAY;MACpF,MAAM,iBAAiB,aAAa,WAAW,gBAAgB;MAC/D,OAAO;QACL,UAAU;QACV,YAAY;QACZ;UACE,YAAY,iBAAiB;UAC7B,YAAY,iBAAiB;UAC7B,WAAW,iBAAiB;UAC5B,eAAe,iBAAiB;QAC1C;QACQ;MACR;MACM,OAAO;QACL,UAAU;QACV,YAAY;QACZ;UACE,YAAY,iBAAiB;UAC7B,YAAY,iBAAiB;UAC7B,WAAW,iBAAiB;UAC5B,eAAe,iBAAiB;QAC1C;QACQ;MACR;IACA;EACE;EAIA,yBAAyB;AACvB,QAAI,iBAAiB,KAAK,YAAY;AACtC,QAAI,kBAAkB,KAAK,YAAY;AACvC,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,aAAa,KAAK,MAAM,iBAAiB,KAAK,YAAY,2BAA2B,GAAG;AAC5F,QAAI,cAAc,KAAK;MACrB,kBAAkB,KAAK,YAAY,2BAA2B;IACpE;AACI,QAAI,YAAY,KAAK,eAAe,KAAK,eAAe;MACtD,OAAO;MACP,QAAQ;IACd,CAAK;AACD,sBAAkB,UAAU;AAC5B,uBAAmB,UAAU;AAC7B,gBAAY,KAAK,eAAe,MAAM,eAAe;MACnD,OAAO,KAAK,YAAY;MACxB,QAAQ;IACd,CAAK;AACD,YAAQ,UAAU;AAClB,uBAAmB,UAAU;AAC7B,SAAK,eAAe,MAAM,gBAAgB,QAAQ;AAClD,gBAAY,KAAK,eAAe,MAAM,eAAe;MACnD,OAAO;MACP,QAAQ;IACd,CAAK;AACD,uBAAmB,UAAU;AAC7B,SAAK,eAAe,MAAM,gBAAgB,MAAM;AAChD,gBAAY,KAAK,eAAe,MAAM,eAAe;MACnD,OAAO;MACP,QAAQ;IACd,CAAK;AACD,YAAQ,UAAU;AAClB,sBAAkB,UAAU;AAC5B,QAAI,iBAAiB,GAAG;AACtB,oBAAc;AACd,uBAAiB;IACnB;AACA,QAAI,kBAAkB,GAAG;AACvB,qBAAe;AACf,wBAAkB;IACpB;AACA,SAAK,eAAe,KAAK,eAAe;MACtC,OAAO;MACP,QAAQ;IACd,CAAK;AACD,SAAK,eAAe,KAAK,iBAAiB,EAAE,GAAG,OAAO,GAAG,MAAA,CAAO;AAChE,SAAK,eAAe,MAAM,SAAS,CAAC,OAAO,QAAQ,UAAU,CAAC;AAC9D,SAAK,eAAe,MAAM,iBAAiB,EAAE,GAAG,OAAO,GAAG,QAAQ,YAAA,CAAa;AAC/E,SAAK,eAAe,MAAM,SAAS,CAAC,OAAO,QAAQ,WAAW,CAAC;AAC/D,SAAK,eAAe,MAAM,iBAAiB,EAAE,GAAG,GAAG,GAAG,MAAA,CAAO;AAC7D,QAAI,KAAK,UAAU,MAAM,KAAK,CAAC,MAAM,UAAU,CAAC,CAAC,GAAG;AAClD,WAAK,eAAe,MAAM,iCAAgC;IAC5D;EACF;EACA,2BAA2B;AACzB,QAAI,iBAAiB,KAAK,YAAY;AACtC,QAAI,kBAAkB,KAAK,YAAY;AACvC,QAAI,YAAY;AAChB,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,aAAa,KAAK,MAAM,iBAAiB,KAAK,YAAY,2BAA2B,GAAG;AAC5F,QAAI,cAAc,KAAK;MACrB,kBAAkB,KAAK,YAAY,2BAA2B;IACpE;AACI,QAAI,YAAY,KAAK,eAAe,KAAK,eAAe;MACtD,OAAO;MACP,QAAQ;IACd,CAAK;AACD,sBAAkB,UAAU;AAC5B,uBAAmB,UAAU;AAC7B,gBAAY,KAAK,eAAe,MAAM,eAAe;MACnD,OAAO,KAAK,YAAY;MACxB,QAAQ;IACd,CAAK;AACD,gBAAY,UAAU;AACtB,uBAAmB,UAAU;AAC7B,SAAK,eAAe,MAAM,gBAAgB,MAAM;AAChD,gBAAY,KAAK,eAAe,MAAM,eAAe;MACnD,OAAO;MACP,QAAQ;IACd,CAAK;AACD,sBAAkB,UAAU;AAC5B,YAAQ,UAAU;AAClB,SAAK,eAAe,MAAM,gBAAgB,KAAK;AAC/C,gBAAY,KAAK,eAAe,MAAM,eAAe;MACnD,OAAO;MACP,QAAQ;IACd,CAAK;AACD,uBAAmB,UAAU;AAC7B,YAAQ,YAAY,UAAU;AAC9B,QAAI,iBAAiB,GAAG;AACtB,oBAAc;AACd,uBAAiB;IACnB;AACA,QAAI,kBAAkB,GAAG;AACvB,qBAAe;AACf,wBAAkB;IACpB;AACA,SAAK,eAAe,KAAK,eAAe;MACtC,OAAO;MACP,QAAQ;IACd,CAAK;AACD,SAAK,eAAe,KAAK,iBAAiB,EAAE,GAAG,OAAO,GAAG,MAAA,CAAO;AAChE,SAAK,eAAe,MAAM,SAAS,CAAC,OAAO,QAAQ,UAAU,CAAC;AAC9D,SAAK,eAAe,MAAM,iBAAiB,EAAE,GAAG,OAAO,GAAG,UAAA,CAAW;AACrE,SAAK,eAAe,MAAM,SAAS,CAAC,OAAO,QAAQ,WAAW,CAAC;AAC/D,SAAK,eAAe,MAAM,iBAAiB,EAAE,GAAG,GAAG,GAAG,MAAA,CAAO;AAC7D,QAAI,KAAK,UAAU,MAAM,KAAK,CAAC,MAAM,UAAU,CAAC,CAAC,GAAG;AAClD,WAAK,eAAe,MAAM,iCAAgC;IAC5D;EACF;EACA,iBAAiB;AACf,QAAI,KAAK,YAAY,qBAAqB,cAAc;AACtD,WAAK,yBAAwB;IAC/B,OAAO;AACL,WAAK,uBAAsB;IAC7B;EACF;EACA,qBAAqB;AACnB,SAAK,eAAc;AACnB,UAAM,eAAe,CAAA;AACrB,SAAK,eAAe,KAAK,QAAQ,KAAK,eAAe,OAAO,KAAK,eAAe,KAAK;AACrF,eAAW,aAAa,OAAO,OAAO,KAAK,cAAc,GAAG;AAC1D,mBAAa,KAAK,GAAG,UAAU,oBAAmB,CAAE;IACtD;AACA,WAAO;EACT;AACF,GAlII,OAAO,IAAM,cAAc,GAhCZ;AAqKnB,IAAI,kBAAiB,KAAA,MAAM;EAIzB,OAAO,MAAM,QAAQ,WAAW,kBAAkB,cAAc;AAC9D,UAAM,eAAe,IAAI,aAAa,QAAQ,WAAW,kBAAkB,YAAY;AACvF,WAAO,aAAa,mBAAkB;EACxC;AACF,GANI,OAAO,IAAM,gBAAgB,GAFZ;AAWrB,IAAI,YAAY;AAChB,IAAI;AACJ,IAAI,gBAAgB,sBAAqB;AACzC,IAAI,qBAAqB,2BAA0B;AACnD,IAAI,cAAc,oBAAmB;AACrC,IAAI,mBAAmB,mBAAmB,iBAAiB,MAAM,GAAG,EAAE,IAAI,CAAC,UAAU,MAAM,KAAA,CAAM;AACjG,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,SAAS,6BAA6B;AACpC,QAAM,wBAAwBC,mBAAiB;AAC/C,QAAM,SAAS,UAAS;AACxB,SAAO,cAAc,sBAAsB,SAAS,OAAO,eAAe,OAAO;AACnF;AACA,OAAO,4BAA4B,4BAA4B;AAC/D,SAAS,wBAAwB;AAC/B,QAAM,SAAS,UAAS;AACxB,SAAO;IACL,sBAAsB;IACtB,OAAO;EACX;AACA;AACA,OAAO,uBAAuB,uBAAuB;AACrD,SAAS,sBAAsB;AAC7B,SAAO;IACL,OAAO;MACL,MAAM;MACN,OAAO;MACP,KAAK;MACL,KAAK;IACX;IACI,OAAO;MACL,MAAM;MACN,OAAO;MACP,YAAY,CAAA;IAClB;IACI,OAAO;IACP,OAAO,CAAA;EACX;AACA;AACA,OAAO,qBAAqB,qBAAqB;AACjD,SAAS,cAAc,MAAM;AAC3B,QAAM,SAAS,UAAS;AACxB,SAAO,aAAa,KAAK,KAAI,GAAI,MAAM;AACzC;AACA,OAAO,eAAe,eAAe;AACrC,SAAS,WAAW,MAAM;AACxB,gBAAc;AAChB;AACA,OAAO,YAAY,YAAY;AAC/B,SAAS,eAAe,aAAa;AACnC,MAAI,gBAAgB,cAAc;AAChC,kBAAc,mBAAmB;EACnC,OAAO;AACL,kBAAc,mBAAmB;EACnC;AACF;AACA,OAAO,gBAAgB,gBAAgB;AACvC,SAAS,cAAc,OAAO;AAC5B,cAAY,MAAM,QAAQ,cAAc,MAAM,IAAI;AACpD;AACA,OAAO,eAAe,eAAe;AACrC,SAAS,kBAAkB,KAAK,KAAK;AACnC,cAAY,QAAQ,EAAE,MAAM,UAAU,OAAO,YAAY,MAAM,OAAO,KAAK,IAAG;AAC9E,gBAAc;AAChB;AACA,OAAO,mBAAmB,mBAAmB;AAC7C,SAAS,aAAa,YAAY;AAChC,cAAY,QAAQ;IAClB,MAAM;IACN,OAAO,YAAY,MAAM;IACzB,YAAY,WAAW,IAAI,CAAC,MAAM,cAAc,EAAE,IAAI,CAAC;EAC3D;AACE,gBAAc;AAChB;AACA,OAAO,cAAc,cAAc;AACnC,SAAS,cAAc,OAAO;AAC5B,cAAY,MAAM,QAAQ,cAAc,MAAM,IAAI;AACpD;AACA,OAAO,eAAe,eAAe;AACrC,SAAS,kBAAkB,KAAK,KAAK;AACnC,cAAY,QAAQ,EAAE,MAAM,UAAU,OAAO,YAAY,MAAM,OAAO,KAAK,IAAG;AAC9E,gBAAc;AAChB;AACA,OAAO,mBAAmB,mBAAmB;AAC7C,SAAS,0BAA0B,MAAM;AACvC,QAAM,WAAW,KAAK,IAAI,GAAG,IAAI;AACjC,QAAM,WAAW,KAAK,IAAI,GAAG,IAAI;AACjC,QAAM,eAAe,iBAAiB,YAAY,KAAK,IAAI,YAAY,MAAM,MAAM;AACnF,QAAM,eAAe,iBAAiB,YAAY,KAAK,IAAI,YAAY,MAAM,MAAM;AACnF,cAAY,QAAQ;IAClB,MAAM;IACN,OAAO,YAAY,MAAM;IACzB,KAAK,KAAK,IAAI,cAAc,QAAQ;IACpC,KAAK,KAAK,IAAI,cAAc,QAAQ;EACxC;AACA;AACA,OAAO,2BAA2B,2BAA2B;AAC7D,SAAS,6BAA6B,MAAM;AAC1C,MAAI,UAAU,CAAA;AACd,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;EACT;AACA,MAAI,CAAC,aAAa;AAChB,UAAM,eAAe,iBAAiB,YAAY,KAAK,IAAI,YAAY,MAAM,MAAM;AACnF,UAAM,eAAe,iBAAiB,YAAY,KAAK,IAAI,YAAY,MAAM,MAAM;AACnF,sBAAkB,KAAK,IAAI,cAAc,CAAC,GAAG,KAAK,IAAI,cAAc,KAAK,MAAM,CAAC;EAClF;AACA,MAAI,CAAC,aAAa;AAChB,8BAA0B,IAAI;EAChC;AACA,MAAI,eAAe,YAAY,KAAK,GAAG;AACrC,cAAU,YAAY,MAAM,WAAW,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;EACnE;AACA,MAAI,iBAAiB,YAAY,KAAK,GAAG;AACvC,UAAM,MAAM,YAAY,MAAM;AAC9B,UAAM,MAAM,YAAY,MAAM;AAC9B,UAAM,QAAQ,MAAM,QAAQ,KAAK,SAAS;AAC1C,UAAM,aAAa,CAAA;AACnB,aAAS,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM;AACrC,iBAAW,KAAK,GAAG,CAAC,EAAE;IACxB;AACA,cAAU,WAAW,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;EACjD;AACA,SAAO;AACT;AACA,OAAO,8BAA8B,8BAA8B;AACnE,SAAS,wBAAwB,YAAY;AAC3C,SAAO,iBAAiB,eAAe,IAAI,IAAI,aAAa,iBAAiB,MAAM;AACrF;AACA,OAAO,yBAAyB,yBAAyB;AACzD,SAAS,YAAY,OAAO,MAAM;AAChC,QAAM,WAAW,6BAA6B,IAAI;AAClD,cAAY,MAAM,KAAK;IACrB,MAAM;IACN,YAAY,wBAAwB,SAAS;IAC7C,aAAa;IACb,MAAM;EACV,CAAG;AACD;AACF;AACA,OAAO,aAAa,aAAa;AACjC,SAAS,WAAW,OAAO,MAAM;AAC/B,QAAM,WAAW,6BAA6B,IAAI;AAClD,cAAY,MAAM,KAAK;IACrB,MAAM;IACN,MAAM,wBAAwB,SAAS;IACvC,MAAM;EACV,CAAG;AACD;AACF;AACA,OAAO,YAAY,YAAY;AAC/B,SAAS,kBAAkB;AACzB,MAAI,YAAY,MAAM,WAAW,GAAG;AAClC,UAAM,MAAM,yDAAyD;EACvE;AACA,cAAY,QAAQ,gBAAe;AACnC,SAAO,eAAe,MAAM,eAAe,aAAa,oBAAoB,WAAW;AACzF;AACA,OAAO,iBAAiB,iBAAiB;AACzC,SAAS,sBAAsB;AAC7B,SAAO;AACT;AACA,OAAO,qBAAqB,qBAAqB;AACjD,SAAS,iBAAiB;AACxB,SAAO;AACT;AACA,OAAO,gBAAgB,gBAAgB;AACvC,SAAS,iBAAiB;AACxB,SAAO;AACT;AACA,OAAO,gBAAgB,gBAAgB;AACvC,IAAI,SAAyB,OAAO,WAAW;AAC7C,UAAK;AACL,cAAY;AACZ,kBAAgB,sBAAqB;AACrC,gBAAc,oBAAmB;AACjC,uBAAqB,2BAA0B;AAC/C,qBAAmB,mBAAmB,iBAAiB,MAAM,GAAG,EAAE,IAAI,CAAC,UAAU,MAAM,KAAA,CAAM;AAC7F,gBAAc;AACd,gBAAc;AAChB,GAAG,OAAO;AACV,IAAI,oBAAoB;EACtB;EACA,OAAO;EACP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AAGA,IAAI,OAAuB,OAAO,CAAC,KAAK,IAAI,UAAU,YAAY;AAChE,QAAM,KAAK,QAAQ;AACnB,QAAM,cAAc,GAAG,oBAAmB;AAC1C,QAAM,cAAc,GAAG,eAAc;AACrC,QAAM,YAAY,GAAG,eAAc,EAAG,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC;AACzE,WAAS,oBAAoB,eAAe;AAC1C,WAAO,kBAAkB,QAAQ,qBAAqB;EACxD;AACA,SAAO,qBAAqB,qBAAqB;AACjD,WAAS,cAAc,aAAa;AAClC,WAAO,gBAAgB,SAAS,UAAU,gBAAgB,UAAU,QAAQ;EAC9E;AACA,SAAO,eAAe,eAAe;AACrC,WAAS,sBAAsB,MAAM;AACnC,WAAO,aAAa,KAAK,CAAC,KAAK,KAAK,CAAC,YAAY,KAAK,YAAY,CAAC;EACrE;AACA,SAAO,uBAAuB,uBAAuB;AACrD,MAAI,MAAM,8BAA8B,GAAG;AAC3C,QAAM,MAAM,iBAAiB,EAAE;AAC/B,QAAM,QAAQ,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,MAAM;AAClD,QAAM,aAAa,MAAM,OAAO,MAAM,EAAE,KAAK,SAAS,YAAY,KAAK,EAAE,KAAK,UAAU,YAAY,MAAM,EAAE,KAAK,SAAS,YAAY;AACtI,mBAAiB,KAAK,YAAY,QAAQ,YAAY,OAAO,IAAI;AACjE,MAAI,KAAK,WAAW,OAAO,YAAY,KAAK,IAAI,YAAY,MAAM,EAAE;AACpE,aAAW,KAAK,QAAQ,YAAY,eAAe;AACnD,KAAG,WAAW,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,mBAAmB,CAAC;AAChE,QAAM,SAAS,GAAG,gBAAe;AACjC,QAAM,SAAS,CAAA;AACf,WAAS,SAAS,OAAO;AACvB,QAAI,OAAO;AACX,QAAI,SAAS;AACb,eAAW,CAAC,CAAC,KAAK,MAAM,QAAO,GAAI;AACjC,UAAI,SAAS;AACb,UAAI,IAAI,KAAK,OAAO,MAAM,GAAG;AAC3B,iBAAS,OAAO,MAAM;MACxB;AACA,gBAAU,MAAM,CAAC;AACjB,aAAO,OAAO,MAAM;AACpB,UAAI,CAAC,MAAM;AACT,eAAO,OAAO,MAAM,IAAI,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,MAAM,CAAC,CAAC;MACnE;IACF;AACA,WAAO;EACT;AACA,SAAO,UAAU,UAAU;AAC3B,aAAW,SAAS,QAAQ;AAC1B,QAAI,MAAM,KAAK,WAAW,GAAG;AAC3B;IACF;AACA,UAAM,aAAa,SAAS,MAAM,UAAU;AAC5C,YAAQ,MAAM,MAAI;MAChB,KAAK;AACH,mBAAW,UAAU,MAAM,EAAE,KAAK,MAAM,IAAI,EAAE,MAAK,EAAG,OAAO,MAAM,EAAE,KAAK,KAAK,CAAC,SAAS,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,SAAS,KAAK,CAAC,EAAE,KAAK,SAAS,CAAC,SAAS,KAAK,KAAK,EAAE,KAAK,UAAU,CAAC,SAAS,KAAK,MAAM,EAAE,KAAK,QAAQ,CAAC,SAAS,KAAK,IAAI,EAAE,KAAK,UAAU,CAAC,SAAS,KAAK,UAAU,EAAE,KAAK,gBAAgB,CAAC,SAAS,KAAK,WAAW;AAClU,YAAI,YAAY,eAAe;AAC7B,cAAI,YAAY,qBAAqB,cAAc;AACjD,gBAAI,oBAAoB,SAAS,MAAM,UAAU;AAC/C,oBAAM,EAAE,MAAM,MAAK,IAAK;AACxB,oBAAM,YAAY,WAAW,MAAM,SAAS;AAC5C,qBAAO,aAAa,KAAK,QAAQ;YACnC;AAEA,mBAAO,mBAAmB,kBAAkB;AAC5C,kBAAM,kBAAkB;AACxB,kBAAM,aAAa,MAAM,KAAK,IAAI,CAAC,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,UAAU,CAAC,EAAE,SAAQ,EAAE,EAAG,EAAE,OAAO,CAAC,SAAS,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,SAAS,CAAC;AACvJ,kBAAM,qBAAqB,WAAW,IAAI,CAAC,SAAS;AAClD,oBAAM,EAAE,KAAI,IAAK;AACjB,kBAAI,WAAW,KAAK,SAAS;AAC7B,qBAAO,CAAC,kBAAkB,MAAM,QAAQ,KAAK,WAAW,GAAG;AACzD,4BAAY;cACd;AACA,qBAAO;YACT,CAAC;AACD,kBAAM,kBAAkB,KAAK,MAAM,KAAK,IAAI,GAAG,kBAAkB,CAAC;AAClE,uBAAW,UAAU,MAAM,EAAE,KAAK,UAAU,EAAE,MAAK,EAAG,OAAO,MAAM,EAAE,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,EAAE,EAAE,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,SAAS,CAAC,EAAE,KAAK,eAAe,KAAK,EAAE,KAAK,qBAAqB,QAAQ,EAAE,KAAK,QAAQ,OAAO,EAAE,KAAK,aAAa,GAAG,eAAe,IAAI,EAAE,KAAK,CAAC,SAAS,KAAK,KAAK;UAC/U,OAAO;AACL,gBAAI,aAAa,SAAS,MAAM,UAAU,UAAU;AAClD,oBAAM,EAAE,MAAM,MAAK,IAAK;AACxB,oBAAM,kBAAkB;AACxB,oBAAM,YAAY,WAAW,MAAM,SAAS;AAC5C,oBAAM,UAAU,KAAK,IAAI,KAAK,QAAQ;AACtC,oBAAM,WAAW,UAAU,YAAY;AACvC,oBAAM,YAAY,UAAU,YAAY;AACxC,oBAAM,iBAAiB,YAAY,KAAK,KAAK,aAAa,KAAK,IAAI,KAAK;AACxE,oBAAM,eAAe,KAAK,IAAI,WAAW,YAAY,KAAK,IAAI,KAAK;AACnE,qBAAO,kBAAkB;YAC3B;AAEA,mBAAO,YAAY,WAAW;AAC9B,kBAAM,UAAU;AAChB,kBAAM,aAAa,MAAM,KAAK,IAAI,CAAC,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,UAAU,CAAC,EAAE,SAAQ,EAAE,EAAG,EAAE,OAAO,CAAC,SAAS,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,SAAS,CAAC;AACvJ,kBAAM,qBAAqB,WAAW,IAAI,CAAC,SAAS;AAClD,oBAAM,EAAE,MAAM,MAAK,IAAK;AACxB,kBAAI,WAAW,KAAK,SAAS,MAAM,SAAS;AAC5C,qBAAO,CAAC,WAAW,MAAM,UAAU,OAAO,KAAK,WAAW,GAAG;AAC3D,4BAAY;cACd;AACA,qBAAO;YACT,CAAC;AACD,kBAAM,kBAAkB,KAAK,MAAM,KAAK,IAAI,GAAG,kBAAkB,CAAC;AAClE,uBAAW,UAAU,MAAM,EAAE,KAAK,UAAU,EAAE,MAAA,EAAQ,OAAO,MAAM,EAAE,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,CAAC,EAAE,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK,IAAI,OAAO,EAAE,KAAK,eAAe,QAAQ,EAAE,KAAK,qBAAqB,SAAS,EAAE,KAAK,QAAQ,OAAO,EAAE,KAAK,aAAa,GAAG,eAAe,IAAI,EAAE,KAAK,CAAC,SAAS,KAAK,KAAK;UACrU;QACF;AACA;MACF,KAAK;AACH,mBAAW,UAAU,MAAM,EAAE,KAAK,MAAM,IAAI,EAAE,MAAK,EAAG,OAAO,MAAM,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,QAAQ,CAAC,SAAS,KAAK,IAAI,EAAE,KAAK,aAAa,CAAC,SAAS,KAAK,QAAQ,EAAE,KAAK,qBAAqB,CAAC,SAAS,oBAAoB,KAAK,WAAW,CAAC,EAAE,KAAK,eAAe,CAAC,SAAS,cAAc,KAAK,aAAa,CAAC,EAAE,KAAK,aAAa,CAAC,SAAS,sBAAsB,IAAI,CAAC,EAAE,KAAK,CAAC,SAAS,KAAK,IAAI;AACzY;MACF,KAAK;AACH,mBAAW,UAAU,MAAM,EAAE,KAAK,MAAM,IAAI,EAAE,MAAK,EAAG,OAAO,MAAM,EAAE,KAAK,KAAK,CAAC,SAAS,KAAK,IAAI,EAAE,KAAK,QAAQ,CAAC,SAAS,KAAK,OAAO,KAAK,OAAO,MAAM,EAAE,KAAK,UAAU,CAAC,SAAS,KAAK,UAAU,EAAE,KAAK,gBAAgB,CAAC,SAAS,KAAK,WAAW;AACpP;IACR;EACE;AACF,GAAG,MAAM;AACT,IAAI,0BAA0B;EAC5B;AACF;AAGG,IAAC,UAAU;EACZ,QAAQ;EACR,IAAI;EACJ,UAAU;AACZ;", "names": ["range", "sequence", "scaleBand", "scaleLinear", "getThemeVariables"]}