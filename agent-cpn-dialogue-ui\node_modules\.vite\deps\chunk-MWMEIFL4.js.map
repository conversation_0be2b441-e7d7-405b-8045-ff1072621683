{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/pug.mjs"], "sourcesContent": ["import javascript from './javascript.mjs';\nimport css from './css.mjs';\nimport sass from './sass.mjs';\nimport scss from './scss.mjs';\nimport stylus from './stylus.mjs';\nimport coffee from './coffee.mjs';\nimport html from './html.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Pug\", \"name\": \"pug\", \"patterns\": [{ \"comment\": \"Doctype declaration.\", \"match\": \"^(!!!|doctype)(\\\\s*[a-zA-Z0-9-_]+)?\", \"name\": \"meta.tag.sgml.doctype.html\" }, { \"begin\": \"^(\\\\s*)//-\", \"comment\": \"Unbuffered (pug-only) comments.\", \"end\": \"^(?!(\\\\1\\\\s)|\\\\s*$)\", \"name\": \"comment.unbuffered.block.pug\" }, { \"begin\": \"^(\\\\s*)//\", \"comment\": \"Buffered (html) comments.\", \"end\": \"^(?!(\\\\1\\\\s)|\\\\s*$)\", \"name\": \"string.comment.buffered.block.pug\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"invalid.illegal.comment.comment.block.pug\" } }, \"comment\": \"Buffered comments inside buffered comments will generate invalid html.\", \"match\": \"^\\\\s*(//)(?!-)\", \"name\": \"string.comment.buffered.block.pug\" }] }, { \"begin\": \"<!--\", \"end\": \"--\\\\s*>\", \"name\": \"comment.unbuffered.block.pug\", \"patterns\": [{ \"match\": \"--\", \"name\": \"invalid.illegal.comment.comment.block.pug\" }] }, { \"begin\": \"^(\\\\s*)-$\", \"comment\": \"Unbuffered code block.\", \"end\": \"^(?!(\\\\1\\\\s)|\\\\s*$)\", \"name\": \"source.js\", \"patterns\": [{ \"include\": \"source.js\" }] }, { \"begin\": \"^(\\\\s*)(script)((\\\\.$)|(?=[^\\\\n]*((text|application)/javascript|module).*\\\\.$))\", \"beginCaptures\": { \"2\": { \"name\": \"entity.name.tag.pug\" } }, \"comment\": \"Script tag with JavaScript code.\", \"end\": \"^(?!(\\\\1\\\\s)|\\\\s*$)\", \"name\": \"meta.tag.other\", \"patterns\": [{ \"begin\": \"\\\\G(?=\\\\()\", \"end\": \"$\", \"patterns\": [{ \"include\": \"#tag_attributes\" }] }, { \"begin\": \"\\\\G(?=[.#])\", \"end\": \"$\", \"patterns\": [{ \"include\": \"#complete_tag\" }] }, { \"include\": \"source.js\" }] }, { \"begin\": \"^(\\\\s*)(style)((\\\\.$)|(?=[.#(].*\\\\.$))\", \"beginCaptures\": { \"2\": { \"name\": \"entity.name.tag.pug\" } }, \"comment\": \"Style tag with CSS code.\", \"end\": \"^(?!(\\\\1\\\\s)|\\\\s*$)\", \"name\": \"meta.tag.other\", \"patterns\": [{ \"begin\": \"\\\\G(?=\\\\()\", \"end\": \"$\", \"patterns\": [{ \"include\": \"#tag_attributes\" }] }, { \"begin\": \"\\\\G(?=[.#])\", \"end\": \"$\", \"patterns\": [{ \"include\": \"#complete_tag\" }] }, { \"include\": \"source.css\" }] }, { \"begin\": \"^(\\\\s*):(sass)(?=\\\\(|$)\", \"beginCaptures\": { \"2\": { \"name\": \"constant.language.name.sass.filter.pug\" } }, \"end\": \"^(?!(\\\\1\\\\s)|\\\\s*$)\", \"name\": \"source.sass.filter.pug\", \"patterns\": [{ \"include\": \"#tag_attributes\" }, { \"include\": \"source.sass\" }] }, { \"begin\": \"^(\\\\s*):(scss)(?=\\\\(|$)\", \"beginCaptures\": { \"2\": { \"name\": \"constant.language.name.scss.filter.pug\" } }, \"end\": \"^(?!(\\\\1\\\\s)|\\\\s*$)\", \"name\": \"source.css.scss.filter.pug\", \"patterns\": [{ \"include\": \"#tag_attributes\" }, { \"include\": \"source.css.scss\" }] }, { \"begin\": \"^(\\\\s*):(less)(?=\\\\(|$)\", \"beginCaptures\": { \"2\": { \"name\": \"constant.language.name.less.filter.pug\" } }, \"end\": \"^(?!(\\\\1\\\\s)|\\\\s*$)\", \"name\": \"source.less.filter.pug\", \"patterns\": [{ \"include\": \"#tag_attributes\" }, { \"include\": \"source.less\" }] }, { \"begin\": \"^(\\\\s*):(stylus)(?=\\\\(|$)\", \"beginCaptures\": { \"2\": { \"name\": \"constant.language.name.stylus.filter.pug\" } }, \"end\": \"^(?!(\\\\1\\\\s)|\\\\s*$)\", \"patterns\": [{ \"include\": \"#tag_attributes\" }, { \"include\": \"source.stylus\" }] }, { \"begin\": \"^(\\\\s*):(coffee(-?script)?)(?=\\\\(|$)\", \"beginCaptures\": { \"2\": { \"name\": \"constant.language.name.coffeescript.filter.pug\" } }, \"end\": \"^(?!(\\\\1\\\\s)|\\\\s*$)\", \"name\": \"source.coffeescript.filter.pug\", \"patterns\": [{ \"include\": \"#tag_attributes\" }, { \"include\": \"source.coffee\" }] }, { \"begin\": \"^(\\\\s*):(uglify-js)(?=\\\\(|$)\", \"beginCaptures\": { \"2\": { \"name\": \"constant.language.name.js.filter.pug\" } }, \"end\": \"^(?!(\\\\1\\\\s)|\\\\s*$)\", \"name\": \"source.js.filter.pug\", \"patterns\": [{ \"include\": \"#tag_attributes\" }, { \"include\": \"source.js\" }] }, { \"begin\": \"^(\\\\s*)((:(?=.))|(:$))\", \"beginCaptures\": { \"4\": { \"name\": \"invalid.illegal.empty.generic.filter.pug\" } }, \"comment\": \"Generic Pug filter.\", \"end\": \"^(?!(\\\\1\\\\s)|\\\\s*$)\", \"patterns\": [{ \"begin\": \"\\\\G(?<=:)(?=.)\", \"end\": \"$\", \"name\": \"name.generic.filter.pug\", \"patterns\": [{ \"match\": \"\\\\G\\\\(\", \"name\": \"invalid.illegal.name.generic.filter.pug\" }, { \"match\": \"[\\\\w-]\", \"name\": \"constant.language.name.generic.filter.pug\" }, { \"include\": \"#tag_attributes\" }, { \"match\": \"\\\\W\", \"name\": \"invalid.illegal.name.generic.filter.pug\" }] }] }, { \"begin\": `^(\\\\s*)(?:(?=\\\\.$)|(?:(?=[\\\\w.#].*?\\\\.$)(?=(?:(?:(?:(?:(?:#[\\\\w-]+)|(?:\\\\.[\\\\w-]+))|(?:(?:[#!]\\\\{[^}]*\\\\})|(?:\\\\w(?:(?:[\\\\w:-]+[\\\\w-])|(?:[\\\\w-]*)))))(?:(?:#[\\\\w-]+)|(?:\\\\.[\\\\w-]+)|(?:\\\\((?:[^()\\\\'\\\\\"]*(?:(?:\\\\'(?:[^\\\\']|(?:(?<!\\\\\\\\)\\\\\\\\\\\\'))*\\\\')|(?:\\\\\"(?:[^\\\\\"]|(?:(?<!\\\\\\\\)\\\\\\\\\\\\\"))*\\\\\")))*[^()]*\\\\))*)*)(?:(?:(?::\\\\s+)|(?<=\\\\)))(?:(?:(?:(?:#[\\\\w-]+)|(?:\\\\.[\\\\w-]+))|(?:(?:[#!]\\\\{[^}]*\\\\})|(?:\\\\w(?:(?:[\\\\w:-]+[\\\\w-])|(?:[\\\\w-]*)))))(?:(?:#[\\\\w-]+)|(?:\\\\.[\\\\w-]+)|(?:\\\\((?:[^()\\\\'\\\\\"]*(?:(?:\\\\'(?:[^\\\\']|(?:(?<!\\\\\\\\)\\\\\\\\\\\\'))*\\\\')|(?:\\\\\"(?:[^\\\\\"]|(?:(?<!\\\\\\\\)\\\\\\\\\\\\\"))*\\\\\")))*[^()]*\\\\))*)*))*)\\\\.$)(?:(?:(#[\\\\w-]+)|(\\\\.[\\\\w-]+))|((?:[#!]\\\\{[^}]*\\\\})|(?:\\\\w(?:(?:[\\\\w:-]+[\\\\w-])|(?:[\\\\w-]*)))))))`, \"beginCaptures\": { \"2\": { \"name\": \"meta.selector.css entity.other.attribute-name.id.css.pug\" }, \"3\": { \"name\": \"meta.selector.css entity.other.attribute-name.class.css.pug\" }, \"4\": { \"name\": \"meta.tag.other entity.name.tag.pug\" } }, \"comment\": \"Generated from dot_block_tag.py\", \"end\": \"^(?!(\\\\1\\\\s)|\\\\s*$)\", \"patterns\": [{ \"match\": \"\\\\.$\", \"name\": \"storage.type.function.pug.dot-block-dot\" }, { \"include\": \"#tag_attributes\" }, { \"include\": \"#complete_tag\" }, { \"begin\": \"^(?=.)\", \"end\": \"$\", \"name\": \"text.block.pug\", \"patterns\": [{ \"include\": \"#inline_pug\" }, { \"include\": \"#embedded_html\" }, { \"include\": \"#html_entity\" }, { \"include\": \"#interpolated_value\" }, { \"include\": \"#interpolated_error\" }] }] }, { \"begin\": \"^\\\\s*\", \"comment\": \"All constructs that generally span a single line starting with any number of white-spaces.\", \"end\": \"$\", \"patterns\": [{ \"include\": \"#inline_pug\" }, { \"include\": \"#blocks_and_includes\" }, { \"include\": \"#unbuffered_code\" }, { \"include\": \"#mixin_definition\" }, { \"include\": \"#mixin_call\" }, { \"include\": \"#flow_control\" }, { \"include\": \"#flow_control_each\" }, { \"include\": \"#case_conds\" }, { \"begin\": \"\\\\|\", \"comment\": \"Tag pipe text line.\", \"end\": \"$\", \"name\": \"text.block.pipe.pug\", \"patterns\": [{ \"include\": \"#inline_pug\" }, { \"include\": \"#embedded_html\" }, { \"include\": \"#html_entity\" }, { \"include\": \"#interpolated_value\" }, { \"include\": \"#interpolated_error\" }] }, { \"include\": \"#printed_expression\" }, { \"begin\": \"\\\\G(?=(#[^\\\\{\\\\w-])|[^\\\\w.#])\", \"comment\": \"Line starting with characters incompatible with tag name/id/class is standalone text.\", \"end\": \"$\", \"patterns\": [{ \"begin\": \"</?(?=[!#])\", \"end\": \">|$\", \"patterns\": [{ \"include\": \"#inline_pug\" }, { \"include\": \"#interpolated_value\" }, { \"include\": \"#interpolated_error\" }] }, { \"include\": \"#inline_pug\" }, { \"include\": \"#embedded_html\" }, { \"include\": \"#html_entity\" }, { \"include\": \"#interpolated_value\" }, { \"include\": \"#interpolated_error\" }] }, { \"include\": \"#complete_tag\" }] }], \"repository\": { \"babel_parens\": { \"begin\": \"\\\\(\", \"end\": \"\\\\)|(({\\\\s*)?$)\", \"patterns\": [{ \"include\": \"#babel_parens\" }, { \"include\": \"source.js\" }] }, \"blocks_and_includes\": { \"captures\": { \"1\": { \"name\": \"storage.type.import.include.pug\" }, \"4\": { \"name\": \"variable.control.import.include.pug\" } }, \"comment\": \"Template blocks and includes.\", \"match\": \"(extends|include|yield|append|prepend|block( (append|prepend))?)\\\\s+(.*)$\", \"name\": \"meta.first-class.pug\" }, \"case_conds\": { \"begin\": \"(default|when)((\\\\s+|(?=:))|$)\", \"captures\": { \"1\": { \"name\": \"storage.type.function.pug\" } }, \"comment\": \"Pug case conditionals.\", \"end\": \"$\", \"name\": \"meta.control.flow.pug\", \"patterns\": [{ \"begin\": \"\\\\G(?!:)\", \"end\": \"(?=:\\\\s+)|$\", \"name\": \"js.embedded.control.flow.pug\", \"patterns\": [{ \"include\": \"#case_when_paren\" }, { \"include\": \"source.js\" }] }, { \"begin\": \":\\\\s+\", \"end\": \"$\", \"name\": \"tag.case.control.flow.pug\", \"patterns\": [{ \"include\": \"#complete_tag\" }] }] }, \"case_when_paren\": { \"begin\": \"\\\\(\", \"end\": \"\\\\)\", \"name\": \"js.when.control.flow.pug\", \"patterns\": [{ \"include\": \"#case_when_paren\" }, { \"match\": \":\", \"name\": \"invalid.illegal.name.tag.pug\" }, { \"include\": \"source.js\" }] }, \"complete_tag\": { \"begin\": \"(?=[\\\\w.#])|(:\\\\s*)\", \"end\": \"(\\\\.?$)|(?=:.)\", \"endCaptures\": { \"1\": { \"name\": \"storage.type.function.pug.dot-block-dot\" } }, \"patterns\": [{ \"include\": \"#blocks_and_includes\" }, { \"include\": \"#unbuffered_code\" }, { \"include\": \"#mixin_call\" }, { \"include\": \"#flow_control\" }, { \"include\": \"#flow_control_each\" }, { \"match\": \"(?<=:)\\\\w.*$\", \"name\": \"invalid.illegal.name.tag.pug\" }, { \"include\": \"#tag_name\" }, { \"include\": \"#tag_id\" }, { \"include\": \"#tag_classes\" }, { \"include\": \"#tag_attributes\" }, { \"include\": \"#tag_mixin_attributes\" }, { \"captures\": { \"2\": { \"name\": \"invalid.illegal.end.tag.pug\" }, \"4\": { \"name\": \"invalid.illegal.end.tag.pug\" } }, \"match\": \"((\\\\.)\\\\s+$)|((:)\\\\s*$)\" }, { \"include\": \"#printed_expression\" }, { \"include\": \"#tag_text\" }] }, \"embedded_html\": { \"begin\": \"(?=<[^>]*>)\", \"end\": \"$|(?=>)\", \"name\": \"html\", \"patterns\": [{ \"include\": \"text.html.basic\" }, { \"include\": \"#interpolated_value\" }, { \"include\": \"#interpolated_error\" }] }, \"flow_control\": { \"begin\": \"(for|if|else if|else|until|while|unless|case)(\\\\s+|$)\", \"captures\": { \"1\": { \"name\": \"storage.type.function.pug\" } }, \"comment\": \"Pug control flow.\", \"end\": \"$\", \"name\": \"meta.control.flow.pug\", \"patterns\": [{ \"begin\": \"\", \"end\": \"$\", \"name\": \"js.embedded.control.flow.pug\", \"patterns\": [{ \"include\": \"source.js\" }] }] }, \"flow_control_each\": { \"begin\": \"(each)(\\\\s+|$)\", \"captures\": { \"1\": { \"name\": \"storage.type.function.pug\" } }, \"end\": \"$\", \"name\": \"meta.control.flow.pug.each\", \"patterns\": [{ \"match\": \"([\\\\w$_]+)(?:\\\\s*,\\\\s*([\\\\w$_]+))?\", \"name\": \"variable.other.pug.each-var\" }, { \"begin\": \"\", \"end\": \"$\", \"name\": \"js.embedded.control.flow.pug\", \"patterns\": [{ \"include\": \"source.js\" }] }] }, \"html_entity\": { \"patterns\": [{ \"match\": \"(&)([a-zA-Z0-9]+|#[0-9]+|#x[0-9a-fA-F]+)(;)\", \"name\": \"constant.character.entity.html.text.pug\" }, { \"match\": \"[<>&]\", \"name\": \"invalid.illegal.html_entity.text.pug\" }] }, \"inline_pug\": { \"begin\": \"(?<!\\\\\\\\)(#\\\\[)\", \"captures\": { \"1\": { \"name\": \"entity.name.function.pug\" }, \"2\": { \"name\": \"entity.name.function.pug\" } }, \"end\": \"(\\\\])\", \"name\": \"inline.pug\", \"patterns\": [{ \"include\": \"#inline_pug\" }, { \"include\": \"#mixin_call\" }, { \"begin\": \"(?<!\\\\])(?=[\\\\w.#])|(:\\\\s*)\", \"end\": \"(?=\\\\]|(:.)|=|\\\\s)\", \"name\": \"tag.inline.pug\", \"patterns\": [{ \"include\": \"#tag_name\" }, { \"include\": \"#tag_id\" }, { \"include\": \"#tag_classes\" }, { \"include\": \"#tag_attributes\" }, { \"include\": \"#tag_mixin_attributes\" }, { \"include\": \"#inline_pug\" }, { \"match\": \"\\\\[\", \"name\": \"invalid.illegal.tag.pug\" }] }, { \"include\": \"#unbuffered_code\" }, { \"include\": \"#printed_expression\" }, { \"match\": \"\\\\[\", \"name\": \"invalid.illegal.tag.pug\" }, { \"include\": \"#inline_pug_text\" }] }, \"inline_pug_text\": { \"begin\": \"\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#inline_pug_text\" }] }, { \"include\": \"#inline_pug\" }, { \"include\": \"#embedded_html\" }, { \"include\": \"#html_entity\" }, { \"include\": \"#interpolated_value\" }, { \"include\": \"#interpolated_error\" }] }, \"interpolated_error\": { \"match\": \"(?<!\\\\\\\\)[#!]\\\\{(?=[^}]*$)\", \"name\": \"invalid.illegal.tag.pug\" }, \"interpolated_value\": { \"begin\": \"(?<!\\\\\\\\)[#!]\\\\{(?=.*?\\\\})\", \"end\": \"\\\\}\", \"name\": \"string.interpolated.pug\", \"patterns\": [{ \"match\": \"{\", \"name\": \"invalid.illegal.tag.pug\" }, { \"include\": \"source.js\" }] }, \"js_braces\": { \"begin\": \"\\\\{\", \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#js_braces\" }, { \"include\": \"source.js\" }] }, \"js_brackets\": { \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#js_brackets\" }, { \"include\": \"source.js\" }] }, \"js_parens\": { \"begin\": \"\\\\(\", \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#js_parens\" }, { \"include\": \"source.js\" }] }, \"mixin_call\": { \"begin\": \"((?:mixin\\\\s+)|\\\\+)([\\\\w-]+)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function.pug\" }, \"2\": { \"name\": \"meta.tag.other entity.name.function.pug\" } }, \"end\": \"(?!\\\\()|$\", \"patterns\": [{ \"begin\": \"(?<!\\\\))\\\\(\", \"end\": \"\\\\)\", \"name\": \"args.mixin.pug\", \"patterns\": [{ \"include\": \"#js_parens\" }, { \"captures\": { \"1\": { \"name\": \"meta.tag.other entity.other.attribute-name.tag.pug\" } }, \"match\": \"([^\\\\s(),=/]+)\\\\s*=\\\\s*\" }, { \"include\": \"source.js\" }] }, { \"include\": \"#tag_attributes\" }] }, \"mixin_definition\": { \"captures\": { \"1\": { \"name\": \"storage.type.function.pug\" }, \"2\": { \"name\": \"meta.tag.other entity.name.function.pug\" }, \"3\": { \"name\": \"punctuation.definition.parameters.begin.js\" }, \"4\": { \"name\": \"variable.parameter.function.js\" }, \"5\": { \"name\": \"punctuation.definition.parameters.begin.js\" } }, \"match\": \"(mixin\\\\s+)([\\\\w-]+)(?:(\\\\()\\\\s*((?:[a-zA-Z_]\\\\w*\\\\s*)(?:,\\\\s*[a-zA-Z_]\\\\w*\\\\s*)*)(\\\\)))?$\" }, \"printed_expression\": { \"begin\": \"(!?\\\\=)\\\\s*\", \"captures\": { \"1\": { \"name\": \"constant\" } }, \"end\": \"(?=\\\\])|$\", \"name\": \"source.js\", \"patterns\": [{ \"include\": \"#js_brackets\" }, { \"include\": \"source.js\" }] }, \"tag_attribute_name\": { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.tag.pug\" } }, \"match\": \"([^\\\\s(),=/!]+)\\\\s*\" }, \"tag_attribute_name_paren\": { \"begin\": \"\\\\(\\\\s*\", \"end\": \"\\\\)\", \"name\": \"entity.other.attribute-name.tag.pug\", \"patterns\": [{ \"include\": \"#tag_attribute_name_paren\" }, { \"include\": \"#tag_attribute_name\" }] }, \"tag_attributes\": { \"begin\": \"(\\\\(\\\\s*)\", \"captures\": { \"1\": { \"name\": \"constant.name.attribute.tag.pug\" } }, \"end\": \"(\\\\))\", \"name\": \"meta.tag.other\", \"patterns\": [{ \"include\": \"#tag_attribute_name_paren\" }, { \"include\": \"#tag_attribute_name\" }, { \"match\": \"!(?!=)\", \"name\": \"invalid.illegal.tag.pug\" }, { \"begin\": \"=\\\\s*\", \"end\": \"$|(?=,|(?:\\\\s+[^!%&*\\\\-+~|<>?/])|\\\\))\", \"name\": \"attribute_value\", \"patterns\": [{ \"include\": \"#js_parens\" }, { \"include\": \"#js_brackets\" }, { \"include\": \"#js_braces\" }, { \"include\": \"source.js\" }] }, { \"begin\": \"(?<=[%&*\\\\-+~|<>:?/])\\\\s+\", \"end\": \"$|(?=,|(?:\\\\s+[^!%&*\\\\-+~|<>?/])|\\\\))\", \"name\": \"attribute_value2\", \"patterns\": [{ \"include\": \"#js_parens\" }, { \"include\": \"#js_brackets\" }, { \"include\": \"#js_braces\" }, { \"include\": \"source.js\" }] }] }, \"tag_classes\": { \"captures\": { \"1\": { \"name\": \"invalid.illegal.tag.pug\" } }, \"match\": \"\\\\.([^\\\\w-])?[\\\\w-]*\", \"name\": \"meta.selector.css entity.other.attribute-name.class.css.pug\" }, \"tag_id\": { \"match\": \"#[\\\\w-]+\", \"name\": \"meta.selector.css entity.other.attribute-name.id.css.pug\" }, \"tag_mixin_attributes\": { \"begin\": \"(&attributes\\\\()\", \"captures\": { \"1\": { \"name\": \"entity.name.function.pug\" } }, \"end\": \"(\\\\))\", \"name\": \"meta.tag.other\", \"patterns\": [{ \"match\": \"attributes(?=\\\\))\", \"name\": \"storage.type.keyword.pug\" }, { \"include\": \"source.js\" }] }, \"tag_name\": { \"begin\": \"([#!]\\\\{(?=.*?\\\\}))|(\\\\w(([\\\\w:-]+[\\\\w-])|([\\\\w-]*)))\", \"end\": \"(\\\\G(?<!\\\\5[^\\\\w-]))|\\\\}|$\", \"name\": \"meta.tag.other entity.name.tag.pug\", \"patterns\": [{ \"begin\": \"\\\\G(?<=\\\\{)\", \"end\": \"(?=\\\\})\", \"name\": \"meta.tag.other entity.name.tag.pug\", \"patterns\": [{ \"match\": \"{\", \"name\": \"invalid.illegal.tag.pug\" }, { \"include\": \"source.js\" }] }] }, \"tag_text\": { \"begin\": \"(?=.)\", \"end\": \"$\", \"patterns\": [{ \"include\": \"#inline_pug\" }, { \"include\": \"#embedded_html\" }, { \"include\": \"#html_entity\" }, { \"include\": \"#interpolated_value\" }, { \"include\": \"#interpolated_error\" }] }, \"unbuffered_code\": { \"begin\": \"(-|(([a-zA-Z0-9_]+)\\\\s+=))\", \"beginCaptures\": { \"3\": { \"name\": \"variable.parameter.javascript.embedded.pug\" } }, \"comment\": \"name = function() {}\", \"end\": \"(?=\\\\])|(({\\\\s*)?$)\", \"name\": \"source.js\", \"patterns\": [{ \"include\": \"#js_brackets\" }, { \"include\": \"#babel_parens\" }, { \"include\": \"source.js\" }] } }, \"scopeName\": \"text.pug\", \"embeddedLangs\": [\"javascript\", \"css\", \"sass\", \"scss\", \"stylus\", \"coffee\", \"html\"], \"aliases\": [\"jade\"] });\nvar pug = [\n  ...javascript,\n  ...css,\n  ...sass,\n  ...scss,\n  ...stylus,\n  ...coffee,\n  ...html,\n  lang\n];\n\nexport { pug as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,OAAO,QAAQ,OAAO,YAAY,CAAC,EAAE,WAAW,wBAAwB,SAAS,uCAAuC,QAAQ,6BAA6B,GAAG,EAAE,SAAS,cAAc,WAAW,mCAAmC,OAAO,uBAAuB,QAAQ,+BAA+B,GAAG,EAAE,SAAS,aAAa,WAAW,6BAA6B,OAAO,uBAAuB,QAAQ,qCAAqC,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,WAAW,0EAA0E,SAAS,kBAAkB,QAAQ,oCAAoC,CAAC,EAAE,GAAG,EAAE,SAAS,QAAQ,OAAO,WAAW,QAAQ,gCAAgC,YAAY,CAAC,EAAE,SAAS,MAAM,QAAQ,4CAA4C,CAAC,EAAE,GAAG,EAAE,SAAS,aAAa,WAAW,0BAA0B,OAAO,uBAAuB,QAAQ,aAAa,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,mFAAmF,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,WAAW,oCAAoC,OAAO,uBAAuB,QAAQ,kBAAkB,YAAY,CAAC,EAAE,SAAS,cAAc,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,eAAe,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,0CAA0C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,WAAW,4BAA4B,OAAO,uBAAuB,QAAQ,kBAAkB,YAAY,CAAC,EAAE,SAAS,cAAc,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,eAAe,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,2BAA2B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,uBAAuB,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,2BAA2B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,uBAAuB,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,2BAA2B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,uBAAuB,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,6BAA6B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,uBAAuB,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,wCAAwC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,uBAAuB,QAAQ,kCAAkC,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,gCAAgC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,uBAAuB,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,0BAA0B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,WAAW,uBAAuB,OAAO,uBAAuB,YAAY,CAAC,EAAE,SAAS,kBAAkB,OAAO,KAAK,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,UAAU,QAAQ,0CAA0C,GAAG,EAAE,SAAS,UAAU,QAAQ,4CAA4C,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,SAAS,OAAO,QAAQ,0CAA0C,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,8rBAA8rB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2DAA2D,GAAG,KAAK,EAAE,QAAQ,8DAA8D,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,WAAW,mCAAmC,OAAO,uBAAuB,YAAY,CAAC,EAAE,SAAS,QAAQ,QAAQ,0CAA0C,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,SAAS,UAAU,OAAO,KAAK,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,SAAS,WAAW,8FAA8F,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,SAAS,OAAO,WAAW,uBAAuB,OAAO,KAAK,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,SAAS,iCAAiC,WAAW,yFAAyF,OAAO,KAAK,YAAY,CAAC,EAAE,SAAS,eAAe,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,gBAAgB,EAAE,SAAS,OAAO,OAAO,mBAAmB,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,WAAW,iCAAiC,SAAS,6EAA6E,QAAQ,uBAAuB,GAAG,cAAc,EAAE,SAAS,kCAAkC,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,WAAW,0BAA0B,OAAO,KAAK,QAAQ,yBAAyB,YAAY,CAAC,EAAE,SAAS,YAAY,OAAO,eAAe,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,SAAS,OAAO,KAAK,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,OAAO,OAAO,OAAO,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,SAAS,KAAK,QAAQ,+BAA+B,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,uBAAuB,OAAO,kBAAkB,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,gBAAgB,QAAQ,+BAA+B,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,0BAA0B,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,eAAe,OAAO,WAAW,QAAQ,QAAQ,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,yDAAyD,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,WAAW,qBAAqB,OAAO,KAAK,QAAQ,yBAAyB,YAAY,CAAC,EAAE,SAAS,IAAI,OAAO,KAAK,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,kBAAkB,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,KAAK,QAAQ,8BAA8B,YAAY,CAAC,EAAE,SAAS,sCAAsC,QAAQ,8BAA8B,GAAG,EAAE,SAAS,IAAI,OAAO,KAAK,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,+CAA+C,QAAQ,0CAA0C,GAAG,EAAE,SAAS,SAAS,QAAQ,uCAAuC,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,mBAAmB,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,OAAO,SAAS,QAAQ,cAAc,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,SAAS,+BAA+B,OAAO,sBAAsB,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,SAAS,OAAO,QAAQ,0BAA0B,CAAC,EAAE,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,SAAS,OAAO,QAAQ,0BAA0B,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,IAAI,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,sBAAsB,EAAE,SAAS,8BAA8B,QAAQ,0BAA0B,GAAG,sBAAsB,EAAE,SAAS,8BAA8B,OAAO,OAAO,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,KAAK,QAAQ,0BAA0B,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,eAAe,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,gCAAgC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,aAAa,YAAY,CAAC,EAAE,SAAS,eAAe,OAAO,OAAO,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,SAAS,0BAA0B,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,oBAAoB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,SAAS,6FAA6F,GAAG,sBAAsB,EAAE,SAAS,eAAe,YAAY,EAAE,KAAK,EAAE,QAAQ,WAAW,EAAE,GAAG,OAAO,aAAa,QAAQ,aAAa,YAAY,CAAC,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,sBAAsB,GAAG,4BAA4B,EAAE,SAAS,WAAW,OAAO,OAAO,QAAQ,uCAAuC,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,aAAa,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,SAAS,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,SAAS,UAAU,QAAQ,0BAA0B,GAAG,EAAE,SAAS,SAAS,OAAO,yCAAyC,QAAQ,mBAAmB,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,6BAA6B,OAAO,yCAAyC,QAAQ,oBAAoB,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,SAAS,wBAAwB,QAAQ,8DAA8D,GAAG,UAAU,EAAE,SAAS,YAAY,QAAQ,2DAA2D,GAAG,wBAAwB,EAAE,SAAS,oBAAoB,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,OAAO,SAAS,QAAQ,kBAAkB,YAAY,CAAC,EAAE,SAAS,qBAAqB,QAAQ,2BAA2B,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,yDAAyD,OAAO,8BAA8B,QAAQ,sCAAsC,YAAY,CAAC,EAAE,SAAS,eAAe,OAAO,WAAW,QAAQ,sCAAsC,YAAY,CAAC,EAAE,SAAS,KAAK,QAAQ,0BAA0B,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,SAAS,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,8BAA8B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,wBAAwB,OAAO,uBAAuB,QAAQ,aAAa,YAAY,CAAC,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,EAAE,GAAG,aAAa,YAAY,iBAAiB,CAAC,cAAc,OAAO,QAAQ,QAAQ,UAAU,UAAU,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;AACrue,IAAI,MAAM;AAAA,EACR,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}