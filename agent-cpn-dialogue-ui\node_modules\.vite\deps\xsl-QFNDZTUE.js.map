{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/xsl.mjs"], "sourcesContent": ["import xml from './xml.mjs';\nimport './java.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"XSL\", \"name\": \"xsl\", \"patterns\": [{ \"begin\": \"(<)(xsl)((:))(template)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"entity.name.tag.namespace.xml\" }, \"3\": { \"name\": \"entity.name.tag.xml\" }, \"4\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"5\": { \"name\": \"entity.name.tag.localname.xml\" } }, \"end\": \"(>)\", \"name\": \"meta.tag.xml.template\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.namespace.xml\" }, \"2\": { \"name\": \"entity.other.attribute-name.xml\" }, \"3\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"4\": { \"name\": \"entity.other.attribute-name.localname.xml\" } }, \"match\": \" (?:([-_a-zA-Z0-9]+)((:)))?([a-zA-Z-]+)\" }, { \"include\": \"#doublequotedString\" }, { \"include\": \"#singlequotedString\" }] }, { \"include\": \"text.xml\" }], \"repository\": { \"doublequotedString\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.xml\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.xml\" } }, \"name\": \"string.quoted.double.xml\" }, \"singlequotedString\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.xml\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.xml\" } }, \"name\": \"string.quoted.single.xml\" } }, \"scopeName\": \"text.xml.xsl\", \"embeddedLangs\": [\"xml\"] });\nvar xsl = [\n  ...xml,\n  lang\n];\n\nexport { xsl as default };\n"], "mappings": ";;;;;;;AAGA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,OAAO,QAAQ,OAAO,YAAY,CAAC,EAAE,SAAS,2BAA2B,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,OAAO,QAAQ,yBAAyB,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,SAAS,0CAA0C,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,EAAE,WAAW,WAAW,CAAC,GAAG,cAAc,EAAE,sBAAsB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,2BAA2B,GAAG,sBAAsB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,2BAA2B,EAAE,GAAG,aAAa,gBAAgB,iBAAiB,CAAC,KAAK,EAAE,CAAC;AACx4C,IAAI,MAAM;AAAA,EACR,GAAG;AAAA,EACH;AACF;", "names": []}