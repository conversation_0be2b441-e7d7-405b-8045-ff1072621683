{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/graphql.mjs"], "sourcesContent": ["import javascript from './javascript.mjs';\nimport typescript from './typescript.mjs';\nimport jsx from './jsx.mjs';\nimport tsx from './tsx.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"GraphQL\", \"fileTypes\": [\"graphql\", \"graphqls\", \"gql\", \"graphcool\"], \"name\": \"graphql\", \"patterns\": [{ \"include\": \"#graphql\" }], \"repository\": { \"graphql\": { \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-fragment-definition\" }, { \"include\": \"#graphql-directive-definition\" }, { \"include\": \"#graphql-type-interface\" }, { \"include\": \"#graphql-enum\" }, { \"include\": \"#graphql-scalar\" }, { \"include\": \"#graphql-union\" }, { \"include\": \"#graphql-schema\" }, { \"include\": \"#graphql-operation-def\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-ampersand\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.logical.graphql\" } }, \"match\": \"\\\\s*(&)\" }, \"graphql-arguments\": { \"begin\": \"\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.round.directive.graphql\" } }, \"end\": \"\\\\s*(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"meta.brace.round.directive.graphql\" } }, \"name\": \"meta.arguments.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"begin\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)(?:\\\\s*(:))\", \"beginCaptures\": { \"1\": { \"name\": \"variable.parameter.graphql\" }, \"2\": { \"name\": \"punctuation.colon.graphql\" } }, \"end\": \"(?=\\\\s*(?:(?:([_A-Za-z][_0-9A-Za-z]*)\\\\s*(:))|\\\\)))|\\\\s*(,)\", \"endCaptures\": { \"3\": { \"name\": \"punctuation.comma.graphql\" } }, \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-value\" }, { \"include\": \"#graphql-skip-newlines\" }] }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-boolean-value\": { \"captures\": { \"1\": { \"name\": \"constant.language.boolean.graphql\" } }, \"match\": \"\\\\s*\\\\b(true|false)\\\\b\" }, \"graphql-colon\": { \"captures\": { \"1\": { \"name\": \"punctuation.colon.graphql\" } }, \"match\": \"\\\\s*(:)\" }, \"graphql-comma\": { \"captures\": { \"1\": { \"name\": \"punctuation.comma.graphql\" } }, \"match\": \"\\\\s*(,)\" }, \"graphql-comment\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.graphql\" } }, \"comment\": \"need to prefix comment space with a scope else Atom's reflow cmd doesn't work\", \"match\": \"(\\\\s*)(#).*\", \"name\": \"comment.line.graphql.js\" }, { \"begin\": '(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.graphql\" } }, \"end\": '(\"\"\")', \"name\": \"comment.line.graphql.js\" }, { \"begin\": '(\")', \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.graphql\" } }, \"end\": '(\")', \"name\": \"comment.line.graphql.js\" }] }, \"graphql-description-docstring\": { \"begin\": '\"\"\"', \"end\": '\"\"\"', \"name\": \"comment.block.graphql\" }, \"graphql-description-singleline\": { \"match\": '#(?=([^\"]*\"[^\"]*\")*[^\"]*$).*$', \"name\": \"comment.line.number-sign.graphql\" }, \"graphql-directive\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*((@)\\\\s*([_A-Za-z][_0-9A-Za-z]*))\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.directive.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-arguments\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-directive-definition\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(\\\\bdirective\\\\b)\\\\s*(@[_A-Za-z][_0-9A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.directive.graphql\" }, \"2\": { \"name\": \"entity.name.function.directive.graphql\" }, \"3\": { \"name\": \"keyword.on.graphql\" }, \"4\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-variable-definitions\" }, { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(\\\\bon\\\\b)\\\\s*([_A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.on.graphql\" }, \"2\": { \"name\": \"support.type.location.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"captures\": { \"2\": { \"name\": \"support.type.location.graphql\" } }, \"match\": \"\\\\s*(\\\\|)\\\\s*([_A-Za-z]*)\" }] }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-enum\": { \"begin\": \"\\\\s*+\\\\b(enum)\\\\b\\\\s*([_A-Za-z][_0-9A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.enum.graphql\" }, \"2\": { \"name\": \"support.type.enum.graphql\" } }, \"end\": \"(?<=})\", \"name\": \"meta.enum.graphql\", \"patterns\": [{ \"begin\": \"\\\\s*({)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"name\": \"meta.type.object.graphql\", \"patterns\": [{ \"include\": \"#graphql-object-type\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-enum-value\" }, { \"include\": \"#literal-quasi-embedded\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }] }, \"graphql-enum-value\": { \"match\": \"\\\\s*(?!=\\\\b(true|false|null)\\\\b)([_A-Za-z][_0-9A-Za-z]*)\", \"name\": \"constant.character.enum.graphql\" }, \"graphql-field\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"string.unquoted.alias.graphql\" }, \"2\": { \"name\": \"punctuation.colon.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\\\\s*(:)\" }, { \"captures\": { \"1\": { \"name\": \"variable.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, { \"include\": \"#graphql-arguments\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-selection-set\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-float-value\": { \"captures\": { \"1\": { \"name\": \"constant.numeric.float.graphql\" } }, \"match\": \"\\\\s*(-?(0|[1-9][0-9]*)(\\\\.[0-9]+)?((e|E)(\\\\+|-)?[0-9]+)?)\" }, \"graphql-fragment-definition\": { \"begin\": \"\\\\s*(?:(\\\\bfragment\\\\b)\\\\s*([_A-Za-z][_0-9A-Za-z]*)?\\\\s*(?:(\\\\bon\\\\b)\\\\s*([_A-Za-z][_0-9A-Za-z]*)))\", \"captures\": { \"1\": { \"name\": \"keyword.fragment.graphql\" }, \"2\": { \"name\": \"entity.name.fragment.graphql\" }, \"3\": { \"name\": \"keyword.on.graphql\" }, \"4\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?<=})\", \"name\": \"meta.fragment.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-selection-set\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-fragment-spread\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(\\\\.\\\\.\\\\.)\\\\s*(?!\\\\bon\\\\b)([_A-Za-z][_0-9A-Za-z]*)\", \"captures\": { \"1\": { \"name\": \"keyword.operator.spread.graphql\" }, \"2\": { \"name\": \"variable.fragment.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-selection-set\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-ignore-spaces\": { \"match\": \"\\\\s*\" }, \"graphql-inline-fragment\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(\\\\.\\\\.\\\\.)\\\\s*(?:(\\\\bon\\\\b)\\\\s*([_A-Za-z][_0-9A-Za-z]*))?\", \"captures\": { \"1\": { \"name\": \"keyword.operator.spread.graphql\" }, \"2\": { \"name\": \"keyword.on.graphql\" }, \"3\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-selection-set\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-input-types\": { \"patterns\": [{ \"include\": \"#graphql-scalar-type\" }, { \"captures\": { \"1\": { \"name\": \"support.type.graphql\" }, \"2\": { \"name\": \"keyword.operator.nulltype.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)(?:\\\\s*(!))?\" }, { \"begin\": \"\\\\s*(\\\\[)\", \"captures\": { \"1\": { \"name\": \"meta.brace.square.graphql\" }, \"2\": { \"name\": \"keyword.operator.nulltype.graphql\" } }, \"end\": \"\\\\s*(\\\\])(?:\\\\s*(!))?\", \"name\": \"meta.type.list.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-input-types\" }, { \"include\": \"#graphql-comma\" }, { \"include\": \"#literal-quasi-embedded\" }] }] }, \"graphql-list-value\": { \"patterns\": [{ \"begin\": \"\\\\s*+(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.square.graphql\" } }, \"end\": \"\\\\s*(\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"meta.brace.square.graphql\" } }, \"name\": \"meta.listvalues.graphql\", \"patterns\": [{ \"include\": \"#graphql-value\" }] }] }, \"graphql-name\": { \"captures\": { \"1\": { \"name\": \"entity.name.function.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, \"graphql-null-value\": { \"captures\": { \"1\": { \"name\": \"constant.language.null.graphql\" } }, \"match\": \"\\\\s*\\\\b(null)\\\\b\" }, \"graphql-object-field\": { \"captures\": { \"1\": { \"name\": \"constant.object.key.graphql\" }, \"2\": { \"name\": \"string.unquoted.graphql\" }, \"3\": { \"name\": \"punctuation.graphql\" } }, \"match\": \"\\\\s*(([_A-Za-z][_0-9A-Za-z]*))\\\\s*(:)\" }, \"graphql-object-value\": { \"patterns\": [{ \"begin\": \"\\\\s*+({)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.curly.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"meta.brace.curly.graphql\" } }, \"name\": \"meta.objectvalues.graphql\", \"patterns\": [{ \"include\": \"#graphql-object-field\" }, { \"include\": \"#graphql-value\" }] }] }, \"graphql-operation-def\": { \"patterns\": [{ \"include\": \"#graphql-query-mutation\" }, { \"include\": \"#graphql-name\" }, { \"include\": \"#graphql-variable-definitions\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-selection-set\" }] }, \"graphql-query-mutation\": { \"captures\": { \"1\": { \"name\": \"keyword.operation.graphql\" } }, \"match\": \"\\\\s*\\\\b(query|mutation)\\\\b\" }, \"graphql-scalar\": { \"captures\": { \"1\": { \"name\": \"keyword.scalar.graphql\" }, \"2\": { \"name\": \"entity.scalar.graphql\" } }, \"match\": \"\\\\s*\\\\b(scalar)\\\\b\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, \"graphql-scalar-type\": { \"captures\": { \"1\": { \"name\": \"support.type.builtin.graphql\" }, \"2\": { \"name\": \"keyword.operator.nulltype.graphql\" } }, \"match\": \"\\\\s*\\\\b(Int|Float|String|Boolean|ID)\\\\b(?:\\\\s*(!))?\" }, \"graphql-schema\": { \"begin\": \"\\\\s*\\\\b(schema)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.schema.graphql\" } }, \"end\": \"(?<=})\", \"patterns\": [{ \"begin\": \"\\\\s*({)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"patterns\": [{ \"begin\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)(?=\\\\s*\\\\(|:)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.arguments.graphql\" } }, \"end\": \"(?=\\\\s*(([_A-Za-z][_0-9A-Za-z]*)\\\\s*(\\\\(|:)|(})))|\\\\s*(,)\", \"endCaptures\": { \"5\": { \"name\": \"punctuation.comma.graphql\" } }, \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"support.type.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-colon\" }, { \"include\": \"#graphql-skip-newlines\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-skip-newlines\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-selection-set\": { \"begin\": \"\\\\s*({)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"name\": \"meta.selectionset.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-field\" }, { \"include\": \"#graphql-fragment-spread\" }, { \"include\": \"#graphql-inline-fragment\" }, { \"include\": \"#graphql-comma\" }, { \"include\": \"#native-interpolation\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-skip-newlines\": { \"match\": \"\\\\s*\\n\" }, \"graphql-string-content\": { \"patterns\": [{ \"match\": `\\\\\\\\[/'\"\\\\\\\\nrtbf]`, \"name\": \"constant.character.escape.graphql\" }, { \"match\": \"\\\\\\\\u([0-9a-fA-F]{4})\", \"name\": \"constant.character.escape.graphql\" }] }, \"graphql-string-value\": { \"begin\": '\\\\s*+((\"))', \"beginCaptures\": { \"1\": { \"name\": \"string.quoted.double.graphql\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.graphql\" } }, \"contentName\": \"string.quoted.double.graphql\", \"end\": '\\\\s*+(?:((\"))|(\\n))', \"endCaptures\": { \"1\": { \"name\": \"string.quoted.double.graphql\" }, \"2\": { \"name\": \"punctuation.definition.string.end.graphql\" }, \"3\": { \"name\": \"invalid.illegal.newline.graphql\" } }, \"patterns\": [{ \"include\": \"#graphql-string-content\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-type-definition\": { \"begin\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)(?=\\\\s*\\\\(|:)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.graphql\" } }, \"comment\": \"key (optionalArgs): Type\", \"end\": \"(?=\\\\s*(([_A-Za-z][_0-9A-Za-z]*)\\\\s*(\\\\(|:)|(})))|\\\\s*(,)\", \"endCaptures\": { \"5\": { \"name\": \"punctuation.comma.graphql\" } }, \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-variable-definitions\" }, { \"include\": \"#graphql-type-object\" }, { \"include\": \"#graphql-colon\" }, { \"include\": \"#graphql-input-types\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-type-interface\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*\\\\b(?:(extends?)?\\\\b\\\\s*\\\\b(type)|(interface)|(input))\\\\b\\\\s*([_A-Za-z][_0-9A-Za-z]*)?\", \"captures\": { \"1\": { \"name\": \"keyword.type.graphql\" }, \"2\": { \"name\": \"keyword.type.graphql\" }, \"3\": { \"name\": \"keyword.interface.graphql\" }, \"4\": { \"name\": \"keyword.input.graphql\" }, \"5\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"name\": \"meta.type.interface.graphql\", \"patterns\": [{ \"begin\": \"\\\\s*\\\\b(implements)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.implements.graphql\" } }, \"end\": \"\\\\s*(?={)\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"support.type.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-ampersand\" }, { \"include\": \"#graphql-comma\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-type-object\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-ignore-spaces\" }] }, \"graphql-type-object\": { \"begin\": \"\\\\s*({)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"name\": \"meta.type.object.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-object-type\" }, { \"include\": \"#graphql-type-definition\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-union\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*\\\\b(union)\\\\b\\\\s*([_A-Za-z][_0-9A-Za-z]*)\", \"captures\": { \"1\": { \"name\": \"keyword.union.graphql\" }, \"2\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(=)\\\\s*([_A-Za-z][_0-9A-Za-z]*)\", \"captures\": { \"1\": { \"name\": \"punctuation.assignment.graphql\" }, \"2\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.or.graphql\" }, \"2\": { \"name\": \"support.type.graphql\" } }, \"match\": \"\\\\s*(\\\\|)\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-union-mark\": { \"captures\": { \"1\": { \"name\": \"punctuation.union.graphql\" } }, \"match\": \"\\\\s*(\\\\|)\" }, \"graphql-value\": { \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-variable-name\" }, { \"include\": \"#graphql-float-value\" }, { \"include\": \"#graphql-string-value\" }, { \"include\": \"#graphql-boolean-value\" }, { \"include\": \"#graphql-null-value\" }, { \"include\": \"#graphql-enum-value\" }, { \"include\": \"#graphql-list-value\" }, { \"include\": \"#graphql-object-value\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-variable-assignment\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s(=)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.assignment.graphql\" } }, \"end\": \"(?=[\\n,)])\", \"patterns\": [{ \"include\": \"#graphql-value\" }] }, \"graphql-variable-definition\": { \"begin\": \"\\\\s*(\\\\$?[_A-Za-z][_0-9A-Za-z]*)(?=\\\\s*\\\\(|:)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.parameter.graphql\" } }, \"comment\": \"variable: type = value,.... which may be a list\", \"end\": \"(?=\\\\s*((\\\\$?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(\\\\(|:)|(}|\\\\))))|\\\\s*(,)\", \"endCaptures\": { \"5\": { \"name\": \"punctuation.comma.graphql\" } }, \"name\": \"meta.variables.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-colon\" }, { \"include\": \"#graphql-input-types\" }, { \"include\": \"#graphql-variable-assignment\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-variable-definitions\": { \"begin\": \"\\\\s*(\\\\()\", \"captures\": { \"1\": { \"name\": \"meta.brace.round.graphql\" } }, \"end\": \"\\\\s*(\\\\))\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-variable-definition\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-variable-name\": { \"captures\": { \"1\": { \"name\": \"variable.graphql\" } }, \"match\": \"\\\\s*(\\\\$[_A-Za-z][_0-9A-Za-z]*)\" }, \"native-interpolation\": { \"begin\": \"\\\\s*(\\\\${)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.substitution.begin\" } }, \"end\": \"(})\", \"endCaptures\": { \"1\": { \"name\": \"keyword.other.substitution.end\" } }, \"name\": \"native.interpolation\", \"patterns\": [{ \"include\": \"source.js\" }, { \"include\": \"source.ts\" }, { \"include\": \"source.js.jsx\" }, { \"include\": \"source.tsx\" }] } }, \"scopeName\": \"source.graphql\", \"embeddedLangs\": [\"javascript\", \"typescript\", \"jsx\", \"tsx\"], \"aliases\": [\"gql\"] });\nvar graphql = [\n  ...javascript,\n  ...typescript,\n  ...jsx,\n  ...tsx,\n  lang\n];\n\nexport { graphql as default };\n"], "mappings": ";;;;;;;;;;;;;;AAKA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,WAAW,aAAa,CAAC,WAAW,YAAY,OAAO,WAAW,GAAG,QAAQ,WAAW,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,GAAG,cAAc,EAAE,WAAW,EAAE,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,UAAU,GAAG,qBAAqB,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,SAAS,2CAA2C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,+DAA+D,eAAe,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,yBAAyB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,SAAS,yBAAyB,GAAG,iBAAiB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,SAAS,UAAU,GAAG,iBAAiB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,SAAS,UAAU,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,WAAW,iFAAiF,SAAS,eAAe,QAAQ,0BAA0B,GAAG,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,SAAS,QAAQ,0BAA0B,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,OAAO,QAAQ,0BAA0B,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,OAAO,OAAO,OAAO,QAAQ,wBAAwB,GAAG,kCAAkC,EAAE,SAAS,iCAAiC,QAAQ,mCAAmC,GAAG,qBAAqB,EAAE,uBAAuB,GAAG,SAAS,yCAAyC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,gCAAgC,EAAE,uBAAuB,GAAG,SAAS,sDAAsD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,gCAAgC,GAAG,EAAE,uBAAuB,GAAG,SAAS,kCAAkC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,SAAS,4BAA4B,CAAC,EAAE,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,iDAAiD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,UAAU,QAAQ,qBAAqB,YAAY,CAAC,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,sBAAsB,EAAE,SAAS,4DAA4D,QAAQ,kCAAkC,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,SAAS,sCAAsC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mBAAmB,EAAE,GAAG,SAAS,+BAA+B,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,4DAA4D,GAAG,+BAA+B,EAAE,SAAS,uGAAuG,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,UAAU,QAAQ,yBAAyB,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,2BAA2B,EAAE,uBAAuB,GAAG,SAAS,2DAA2D,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,OAAO,GAAG,2BAA2B,EAAE,uBAAuB,GAAG,SAAS,kEAAkE,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,SAAS,2CAA2C,GAAG,EAAE,SAAS,aAAa,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,yBAAyB,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS,+BAA+B,GAAG,sBAAsB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,mBAAmB,GAAG,wBAAwB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,SAAS,wCAAwC,GAAG,wBAAwB,EAAE,YAAY,CAAC,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,CAAC,EAAE,GAAG,yBAAyB,EAAE,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,0BAA0B,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,SAAS,6BAA6B,GAAG,kBAAkB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,SAAS,iDAAiD,GAAG,uBAAuB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,SAAS,sDAAsD,GAAG,kBAAkB,EAAE,SAAS,sBAAsB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,UAAU,YAAY,CAAC,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,6CAA6C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,6DAA6D,eAAe,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,SAAS,+BAA+B,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,SAAS,GAAG,0BAA0B,EAAE,YAAY,CAAC,EAAE,SAAS,sBAAsB,QAAQ,oCAAoC,GAAG,EAAE,SAAS,yBAAyB,QAAQ,oCAAoC,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,eAAe,gCAAgC,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,2BAA2B,EAAE,SAAS,6CAA6C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mBAAmB,EAAE,GAAG,WAAW,4BAA4B,OAAO,6DAA6D,eAAe,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,0BAA0B,EAAE,uBAAuB,GAAG,SAAS,8FAA8F,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,SAAS,QAAQ,+BAA+B,YAAY,CAAC,EAAE,SAAS,8BAA8B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,aAAa,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,SAAS,+BAA+B,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,iBAAiB,EAAE,uBAAuB,GAAG,SAAS,iDAAiD,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,SAAS,YAAY,CAAC,EAAE,uBAAuB,GAAG,SAAS,uCAAuC,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,SAAS,wCAAwC,CAAC,EAAE,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,SAAS,YAAY,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,+BAA+B,EAAE,uBAAuB,GAAG,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,OAAO,cAAc,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,+BAA+B,EAAE,SAAS,iDAAiD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,WAAW,mDAAmD,OAAO,qEAAqE,eAAe,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,gCAAgC,EAAE,SAAS,aAAa,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,OAAO,aAAa,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,yBAAyB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mBAAmB,EAAE,GAAG,SAAS,kCAAkC,GAAG,wBAAwB,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,EAAE,GAAG,aAAa,kBAAkB,iBAAiB,CAAC,cAAc,cAAc,OAAO,KAAK,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;AACrgmB,IAAI,UAAU;AAAA,EACZ,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}