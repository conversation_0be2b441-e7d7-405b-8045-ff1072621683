{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-shape/src/arc.js"], "sourcesContent": ["import constant from \"./constant.js\";\nimport {abs, acos, asin, atan2, cos, epsilon, halfPi, max, min, pi, sin, sqrt, tau} from \"./math.js\";\nimport {withPath} from \"./path.js\";\n\nfunction arcInnerRadius(d) {\n  return d.innerRadius;\n}\n\nfunction arcOuterRadius(d) {\n  return d.outerRadius;\n}\n\nfunction arcStartAngle(d) {\n  return d.startAngle;\n}\n\nfunction arcEndAngle(d) {\n  return d.endAngle;\n}\n\nfunction arcPadAngle(d) {\n  return d && d.padAngle; // Note: optional!\n}\n\nfunction intersect(x0, y0, x1, y1, x2, y2, x3, y3) {\n  var x10 = x1 - x0, y10 = y1 - y0,\n      x32 = x3 - x2, y32 = y3 - y2,\n      t = y32 * x10 - x32 * y10;\n  if (t * t < epsilon) return;\n  t = (x32 * (y0 - y2) - y32 * (x0 - x2)) / t;\n  return [x0 + t * x10, y0 + t * y10];\n}\n\n// Compute perpendicular offset line of length rc.\n// http://mathworld.wolfram.com/Circle-LineIntersection.html\nfunction cornerTangents(x0, y0, x1, y1, r1, rc, cw) {\n  var x01 = x0 - x1,\n      y01 = y0 - y1,\n      lo = (cw ? rc : -rc) / sqrt(x01 * x01 + y01 * y01),\n      ox = lo * y01,\n      oy = -lo * x01,\n      x11 = x0 + ox,\n      y11 = y0 + oy,\n      x10 = x1 + ox,\n      y10 = y1 + oy,\n      x00 = (x11 + x10) / 2,\n      y00 = (y11 + y10) / 2,\n      dx = x10 - x11,\n      dy = y10 - y11,\n      d2 = dx * dx + dy * dy,\n      r = r1 - rc,\n      D = x11 * y10 - x10 * y11,\n      d = (dy < 0 ? -1 : 1) * sqrt(max(0, r * r * d2 - D * D)),\n      cx0 = (D * dy - dx * d) / d2,\n      cy0 = (-D * dx - dy * d) / d2,\n      cx1 = (D * dy + dx * d) / d2,\n      cy1 = (-D * dx + dy * d) / d2,\n      dx0 = cx0 - x00,\n      dy0 = cy0 - y00,\n      dx1 = cx1 - x00,\n      dy1 = cy1 - y00;\n\n  // Pick the closer of the two intersection points.\n  // TODO Is there a faster way to determine which intersection to use?\n  if (dx0 * dx0 + dy0 * dy0 > dx1 * dx1 + dy1 * dy1) cx0 = cx1, cy0 = cy1;\n\n  return {\n    cx: cx0,\n    cy: cy0,\n    x01: -ox,\n    y01: -oy,\n    x11: cx0 * (r1 / r - 1),\n    y11: cy0 * (r1 / r - 1)\n  };\n}\n\nexport default function() {\n  var innerRadius = arcInnerRadius,\n      outerRadius = arcOuterRadius,\n      cornerRadius = constant(0),\n      padRadius = null,\n      startAngle = arcStartAngle,\n      endAngle = arcEndAngle,\n      padAngle = arcPadAngle,\n      context = null,\n      path = withPath(arc);\n\n  function arc() {\n    var buffer,\n        r,\n        r0 = +innerRadius.apply(this, arguments),\n        r1 = +outerRadius.apply(this, arguments),\n        a0 = startAngle.apply(this, arguments) - halfPi,\n        a1 = endAngle.apply(this, arguments) - halfPi,\n        da = abs(a1 - a0),\n        cw = a1 > a0;\n\n    if (!context) context = buffer = path();\n\n    // Ensure that the outer radius is always larger than the inner radius.\n    if (r1 < r0) r = r1, r1 = r0, r0 = r;\n\n    // Is it a point?\n    if (!(r1 > epsilon)) context.moveTo(0, 0);\n\n    // Or is it a circle or annulus?\n    else if (da > tau - epsilon) {\n      context.moveTo(r1 * cos(a0), r1 * sin(a0));\n      context.arc(0, 0, r1, a0, a1, !cw);\n      if (r0 > epsilon) {\n        context.moveTo(r0 * cos(a1), r0 * sin(a1));\n        context.arc(0, 0, r0, a1, a0, cw);\n      }\n    }\n\n    // Or is it a circular or annular sector?\n    else {\n      var a01 = a0,\n          a11 = a1,\n          a00 = a0,\n          a10 = a1,\n          da0 = da,\n          da1 = da,\n          ap = padAngle.apply(this, arguments) / 2,\n          rp = (ap > epsilon) && (padRadius ? +padRadius.apply(this, arguments) : sqrt(r0 * r0 + r1 * r1)),\n          rc = min(abs(r1 - r0) / 2, +cornerRadius.apply(this, arguments)),\n          rc0 = rc,\n          rc1 = rc,\n          t0,\n          t1;\n\n      // Apply padding? Note that since r1 ≥ r0, da1 ≥ da0.\n      if (rp > epsilon) {\n        var p0 = asin(rp / r0 * sin(ap)),\n            p1 = asin(rp / r1 * sin(ap));\n        if ((da0 -= p0 * 2) > epsilon) p0 *= (cw ? 1 : -1), a00 += p0, a10 -= p0;\n        else da0 = 0, a00 = a10 = (a0 + a1) / 2;\n        if ((da1 -= p1 * 2) > epsilon) p1 *= (cw ? 1 : -1), a01 += p1, a11 -= p1;\n        else da1 = 0, a01 = a11 = (a0 + a1) / 2;\n      }\n\n      var x01 = r1 * cos(a01),\n          y01 = r1 * sin(a01),\n          x10 = r0 * cos(a10),\n          y10 = r0 * sin(a10);\n\n      // Apply rounded corners?\n      if (rc > epsilon) {\n        var x11 = r1 * cos(a11),\n            y11 = r1 * sin(a11),\n            x00 = r0 * cos(a00),\n            y00 = r0 * sin(a00),\n            oc;\n\n        // Restrict the corner radius according to the sector angle. If this\n        // intersection fails, it’s probably because the arc is too small, so\n        // disable the corner radius entirely.\n        if (da < pi) {\n          if (oc = intersect(x01, y01, x00, y00, x11, y11, x10, y10)) {\n            var ax = x01 - oc[0],\n                ay = y01 - oc[1],\n                bx = x11 - oc[0],\n                by = y11 - oc[1],\n                kc = 1 / sin(acos((ax * bx + ay * by) / (sqrt(ax * ax + ay * ay) * sqrt(bx * bx + by * by))) / 2),\n                lc = sqrt(oc[0] * oc[0] + oc[1] * oc[1]);\n            rc0 = min(rc, (r0 - lc) / (kc - 1));\n            rc1 = min(rc, (r1 - lc) / (kc + 1));\n          } else {\n            rc0 = rc1 = 0;\n          }\n        }\n      }\n\n      // Is the sector collapsed to a line?\n      if (!(da1 > epsilon)) context.moveTo(x01, y01);\n\n      // Does the sector’s outer ring have rounded corners?\n      else if (rc1 > epsilon) {\n        t0 = cornerTangents(x00, y00, x01, y01, r1, rc1, cw);\n        t1 = cornerTangents(x11, y11, x10, y10, r1, rc1, cw);\n\n        context.moveTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc1 < rc) context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r1, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), !cw);\n          context.arc(t1.cx, t1.cy, rc1, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the outer ring just a circular arc?\n      else context.moveTo(x01, y01), context.arc(0, 0, r1, a01, a11, !cw);\n\n      // Is there no inner ring, and it’s a circular sector?\n      // Or perhaps it’s an annular sector collapsed due to padding?\n      if (!(r0 > epsilon) || !(da0 > epsilon)) context.lineTo(x10, y10);\n\n      // Does the sector’s inner ring (or point) have rounded corners?\n      else if (rc0 > epsilon) {\n        t0 = cornerTangents(x10, y10, x11, y11, r0, -rc0, cw);\n        t1 = cornerTangents(x01, y01, x00, y00, r0, -rc0, cw);\n\n        context.lineTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc0 < rc) context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r0, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), cw);\n          context.arc(t1.cx, t1.cy, rc0, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the inner ring just a circular arc?\n      else context.arc(0, 0, r0, a10, a00, cw);\n    }\n\n    context.closePath();\n\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  arc.centroid = function() {\n    var r = (+innerRadius.apply(this, arguments) + +outerRadius.apply(this, arguments)) / 2,\n        a = (+startAngle.apply(this, arguments) + +endAngle.apply(this, arguments)) / 2 - pi / 2;\n    return [cos(a) * r, sin(a) * r];\n  };\n\n  arc.innerRadius = function(_) {\n    return arguments.length ? (innerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : innerRadius;\n  };\n\n  arc.outerRadius = function(_) {\n    return arguments.length ? (outerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : outerRadius;\n  };\n\n  arc.cornerRadius = function(_) {\n    return arguments.length ? (cornerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : cornerRadius;\n  };\n\n  arc.padRadius = function(_) {\n    return arguments.length ? (padRadius = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), arc) : padRadius;\n  };\n\n  arc.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : startAngle;\n  };\n\n  arc.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : endAngle;\n  };\n\n  arc.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : padAngle;\n  };\n\n  arc.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), arc) : context;\n  };\n\n  return arc;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAIA,SAAS,eAAe,GAAG;AACzB,SAAO,EAAE;AACX;AAEA,SAAS,eAAe,GAAG;AACzB,SAAO,EAAE;AACX;AAEA,SAAS,cAAc,GAAG;AACxB,SAAO,EAAE;AACX;AAEA,SAAS,YAAY,GAAG;AACtB,SAAO,EAAE;AACX;AAEA,SAAS,YAAY,GAAG;AACtB,SAAO,KAAK,EAAE;AAChB;AAEA,SAAS,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACjD,MAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAC1B,MAAM,KAAK,IAAI,MAAM,KAAK,IAC1B,IAAI,MAAM,MAAM,MAAM;AAC1B,MAAI,IAAI,IAAI;AAAS;AACrB,OAAK,OAAO,KAAK,MAAM,OAAO,KAAK,OAAO;AAC1C,SAAO,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG;AACpC;AAIA,SAAS,eAAe,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAClD,MAAI,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,KAAK,CAAC,MAAM,KAAK,MAAM,MAAM,MAAM,GAAG,GACjD,KAAK,KAAK,KACV,KAAK,CAAC,KAAK,KACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,OAAO,MAAM,OAAO,GACpB,OAAO,MAAM,OAAO,GACpB,KAAK,MAAM,KACX,KAAK,MAAM,KACX,KAAK,KAAK,KAAK,KAAK,IACpB,IAAI,KAAK,IACT,IAAI,MAAM,MAAM,MAAM,KACtB,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,GACvD,OAAO,IAAI,KAAK,KAAK,KAAK,IAC1B,OAAO,CAAC,IAAI,KAAK,KAAK,KAAK,IAC3B,OAAO,IAAI,KAAK,KAAK,KAAK,IAC1B,OAAO,CAAC,IAAI,KAAK,KAAK,KAAK,IAC3B,MAAM,MAAM,KACZ,MAAM,MAAM,KACZ,MAAM,MAAM,KACZ,MAAM,MAAM;AAIhB,MAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AAAK,UAAM,KAAK,MAAM;AAEpE,SAAO;IACL,IAAI;IACJ,IAAI;IACJ,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,OAAO,KAAK,IAAI;IACrB,KAAK,OAAO,KAAK,IAAI;EACzB;AACA;AAEe,SAAA,QAAW;AACxB,MAAI,cAAc,gBACd,cAAc,gBACd,eAAe,SAAS,CAAC,GACzB,YAAY,MACZ,aAAa,eACb,WAAW,aACX,WAAW,aACX,UAAU,MACV,OAAO,SAAS,GAAG;AAEvB,WAAS,MAAM;AACb,QAAI,QACA,GACA,KAAK,CAAC,YAAY,MAAM,MAAM,SAAS,GACvC,KAAK,CAAC,YAAY,MAAM,MAAM,SAAS,GACvC,KAAK,WAAW,MAAM,MAAM,SAAS,IAAI,QACzC,KAAK,SAAS,MAAM,MAAM,SAAS,IAAI,QACvC,KAAK,MAAI,KAAK,EAAE,GAChB,KAAK,KAAK;AAEd,QAAI,CAAC;AAAS,gBAAU,SAAS,KAAI;AAGrC,QAAI,KAAK;AAAI,UAAI,IAAI,KAAK,IAAI,KAAK;AAGnC,QAAI,EAAE,KAAK;AAAU,cAAQ,OAAO,GAAG,CAAC;aAG/B,KAAK,MAAM,SAAS;AAC3B,cAAQ,OAAO,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AACzC,cAAQ,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,EAAE;AACjC,UAAI,KAAK,SAAS;AAChB,gBAAQ,OAAO,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AACzC,gBAAQ,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE;MAClC;IACF,OAGK;AACH,UAAI,MAAM,IACN,MAAM,IACN,MAAM,IACN,MAAM,IACN,MAAM,IACN,MAAM,IACN,KAAK,SAAS,MAAM,MAAM,SAAS,IAAI,GACvC,KAAM,KAAK,YAAa,YAAY,CAAC,UAAU,MAAM,MAAM,SAAS,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE,IAC9F,KAAK,IAAI,MAAI,KAAK,EAAE,IAAI,GAAG,CAAC,aAAa,MAAM,MAAM,SAAS,CAAC,GAC/D,MAAM,IACN,MAAM,IACN,IACA;AAGJ,UAAI,KAAK,SAAS;AAChB,YAAI,KAAK,KAAK,KAAK,KAAK,IAAI,EAAE,CAAC,GAC3B,KAAK,KAAK,KAAK,KAAK,IAAI,EAAE,CAAC;AAC/B,aAAK,OAAO,KAAK,KAAK;AAAS,gBAAO,KAAK,IAAI,IAAK,OAAO,IAAI,OAAO;;AACjE,gBAAM,GAAG,MAAM,OAAO,KAAK,MAAM;AACtC,aAAK,OAAO,KAAK,KAAK;AAAS,gBAAO,KAAK,IAAI,IAAK,OAAO,IAAI,OAAO;;AACjE,gBAAM,GAAG,MAAM,OAAO,KAAK,MAAM;MACxC;AAEA,UAAI,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG;AAGtB,UAAI,KAAK,SAAS;AAChB,YAAI,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB;AAKJ,YAAI,KAAK,IAAI;AACX,cAAI,KAAK,UAAU,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG;AAC1D,gBAAI,KAAK,MAAM,GAAG,CAAC,GACf,KAAK,MAAM,GAAG,CAAC,GACf,KAAK,MAAM,GAAG,CAAC,GACf,KAAK,MAAM,GAAG,CAAC,GACf,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE,EAAE,IAAI,CAAC,GAChG,KAAK,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;AAC3C,kBAAM,IAAI,KAAK,KAAK,OAAO,KAAK,EAAE;AAClC,kBAAM,IAAI,KAAK,KAAK,OAAO,KAAK,EAAE;UACpC,OAAO;AACL,kBAAM,MAAM;UACd;QACF;MACF;AAGA,UAAI,EAAE,MAAM;AAAU,gBAAQ,OAAO,KAAK,GAAG;eAGpC,MAAM,SAAS;AACtB,aAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,EAAE;AACnD,aAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,EAAE;AAEnD,gBAAQ,OAAO,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG;AAG7C,YAAI,MAAM;AAAI,kBAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;aAGzF;AACH,kBAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;AAChF,kBAAQ,IAAI,GAAG,GAAG,IAAI,MAAM,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;AACvG,kBAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;QAClF;MACF;AAGK,gBAAQ,OAAO,KAAK,GAAG,GAAG,QAAQ,IAAI,GAAG,GAAG,IAAI,KAAK,KAAK,CAAC,EAAE;AAIlE,UAAI,EAAE,KAAK,YAAY,EAAE,MAAM;AAAU,gBAAQ,OAAO,KAAK,GAAG;eAGvD,MAAM,SAAS;AACtB,aAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AACpD,aAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AAEpD,gBAAQ,OAAO,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG;AAG7C,YAAI,MAAM;AAAI,kBAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;aAGzF;AACH,kBAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;AAChF,kBAAQ,IAAI,GAAG,GAAG,IAAI,MAAM,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,EAAE;AACtG,kBAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;QAClF;MACF;AAGK,gBAAQ,IAAI,GAAG,GAAG,IAAI,KAAK,KAAK,EAAE;IACzC;AAEA,YAAQ,UAAS;AAEjB,QAAI;AAAQ,aAAO,UAAU,MAAM,SAAS,MAAM;EACpD;AAEA,MAAI,WAAW,WAAW;AACxB,QAAI,KAAK,CAAC,YAAY,MAAM,MAAM,SAAS,IAAI,CAAC,YAAY,MAAM,MAAM,SAAS,KAAK,GAClF,KAAK,CAAC,WAAW,MAAM,MAAM,SAAS,IAAI,CAAC,SAAS,MAAM,MAAM,SAAS,KAAK,IAAI,KAAK;AAC3F,WAAO,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;EAChC;AAEA,MAAI,cAAc,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,cAAc,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAG,OAAO;EAC9F;AAEA,MAAI,cAAc,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,cAAc,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAG,OAAO;EAC9F;AAEA,MAAI,eAAe,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,eAAe,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAG,OAAO;EAC/F;AAEA,MAAI,YAAY,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,YAAY,KAAK,OAAO,OAAO,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAG,OAAO;EAC/G;AAEA,MAAI,aAAa,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,aAAa,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAG,OAAO;EAC7F;AAEA,MAAI,WAAW,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAG,OAAO;EAC3F;AAEA,MAAI,WAAW,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAG,OAAO;EAC3F;AAEA,MAAI,UAAU,SAAS,GAAG;AACxB,WAAO,UAAU,UAAW,UAAU,KAAK,OAAO,OAAO,GAAI,OAAO;EACtE;AAEA,SAAO;AACT;", "names": []}