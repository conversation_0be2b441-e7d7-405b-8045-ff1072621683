import {
  toml
} from "./chunk-CSP2ELNL.js";
import {
  pug
} from "./chunk-MWMEIFL4.js";
import {
  jsonc
} from "./chunk-LGR2JCSJ.js";
import {
  json5
} from "./chunk-RICDBS3V.js";
import {
  graphql
} from "./chunk-7IIZ3PFX.js";
import {
  jsx
} from "./chunk-5LQU3VR6.js";
import {
  yaml
} from "./chunk-5WCIUZ4S.js";
import {
  markdown
} from "./chunk-52XQNEDG.js";
import {
  sass
} from "./chunk-7PKVM2LP.js";
import {
  less
} from "./chunk-YPP66BBD.js";
import {
  tsx
} from "./chunk-755OXIF2.js";
import {
  json
} from "./chunk-O62JQS37.js";
import {
  typescript
} from "./chunk-YIYGYI63.js";
import {
  stylus
} from "./chunk-JNGEFRBD.js";
import {
  html
} from "./chunk-2HN7PBVQ.js";
import {
  scss
} from "./chunk-53NCHPBP.js";
import {
  css
} from "./chunk-2DKB27CY.js";
import {
  javascript
} from "./chunk-B3KVBTAC.js";

// node_modules/.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/vue.mjs
var lang$4 = Object.freeze({ "fileTypes": [], "injectTo": ["text.html.markdown"], "injectionSelector": "L:text.html.markdown", "name": "markdown-vue", "patterns": [{ "include": "#vue-code-block" }], "repository": { "vue-code-block": { "begin": "(^|\\G)(\\s*)(`{3,}|~{3,})\\s*(?i:(vue)((\\s+|:|,|\\{|\\?)[^`~]*)?$)", "beginCaptures": { "3": { "name": "punctuation.definition.markdown" }, "4": { "name": "fenced_code.block.language.markdown" }, "5": { "name": "fenced_code.block.language.attributes.markdown" } }, "end": "(^|\\G)(\\2|\\s{0,3})(\\3)\\s*$", "endCaptures": { "3": { "name": "punctuation.definition.markdown" } }, "name": "markup.fenced_code.block.markdown", "patterns": [{ "include": "source.vue" }] } }, "scopeName": "markdown.vue.codeblock" });
var markdown_vue = [
  lang$4
];
var lang$3 = Object.freeze({ "fileTypes": [], "injectTo": ["source.vue", "text.html.markdown", "text.html.derivative", "text.pug"], "injectionSelector": "L:meta.tag -meta.attribute -entity.name.tag.pug -attribute_value -source.tsx -source.js.jsx, L:meta.element -meta.attribute", "name": "vue-directives", "patterns": [{ "include": "source.vue#vue-directives" }], "scopeName": "vue.directives" });
var vue_directives = [
  lang$3
];
var lang$2 = Object.freeze({ "fileTypes": [], "injectTo": ["source.vue", "text.html.markdown", "text.html.derivative", "text.pug"], "injectionSelector": "L:text.pug -comment -string.comment, L:text.html.derivative -comment.block, L:text.html.markdown -comment.block", "name": "vue-interpolations", "patterns": [{ "include": "source.vue#vue-interpolations" }], "scopeName": "vue.interpolations" });
var vue_interpolations = [
  lang$2
];
var lang$1 = Object.freeze({ "fileTypes": [], "injectTo": ["source.vue"], "injectionSelector": "L:source.css -comment, L:source.postcss -comment, L:source.sass -comment, L:source.stylus -comment", "name": "vue-sfc-style-variable-injection", "patterns": [{ "include": "#vue-sfc-style-variable-injection" }], "repository": { "vue-sfc-style-variable-injection": { "begin": "\\b(v-bind)\\s*\\(", "beginCaptures": { "1": { "name": "entity.name.function" } }, "end": "\\)", "name": "vue.sfc.style.variable.injection.v-bind", "patterns": [{ "begin": `('|")`, "beginCaptures": { "1": { "name": "punctuation.definition.tag.begin.html" } }, "end": "(\\1)", "endCaptures": { "1": { "name": "punctuation.definition.tag.end.html" } }, "name": "source.ts.embedded.html.vue", "patterns": [{ "include": "source.js" }] }, { "include": "source.js" }] } }, "scopeName": "vue.sfc.style.variable.injection", "embeddedLangs": ["javascript"] });
var vue_sfc_style_variable_injection = [
  ...javascript,
  lang$1
];
var lang = Object.freeze({ "displayName": "Vue", "name": "vue", "patterns": [{ "include": "text.html.basic#comment" }, { "include": "#self-closing-tag" }, { "begin": "(<)", "beginCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" } }, "end": "(>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.end.html.vue" } }, "patterns": [{ "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)md\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "text.html.markdown", "patterns": [{ "include": "text.html.markdown" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)html\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "text.html.derivative", "patterns": [{ "include": "#html-stuff" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)pug\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "text.pug", "patterns": [{ "include": "text.pug" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)stylus\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.stylus", "patterns": [{ "include": "source.stylus" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)postcss\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.postcss", "patterns": [{ "include": "source.postcss" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)sass\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.sass", "patterns": [{ "include": "source.sass" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)css\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.css", "patterns": [{ "include": "source.css" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)scss\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.css.scss", "patterns": [{ "include": "source.css.scss" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)less\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.css.less", "patterns": [{ "include": "source.css.less" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)js\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.js", "patterns": [{ "include": "source.js" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)ts\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.ts", "patterns": [{ "include": "source.ts" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)jsx\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.js.jsx", "patterns": [{ "include": "source.js.jsx" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)tsx\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.tsx", "patterns": [{ "include": "source.tsx" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)json\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.json", "patterns": [{ "include": "source.json" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)jsonc\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.json.comments", "patterns": [{ "include": "source.json.comments" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)json5\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.json5", "patterns": [{ "include": "source.json5" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)yaml\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.yaml", "patterns": [{ "include": "source.yaml" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)toml\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.toml", "patterns": [{ "include": "source.toml" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)(gql|graphql)\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.graphql", "patterns": [{ "include": "source.graphql" }] }] }, { "begin": `([a-zA-Z0-9:-]+)\\b(?=[^>]*\\blang\\s*=\\s*(['"]?)vue\\b\\2)`, "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "source.vue", "patterns": [{ "include": "source.vue" }] }] }, { "begin": "(template)\\b", "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/template\\b)", "name": "text.html.derivative", "patterns": [{ "include": "#html-stuff" }] }] }, { "begin": "(script)\\b", "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/script\\b)", "name": "source.js", "patterns": [{ "include": "source.js" }] }] }, { "begin": "(style)\\b", "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/style\\b)", "name": "source.css", "patterns": [{ "include": "source.css" }] }] }, { "begin": "([a-zA-Z0-9:-]+)", "beginCaptures": { "1": { "name": "entity.name.tag.$1.html.vue" } }, "end": "(</)(\\1)\\s*(?=>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "patterns": [{ "include": "#tag-stuff" }, { "begin": "(?<=>)", "end": "(?=<\\/)", "name": "text" }] }] }], "repository": { "html-stuff": { "patterns": [{ "include": "#template-tag" }, { "include": "text.html.derivative" }, { "include": "text.html.basic" }] }, "self-closing-tag": { "begin": "(<)([a-zA-Z0-9:-]+)(?=([^>]+/>))", "beginCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "end": "(/>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.end.html.vue" } }, "name": "self-closing-tag", "patterns": [{ "include": "#tag-stuff" }] }, "tag-stuff": { "begin": "\\G", "end": "(?=/>)|(>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.end.html.vue" } }, "name": "meta.tag-stuff", "patterns": [{ "include": "#vue-directives" }, { "include": "text.html.basic#attribute" }] }, "template-tag": { "patterns": [{ "include": "#template-tag-1" }, { "include": "#template-tag-2" }] }, "template-tag-1": { "begin": "(<)(template)\\b(>)", "beginCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" }, "3": { "name": "punctuation.definition.tag.end.html.vue" } }, "end": "(/?>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.end.html.vue" } }, "name": "meta.template-tag.start", "patterns": [{ "begin": "\\G", "end": "(?=/>)|((</)(template)\\b)", "endCaptures": { "2": { "name": "punctuation.definition.tag.begin.html.vue" }, "3": { "name": "entity.name.tag.$3.html.vue" } }, "name": "meta.template-tag.end", "patterns": [{ "include": "#html-stuff" }] }] }, "template-tag-2": { "begin": "(<)(template)\\b", "beginCaptures": { "1": { "name": "punctuation.definition.tag.begin.html.vue" }, "2": { "name": "entity.name.tag.$2.html.vue" } }, "end": "(/?>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.end.html.vue" } }, "name": "meta.template-tag.start", "patterns": [{ "begin": "\\G", "end": "(?=/>)|((</)(template)\\b)", "endCaptures": { "2": { "name": "punctuation.definition.tag.begin.html.vue" }, "3": { "name": "entity.name.tag.$3.html.vue" } }, "name": "meta.template-tag.end", "patterns": [{ "include": "#tag-stuff" }, { "include": "#html-stuff" }] }] }, "vue-directives": { "patterns": [{ "include": "#vue-directives-control" }, { "include": "#vue-directives-style-attr" }, { "include": "#vue-directives-original" }, { "include": "#vue-directives-generic-attr" }] }, "vue-directives-control": { "begin": "(v-for)|(v-if|v-else-if|v-else)", "captures": { "1": { "name": "keyword.control.loop.vue" }, "2": { "name": "keyword.control.conditional.vue" } }, "end": "(?=\\s*+[^=\\s])", "name": "meta.attribute.directive.control.vue", "patterns": [{ "include": "#vue-directives-expression" }] }, "vue-directives-expression": { "patterns": [{ "begin": "(=)\\s*('|\"|`)", "beginCaptures": { "1": { "name": "punctuation.separator.key-value.html.vue" }, "2": { "name": "punctuation.definition.string.begin.html.vue" } }, "end": "(\\2)", "endCaptures": { "1": { "name": "punctuation.definition.string.end.html.vue" } }, "patterns": [{ "begin": "(?<=('|\"|`))", "end": "(?=\\1)", "name": "source.ts.embedded.html.vue", "patterns": [{ "include": "source.ts" }] }] }, { "begin": "(=)\\s*(?=[^'\"`])", "beginCaptures": { "1": { "name": "punctuation.separator.key-value.html.vue" } }, "end": "(?=(\\s|>|\\/>))", "patterns": [{ "begin": "(?=[^'\"`])", "end": "(?=(\\s|>|\\/>))", "name": "source.ts.embedded.html.vue", "patterns": [{ "include": "source.ts" }] }] }] }, "vue-directives-generic-attr": { "begin": "\\b(generic)\\s*(=)", "captures": { "1": { "name": "entity.other.attribute-name.html.vue" }, "2": { "name": "punctuation.separator.key-value.html.vue" } }, "end": `(?<='|")`, "name": "meta.attribute.generic.vue", "patterns": [{ "begin": `('|")`, "beginCaptures": { "1": { "name": "punctuation.definition.string.begin.html.vue" } }, "comment": "https://github.com/microsoft/vscode/blob/fd4346210f59135fad81a8b8c4cea7bf5a9ca6b4/extensions/typescript-basics/syntaxes/TypeScript.tmLanguage.json#L4002-L4020", "end": "(\\1)", "endCaptures": { "1": { "name": "punctuation.definition.string.end.html.vue" } }, "name": "meta.type.parameters.vue", "patterns": [{ "include": "source.ts#comment" }, { "match": "(?<![_$[:alnum:]])(?:(?<=\\.\\.\\.)|(?<!\\.))(extends|in|out)(?![_$[:alnum:]])(?:(?=\\.\\.\\.)|(?!\\.))", "name": "storage.modifier.ts" }, { "include": "source.ts#type" }, { "include": "source.ts#punctuation-comma" }, { "match": "(=)(?!>)", "name": "keyword.operator.assignment.ts" }] }] }, "vue-directives-original": { "begin": "(?:\\b(v-)|([:\\.])|(@)|(#))(\\[?)([\\w\\-]*)(\\]?)(?:\\.([\\w\\-]*))*", "beginCaptures": { "1": { "name": "entity.other.attribute-name.html.vue" }, "2": { "name": "punctuation.attribute-shorthand.bind.html.vue" }, "3": { "name": "punctuation.attribute-shorthand.event.html.vue" }, "4": { "name": "punctuation.attribute-shorthand.slot.html.vue" }, "5": { "name": "punctuation.separator.key-value.html.vue" }, "6": { "name": "entity.other.attribute-name.html.vue" }, "7": { "name": "punctuation.separator.key-value.html.vue" }, "8": { "name": "entity.other.attribute-name.html.vue" }, "9": { "name": "punctuation.separator.key-value.html.vue" } }, "end": "(?=\\s*+[^=\\s])", "endCaptures": { "1": { "name": "punctuation.definition.string.end.html.vue" } }, "name": "meta.attribute.directive.vue", "patterns": [{ "include": "#vue-directives-expression" }] }, "vue-directives-style-attr": { "begin": "\\b(style)\\s*(=)", "captures": { "1": { "name": "entity.other.attribute-name.html.vue" }, "2": { "name": "punctuation.separator.key-value.html.vue" } }, "end": `(?<='|")`, "name": "meta.attribute.style.vue", "patterns": [{ "begin": `('|")`, "beginCaptures": { "1": { "name": "punctuation.definition.string.begin.html.vue" } }, "comment": "Copy from source.css#rule-list-innards", "end": "(\\1)", "endCaptures": { "1": { "name": "punctuation.definition.string.end.html.vue" } }, "name": "source.css.embedded.html.vue", "patterns": [{ "include": "source.css#comment-block" }, { "include": "source.css#escapes" }, { "include": "source.css#font-features" }, { "match": "(?x) (?<![\\w-])\n--\n(?:[-a-zA-Z_]    | [^\\x00-\\x7F])\n(?:[-a-zA-Z0-9_] | [^\\x00-\\x7F]\n|\\\\(?:[0-9a-fA-F]{1,6}|.)\n)*", "name": "variable.css" }, { "begin": "(?<![-a-zA-Z])(?=[-a-zA-Z])", "end": "$|(?![-a-zA-Z])", "name": "meta.property-name.css", "patterns": [{ "include": "source.css#property-names" }] }, { "begin": "(:)\\s*", "beginCaptures": { "1": { "name": "punctuation.separator.key-value.css" } }, "comment": "Modify end to fix #199. TODO: handle ' character.", "contentName": "meta.property-value.css", "end": `\\s*(;)|\\s*(?='|")`, "endCaptures": { "1": { "name": "punctuation.terminator.rule.css" } }, "patterns": [{ "include": "source.css#comment-block" }, { "include": "source.css#property-values" }] }, { "match": ";", "name": "punctuation.terminator.rule.css" }] }] }, "vue-interpolations": { "patterns": [{ "begin": "(\\{\\{)", "beginCaptures": { "1": { "name": "punctuation.definition.interpolation.begin.html.vue" } }, "end": "(\\}\\})", "endCaptures": { "1": { "name": "punctuation.definition.interpolation.end.html.vue" } }, "name": "expression.embedded.vue", "patterns": [{ "begin": "\\G", "end": "(?=\\}\\})", "name": "source.ts.embedded.html.vue", "patterns": [{ "include": "source.ts" }] }] }] } }, "scopeName": "source.vue", "embeddedLangs": ["html", "markdown", "pug", "stylus", "sass", "css", "scss", "less", "javascript", "typescript", "jsx", "tsx", "json", "jsonc", "json5", "yaml", "toml", "graphql", "markdown-vue", "vue-directives", "vue-interpolations", "vue-sfc-style-variable-injection"] });
var vue = [
  ...html,
  ...markdown,
  ...pug,
  ...stylus,
  ...sass,
  ...css,
  ...scss,
  ...less,
  ...javascript,
  ...typescript,
  ...jsx,
  ...tsx,
  ...json,
  ...jsonc,
  ...json5,
  ...yaml,
  ...toml,
  ...graphql,
  ...markdown_vue,
  ...vue_directives,
  ...vue_interpolations,
  ...vue_sfc_style_variable_injection,
  lang
];

export {
  vue
};
//# sourceMappingURL=chunk-3FQ5TW6X.js.map
