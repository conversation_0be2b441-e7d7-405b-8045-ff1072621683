import {
  baseClone
} from "./chunk-LNPCGL3T.js";

// node_modules/.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/vue-element-plus-x/dist/cloneDeep-ChqMBOjd.js
var CLONE_DEEP_FLAG = 1;
var CLONE_SYMBOLS_FLAG = 4;
function cloneDeep(value) {
  return baseClone(value, CLONE_DEEP_FLAG | CLONE_SYMBOLS_FLAG);
}

export {
  cloneDeep
};
//# sourceMappingURL=chunk-VKWRLPHX.js.map
