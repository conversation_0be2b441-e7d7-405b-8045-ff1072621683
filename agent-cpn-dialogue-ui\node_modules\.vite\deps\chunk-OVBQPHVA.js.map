{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/forIn.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/forOwn.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/_baseGt.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/mapValues.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/max.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/minBy.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/_baseSortBy.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/_compareAscending.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/_compareMultiple.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/_baseOrderBy.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/_baseRange.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/_createRange.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/range.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/sortBy.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/uniqueId.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/_baseZipObject.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/lodash-es/zipObject.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/data/list.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/greedy-fas.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/acyclic.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/util.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/add-border-segments.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/coordinate-system.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/normalize.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/rank/util.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/rank/feasible-tree.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/graphlib/alg/topsort.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/graphlib/alg/dfs.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/graphlib/alg/postorder.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/graphlib/alg/preorder.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/rank/network-simplex.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/rank/index.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/nesting-graph.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/order/add-subgraph-constraints.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/order/build-layer-graph.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/order/cross-count.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/order/init-order.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/order/barycenter.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/order/resolve-conflicts.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/order/sort.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/order/sort-subgraph.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/order/index.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/parent-dummy-chains.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/position/bk.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/position/index.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/dagre-d3-es/src/dagre/layout.js"], "sourcesContent": ["import baseFor from './_baseFor.js';\nimport castFunction from './_castFunction.js';\nimport keysIn from './keysIn.js';\n\n/**\n * Iterates over own and inherited enumerable string keyed properties of an\n * object and invokes `iteratee` for each property. The iteratee is invoked\n * with three arguments: (value, key, object). Iteratee functions may exit\n * iteration early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 0.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forInRight\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forIn(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'a', 'b', then 'c' (iteration order is not guaranteed).\n */\nfunction forIn(object, iteratee) {\n  return object == null\n    ? object\n    : baseFor(object, castFunction(iteratee), keysIn);\n}\n\nexport default forIn;\n", "import baseForOwn from './_baseForOwn.js';\nimport castFunction from './_castFunction.js';\n\n/**\n * Iterates over own enumerable string keyed properties of an object and\n * invokes `iteratee` for each property. The iteratee is invoked with three\n * arguments: (value, key, object). Iteratee functions may exit iteration\n * early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 0.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forOwnRight\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forOwn(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'a' then 'b' (iteration order is not guaranteed).\n */\nfunction forOwn(object, iteratee) {\n  return object && baseForOwn(object, castFunction(iteratee));\n}\n\nexport default forOwn;\n", "/**\n * The base implementation of `_.gt` which doesn't coerce arguments.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is greater than `other`,\n *  else `false`.\n */\nfunction baseGt(value, other) {\n  return value > other;\n}\n\nexport default baseGt;\n", "import baseAssignValue from './_baseAssignValue.js';\nimport baseForOwn from './_baseForOwn.js';\nimport baseIteratee from './_baseIteratee.js';\n\n/**\n * Creates an object with the same keys as `object` and values generated\n * by running each own enumerable string keyed property of `object` thru\n * `iteratee`. The iteratee is invoked with three arguments:\n * (value, key, object).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns the new mapped object.\n * @see _.mapKeys\n * @example\n *\n * var users = {\n *   'fred':    { 'user': 'fred',    'age': 40 },\n *   'pebbles': { 'user': 'pebbles', 'age': 1 }\n * };\n *\n * _.mapValues(users, function(o) { return o.age; });\n * // => { 'fred': 40, 'pebbles': 1 } (iteration order is not guaranteed)\n *\n * // The `_.property` iteratee shorthand.\n * _.mapValues(users, 'age');\n * // => { 'fred': 40, 'pebbles': 1 } (iteration order is not guaranteed)\n */\nfunction mapValues(object, iteratee) {\n  var result = {};\n  iteratee = baseIteratee(iteratee, 3);\n\n  baseForOwn(object, function(value, key, object) {\n    baseAssignValue(result, key, iteratee(value, key, object));\n  });\n  return result;\n}\n\nexport default mapValues;\n", "import baseExtremum from './_baseExtremum.js';\nimport baseGt from './_baseGt.js';\nimport identity from './identity.js';\n\n/**\n * Computes the maximum value of `array`. If `array` is empty or falsey,\n * `undefined` is returned.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {*} Returns the maximum value.\n * @example\n *\n * _.max([4, 2, 8, 6]);\n * // => 8\n *\n * _.max([]);\n * // => undefined\n */\nfunction max(array) {\n  return (array && array.length)\n    ? baseExtremum(array, identity, baseGt)\n    : undefined;\n}\n\nexport default max;\n", "import baseExtremum from './_baseExtremum.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseLt from './_baseLt.js';\n\n/**\n * This method is like `_.min` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * the value is ranked. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {*} Returns the minimum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * _.minBy(objects, function(o) { return o.n; });\n * // => { 'n': 1 }\n *\n * // The `_.property` iteratee shorthand.\n * _.minBy(objects, 'n');\n * // => { 'n': 1 }\n */\nfunction minBy(array, iteratee) {\n  return (array && array.length)\n    ? baseExtremum(array, baseIteratee(iteratee, 2), baseLt)\n    : undefined;\n}\n\nexport default minBy;\n", "/**\n * The base implementation of `_.sortBy` which uses `comparer` to define the\n * sort order of `array` and replaces criteria objects with their corresponding\n * values.\n *\n * @private\n * @param {Array} array The array to sort.\n * @param {Function} comparer The function to define sort order.\n * @returns {Array} Returns `array`.\n */\nfunction baseSortBy(array, comparer) {\n  var length = array.length;\n\n  array.sort(comparer);\n  while (length--) {\n    array[length] = array[length].value;\n  }\n  return array;\n}\n\nexport default baseSortBy;\n", "import isSymbol from './isSymbol.js';\n\n/**\n * Compares values to sort them in ascending order.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {number} Returns the sort order indicator for `value`.\n */\nfunction compareAscending(value, other) {\n  if (value !== other) {\n    var valIsDefined = value !== undefined,\n        valIsNull = value === null,\n        valIsReflexive = value === value,\n        valIsSymbol = isSymbol(value);\n\n    var othIsDefined = other !== undefined,\n        othIsNull = other === null,\n        othIsReflexive = other === other,\n        othIsSymbol = isSymbol(other);\n\n    if ((!othIsNull && !othIsSymbol && !valIsSymbol && value > other) ||\n        (valIsSymbol && othIsDefined && othIsReflexive && !othIsNull && !othIsSymbol) ||\n        (valIsNull && othIsDefined && othIsReflexive) ||\n        (!valIsDefined && othIsReflexive) ||\n        !valIsReflexive) {\n      return 1;\n    }\n    if ((!valIsNull && !valIsSymbol && !othIsSymbol && value < other) ||\n        (othIsSymbol && valIsDefined && valIsReflexive && !valIsNull && !valIsSymbol) ||\n        (othIsNull && valIsDefined && valIsReflexive) ||\n        (!othIsDefined && valIsReflexive) ||\n        !othIsReflexive) {\n      return -1;\n    }\n  }\n  return 0;\n}\n\nexport default compareAscending;\n", "import compareAscending from './_compareAscending.js';\n\n/**\n * Used by `_.orderBy` to compare multiple properties of a value to another\n * and stable sort them.\n *\n * If `orders` is unspecified, all values are sorted in ascending order. Otherwise,\n * specify an order of \"desc\" for descending or \"asc\" for ascending sort order\n * of corresponding values.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {boolean[]|string[]} orders The order to sort by for each property.\n * @returns {number} Returns the sort order indicator for `object`.\n */\nfunction compareMultiple(object, other, orders) {\n  var index = -1,\n      objCriteria = object.criteria,\n      othCriteria = other.criteria,\n      length = objCriteria.length,\n      ordersLength = orders.length;\n\n  while (++index < length) {\n    var result = compareAscending(objCriteria[index], othCriteria[index]);\n    if (result) {\n      if (index >= ordersLength) {\n        return result;\n      }\n      var order = orders[index];\n      return result * (order == 'desc' ? -1 : 1);\n    }\n  }\n  // Fixes an `Array#sort` bug in the JS engine embedded in Adobe applications\n  // that causes it, under certain circumstances, to provide the same value for\n  // `object` and `other`. See https://github.com/jashkenas/underscore/pull/1247\n  // for more details.\n  //\n  // This also ensures a stable sort in V8 and other engines.\n  // See https://bugs.chromium.org/p/v8/issues/detail?id=90 for more details.\n  return object.index - other.index;\n}\n\nexport default compareMultiple;\n", "import arrayMap from './_arrayMap.js';\nimport baseGet from './_baseGet.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseMap from './_baseMap.js';\nimport baseSortBy from './_baseSortBy.js';\nimport baseUnary from './_baseUnary.js';\nimport compareMultiple from './_compareMultiple.js';\nimport identity from './identity.js';\nimport isArray from './isArray.js';\n\n/**\n * The base implementation of `_.orderBy` without param guards.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function[]|Object[]|string[]} iteratees The iteratees to sort by.\n * @param {string[]} orders The sort orders of `iteratees`.\n * @returns {Array} Returns the new sorted array.\n */\nfunction baseOrderBy(collection, iteratees, orders) {\n  if (iteratees.length) {\n    iteratees = arrayMap(iteratees, function(iteratee) {\n      if (isArray(iteratee)) {\n        return function(value) {\n          return baseGet(value, iteratee.length === 1 ? iteratee[0] : iteratee);\n        }\n      }\n      return iteratee;\n    });\n  } else {\n    iteratees = [identity];\n  }\n\n  var index = -1;\n  iteratees = arrayMap(iteratees, baseUnary(baseIteratee));\n\n  var result = baseMap(collection, function(value, key, collection) {\n    var criteria = arrayMap(iteratees, function(iteratee) {\n      return iteratee(value);\n    });\n    return { 'criteria': criteria, 'index': ++index, 'value': value };\n  });\n\n  return baseSortBy(result, function(object, other) {\n    return compareMultiple(object, other, orders);\n  });\n}\n\nexport default baseOrderBy;\n", "/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil,\n    nativeMax = Math.max;\n\n/**\n * The base implementation of `_.range` and `_.rangeRight` which doesn't\n * coerce arguments.\n *\n * @private\n * @param {number} start The start of the range.\n * @param {number} end The end of the range.\n * @param {number} step The value to increment or decrement by.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Array} Returns the range of numbers.\n */\nfunction baseRange(start, end, step, fromRight) {\n  var index = -1,\n      length = nativeMax(nativeCeil((end - start) / (step || 1)), 0),\n      result = Array(length);\n\n  while (length--) {\n    result[fromRight ? length : ++index] = start;\n    start += step;\n  }\n  return result;\n}\n\nexport default baseRange;\n", "import baseRange from './_baseRange.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toFinite from './toFinite.js';\n\n/**\n * Creates a `_.range` or `_.rangeRight` function.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new range function.\n */\nfunction createRange(fromRight) {\n  return function(start, end, step) {\n    if (step && typeof step != 'number' && isIterateeCall(start, end, step)) {\n      end = step = undefined;\n    }\n    // Ensure the sign of `-0` is preserved.\n    start = toFinite(start);\n    if (end === undefined) {\n      end = start;\n      start = 0;\n    } else {\n      end = toFinite(end);\n    }\n    step = step === undefined ? (start < end ? 1 : -1) : toFinite(step);\n    return baseRange(start, end, step, fromRight);\n  };\n}\n\nexport default createRange;\n", "import createRange from './_createRange.js';\n\n/**\n * Creates an array of numbers (positive and/or negative) progressing from\n * `start` up to, but not including, `end`. A step of `-1` is used if a negative\n * `start` is specified without an `end` or `step`. If `end` is not specified,\n * it's set to `start` with `start` then set to `0`.\n *\n * **Note:** JavaScript follows the IEEE-754 standard for resolving\n * floating-point values which can produce unexpected results.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {number} [start=0] The start of the range.\n * @param {number} end The end of the range.\n * @param {number} [step=1] The value to increment or decrement by.\n * @returns {Array} Returns the range of numbers.\n * @see _.inRange, _.rangeRight\n * @example\n *\n * _.range(4);\n * // => [0, 1, 2, 3]\n *\n * _.range(-4);\n * // => [0, -1, -2, -3]\n *\n * _.range(1, 5);\n * // => [1, 2, 3, 4]\n *\n * _.range(0, 20, 5);\n * // => [0, 5, 10, 15]\n *\n * _.range(0, -4, -1);\n * // => [0, -1, -2, -3]\n *\n * _.range(1, 4, 0);\n * // => [1, 1, 1]\n *\n * _.range(0);\n * // => []\n */\nvar range = createRange();\n\nexport default range;\n", "import baseFlatten from './_baseFlatten.js';\nimport baseOrderBy from './_baseOrderBy.js';\nimport baseRest from './_baseRest.js';\nimport isIterateeCall from './_isIterateeCall.js';\n\n/**\n * Creates an array of elements, sorted in ascending order by the results of\n * running each element in a collection thru each iteratee. This method\n * performs a stable sort, that is, it preserves the original sort order of\n * equal elements. The iteratees are invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {...(Function|Function[])} [iteratees=[_.identity]]\n *  The iteratees to sort by.\n * @returns {Array} Returns the new sorted array.\n * @example\n *\n * var users = [\n *   { 'user': 'fred',   'age': 48 },\n *   { 'user': 'barney', 'age': 36 },\n *   { 'user': 'fred',   'age': 30 },\n *   { 'user': 'barney', 'age': 34 }\n * ];\n *\n * _.sortBy(users, [function(o) { return o.user; }]);\n * // => objects for [['barney', 36], ['barney', 34], ['fred', 48], ['fred', 30]]\n *\n * _.sortBy(users, ['user', 'age']);\n * // => objects for [['barney', 34], ['barney', 36], ['fred', 30], ['fred', 48]]\n */\nvar sortBy = baseRest(function(collection, iteratees) {\n  if (collection == null) {\n    return [];\n  }\n  var length = iteratees.length;\n  if (length > 1 && isIterateeCall(collection, iteratees[0], iteratees[1])) {\n    iteratees = [];\n  } else if (length > 2 && isIterateeCall(iteratees[0], iteratees[1], iteratees[2])) {\n    iteratees = [iteratees[0]];\n  }\n  return baseOrderBy(collection, baseFlatten(iteratees, 1), []);\n});\n\nexport default sortBy;\n", "import toString from './toString.js';\n\n/** Used to generate unique IDs. */\nvar idCounter = 0;\n\n/**\n * Generates a unique ID. If `prefix` is given, the ID is appended to it.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {string} [prefix=''] The value to prefix the ID with.\n * @returns {string} Returns the unique ID.\n * @example\n *\n * _.uniqueId('contact_');\n * // => 'contact_104'\n *\n * _.uniqueId();\n * // => '105'\n */\nfunction uniqueId(prefix) {\n  var id = ++idCounter;\n  return toString(prefix) + id;\n}\n\nexport default uniqueId;\n", "/**\n * This base implementation of `_.zipObject` which assigns values using `assignFunc`.\n *\n * @private\n * @param {Array} props The property identifiers.\n * @param {Array} values The property values.\n * @param {Function} assignFunc The function to assign values.\n * @returns {Object} Returns the new object.\n */\nfunction baseZipObject(props, values, assignFunc) {\n  var index = -1,\n      length = props.length,\n      valsLength = values.length,\n      result = {};\n\n  while (++index < length) {\n    var value = index < valsLength ? values[index] : undefined;\n    assignFunc(result, props[index], value);\n  }\n  return result;\n}\n\nexport default baseZipObject;\n", "import assignValue from './_assignValue.js';\nimport baseZipObject from './_baseZipObject.js';\n\n/**\n * This method is like `_.fromPairs` except that it accepts two arrays,\n * one of property identifiers and one of corresponding values.\n *\n * @static\n * @memberOf _\n * @since 0.4.0\n * @category Array\n * @param {Array} [props=[]] The property identifiers.\n * @param {Array} [values=[]] The property values.\n * @returns {Object} Returns the new object.\n * @example\n *\n * _.zipObject(['a', 'b'], [1, 2]);\n * // => { 'a': 1, 'b': 2 }\n */\nfunction zipObject(props, values) {\n  return baseZipObject(props || [], values || [], assignValue);\n}\n\nexport default zipObject;\n", "/*\n * Simple doubly linked list implementation derived from <PERSON><PERSON><PERSON>, et al.,\n * \"Introduction to Algorithms\".\n */\n\nexport { List };\n\nclass List {\n  constructor() {\n    var sentinel = {};\n    sentinel._next = sentinel._prev = sentinel;\n    this._sentinel = sentinel;\n  }\n  dequeue() {\n    var sentinel = this._sentinel;\n    var entry = sentinel._prev;\n    if (entry !== sentinel) {\n      unlink(entry);\n      return entry;\n    }\n  }\n  enqueue(entry) {\n    var sentinel = this._sentinel;\n    if (entry._prev && entry._next) {\n      unlink(entry);\n    }\n    entry._next = sentinel._next;\n    sentinel._next._prev = entry;\n    sentinel._next = entry;\n    entry._prev = sentinel;\n  }\n  toString() {\n    var strs = [];\n    var sentinel = this._sentinel;\n    var curr = sentinel._prev;\n    while (curr !== sentinel) {\n      strs.push(JSON.stringify(curr, filterOutLinks));\n      curr = curr._prev;\n    }\n    return '[' + strs.join(', ') + ']';\n  }\n}\n\nfunction unlink(entry) {\n  entry._prev._next = entry._next;\n  entry._next._prev = entry._prev;\n  delete entry._next;\n  delete entry._prev;\n}\n\nfunction filterOutLinks(k, v) {\n  if (k !== '_next' && k !== '_prev') {\n    return v;\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\nimport { List } from './data/list.js';\n\n/*\n * A greedy heuristic for finding a feedback arc set for a graph. A feedback\n * arc set is a set of edges that can be removed to make a graph acyclic.\n * The algorithm comes from: <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>, \"A fast and\n * effective heuristic for the feedback arc set problem.\" This implementation\n * adjusts that from the paper to allow for weighted edges.\n */\nexport { greedyFAS };\n\nvar DEFAULT_WEIGHT_FN = _.constant(1);\n\nfunction greedyFAS(g, weightFn) {\n  if (g.nodeCount() <= 1) {\n    return [];\n  }\n  var state = buildState(g, weightFn || DEFAULT_WEIGHT_FN);\n  var results = doGreedyFAS(state.graph, state.buckets, state.zeroIdx);\n\n  // Expand multi-edges\n  return _.flatten(\n    _.map(results, function (e) {\n      return g.outEdges(e.v, e.w);\n    }),\n  );\n}\n\nfunction doGreedyFAS(g, buckets, zeroIdx) {\n  var results = [];\n  var sources = buckets[buckets.length - 1];\n  var sinks = buckets[0];\n\n  var entry;\n  while (g.nodeCount()) {\n    while ((entry = sinks.dequeue())) {\n      removeNode(g, buckets, zeroIdx, entry);\n    }\n    while ((entry = sources.dequeue())) {\n      removeNode(g, buckets, zeroIdx, entry);\n    }\n    if (g.nodeCount()) {\n      for (var i = buckets.length - 2; i > 0; --i) {\n        entry = buckets[i].dequeue();\n        if (entry) {\n          results = results.concat(removeNode(g, buckets, zeroIdx, entry, true));\n          break;\n        }\n      }\n    }\n  }\n\n  return results;\n}\n\nfunction removeNode(g, buckets, zeroIdx, entry, collectPredecessors) {\n  var results = collectPredecessors ? [] : undefined;\n\n  _.forEach(g.inEdges(entry.v), function (edge) {\n    var weight = g.edge(edge);\n    var uEntry = g.node(edge.v);\n\n    if (collectPredecessors) {\n      results.push({ v: edge.v, w: edge.w });\n    }\n\n    uEntry.out -= weight;\n    assignBucket(buckets, zeroIdx, uEntry);\n  });\n\n  _.forEach(g.outEdges(entry.v), function (edge) {\n    var weight = g.edge(edge);\n    var w = edge.w;\n    var wEntry = g.node(w);\n    wEntry['in'] -= weight;\n    assignBucket(buckets, zeroIdx, wEntry);\n  });\n\n  g.removeNode(entry.v);\n\n  return results;\n}\n\nfunction buildState(g, weightFn) {\n  var fasGraph = new Graph();\n  var maxIn = 0;\n  var maxOut = 0;\n\n  _.forEach(g.nodes(), function (v) {\n    fasGraph.setNode(v, { v: v, in: 0, out: 0 });\n  });\n\n  // Aggregate weights on nodes, but also sum the weights across multi-edges\n  // into a single edge for the fasGraph.\n  _.forEach(g.edges(), function (e) {\n    var prevWeight = fasGraph.edge(e.v, e.w) || 0;\n    var weight = weightFn(e);\n    var edgeWeight = prevWeight + weight;\n    fasGraph.setEdge(e.v, e.w, edgeWeight);\n    maxOut = Math.max(maxOut, (fasGraph.node(e.v).out += weight));\n    maxIn = Math.max(maxIn, (fasGraph.node(e.w)['in'] += weight));\n  });\n\n  var buckets = _.range(maxOut + maxIn + 3).map(function () {\n    return new List();\n  });\n  var zeroIdx = maxIn + 1;\n\n  _.forEach(fasGraph.nodes(), function (v) {\n    assignBucket(buckets, zeroIdx, fasGraph.node(v));\n  });\n\n  return { graph: fasGraph, buckets: buckets, zeroIdx: zeroIdx };\n}\n\nfunction assignBucket(buckets, zeroIdx, entry) {\n  if (!entry.out) {\n    buckets[0].enqueue(entry);\n  } else if (!entry['in']) {\n    buckets[buckets.length - 1].enqueue(entry);\n  } else {\n    buckets[entry.out - entry['in'] + zeroIdx].enqueue(entry);\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { greedyFAS } from './greedy-fas.js';\n\nexport { run, undo };\n\nfunction run(g) {\n  var fas = g.graph().acyclicer === 'greedy' ? greedyFAS(g, weightFn(g)) : dfsFAS(g);\n  _.forEach(fas, function (e) {\n    var label = g.edge(e);\n    g.removeEdge(e);\n    label.forwardName = e.name;\n    label.reversed = true;\n    g.setEdge(e.w, e.v, label, _.uniqueId('rev'));\n  });\n\n  function weightFn(g) {\n    return function (e) {\n      return g.edge(e).weight;\n    };\n  }\n}\n\nfunction dfsFAS(g) {\n  var fas = [];\n  var stack = {};\n  var visited = {};\n\n  function dfs(v) {\n    if (Object.prototype.hasOwnProperty.call(visited, v)) {\n      return;\n    }\n    visited[v] = true;\n    stack[v] = true;\n    _.forEach(g.outEdges(v), function (e) {\n      if (Object.prototype.hasOwnProperty.call(stack, e.w)) {\n        fas.push(e);\n      } else {\n        dfs(e.w);\n      }\n    });\n    delete stack[v];\n  }\n\n  _.forEach(g.nodes(), dfs);\n  return fas;\n}\n\nfunction undo(g) {\n  _.forEach(g.edges(), function (e) {\n    var label = g.edge(e);\n    if (label.reversed) {\n      g.removeEdge(e);\n\n      var forwardName = label.forwardName;\n      delete label.reversed;\n      delete label.forwardName;\n      g.setEdge(e.w, e.v, label, forwardName);\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\n\nexport {\n  addDummyNode,\n  simplify,\n  asNonCompoundGraph,\n  successorWeights,\n  predecessorWeights,\n  intersectRect,\n  buildLayerMatrix,\n  normalizeRanks,\n  removeEmptyRanks,\n  addBorderNode,\n  maxRank,\n  partition,\n  time,\n  notime,\n};\n\n/*\n * Adds a dummy node to the graph and return v.\n */\nfunction addDummyNode(g, type, attrs, name) {\n  var v;\n  do {\n    v = _.uniqueId(name);\n  } while (g.hasNode(v));\n\n  attrs.dummy = type;\n  g.setNode(v, attrs);\n  return v;\n}\n\n/*\n * Returns a new graph with only simple edges. Handles aggregation of data\n * associated with multi-edges.\n */\nfunction simplify(g) {\n  var simplified = new Graph().setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    simplified.setNode(v, g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var simpleLabel = simplified.edge(e.v, e.w) || { weight: 0, minlen: 1 };\n    var label = g.edge(e);\n    simplified.setEdge(e.v, e.w, {\n      weight: simpleLabel.weight + label.weight,\n      minlen: Math.max(simpleLabel.minlen, label.minlen),\n    });\n  });\n  return simplified;\n}\n\nfunction asNonCompoundGraph(g) {\n  var simplified = new Graph({ multigraph: g.isMultigraph() }).setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    if (!g.children(v).length) {\n      simplified.setNode(v, g.node(v));\n    }\n  });\n  _.forEach(g.edges(), function (e) {\n    simplified.setEdge(e, g.edge(e));\n  });\n  return simplified;\n}\n\nfunction successorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var sucs = {};\n    _.forEach(g.outEdges(v), function (e) {\n      sucs[e.w] = (sucs[e.w] || 0) + g.edge(e).weight;\n    });\n    return sucs;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\nfunction predecessorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var preds = {};\n    _.forEach(g.inEdges(v), function (e) {\n      preds[e.v] = (preds[e.v] || 0) + g.edge(e).weight;\n    });\n    return preds;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\n/*\n * Finds where a line starting at point ({x, y}) would intersect a rectangle\n * ({x, y, width, height}) if it were pointing at the rectangle's center.\n */\nfunction intersectRect(rect, point) {\n  var x = rect.x;\n  var y = rect.y;\n\n  // Rectangle intersection algorithm from:\n  // http://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = rect.width / 2;\n  var h = rect.height / 2;\n\n  if (!dx && !dy) {\n    throw new Error('Not possible to find intersection inside of the rectangle');\n  }\n\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = (h * dx) / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = (w * dy) / dx;\n  }\n\n  return { x: x + sx, y: y + sy };\n}\n\n/*\n * Given a DAG with each node assigned \"rank\" and \"order\" properties, this\n * function will produce a matrix with the ids of each node.\n */\nfunction buildLayerMatrix(g) {\n  var layering = _.map(_.range(maxRank(g) + 1), function () {\n    return [];\n  });\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    var rank = node.rank;\n    if (!_.isUndefined(rank)) {\n      layering[rank][node.order] = v;\n    }\n  });\n  return layering;\n}\n\n/*\n * Adjusts the ranks for all nodes in the graph such that all nodes v have\n * rank(v) >= 0 and at least one node w has rank(w) = 0.\n */\nfunction normalizeRanks(g) {\n  var min = _.min(\n    _.map(g.nodes(), function (v) {\n      return g.node(v).rank;\n    }),\n  );\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (_.has(node, 'rank')) {\n      node.rank -= min;\n    }\n  });\n}\n\nfunction removeEmptyRanks(g) {\n  // Ranks may not start at 0, so we need to offset them\n  var offset = _.min(\n    _.map(g.nodes(), function (v) {\n      return g.node(v).rank;\n    }),\n  );\n\n  var layers = [];\n  _.forEach(g.nodes(), function (v) {\n    var rank = g.node(v).rank - offset;\n    if (!layers[rank]) {\n      layers[rank] = [];\n    }\n    layers[rank].push(v);\n  });\n\n  var delta = 0;\n  var nodeRankFactor = g.graph().nodeRankFactor;\n  _.forEach(layers, function (vs, i) {\n    if (_.isUndefined(vs) && i % nodeRankFactor !== 0) {\n      --delta;\n    } else if (delta) {\n      _.forEach(vs, function (v) {\n        g.node(v).rank += delta;\n      });\n    }\n  });\n}\n\nfunction addBorderNode(g, prefix, rank, order) {\n  var node = {\n    width: 0,\n    height: 0,\n  };\n  if (arguments.length >= 4) {\n    node.rank = rank;\n    node.order = order;\n  }\n  return addDummyNode(g, 'border', node, prefix);\n}\n\nfunction maxRank(g) {\n  return _.max(\n    _.map(g.nodes(), function (v) {\n      var rank = g.node(v).rank;\n      if (!_.isUndefined(rank)) {\n        return rank;\n      }\n    }),\n  );\n}\n\n/*\n * Partition a collection into two groups: `lhs` and `rhs`. If the supplied\n * function returns true for an entry it goes into `lhs`. Otherwise it goes\n * into `rhs.\n */\nfunction partition(collection, fn) {\n  var result = { lhs: [], rhs: [] };\n  _.forEach(collection, function (value) {\n    if (fn(value)) {\n      result.lhs.push(value);\n    } else {\n      result.rhs.push(value);\n    }\n  });\n  return result;\n}\n\n/*\n * Returns a new function that wraps `fn` with a timer. The wrapper logs the\n * time it takes to execute the function.\n */\nfunction time(name, fn) {\n  var start = _.now();\n  try {\n    return fn();\n  } finally {\n    console.log(name + ' time: ' + (_.now() - start) + 'ms');\n  }\n}\n\nfunction notime(name, fn) {\n  return fn();\n}\n", "import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { addBorderSegments };\n\nfunction addBorderSegments(g) {\n  function dfs(v) {\n    var children = g.children(v);\n    var node = g.node(v);\n    if (children.length) {\n      _.forEach(children, dfs);\n    }\n\n    if (Object.prototype.hasOwnProperty.call(node, 'minRank')) {\n      node.borderLeft = [];\n      node.borderRight = [];\n      for (var rank = node.minRank, maxRank = node.maxRank + 1; rank < maxRank; ++rank) {\n        addBorderNode(g, 'borderLeft', '_bl', v, node, rank);\n        addBorderNode(g, 'borderRight', '_br', v, node, rank);\n      }\n    }\n  }\n\n  _.forEach(g.children(), dfs);\n}\n\nfunction addBorderNode(g, prop, prefix, sg, sgNode, rank) {\n  var label = { width: 0, height: 0, rank: rank, borderType: prop };\n  var prev = sgNode[prop][rank - 1];\n  var curr = util.addDummyNode(g, 'border', label, prefix);\n  sgNode[prop][rank] = curr;\n  g.setParent(curr, sg);\n  if (prev) {\n    g.setEdge(prev, curr, { weight: 1 });\n  }\n}\n", "import * as _ from 'lodash-es';\n\nexport { adjust, undo };\n\nfunction adjust(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === 'lr' || rankDir === 'rl') {\n    swapWidthHeight(g);\n  }\n}\n\nfunction undo(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === 'bt' || rankDir === 'rl') {\n    reverseY(g);\n  }\n\n  if (rankDir === 'lr' || rankDir === 'rl') {\n    swapXY(g);\n    swapWidthHeight(g);\n  }\n}\n\nfunction swapWidthHeight(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapWidthHeightOne(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    swapWidthHeightOne(g.edge(e));\n  });\n}\n\nfunction swapWidthHeightOne(attrs) {\n  var w = attrs.width;\n  attrs.width = attrs.height;\n  attrs.height = w;\n}\n\nfunction reverseY(g) {\n  _.forEach(g.nodes(), function (v) {\n    reverseYOne(g.node(v));\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, reverseYOne);\n    if (Object.prototype.hasOwnProperty.call(edge, 'y')) {\n      reverseYOne(edge);\n    }\n  });\n}\n\nfunction reverseYOne(attrs) {\n  attrs.y = -attrs.y;\n}\n\nfunction swapXY(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapXYOne(g.node(v));\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, swapXYOne);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      swapXYOne(edge);\n    }\n  });\n}\n\nfunction swapXYOne(attrs) {\n  var x = attrs.x;\n  attrs.x = attrs.y;\n  attrs.y = x;\n}\n", "/**\n * TypeScript type imports:\n *\n * @import { Graph } from '../graphlib/graph.js';\n */\nimport * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { run, undo };\n\n/*\n * Breaks any long edges in the graph into short segments that span 1 layer\n * each. This operation is undoable with the denormalize function.\n *\n * Pre-conditions:\n *\n *    1. The input graph is a DAG.\n *    2. Each node in the graph has a \"rank\" property.\n *\n * Post-condition:\n *\n *    1. All edges in the graph have a length of 1.\n *    2. Dummy nodes are added where edges have been split into segments.\n *    3. The graph is augmented with a \"dummyChains\" attribute which contains\n *       the first dummy in each chain of dummy nodes produced.\n */\nfunction run(g) {\n  g.graph().dummyChains = [];\n  _.forEach(g.edges(), function (edge) {\n    normalizeEdge(g, edge);\n  });\n}\n\n/**\n * @param {Graph} g\n */\nfunction normalizeEdge(g, e) {\n  var v = e.v;\n  var vRank = g.node(v).rank;\n  var w = e.w;\n  var wRank = g.node(w).rank;\n  var name = e.name;\n  var edgeLabel = g.edge(e);\n  var labelRank = edgeLabel.labelRank;\n\n  if (wRank === vRank + 1) return;\n\n  g.removeEdge(e);\n\n  /**\n   * @typedef {Object} Attrs\n   * @property {number} width\n   * @property {number} height\n   * @property {ReturnType<Graph[\"node\"]>} edgeLabel\n   * @property {any} edgeObj\n   * @property {ReturnType<Graph[\"node\"]>[\"rank\"]} rank\n   * @property {string} [dummy]\n   * @property {ReturnType<Graph[\"node\"]>[\"labelpos\"]} [labelpos]\n   */\n\n  /** @type {Attrs | undefined} */\n  var attrs = undefined;\n  var dummy, i;\n  for (i = 0, ++vRank; vRank < wRank; ++i, ++vRank) {\n    edgeLabel.points = [];\n    attrs = {\n      width: 0,\n      height: 0,\n      edgeLabel: edgeLabel,\n      edgeObj: e,\n      rank: vRank,\n    };\n    dummy = util.addDummyNode(g, 'edge', attrs, '_d');\n    if (vRank === labelRank) {\n      attrs.width = edgeLabel.width;\n      attrs.height = edgeLabel.height;\n      attrs.dummy = 'edge-label';\n      attrs.labelpos = edgeLabel.labelpos;\n    }\n    g.setEdge(v, dummy, { weight: edgeLabel.weight }, name);\n    if (i === 0) {\n      g.graph().dummyChains.push(dummy);\n    }\n    v = dummy;\n  }\n\n  g.setEdge(v, w, { weight: edgeLabel.weight }, name);\n}\n\nfunction undo(g) {\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var origLabel = node.edgeLabel;\n    var w;\n    g.setEdge(node.edgeObj, origLabel);\n    while (node.dummy) {\n      w = g.successors(v)[0];\n      g.removeNode(v);\n      origLabel.points.push({ x: node.x, y: node.y });\n      if (node.dummy === 'edge-label') {\n        origLabel.x = node.x;\n        origLabel.y = node.y;\n        origLabel.width = node.width;\n        origLabel.height = node.height;\n      }\n      v = w;\n      node = g.node(v);\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { longestPath, slack };\n\n/*\n * Initializes ranks for the input graph using the longest path algorithm. This\n * algorithm scales well and is fast in practice, it yields rather poor\n * solutions. Nodes are pushed to the lowest layer possible, leaving the bottom\n * ranks wide and leaving edges longer than necessary. However, due to its\n * speed, this algorithm is good for getting an initial ranking that can be fed\n * into other algorithms.\n *\n * This algorithm does not normalize layers because it will be used by other\n * algorithms in most cases. If using this algorithm directly, be sure to\n * run normalize at the end.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG.\n *    2. Input graph node labels can be assigned properties.\n *\n * Post-conditions:\n *\n *    1. Each node will be assign an (unnormalized) \"rank\" property.\n */\nfunction longestPath(g) {\n  var visited = {};\n\n  function dfs(v) {\n    var label = g.node(v);\n    if (Object.prototype.hasOwnProperty.call(visited, v)) {\n      return label.rank;\n    }\n    visited[v] = true;\n\n    var rank = _.min(\n      _.map(g.outEdges(v), function (e) {\n        return dfs(e.w) - g.edge(e).minlen;\n      }),\n    );\n\n    if (\n      rank === Number.POSITIVE_INFINITY || // return value of _.map([]) for Lodash 3\n      rank === undefined || // return value of _.map([]) for Lodash 4\n      rank === null\n    ) {\n      // return value of _.map([null])\n      rank = 0;\n    }\n\n    return (label.rank = rank);\n  }\n\n  _.forEach(g.sources(), dfs);\n}\n\n/*\n * Returns the amount of slack for the given edge. The slack is defined as the\n * difference between the length of the edge and its minimum length.\n */\nfunction slack(g, e) {\n  return g.node(e.w).rank - g.node(e.v).rank - g.edge(e).minlen;\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport { slack } from './util.js';\n\nexport { feasibleTree };\n\n/*\n * Constructs a spanning tree with tight edges and adjusted the input node's\n * ranks to achieve this. A tight edge is one that is has a length that matches\n * its \"minlen\" attribute.\n *\n * The basic structure for this function is derived from <PERSON><PERSON><PERSON>, et al., \"A\n * Technique for Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a DAG.\n *    2. Graph must be connected.\n *    3. Graph must have at least one node.\n *    5. Graph nodes must have been previously assigned a \"rank\" property that\n *       respects the \"minlen\" property of incident edges.\n *    6. Graph edges must have a \"minlen\" property.\n *\n * Post-conditions:\n *\n *    - Graph nodes will have their rank adjusted to ensure that all edges are\n *      tight.\n *\n * Returns a tree (undirected graph) that is constructed using only \"tight\"\n * edges.\n */\nfunction feasibleTree(g) {\n  var t = new Graph({ directed: false });\n\n  // Choose arbitrary node from which to start our tree\n  var start = g.nodes()[0];\n  var size = g.nodeCount();\n  t.setNode(start, {});\n\n  var edge, delta;\n  while (tightTree(t, g) < size) {\n    edge = findMinSlackEdge(t, g);\n    delta = t.hasNode(edge.v) ? slack(g, edge) : -slack(g, edge);\n    shiftRanks(t, g, delta);\n  }\n\n  return t;\n}\n\n/*\n * Finds a maximal tree of tight edges and returns the number of nodes in the\n * tree.\n */\nfunction tightTree(t, g) {\n  function dfs(v) {\n    _.forEach(g.nodeEdges(v), function (e) {\n      var edgeV = e.v,\n        w = v === edgeV ? e.w : edgeV;\n      if (!t.hasNode(w) && !slack(g, e)) {\n        t.setNode(w, {});\n        t.setEdge(v, w, {});\n        dfs(w);\n      }\n    });\n  }\n\n  _.forEach(t.nodes(), dfs);\n  return t.nodeCount();\n}\n\n/*\n * Finds the edge with the smallest slack that is incident on tree and returns\n * it.\n */\nfunction findMinSlackEdge(t, g) {\n  return _.minBy(g.edges(), function (e) {\n    if (t.hasNode(e.v) !== t.hasNode(e.w)) {\n      return slack(g, e);\n    }\n  });\n}\n\nfunction shiftRanks(t, g, delta) {\n  _.forEach(t.nodes(), function (v) {\n    g.node(v).rank += delta;\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { topsort, CycleException };\n\ntopsort.CycleException = CycleException;\n\nfunction topsort(g) {\n  var visited = {};\n  var stack = {};\n  var results = [];\n\n  function visit(node) {\n    if (Object.prototype.hasOwnProperty.call(stack, node)) {\n      throw new CycleException();\n    }\n\n    if (!Object.prototype.hasOwnProperty.call(visited, node)) {\n      stack[node] = true;\n      visited[node] = true;\n      _.each(g.predecessors(node), visit);\n      delete stack[node];\n      results.push(node);\n    }\n  }\n\n  _.each(g.sinks(), visit);\n\n  if (_.size(visited) !== g.nodeCount()) {\n    throw new CycleException();\n  }\n\n  return results;\n}\n\nfunction CycleException() {}\nCycleException.prototype = new Error(); // must be an instance of Error to pass testing\n", "import * as _ from 'lodash-es';\n\nexport { dfs };\n\n/*\n * A helper that preforms a pre- or post-order traversal on the input graph\n * and returns the nodes in the order they were visited. If the graph is\n * undirected then this algorithm will navigate using neighbors. If the graph\n * is directed then this algorithm will navigate using successors.\n *\n * Order must be one of \"pre\" or \"post\".\n */\nfunction dfs(g, vs, order) {\n  if (!_.isArray(vs)) {\n    vs = [vs];\n  }\n\n  var navigation = (g.isDirected() ? g.successors : g.neighbors).bind(g);\n\n  var acc = [];\n  var visited = {};\n  _.each(vs, function (v) {\n    if (!g.hasNode(v)) {\n      throw new Error('Graph does not have node: ' + v);\n    }\n\n    doDfs(g, v, order === 'post', visited, navigation, acc);\n  });\n  return acc;\n}\n\nfunction doDfs(g, v, postorder, visited, navigation, acc) {\n  if (!Object.prototype.hasOwnProperty.call(visited, v)) {\n    visited[v] = true;\n\n    if (!postorder) {\n      acc.push(v);\n    }\n    _.each(navigation(v), function (w) {\n      doDfs(g, w, postorder, visited, navigation, acc);\n    });\n    if (postorder) {\n      acc.push(v);\n    }\n  }\n}\n", "import { dfs } from './dfs.js';\n\nexport { postorder };\n\nfunction postorder(g, vs) {\n  return dfs(g, vs, 'post');\n}\n", "import { dfs } from './dfs.js';\n\nexport { preorder };\n\nfunction preorder(g, vs) {\n  return dfs(g, vs, 'pre');\n}\n", "import * as _ from 'lodash-es';\nimport * as alg from '../../graphlib/alg/index.js';\nimport { simplify } from '../util.js';\nimport { feasibleTree } from './feasible-tree.js';\nimport { longestPath, slack } from './util.js';\n\nexport { networkSimplex };\n\n// Expose some internals for testing purposes\nnetworkSimplex.initLowLimValues = initLowLimValues;\nnetworkSimplex.initCutValues = initCutValues;\nnetworkSimplex.calcCutValue = calcCutValue;\nnetworkSimplex.leaveEdge = leaveEdge;\nnetworkSimplex.enterEdge = enterEdge;\nnetworkSimplex.exchangeEdges = exchangeEdges;\n\n/*\n * The network simplex algorithm assigns ranks to each node in the input graph\n * and iteratively improves the ranking to reduce the length of edges.\n *\n * Preconditions:\n *\n *    1. The input graph must be a DAG.\n *    2. All nodes in the graph must have an object value.\n *    3. All edges in the graph must have \"minlen\" and \"weight\" attributes.\n *\n * Postconditions:\n *\n *    1. All nodes in the graph will have an assigned \"rank\" attribute that has\n *       been optimized by the network simplex algorithm. Ranks start at 0.\n *\n *\n * A rough sketch of the algorithm is as follows:\n *\n *    1. Assign initial ranks to each node. We use the longest path algorithm,\n *       which assigns ranks to the lowest position possible. In general this\n *       leads to very wide bottom ranks and unnecessarily long edges.\n *    2. Construct a feasible tight tree. A tight tree is one such that all\n *       edges in the tree have no slack (difference between length of edge\n *       and minlen for the edge). This by itself greatly improves the assigned\n *       rankings by shorting edges.\n *    3. Iteratively find edges that have negative cut values. Generally a\n *       negative cut value indicates that the edge could be removed and a new\n *       tree edge could be added to produce a more compact graph.\n *\n * Much of the algorithms here are derived from Gansner, et al., \"A Technique\n * for Drawing Directed Graphs.\" The structure of the file roughly follows the\n * structure of the overall algorithm.\n */\nfunction networkSimplex(g) {\n  g = simplify(g);\n  longestPath(g);\n  var t = feasibleTree(g);\n  initLowLimValues(t);\n  initCutValues(t, g);\n\n  var e, f;\n  while ((e = leaveEdge(t))) {\n    f = enterEdge(t, g, e);\n    exchangeEdges(t, g, e, f);\n  }\n}\n\n/*\n * Initializes cut values for all edges in the tree.\n */\nfunction initCutValues(t, g) {\n  var vs = alg.postorder(t, t.nodes());\n  vs = vs.slice(0, vs.length - 1);\n  _.forEach(vs, function (v) {\n    assignCutValue(t, g, v);\n  });\n}\n\nfunction assignCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  t.edge(child, parent).cutvalue = calcCutValue(t, g, child);\n}\n\n/*\n * Given the tight tree, its graph, and a child in the graph calculate and\n * return the cut value for the edge between the child and its parent.\n */\nfunction calcCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  // True if the child is on the tail end of the edge in the directed graph\n  var childIsTail = true;\n  // The graph's view of the tree edge we're inspecting\n  var graphEdge = g.edge(child, parent);\n  // The accumulated cut value for the edge between this node and its parent\n  var cutValue = 0;\n\n  if (!graphEdge) {\n    childIsTail = false;\n    graphEdge = g.edge(parent, child);\n  }\n\n  cutValue = graphEdge.weight;\n\n  _.forEach(g.nodeEdges(child), function (e) {\n    var isOutEdge = e.v === child,\n      other = isOutEdge ? e.w : e.v;\n\n    if (other !== parent) {\n      var pointsToHead = isOutEdge === childIsTail,\n        otherWeight = g.edge(e).weight;\n\n      cutValue += pointsToHead ? otherWeight : -otherWeight;\n      if (isTreeEdge(t, child, other)) {\n        var otherCutValue = t.edge(child, other).cutvalue;\n        cutValue += pointsToHead ? -otherCutValue : otherCutValue;\n      }\n    }\n  });\n\n  return cutValue;\n}\n\nfunction initLowLimValues(tree, root) {\n  if (arguments.length < 2) {\n    root = tree.nodes()[0];\n  }\n  dfsAssignLowLim(tree, {}, 1, root);\n}\n\nfunction dfsAssignLowLim(tree, visited, nextLim, v, parent) {\n  var low = nextLim;\n  var label = tree.node(v);\n\n  visited[v] = true;\n  _.forEach(tree.neighbors(v), function (w) {\n    if (!Object.prototype.hasOwnProperty.call(visited, w)) {\n      nextLim = dfsAssignLowLim(tree, visited, nextLim, w, v);\n    }\n  });\n\n  label.low = low;\n  label.lim = nextLim++;\n  if (parent) {\n    label.parent = parent;\n  } else {\n    // TODO should be able to remove this when we incrementally update low lim\n    delete label.parent;\n  }\n\n  return nextLim;\n}\n\nfunction leaveEdge(tree) {\n  return _.find(tree.edges(), function (e) {\n    return tree.edge(e).cutvalue < 0;\n  });\n}\n\nfunction enterEdge(t, g, edge) {\n  var v = edge.v;\n  var w = edge.w;\n\n  // For the rest of this function we assume that v is the tail and w is the\n  // head, so if we don't have this edge in the graph we should flip it to\n  // match the correct orientation.\n  if (!g.hasEdge(v, w)) {\n    v = edge.w;\n    w = edge.v;\n  }\n\n  var vLabel = t.node(v);\n  var wLabel = t.node(w);\n  var tailLabel = vLabel;\n  var flip = false;\n\n  // If the root is in the tail of the edge then we need to flip the logic that\n  // checks for the head and tail nodes in the candidates function below.\n  if (vLabel.lim > wLabel.lim) {\n    tailLabel = wLabel;\n    flip = true;\n  }\n\n  var candidates = _.filter(g.edges(), function (edge) {\n    return (\n      flip === isDescendant(t, t.node(edge.v), tailLabel) &&\n      flip !== isDescendant(t, t.node(edge.w), tailLabel)\n    );\n  });\n\n  return _.minBy(candidates, function (edge) {\n    return slack(g, edge);\n  });\n}\n\nfunction exchangeEdges(t, g, e, f) {\n  var v = e.v;\n  var w = e.w;\n  t.removeEdge(v, w);\n  t.setEdge(f.v, f.w, {});\n  initLowLimValues(t);\n  initCutValues(t, g);\n  updateRanks(t, g);\n}\n\nfunction updateRanks(t, g) {\n  var root = _.find(t.nodes(), function (v) {\n    return !g.node(v).parent;\n  });\n  var vs = alg.preorder(t, root);\n  vs = vs.slice(1);\n  _.forEach(vs, function (v) {\n    var parent = t.node(v).parent,\n      edge = g.edge(v, parent),\n      flipped = false;\n\n    if (!edge) {\n      edge = g.edge(parent, v);\n      flipped = true;\n    }\n\n    g.node(v).rank = g.node(parent).rank + (flipped ? edge.minlen : -edge.minlen);\n  });\n}\n\n/*\n * Returns true if the edge is in the tree.\n */\nfunction isTreeEdge(tree, u, v) {\n  return tree.hasEdge(u, v);\n}\n\n/*\n * Returns true if the specified node is descendant of the root node per the\n * assigned low and lim attributes in the tree.\n */\nfunction isDescendant(tree, vLabel, rootLabel) {\n  return rootLabel.low <= vLabel.lim && vLabel.lim <= rootLabel.lim;\n}\n", "import { feasibleTree } from './feasible-tree.js';\nimport { networkSimplex } from './network-simplex.js';\nimport { longestPath } from './util.js';\n\nexport { rank };\n\n/*\n * Assigns a rank to each node in the input graph that respects the \"minlen\"\n * constraint specified on edges between nodes.\n *\n * This basic structure is derived from <PERSON><PERSON><PERSON>, et al., \"A Technique for\n * Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a connected DAG\n *    2. Graph nodes must be objects\n *    3. Graph edges must have \"weight\" and \"minlen\" attributes\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have a \"rank\" attribute based on the results of the\n *       algorithm. Ranks can start at any index (including negative), we'll\n *       fix them up later.\n */\nfunction rank(g) {\n  switch (g.graph().ranker) {\n    case 'network-simplex':\n      networkSimplexRanker(g);\n      break;\n    case 'tight-tree':\n      tightTreeRanker(g);\n      break;\n    case 'longest-path':\n      longestPathRanker(g);\n      break;\n    default:\n      networkSimplexRanker(g);\n  }\n}\n\n// A fast and simple ranker, but results are far from optimal.\nvar longestPathRanker = longestPath;\n\nfunction tightTreeRanker(g) {\n  longestPath(g);\n  feasibleTree(g);\n}\n\nfunction networkSimplexRanker(g) {\n  networkSimplex(g);\n}\n", "import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { run, cleanup };\n\n/*\n * A nesting graph creates dummy nodes for the tops and bottoms of subgraphs,\n * adds appropriate edges to ensure that all cluster nodes are placed between\n * these boundries, and ensures that the graph is connected.\n *\n * In addition we ensure, through the use of the minlen property, that nodes\n * and subgraph border nodes to not end up on the same rank.\n *\n * Preconditions:\n *\n *    1. Input graph is a DAG\n *    2. Nodes in the input graph has a minlen attribute\n *\n * Postconditions:\n *\n *    1. Input graph is connected.\n *    2. Dummy nodes are added for the tops and bottoms of subgraphs.\n *    3. The minlen attribute for nodes is adjusted to ensure nodes do not\n *       get placed on the same rank as subgraph border nodes.\n *\n * The nesting graph idea comes from <PERSON><PERSON>, \"Layout of Compound Directed\n * Graphs.\"\n */\nfunction run(g) {\n  var root = util.addDummyNode(g, 'root', {}, '_root');\n  var depths = treeDepths(g);\n  var height = _.max(_.values(depths)) - 1; // Note: depths is an Object not an array\n  var nodeSep = 2 * height + 1;\n\n  g.graph().nestingRoot = root;\n\n  // Multiply minlen by nodeSep to align nodes on non-border ranks.\n  _.forEach(g.edges(), function (e) {\n    g.edge(e).minlen *= nodeSep;\n  });\n\n  // Calculate a weight that is sufficient to keep subgraphs vertically compact\n  var weight = sumWeights(g) + 1;\n\n  // Create border nodes and link them up\n  _.forEach(g.children(), function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n  });\n\n  // Save the multiplier for node layers for later removal of empty border\n  // layers.\n  g.graph().nodeRankFactor = nodeSep;\n}\n\nfunction dfs(g, root, nodeSep, weight, height, depths, v) {\n  var children = g.children(v);\n  if (!children.length) {\n    if (v !== root) {\n      g.setEdge(root, v, { weight: 0, minlen: nodeSep });\n    }\n    return;\n  }\n\n  var top = util.addBorderNode(g, '_bt');\n  var bottom = util.addBorderNode(g, '_bb');\n  var label = g.node(v);\n\n  g.setParent(top, v);\n  label.borderTop = top;\n  g.setParent(bottom, v);\n  label.borderBottom = bottom;\n\n  _.forEach(children, function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n\n    var childNode = g.node(child);\n    var childTop = childNode.borderTop ? childNode.borderTop : child;\n    var childBottom = childNode.borderBottom ? childNode.borderBottom : child;\n    var thisWeight = childNode.borderTop ? weight : 2 * weight;\n    var minlen = childTop !== childBottom ? 1 : height - depths[v] + 1;\n\n    g.setEdge(top, childTop, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true,\n    });\n\n    g.setEdge(childBottom, bottom, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true,\n    });\n  });\n\n  if (!g.parent(v)) {\n    g.setEdge(root, top, { weight: 0, minlen: height + depths[v] });\n  }\n}\n\nfunction treeDepths(g) {\n  var depths = {};\n  function dfs(v, depth) {\n    var children = g.children(v);\n    if (children && children.length) {\n      _.forEach(children, function (child) {\n        dfs(child, depth + 1);\n      });\n    }\n    depths[v] = depth;\n  }\n  _.forEach(g.children(), function (v) {\n    dfs(v, 1);\n  });\n  return depths;\n}\n\nfunction sumWeights(g) {\n  return _.reduce(\n    g.edges(),\n    function (acc, e) {\n      return acc + g.edge(e).weight;\n    },\n    0,\n  );\n}\n\nfunction cleanup(g) {\n  var graphLabel = g.graph();\n  g.removeNode(graphLabel.nestingRoot);\n  delete graphLabel.nestingRoot;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.nestingEdge) {\n      g.removeEdge(e);\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { addSubgraphConstraints };\n\nfunction addSubgraphConstraints(g, cg, vs) {\n  var prev = {},\n    rootPrev;\n\n  _.forEach(vs, function (v) {\n    var child = g.parent(v),\n      parent,\n      prevChild;\n    while (child) {\n      parent = g.parent(child);\n      if (parent) {\n        prevChild = prev[parent];\n        prev[parent] = child;\n      } else {\n        prevChild = rootPrev;\n        rootPrev = child;\n      }\n      if (prevChild && prevChild !== child) {\n        cg.setEdge(prevChild, child);\n        return;\n      }\n      child = parent;\n    }\n  });\n\n  /*\n  function dfs(v) {\n    var children = v ? g.children(v) : g.children();\n    if (children.length) {\n      var min = Number.POSITIVE_INFINITY,\n          subgraphs = [];\n      _.each(children, function(child) {\n        var childMin = dfs(child);\n        if (g.children(child).length) {\n          subgraphs.push({ v: child, order: childMin });\n        }\n        min = Math.min(min, childMin);\n      });\n      _.reduce(_.sortBy(subgraphs, \"order\"), function(prev, curr) {\n        cg.setEdge(prev.v, curr.v);\n        return curr;\n      });\n      return min;\n    }\n    return g.node(v).order;\n  }\n  dfs(undefined);\n  */\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\n\nexport { buildLayerGraph };\n\n/*\n * Constructs a graph that can be used to sort a layer of nodes. The graph will\n * contain all base and subgraph nodes from the request layer in their original\n * hierarchy and any edges that are incident on these nodes and are of the type\n * requested by the \"relationship\" parameter.\n *\n * Nodes from the requested rank that do not have parents are assigned a root\n * node in the output graph, which is set in the root graph attribute. This\n * makes it easy to walk the hierarchy of movable nodes during ordering.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG\n *    2. Base nodes in the input graph have a rank attribute\n *    3. Subgraph nodes in the input graph has minRank and maxRank attributes\n *    4. Edges have an assigned weight\n *\n * Post-conditions:\n *\n *    1. Output graph has all nodes in the movable rank with preserved\n *       hierarchy.\n *    2. Root nodes in the movable layer are made children of the node\n *       indicated by the root attribute of the graph.\n *    3. Non-movable nodes incident on movable nodes, selected by the\n *       relationship parameter, are included in the graph (without hierarchy).\n *    4. Edges incident on movable nodes, selected by the relationship\n *       parameter, are added to the output graph.\n *    5. The weights for copied edges are aggregated as need, since the output\n *       graph is not a multi-graph.\n */\nfunction buildLayerGraph(g, rank, relationship) {\n  var root = createRootNode(g),\n    result = new Graph({ compound: true })\n      .setGraph({ root: root })\n      .setDefaultNodeLabel(function (v) {\n        return g.node(v);\n      });\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v),\n      parent = g.parent(v);\n\n    if (node.rank === rank || (node.minRank <= rank && rank <= node.maxRank)) {\n      result.setNode(v);\n      result.setParent(v, parent || root);\n\n      // This assumes we have only short edges!\n      _.forEach(g[relationship](v), function (e) {\n        var u = e.v === v ? e.w : e.v,\n          edge = result.edge(u, v),\n          weight = !_.isUndefined(edge) ? edge.weight : 0;\n        result.setEdge(u, v, { weight: g.edge(e).weight + weight });\n      });\n\n      if (Object.prototype.hasOwnProperty.call(node, 'minRank')) {\n        result.setNode(v, {\n          borderLeft: node.borderLeft[rank],\n          borderRight: node.borderRight[rank],\n        });\n      }\n    }\n  });\n\n  return result;\n}\n\nfunction createRootNode(g) {\n  var v;\n  while (g.hasNode((v = _.uniqueId('_root'))));\n  return v;\n}\n", "import * as _ from 'lodash-es';\n\nexport { crossCount };\n\n/*\n * A function that takes a layering (an array of layers, each with an array of\n * ordererd nodes) and a graph and returns a weighted crossing count.\n *\n * Pre-conditions:\n *\n *    1. Input graph must be simple (not a multigraph), directed, and include\n *       only simple edges.\n *    2. Edges in the input graph must have assigned weights.\n *\n * Post-conditions:\n *\n *    1. The graph and layering matrix are left unchanged.\n *\n * This algorithm is derived from <PERSON><PERSON>, et al., \"Bilayer Cross Counting.\"\n */\nfunction crossCount(g, layering) {\n  var cc = 0;\n  for (var i = 1; i < layering.length; ++i) {\n    cc += twoLayerCrossCount(g, layering[i - 1], layering[i]);\n  }\n  return cc;\n}\n\nfunction twoLayerCrossCount(g, northLayer, southLayer) {\n  // Sort all of the edges between the north and south layers by their position\n  // in the north layer and then the south. Map these edges to the position of\n  // their head in the south layer.\n  var southPos = _.zipObject(\n    southLayer,\n    _.map(southLayer, function (v, i) {\n      return i;\n    }),\n  );\n  var southEntries = _.flatten(\n    _.map(northLayer, function (v) {\n      return _.sortBy(\n        _.map(g.outEdges(v), function (e) {\n          return { pos: southPos[e.w], weight: g.edge(e).weight };\n        }),\n        'pos',\n      );\n    }),\n  );\n\n  // Build the accumulator tree\n  var firstIndex = 1;\n  while (firstIndex < southLayer.length) firstIndex <<= 1;\n  var treeSize = 2 * firstIndex - 1;\n  firstIndex -= 1;\n  var tree = _.map(new Array(treeSize), function () {\n    return 0;\n  });\n\n  // Calculate the weighted crossings\n  var cc = 0;\n  _.forEach(\n    // @ts-expect-error\n    southEntries.forEach(function (entry) {\n      var index = entry.pos + firstIndex;\n      tree[index] += entry.weight;\n      var weightSum = 0;\n      // @ts-expect-error\n      while (index > 0) {\n        // @ts-expect-error\n        if (index % 2) {\n          weightSum += tree[index + 1];\n        }\n        // @ts-expect-error\n        index = (index - 1) >> 1;\n        tree[index] += entry.weight;\n      }\n      cc += entry.weight * weightSum;\n    }),\n  );\n\n  return cc;\n}\n", "import * as _ from 'lodash-es';\n\n/*\n * Assigns an initial order value for each node by performing a DFS search\n * starting from nodes in the first rank. Nodes are assigned an order in their\n * rank as they are first visited.\n *\n * This approach comes from <PERSON><PERSON><PERSON>, et al., \"A Technique for Drawing Directed\n * Graphs.\"\n *\n * Returns a layering matrix with an array per layer and each layer sorted by\n * the order of its nodes.\n */\nexport function initOrder(g) {\n  var visited = {};\n  var simpleNodes = _.filter(g.nodes(), function (v) {\n    return !g.children(v).length;\n  });\n  var maxRank = _.max(\n    _.map(simpleNodes, function (v) {\n      return g.node(v).rank;\n    }),\n  );\n  var layers = _.map(_.range(maxRank + 1), function () {\n    return [];\n  });\n\n  function dfs(v) {\n    if (_.has(visited, v)) return;\n    visited[v] = true;\n    var node = g.node(v);\n    layers[node.rank].push(v);\n    _.forEach(g.successors(v), dfs);\n  }\n\n  var orderedVs = _.sortBy(simpleNodes, function (v) {\n    return g.node(v).rank;\n  });\n  _.forEach(orderedVs, dfs);\n\n  return layers;\n}\n", "import * as _ from 'lodash-es';\n\nexport { barycenter };\n\nfunction barycenter(g, movable) {\n  return _.map(movable, function (v) {\n    var inV = g.inEdges(v);\n    if (!inV.length) {\n      return { v: v };\n    } else {\n      var result = _.reduce(\n        inV,\n        function (acc, e) {\n          var edge = g.edge(e),\n            nodeU = g.node(e.v);\n          return {\n            sum: acc.sum + edge.weight * nodeU.order,\n            weight: acc.weight + edge.weight,\n          };\n        },\n        { sum: 0, weight: 0 },\n      );\n\n      return {\n        v: v,\n        barycenter: result.sum / result.weight,\n        weight: result.weight,\n      };\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { resolveConflicts };\n\n/*\n * Given a list of entries of the form {v, barycenter, weight} and a\n * constraint graph this function will resolve any conflicts between the\n * constraint graph and the barycenters for the entries. If the barycenters for\n * an entry would violate a constraint in the constraint graph then we coalesce\n * the nodes in the conflict into a new node that respects the contraint and\n * aggregates barycenter and weight information.\n *\n * This implementation is based on the description in Forster, \"A Fast and\n * Simple Hueristic for Constrained Two-Level Crossing Reduction,\" thought it\n * differs in some specific details.\n *\n * Pre-conditions:\n *\n *    1. Each entry has the form {v, barycenter, weight}, or if the node has\n *       no barycenter, then {v}.\n *\n * Returns:\n *\n *    A new list of entries of the form {vs, i, barycenter, weight}. The list\n *    `vs` may either be a singleton or it may be an aggregation of nodes\n *    ordered such that they do not violate constraints from the constraint\n *    graph. The property `i` is the lowest original index of any of the\n *    elements in `vs`.\n */\nfunction resolveConflicts(entries, cg) {\n  var mappedEntries = {};\n  _.forEach(entries, function (entry, i) {\n    var tmp = (mappedEntries[entry.v] = {\n      indegree: 0,\n      in: [],\n      out: [],\n      vs: [entry.v],\n      i: i,\n    });\n    if (!_.isUndefined(entry.barycenter)) {\n      // @ts-expect-error\n      tmp.barycenter = entry.barycenter;\n      // @ts-expect-error\n      tmp.weight = entry.weight;\n    }\n  });\n\n  _.forEach(cg.edges(), function (e) {\n    var entryV = mappedEntries[e.v];\n    var entryW = mappedEntries[e.w];\n    if (!_.isUndefined(entryV) && !_.isUndefined(entryW)) {\n      entryW.indegree++;\n      entryV.out.push(mappedEntries[e.w]);\n    }\n  });\n\n  var sourceSet = _.filter(mappedEntries, function (entry) {\n    // @ts-expect-error\n    return !entry.indegree;\n  });\n\n  return doResolveConflicts(sourceSet);\n}\n\nfunction doResolveConflicts(sourceSet) {\n  var entries = [];\n\n  function handleIn(vEntry) {\n    return function (uEntry) {\n      if (uEntry.merged) {\n        return;\n      }\n      if (\n        _.isUndefined(uEntry.barycenter) ||\n        _.isUndefined(vEntry.barycenter) ||\n        uEntry.barycenter >= vEntry.barycenter\n      ) {\n        mergeEntries(vEntry, uEntry);\n      }\n    };\n  }\n\n  function handleOut(vEntry) {\n    return function (wEntry) {\n      wEntry['in'].push(vEntry);\n      if (--wEntry.indegree === 0) {\n        sourceSet.push(wEntry);\n      }\n    };\n  }\n\n  while (sourceSet.length) {\n    var entry = sourceSet.pop();\n    entries.push(entry);\n    _.forEach(entry['in'].reverse(), handleIn(entry));\n    _.forEach(entry.out, handleOut(entry));\n  }\n\n  return _.map(\n    _.filter(entries, function (entry) {\n      return !entry.merged;\n    }),\n    function (entry) {\n      return _.pick(entry, ['vs', 'i', 'barycenter', 'weight']);\n    },\n  );\n}\n\nfunction mergeEntries(target, source) {\n  var sum = 0;\n  var weight = 0;\n\n  if (target.weight) {\n    sum += target.barycenter * target.weight;\n    weight += target.weight;\n  }\n\n  if (source.weight) {\n    sum += source.barycenter * source.weight;\n    weight += source.weight;\n  }\n\n  target.vs = source.vs.concat(target.vs);\n  target.barycenter = sum / weight;\n  target.weight = weight;\n  target.i = Math.min(source.i, target.i);\n  source.merged = true;\n}\n", "import * as _ from 'lodash-es';\nimport * as util from '../util.js';\n\nexport { sort };\n\nfunction sort(entries, biasRight) {\n  var parts = util.partition(entries, function (entry) {\n    return Object.prototype.hasOwnProperty.call(entry, 'barycenter');\n  });\n  var sortable = parts.lhs,\n    unsortable = _.sortBy(parts.rhs, function (entry) {\n      return -entry.i;\n    }),\n    vs = [],\n    sum = 0,\n    weight = 0,\n    vsIndex = 0;\n\n  sortable.sort(compareWithBias(!!biasRight));\n\n  vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n\n  _.forEach(sortable, function (entry) {\n    vsIndex += entry.vs.length;\n    vs.push(entry.vs);\n    sum += entry.barycenter * entry.weight;\n    weight += entry.weight;\n    vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n  });\n\n  var result = { vs: _.flatten(vs) };\n  if (weight) {\n    result.barycenter = sum / weight;\n    result.weight = weight;\n  }\n  return result;\n}\n\nfunction consumeUnsortable(vs, unsortable, index) {\n  var last;\n  while (unsortable.length && (last = _.last(unsortable)).i <= index) {\n    unsortable.pop();\n    vs.push(last.vs);\n    index++;\n  }\n  return index;\n}\n\nfunction compareWithBias(bias) {\n  return function (entryV, entryW) {\n    if (entryV.barycenter < entryW.barycenter) {\n      return -1;\n    } else if (entryV.barycenter > entryW.barycenter) {\n      return 1;\n    }\n\n    return !bias ? entryV.i - entryW.i : entryW.i - entryV.i;\n  };\n}\n", "import * as _ from 'lodash-es';\nimport { barycenter } from './barycenter.js';\nimport { resolveConflicts } from './resolve-conflicts.js';\nimport { sort } from './sort.js';\n\nexport { sortSubgraph };\n\nfunction sortSubgraph(g, v, cg, biasRight) {\n  var movable = g.children(v);\n  var node = g.node(v);\n  var bl = node ? node.borderLeft : undefined;\n  var br = node ? node.borderRight : undefined;\n  var subgraphs = {};\n\n  if (bl) {\n    movable = _.filter(movable, function (w) {\n      return w !== bl && w !== br;\n    });\n  }\n\n  var barycenters = barycenter(g, movable);\n  _.forEach(barycenters, function (entry) {\n    if (g.children(entry.v).length) {\n      var subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);\n      subgraphs[entry.v] = subgraphResult;\n      if (Object.prototype.hasOwnProperty.call(subgraphResult, 'barycenter')) {\n        mergeBarycenters(entry, subgraphResult);\n      }\n    }\n  });\n\n  var entries = resolveConflicts(barycenters, cg);\n  expandSubgraphs(entries, subgraphs);\n\n  var result = sort(entries, biasRight);\n\n  if (bl) {\n    result.vs = _.flatten([bl, result.vs, br]);\n    if (g.predecessors(bl).length) {\n      var blPred = g.node(g.predecessors(bl)[0]),\n        brPred = g.node(g.predecessors(br)[0]);\n      if (!Object.prototype.hasOwnProperty.call(result, 'barycenter')) {\n        result.barycenter = 0;\n        result.weight = 0;\n      }\n      result.barycenter =\n        (result.barycenter * result.weight + blPred.order + brPred.order) / (result.weight + 2);\n      result.weight += 2;\n    }\n  }\n\n  return result;\n}\n\nfunction expandSubgraphs(entries, subgraphs) {\n  _.forEach(entries, function (entry) {\n    entry.vs = _.flatten(\n      entry.vs.map(function (v) {\n        if (subgraphs[v]) {\n          return subgraphs[v].vs;\n        }\n        return v;\n      }),\n    );\n  });\n}\n\nfunction mergeBarycenters(target, other) {\n  if (!_.isUndefined(target.barycenter)) {\n    target.barycenter =\n      (target.barycenter * target.weight + other.barycenter * other.weight) /\n      (target.weight + other.weight);\n    target.weight += other.weight;\n  } else {\n    target.barycenter = other.barycenter;\n    target.weight = other.weight;\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport * as util from '../util.js';\nimport { addSubgraphConstraints } from './add-subgraph-constraints.js';\nimport { buildLayerGraph } from './build-layer-graph.js';\nimport { crossCount } from './cross-count.js';\nimport { initOrder } from './init-order.js';\nimport { sortSubgraph } from './sort-subgraph.js';\n\nexport { order };\n\n/*\n * Applies heuristics to minimize edge crossings in the graph and sets the best\n * order solution as an order attribute on each node.\n *\n * Pre-conditions:\n *\n *    1. Graph must be DAG\n *    2. Graph nodes must be objects with a \"rank\" attribute\n *    3. Graph edges must have the \"weight\" attribute\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have an \"order\" attribute based on the results of the\n *       algorithm.\n */\nfunction order(g) {\n  var maxRank = util.maxRank(g),\n    downLayerGraphs = buildLayerGraphs(g, _.range(1, maxRank + 1), 'inEdges'),\n    upLayerGraphs = buildLayerGraphs(g, _.range(maxRank - 1, -1, -1), 'outEdges');\n\n  var layering = initOrder(g);\n  assignOrder(g, layering);\n\n  var bestCC = Number.POSITIVE_INFINITY,\n    best;\n\n  for (var i = 0, lastBest = 0; lastBest < 4; ++i, ++lastBest) {\n    sweepLayerGraphs(i % 2 ? downLayerGraphs : upLayerGraphs, i % 4 >= 2);\n\n    layering = util.buildLayerMatrix(g);\n    var cc = crossCount(g, layering);\n    if (cc < bestCC) {\n      lastBest = 0;\n      best = _.cloneDeep(layering);\n      bestCC = cc;\n    }\n  }\n\n  assignOrder(g, best);\n}\n\nfunction buildLayerGraphs(g, ranks, relationship) {\n  return _.map(ranks, function (rank) {\n    return buildLayerGraph(g, rank, relationship);\n  });\n}\n\nfunction sweepLayerGraphs(layerGraphs, biasRight) {\n  var cg = new Graph();\n  _.forEach(layerGraphs, function (lg) {\n    var root = lg.graph().root;\n    var sorted = sortSubgraph(lg, root, cg, biasRight);\n    _.forEach(sorted.vs, function (v, i) {\n      lg.node(v).order = i;\n    });\n    addSubgraphConstraints(lg, cg, sorted.vs);\n  });\n}\n\nfunction assignOrder(g, layering) {\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, i) {\n      g.node(v).order = i;\n    });\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { parentDummyChains };\n\nfunction parentDummyChains(g) {\n  var postorderNums = postorder(g);\n\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var edgeObj = node.edgeObj;\n    var pathData = findPath(g, postorderNums, edgeObj.v, edgeObj.w);\n    var path = pathData.path;\n    var lca = pathData.lca;\n    var pathIdx = 0;\n    var pathV = path[pathIdx];\n    var ascending = true;\n\n    while (v !== edgeObj.w) {\n      node = g.node(v);\n\n      if (ascending) {\n        while ((pathV = path[pathIdx]) !== lca && g.node(pathV).maxRank < node.rank) {\n          pathIdx++;\n        }\n\n        if (pathV === lca) {\n          ascending = false;\n        }\n      }\n\n      if (!ascending) {\n        while (\n          pathIdx < path.length - 1 &&\n          g.node((pathV = path[pathIdx + 1])).minRank <= node.rank\n        ) {\n          pathIdx++;\n        }\n        pathV = path[pathIdx];\n      }\n\n      g.setParent(v, pathV);\n      v = g.successors(v)[0];\n    }\n  });\n}\n\n// Find a path from v to w through the lowest common ancestor (LCA). Return the\n// full path and the LCA.\nfunction findPath(g, postorderNums, v, w) {\n  var vPath = [];\n  var wPath = [];\n  var low = Math.min(postorderNums[v].low, postorderNums[w].low);\n  var lim = Math.max(postorderNums[v].lim, postorderNums[w].lim);\n  var parent;\n  var lca;\n\n  // Traverse up from v to find the LCA\n  parent = v;\n  do {\n    parent = g.parent(parent);\n    vPath.push(parent);\n  } while (parent && (postorderNums[parent].low > low || lim > postorderNums[parent].lim));\n  lca = parent;\n\n  // Traverse from w to LCA\n  parent = w;\n  while ((parent = g.parent(parent)) !== lca) {\n    wPath.push(parent);\n  }\n\n  return { path: vPath.concat(wPath.reverse()), lca: lca };\n}\n\nfunction postorder(g) {\n  var result = {};\n  var lim = 0;\n\n  function dfs(v) {\n    var low = lim;\n    _.forEach(g.children(v), dfs);\n    result[v] = { low: low, lim: lim++ };\n  }\n  _.forEach(g.children(), dfs);\n\n  return result;\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport * as util from '../util.js';\n\n/*\n * This module provides coordinate assignment based on <PERSON><PERSON> and <PERSON>, \"Fast\n * and Simple Horizontal Coordinate Assignment.\"\n */\n\nexport {\n  positionX,\n  findType1Conflicts,\n  findType2Conflicts,\n  addConflict,\n  hasConflict,\n  verticalAlignment,\n  horizontalCompaction,\n  alignCoordinates,\n  findSmallestWidthAlignment,\n  balance,\n};\n\n/*\n * Marks all edges in the graph with a type-1 conflict with the \"type1Conflict\"\n * property. A type-1 conflict is one where a non-inner segment crosses an\n * inner segment. An inner segment is an edge with both incident nodes marked\n * with the \"dummy\" property.\n *\n * This algorithm scans layer by layer, starting with the second, for type-1\n * conflicts between the current layer and the previous layer. For each layer\n * it scans the nodes from left to right until it reaches one that is incident\n * on an inner segment. It then scans predecessors to determine if they have\n * edges that cross that inner segment. At the end a final scan is done for all\n * nodes on the current rank to see if they cross the last visited inner\n * segment.\n *\n * This algorithm (safely) assumes that a dummy node will only be incident on a\n * single node in the layers being scanned.\n */\nfunction findType1Conflicts(g, layering) {\n  var conflicts = {};\n\n  function visitLayer(prevLayer, layer) {\n    var // last visited node in the previous layer that is incident on an inner\n      // segment.\n      k0 = 0,\n      // Tracks the last node in this layer scanned for crossings with a type-1\n      // segment.\n      scanPos = 0,\n      prevLayerLength = prevLayer.length,\n      lastNode = _.last(layer);\n\n    _.forEach(layer, function (v, i) {\n      var w = findOtherInnerSegmentNode(g, v),\n        k1 = w ? g.node(w).order : prevLayerLength;\n\n      if (w || v === lastNode) {\n        _.forEach(layer.slice(scanPos, i + 1), function (scanNode) {\n          _.forEach(g.predecessors(scanNode), function (u) {\n            var uLabel = g.node(u),\n              uPos = uLabel.order;\n            if ((uPos < k0 || k1 < uPos) && !(uLabel.dummy && g.node(scanNode).dummy)) {\n              addConflict(conflicts, u, scanNode);\n            }\n          });\n        });\n        // @ts-expect-error\n        scanPos = i + 1;\n        k0 = k1;\n      }\n    });\n\n    return layer;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findType2Conflicts(g, layering) {\n  var conflicts = {};\n\n  function scan(south, southPos, southEnd, prevNorthBorder, nextNorthBorder) {\n    var v;\n    _.forEach(_.range(southPos, southEnd), function (i) {\n      v = south[i];\n      if (g.node(v).dummy) {\n        _.forEach(g.predecessors(v), function (u) {\n          var uNode = g.node(u);\n          if (uNode.dummy && (uNode.order < prevNorthBorder || uNode.order > nextNorthBorder)) {\n            addConflict(conflicts, u, v);\n          }\n        });\n      }\n    });\n  }\n\n  function visitLayer(north, south) {\n    var prevNorthPos = -1,\n      nextNorthPos,\n      southPos = 0;\n\n    _.forEach(south, function (v, southLookahead) {\n      if (g.node(v).dummy === 'border') {\n        var predecessors = g.predecessors(v);\n        if (predecessors.length) {\n          nextNorthPos = g.node(predecessors[0]).order;\n          scan(south, southPos, southLookahead, prevNorthPos, nextNorthPos);\n          // @ts-expect-error\n          southPos = southLookahead;\n          prevNorthPos = nextNorthPos;\n        }\n      }\n      scan(south, southPos, south.length, nextNorthPos, north.length);\n    });\n\n    return south;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findOtherInnerSegmentNode(g, v) {\n  if (g.node(v).dummy) {\n    return _.find(g.predecessors(v), function (u) {\n      return g.node(u).dummy;\n    });\n  }\n}\n\nfunction addConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n\n  var conflictsV = conflicts[v];\n  if (!conflictsV) {\n    conflicts[v] = conflictsV = {};\n  }\n  conflictsV[w] = true;\n}\n\nfunction hasConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return !!conflicts[v] && Object.prototype.hasOwnProperty.call(conflicts[v], w);\n}\n\n/*\n * Try to align nodes into vertical \"blocks\" where possible. This algorithm\n * attempts to align a node with one of its median neighbors. If the edge\n * connecting a neighbor is a type-1 conflict then we ignore that possibility.\n * If a previous node has already formed a block with a node after the node\n * we're trying to form a block with, we also ignore that possibility - our\n * blocks would be split in that scenario.\n */\nfunction verticalAlignment(g, layering, conflicts, neighborFn) {\n  var root = {},\n    align = {},\n    pos = {};\n\n  // We cache the position here based on the layering because the graph and\n  // layering may be out of sync. The layering matrix is manipulated to\n  // generate different extreme alignments.\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, order) {\n      root[v] = v;\n      align[v] = v;\n      pos[v] = order;\n    });\n  });\n\n  _.forEach(layering, function (layer) {\n    var prevIdx = -1;\n    _.forEach(layer, function (v) {\n      var ws = neighborFn(v);\n      if (ws.length) {\n        ws = _.sortBy(ws, function (w) {\n          return pos[w];\n        });\n        var mp = (ws.length - 1) / 2;\n        for (var i = Math.floor(mp), il = Math.ceil(mp); i <= il; ++i) {\n          var w = ws[i];\n          if (align[v] === v && prevIdx < pos[w] && !hasConflict(conflicts, v, w)) {\n            align[w] = v;\n            align[v] = root[v] = root[w];\n            prevIdx = pos[w];\n          }\n        }\n      }\n    });\n  });\n\n  return { root: root, align: align };\n}\n\nfunction horizontalCompaction(g, layering, root, align, reverseSep) {\n  // This portion of the algorithm differs from BK due to a number of problems.\n  // Instead of their algorithm we construct a new block graph and do two\n  // sweeps. The first sweep places blocks with the smallest possible\n  // coordinates. The second sweep removes unused space by moving blocks to the\n  // greatest coordinates without violating separation.\n  var xs = {},\n    blockG = buildBlockGraph(g, layering, root, reverseSep),\n    borderType = reverseSep ? 'borderLeft' : 'borderRight';\n\n  function iterate(setXsFunc, nextNodesFunc) {\n    var stack = blockG.nodes();\n    var elem = stack.pop();\n    var visited = {};\n    while (elem) {\n      if (visited[elem]) {\n        setXsFunc(elem);\n      } else {\n        visited[elem] = true;\n        stack.push(elem);\n        stack = stack.concat(nextNodesFunc(elem));\n      }\n\n      elem = stack.pop();\n    }\n  }\n\n  // First pass, assign smallest coordinates\n  function pass1(elem) {\n    xs[elem] = blockG.inEdges(elem).reduce(function (acc, e) {\n      return Math.max(acc, xs[e.v] + blockG.edge(e));\n    }, 0);\n  }\n\n  // Second pass, assign greatest coordinates\n  function pass2(elem) {\n    var min = blockG.outEdges(elem).reduce(function (acc, e) {\n      return Math.min(acc, xs[e.w] - blockG.edge(e));\n    }, Number.POSITIVE_INFINITY);\n\n    var node = g.node(elem);\n    if (min !== Number.POSITIVE_INFINITY && node.borderType !== borderType) {\n      xs[elem] = Math.max(xs[elem], min);\n    }\n  }\n\n  iterate(pass1, blockG.predecessors.bind(blockG));\n  iterate(pass2, blockG.successors.bind(blockG));\n\n  // Assign x coordinates to all nodes\n  _.forEach(align, function (v) {\n    xs[v] = xs[root[v]];\n  });\n\n  return xs;\n}\n\nfunction buildBlockGraph(g, layering, root, reverseSep) {\n  var blockGraph = new Graph(),\n    graphLabel = g.graph(),\n    sepFn = sep(graphLabel.nodesep, graphLabel.edgesep, reverseSep);\n\n  _.forEach(layering, function (layer) {\n    var u;\n    _.forEach(layer, function (v) {\n      var vRoot = root[v];\n      blockGraph.setNode(vRoot);\n      if (u) {\n        var uRoot = root[u],\n          prevMax = blockGraph.edge(uRoot, vRoot);\n        blockGraph.setEdge(uRoot, vRoot, Math.max(sepFn(g, v, u), prevMax || 0));\n      }\n      u = v;\n    });\n  });\n\n  return blockGraph;\n}\n\n/*\n * Returns the alignment that has the smallest width of the given alignments.\n */\nfunction findSmallestWidthAlignment(g, xss) {\n  return _.minBy(_.values(xss), function (xs) {\n    var max = Number.NEGATIVE_INFINITY;\n    var min = Number.POSITIVE_INFINITY;\n\n    _.forIn(xs, function (x, v) {\n      var halfWidth = width(g, v) / 2;\n\n      max = Math.max(x + halfWidth, max);\n      min = Math.min(x - halfWidth, min);\n    });\n\n    return max - min;\n  });\n}\n\n/*\n * Align the coordinates of each of the layout alignments such that\n * left-biased alignments have their minimum coordinate at the same point as\n * the minimum coordinate of the smallest width alignment and right-biased\n * alignments have their maximum coordinate at the same point as the maximum\n * coordinate of the smallest width alignment.\n */\nfunction alignCoordinates(xss, alignTo) {\n  var alignToVals = _.values(alignTo),\n    alignToMin = _.min(alignToVals),\n    alignToMax = _.max(alignToVals);\n\n  _.forEach(['u', 'd'], function (vert) {\n    _.forEach(['l', 'r'], function (horiz) {\n      var alignment = vert + horiz,\n        xs = xss[alignment],\n        delta;\n      if (xs === alignTo) return;\n\n      var xsVals = _.values(xs);\n      delta = horiz === 'l' ? alignToMin - _.min(xsVals) : alignToMax - _.max(xsVals);\n\n      if (delta) {\n        xss[alignment] = _.mapValues(xs, function (x) {\n          return x + delta;\n        });\n      }\n    });\n  });\n}\n\nfunction balance(xss, align) {\n  return _.mapValues(xss.ul, function (ignore, v) {\n    if (align) {\n      return xss[align.toLowerCase()][v];\n    } else {\n      var xs = _.sortBy(_.map(xss, v));\n      return (xs[1] + xs[2]) / 2;\n    }\n  });\n}\n\nfunction positionX(g) {\n  var layering = util.buildLayerMatrix(g);\n  var conflicts = _.merge(findType1Conflicts(g, layering), findType2Conflicts(g, layering));\n\n  var xss = {};\n  var adjustedLayering;\n  _.forEach(['u', 'd'], function (vert) {\n    adjustedLayering = vert === 'u' ? layering : _.values(layering).reverse();\n    _.forEach(['l', 'r'], function (horiz) {\n      if (horiz === 'r') {\n        adjustedLayering = _.map(adjustedLayering, function (inner) {\n          return _.values(inner).reverse();\n        });\n      }\n\n      var neighborFn = (vert === 'u' ? g.predecessors : g.successors).bind(g);\n      var align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);\n      var xs = horizontalCompaction(g, adjustedLayering, align.root, align.align, horiz === 'r');\n      if (horiz === 'r') {\n        xs = _.mapValues(xs, function (x) {\n          return -x;\n        });\n      }\n      xss[vert + horiz] = xs;\n    });\n  });\n\n  var smallestWidth = findSmallestWidthAlignment(g, xss);\n  alignCoordinates(xss, smallestWidth);\n  return balance(xss, g.graph().align);\n}\n\nfunction sep(nodeSep, edgeSep, reverseSep) {\n  return function (g, v, w) {\n    var vLabel = g.node(v);\n    var wLabel = g.node(w);\n    var sum = 0;\n    var delta;\n\n    sum += vLabel.width / 2;\n    if (Object.prototype.hasOwnProperty.call(vLabel, 'labelpos')) {\n      switch (vLabel.labelpos.toLowerCase()) {\n        case 'l':\n          delta = -vLabel.width / 2;\n          break;\n        case 'r':\n          delta = vLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    sum += (vLabel.dummy ? edgeSep : nodeSep) / 2;\n    sum += (wLabel.dummy ? edgeSep : nodeSep) / 2;\n\n    sum += wLabel.width / 2;\n    if (Object.prototype.hasOwnProperty.call(wLabel, 'labelpos')) {\n      switch (wLabel.labelpos.toLowerCase()) {\n        case 'l':\n          delta = wLabel.width / 2;\n          break;\n        case 'r':\n          delta = -wLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    return sum;\n  };\n}\n\nfunction width(g, v) {\n  return g.node(v).width;\n}\n", "import * as _ from 'lodash-es';\nimport * as util from '../util.js';\nimport { positionX } from './bk.js';\n\nexport { position };\n\nfunction position(g) {\n  g = util.asNonCompoundGraph(g);\n\n  positionY(g);\n  _.forOwn(positionX(g), function (x, v) {\n    g.node(v).x = x;\n  });\n}\n\nfunction positionY(g) {\n  var layering = util.buildLayerMatrix(g);\n  var rankSep = g.graph().ranksep;\n  var prevY = 0;\n  _.forEach(layering, function (layer) {\n    var maxHeight = _.max(\n      _.map(layer, function (v) {\n        return g.node(v).height;\n      }),\n    );\n    _.forEach(layer, function (v) {\n      g.node(v).y = prevY + maxHeight / 2;\n    });\n    prevY += maxHeight + rankSep;\n  });\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\nimport { addBorderSegments } from './add-border-segments.js';\nimport * as coordinateSystem from './coordinate-system.js';\nimport * as acyclic from './acyclic.js';\nimport * as normalize from './normalize.js';\nimport { rank } from './rank/index.js';\nimport * as nestingGraph from './nesting-graph.js';\nimport { order } from './order/index.js';\nimport { parentDummyChains } from './parent-dummy-chains.js';\nimport { position } from './position/index.js';\nimport * as util from './util.js';\n\nexport { layout };\n\nfunction layout(g, opts) {\n  var time = opts && opts.debugTiming ? util.time : util.notime;\n  time('layout', () => {\n    var layoutGraph = time('  buildLayoutGraph', () => buildLayoutGraph(g));\n    time('  runLayout', () => runLayout(layoutGraph, time));\n    time('  updateInputGraph', () => updateInputGraph(g, layoutGraph));\n  });\n}\n\nfunction runLayout(g, time) {\n  time('    makeSpaceForEdgeLabels', () => makeSpaceForEdgeLabels(g));\n  time('    removeSelfEdges', () => removeSelfEdges(g));\n  time('    acyclic', () => acyclic.run(g));\n  time('    nestingGraph.run', () => nestingGraph.run(g));\n  time('    rank', () => rank(util.asNonCompoundGraph(g)));\n  time('    injectEdgeLabelProxies', () => injectEdgeLabelProxies(g));\n  time('    removeEmptyRanks', () => util.removeEmptyRanks(g));\n  time('    nestingGraph.cleanup', () => nestingGraph.cleanup(g));\n  time('    normalizeRanks', () => util.normalizeRanks(g));\n  time('    assignRankMinMax', () => assignRankMinMax(g));\n  time('    removeEdgeLabelProxies', () => removeEdgeLabelProxies(g));\n  time('    normalize.run', () => normalize.run(g));\n  time('    parentDummyChains', () => parentDummyChains(g));\n  time('    addBorderSegments', () => addBorderSegments(g));\n  time('    order', () => order(g));\n  time('    insertSelfEdges', () => insertSelfEdges(g));\n  time('    adjustCoordinateSystem', () => coordinateSystem.adjust(g));\n  time('    position', () => position(g));\n  time('    positionSelfEdges', () => positionSelfEdges(g));\n  time('    removeBorderNodes', () => removeBorderNodes(g));\n  time('    normalize.undo', () => normalize.undo(g));\n  time('    fixupEdgeLabelCoords', () => fixupEdgeLabelCoords(g));\n  time('    undoCoordinateSystem', () => coordinateSystem.undo(g));\n  time('    translateGraph', () => translateGraph(g));\n  time('    assignNodeIntersects', () => assignNodeIntersects(g));\n  time('    reversePoints', () => reversePointsForReversedEdges(g));\n  time('    acyclic.undo', () => acyclic.undo(g));\n}\n\n/*\n * Copies final layout information from the layout graph back to the input\n * graph. This process only copies whitelisted attributes from the layout graph\n * to the input graph, so it serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction updateInputGraph(inputGraph, layoutGraph) {\n  _.forEach(inputGraph.nodes(), function (v) {\n    var inputLabel = inputGraph.node(v);\n    var layoutLabel = layoutGraph.node(v);\n\n    if (inputLabel) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n\n      if (layoutGraph.children(v).length) {\n        inputLabel.width = layoutLabel.width;\n        inputLabel.height = layoutLabel.height;\n      }\n    }\n  });\n\n  _.forEach(inputGraph.edges(), function (e) {\n    var inputLabel = inputGraph.edge(e);\n    var layoutLabel = layoutGraph.edge(e);\n\n    inputLabel.points = layoutLabel.points;\n    if (Object.prototype.hasOwnProperty.call(layoutLabel, 'x')) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n    }\n  });\n\n  inputGraph.graph().width = layoutGraph.graph().width;\n  inputGraph.graph().height = layoutGraph.graph().height;\n}\n\nvar graphNumAttrs = ['nodesep', 'edgesep', 'ranksep', 'marginx', 'marginy'];\nvar graphDefaults = { ranksep: 50, edgesep: 20, nodesep: 50, rankdir: 'tb' };\nvar graphAttrs = ['acyclicer', 'ranker', 'rankdir', 'align'];\nvar nodeNumAttrs = ['width', 'height'];\nvar nodeDefaults = { width: 0, height: 0 };\nvar edgeNumAttrs = ['minlen', 'weight', 'width', 'height', 'labeloffset'];\nvar edgeDefaults = {\n  minlen: 1,\n  weight: 1,\n  width: 0,\n  height: 0,\n  labeloffset: 10,\n  labelpos: 'r',\n};\nvar edgeAttrs = ['labelpos'];\n\n/*\n * Constructs a new graph from the input graph, which can be used for layout.\n * This process copies only whitelisted attributes from the input graph to the\n * layout graph. Thus this function serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction buildLayoutGraph(inputGraph) {\n  var g = new Graph({ multigraph: true, compound: true });\n  var graph = canonicalize(inputGraph.graph());\n\n  g.setGraph(\n    _.merge({}, graphDefaults, selectNumberAttrs(graph, graphNumAttrs), _.pick(graph, graphAttrs)),\n  );\n\n  _.forEach(inputGraph.nodes(), function (v) {\n    var node = canonicalize(inputGraph.node(v));\n    g.setNode(v, _.defaults(selectNumberAttrs(node, nodeNumAttrs), nodeDefaults));\n    g.setParent(v, inputGraph.parent(v));\n  });\n\n  _.forEach(inputGraph.edges(), function (e) {\n    var edge = canonicalize(inputGraph.edge(e));\n    g.setEdge(\n      e,\n      _.merge({}, edgeDefaults, selectNumberAttrs(edge, edgeNumAttrs), _.pick(edge, edgeAttrs)),\n    );\n  });\n\n  return g;\n}\n\n/*\n * This idea comes from the Gansner paper: to account for edge labels in our\n * layout we split each rank in half by doubling minlen and halving ranksep.\n * Then we can place labels at these mid-points between nodes.\n *\n * We also add some minimal padding to the width to push the label for the edge\n * away from the edge itself a bit.\n */\nfunction makeSpaceForEdgeLabels(g) {\n  var graph = g.graph();\n  graph.ranksep /= 2;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    edge.minlen *= 2;\n    if (edge.labelpos.toLowerCase() !== 'c') {\n      if (graph.rankdir === 'TB' || graph.rankdir === 'BT') {\n        edge.width += edge.labeloffset;\n      } else {\n        edge.height += edge.labeloffset;\n      }\n    }\n  });\n}\n\n/*\n * Creates temporary dummy nodes that capture the rank in which each edge's\n * label is going to, if it has one of non-zero width and height. We do this\n * so that we can safely remove empty ranks while preserving balance for the\n * label's position.\n */\nfunction injectEdgeLabelProxies(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.width && edge.height) {\n      var v = g.node(e.v);\n      var w = g.node(e.w);\n      var label = { rank: (w.rank - v.rank) / 2 + v.rank, e: e };\n      util.addDummyNode(g, 'edge-proxy', label, '_ep');\n    }\n  });\n}\n\nfunction assignRankMinMax(g) {\n  var maxRank = 0;\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.borderTop) {\n      node.minRank = g.node(node.borderTop).rank;\n      node.maxRank = g.node(node.borderBottom).rank;\n      // @ts-expect-error\n      maxRank = _.max(maxRank, node.maxRank);\n    }\n  });\n  g.graph().maxRank = maxRank;\n}\n\nfunction removeEdgeLabelProxies(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === 'edge-proxy') {\n      g.edge(node.e).labelRank = node.rank;\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction translateGraph(g) {\n  var minX = Number.POSITIVE_INFINITY;\n  var maxX = 0;\n  var minY = Number.POSITIVE_INFINITY;\n  var maxY = 0;\n  var graphLabel = g.graph();\n  var marginX = graphLabel.marginx || 0;\n  var marginY = graphLabel.marginy || 0;\n\n  function getExtremes(attrs) {\n    var x = attrs.x;\n    var y = attrs.y;\n    var w = attrs.width;\n    var h = attrs.height;\n    minX = Math.min(minX, x - w / 2);\n    maxX = Math.max(maxX, x + w / 2);\n    minY = Math.min(minY, y - h / 2);\n    maxY = Math.max(maxY, y + h / 2);\n  }\n\n  _.forEach(g.nodes(), function (v) {\n    getExtremes(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      getExtremes(edge);\n    }\n  });\n\n  minX -= marginX;\n  minY -= marginY;\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    node.x -= minX;\n    node.y -= minY;\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, function (p) {\n      p.x -= minX;\n      p.y -= minY;\n    });\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      edge.x -= minX;\n    }\n    if (Object.prototype.hasOwnProperty.call(edge, 'y')) {\n      edge.y -= minY;\n    }\n  });\n\n  graphLabel.width = maxX - minX + marginX;\n  graphLabel.height = maxY - minY + marginY;\n}\n\nfunction assignNodeIntersects(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    var nodeV = g.node(e.v);\n    var nodeW = g.node(e.w);\n    var p1, p2;\n    if (!edge.points) {\n      edge.points = [];\n      p1 = nodeW;\n      p2 = nodeV;\n    } else {\n      p1 = edge.points[0];\n      p2 = edge.points[edge.points.length - 1];\n    }\n    edge.points.unshift(util.intersectRect(nodeV, p1));\n    edge.points.push(util.intersectRect(nodeW, p2));\n  });\n}\n\nfunction fixupEdgeLabelCoords(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      if (edge.labelpos === 'l' || edge.labelpos === 'r') {\n        edge.width -= edge.labeloffset;\n      }\n      switch (edge.labelpos) {\n        case 'l':\n          edge.x -= edge.width / 2 + edge.labeloffset;\n          break;\n        case 'r':\n          edge.x += edge.width / 2 + edge.labeloffset;\n          break;\n      }\n    }\n  });\n}\n\nfunction reversePointsForReversedEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.reversed) {\n      edge.points.reverse();\n    }\n  });\n}\n\nfunction removeBorderNodes(g) {\n  _.forEach(g.nodes(), function (v) {\n    if (g.children(v).length) {\n      var node = g.node(v);\n      var t = g.node(node.borderTop);\n      var b = g.node(node.borderBottom);\n      var l = g.node(_.last(node.borderLeft));\n      var r = g.node(_.last(node.borderRight));\n\n      node.width = Math.abs(r.x - l.x);\n      node.height = Math.abs(b.y - t.y);\n      node.x = l.x + node.width / 2;\n      node.y = t.y + node.height / 2;\n    }\n  });\n\n  _.forEach(g.nodes(), function (v) {\n    if (g.node(v).dummy === 'border') {\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction removeSelfEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    if (e.v === e.w) {\n      var node = g.node(e.v);\n      if (!node.selfEdges) {\n        node.selfEdges = [];\n      }\n      node.selfEdges.push({ e: e, label: g.edge(e) });\n      g.removeEdge(e);\n    }\n  });\n}\n\nfunction insertSelfEdges(g) {\n  var layers = util.buildLayerMatrix(g);\n  _.forEach(layers, function (layer) {\n    var orderShift = 0;\n    _.forEach(layer, function (v, i) {\n      var node = g.node(v);\n      node.order = i + orderShift;\n      _.forEach(node.selfEdges, function (selfEdge) {\n        util.addDummyNode(\n          g,\n          'selfedge',\n          {\n            width: selfEdge.label.width,\n            height: selfEdge.label.height,\n            rank: node.rank,\n            order: i + ++orderShift,\n            e: selfEdge.e,\n            label: selfEdge.label,\n          },\n          '_se',\n        );\n      });\n      delete node.selfEdges;\n    });\n  });\n}\n\nfunction positionSelfEdges(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === 'selfedge') {\n      var selfNode = g.node(node.e.v);\n      var x = selfNode.x + selfNode.width / 2;\n      var y = selfNode.y;\n      var dx = node.x - x;\n      var dy = selfNode.height / 2;\n      g.setEdge(node.e, node.label);\n      g.removeNode(v);\n      node.label.points = [\n        { x: x + (2 * dx) / 3, y: y - dy },\n        { x: x + (5 * dx) / 6, y: y - dy },\n        { x: x + dx, y: y },\n        { x: x + (5 * dx) / 6, y: y + dy },\n        { x: x + (2 * dx) / 3, y: y + dy },\n      ];\n      node.label.x = node.x;\n      node.label.y = node.y;\n    }\n  });\n}\n\nfunction selectNumberAttrs(obj, attrs) {\n  return _.mapValues(_.pick(obj, attrs), Number);\n}\n\nfunction canonicalize(attrs) {\n  var newAttrs = {};\n  _.forEach(attrs, function (v, k) {\n    newAttrs[k.toLowerCase()] = v;\n  });\n  return newAttrs;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,SAAS,MAAM,QAAQ,UAAU;AAC/B,SAAO,UAAU,OACb,SACA,QAAQ,QAAQ,aAAa,QAAQ,GAAG,MAAM;AACpD;ACLA,SAAS,OAAO,QAAQ,UAAU;AAChC,SAAO,UAAU,WAAW,QAAQ,aAAa,QAAQ,CAAC;AAC5D;ACxBA,SAAS,OAAO,OAAO,OAAO;AAC5B,SAAO,QAAQ;AACjB;ACqBA,SAAS,UAAU,QAAQ,UAAU;AACnC,MAAI,SAAS,CAAA;AACb,aAAW,aAAa,QAAW;AAEnC,aAAW,QAAQ,SAAS,OAAO,KAAKA,SAAQ;AAC9C,oBAAgB,QAAQ,KAAK,SAAS,OAAO,KAAKA,OAAM,CAAC;EAC3D,CAAC;AACD,SAAO;AACT;AClBA,SAAS,IAAI,OAAO;AAClB,SAAQ,SAAS,MAAM,SACnB,aAAa,OAAO,UAAU,MAAM,IACpC;AACN;ACCA,SAAS,MAAM,OAAO,UAAU;AAC9B,SAAQ,SAAS,MAAM,SACnB,aAAa,OAAO,aAAa,QAAW,GAAG,MAAM,IACrD;AACN;ACrBA,SAAS,WAAW,OAAO,UAAU;AACnC,MAAI,SAAS,MAAM;AAEnB,QAAM,KAAK,QAAQ;AACnB,SAAO,UAAU;AACf,UAAM,MAAM,IAAI,MAAM,MAAM,EAAE;EAChC;AACA,SAAO;AACT;ACRA,SAAS,iBAAiB,OAAO,OAAO;AACtC,MAAI,UAAU,OAAO;AACnB,QAAI,eAAe,UAAU,QACzB,YAAY,UAAU,MACtB,iBAAiB,UAAU,OAC3B,cAAc,SAAS,KAAK;AAEhC,QAAI,eAAe,UAAU,QACzB,YAAY,UAAU,MACtB,iBAAiB,UAAU,OAC3B,cAAc,SAAS,KAAK;AAEhC,QAAK,CAAC,aAAa,CAAC,eAAe,CAAC,eAAe,QAAQ,SACtD,eAAe,gBAAgB,kBAAkB,CAAC,aAAa,CAAC,eAChE,aAAa,gBAAgB,kBAC7B,CAAC,gBAAgB,kBAClB,CAAC,gBAAgB;AACnB,aAAO;IACT;AACA,QAAK,CAAC,aAAa,CAAC,eAAe,CAAC,eAAe,QAAQ,SACtD,eAAe,gBAAgB,kBAAkB,CAAC,aAAa,CAAC,eAChE,aAAa,gBAAgB,kBAC7B,CAAC,gBAAgB,kBAClB,CAAC,gBAAgB;AACnB,aAAO;IACT;EACF;AACA,SAAO;AACT;ACtBA,SAAS,gBAAgB,QAAQ,OAAO,QAAQ;AAC9C,MAAI,QAAQ,IACR,cAAc,OAAO,UACrB,cAAc,MAAM,UACpB,SAAS,YAAY,QACrB,eAAe,OAAO;AAE1B,SAAO,EAAE,QAAQ,QAAQ;AACvB,QAAI,SAAS,iBAAiB,YAAY,KAAK,GAAG,YAAY,KAAK,CAAC;AACpE,QAAI,QAAQ;AACV,UAAI,SAAS,cAAc;AACzB,eAAO;MACT;AACA,UAAIC,SAAQ,OAAO,KAAK;AACxB,aAAO,UAAUA,UAAS,SAAS,KAAK;IAC1C;EACF;AAQA,SAAO,OAAO,QAAQ,MAAM;AAC9B;ACtBA,SAAS,YAAY,YAAY,WAAW,QAAQ;AAClD,MAAI,UAAU,QAAQ;AACpB,gBAAY,SAAS,WAAW,SAAS,UAAU;AACjD,UAAI,QAAQ,QAAQ,GAAG;AACrB,eAAO,SAAS,OAAO;AACrB,iBAAO,QAAQ,OAAO,SAAS,WAAW,IAAI,SAAS,CAAC,IAAI,QAAQ;QACtE;MACF;AACA,aAAO;IACT,CAAC;EACH,OAAO;AACL,gBAAY,CAAC,QAAQ;EACvB;AAEA,MAAI,QAAQ;AACZ,cAAY,SAAS,WAAW,UAAU,YAAY,CAAC;AAEvD,MAAI,SAAS,QAAQ,YAAY,SAAS,OAAO,KAAKC,aAAY;AAChE,QAAI,WAAW,SAAS,WAAW,SAAS,UAAU;AACpD,aAAO,SAAS,KAAK;IACvB,CAAC;AACD,WAAO,EAAE,YAAY,UAAU,SAAS,EAAE,OAAO,SAAS,MAAK;EACjE,CAAC;AAED,SAAO,WAAW,QAAQ,SAAS,QAAQ,OAAO;AAChD,WAAO,gBAAgB,QAAQ,OAAO,MAAM;EAC9C,CAAC;AACH;AC7CA,IAAI,aAAa,KAAK;AAAtB,IACI,YAAY,KAAK;AAarB,SAAS,UAAU,OAAO,KAAK,MAAM,WAAW;AAC9C,MAAI,QAAQ,IACR,SAAS,UAAU,YAAY,MAAM,UAAU,QAAQ,EAAE,GAAG,CAAC,GAC7D,SAAS,MAAM,MAAM;AAEzB,SAAO,UAAU;AACf,WAA4B,EAAE,KAAK,IAAI;AACvC,aAAS;EACX;AACA,SAAO;AACT;ACdA,SAAS,YAAY,WAAW;AAC9B,SAAO,SAAS,OAAO,KAAK,MAAM;AAChC,QAAI,QAAQ,OAAO,QAAQ,YAAY,eAAe,OAAO,KAAK,IAAI,GAAG;AACvE,YAAM,OAAO;IACf;AAEA,YAAQ,SAAS,KAAK;AACtB,QAAI,QAAQ,QAAW;AACrB,YAAM;AACN,cAAQ;IACV,OAAO;AACL,YAAM,SAAS,GAAG;IACpB;AACA,WAAO,SAAS,SAAa,QAAQ,MAAM,IAAI,KAAM,SAAS,IAAI;AAClE,WAAO,UAAU,OAAO,KAAK,IAAe;EAC9C;AACF;ACgBA,IAAI,QAAQ,YAAW;ACTvB,IAAI,SAAS,SAAS,SAAS,YAAY,WAAW;AACpD,MAAI,cAAc,MAAM;AACtB,WAAO,CAAA;EACT;AACA,MAAI,SAAS,UAAU;AACvB,MAAI,SAAS,KAAK,eAAe,YAAY,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG;AACxE,gBAAY,CAAA;EACd,WAAW,SAAS,KAAK,eAAe,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG;AACjF,gBAAY,CAAC,UAAU,CAAC,CAAC;EAC3B;AACA,SAAO,YAAY,YAAY,YAAY,SAAY,GAAG,CAAA,CAAE;AAC9D,CAAC;AC1CD,IAAI,YAAY;AAmBhB,SAAS,SAAS,QAAQ;AACxB,MAAI,KAAK,EAAE;AACX,SAAO,SAAS,MAAM,IAAI;AAC5B;AChBA,SAAS,cAAc,OAAOC,SAAQ,YAAY;AAChD,MAAI,QAAQ,IACR,SAAS,MAAM,QACf,aAAaA,QAAO,QACpB,SAAS,CAAA;AAEb,SAAO,EAAE,QAAQ,QAAQ;AACvB,QAAI,QAAQ,QAAQ,aAAaA,QAAO,KAAK,IAAI;AACjD,eAAW,QAAQ,MAAM,KAAK,GAAG,KAAK;EACxC;AACA,SAAO;AACT;ACDA,SAAS,UAAU,OAAOA,SAAQ;AAChC,SAAO,cAAc,SAAS,CAAA,GAAIA,WAAU,CAAA,GAAI,WAAW;AAC7D;ACdA,IAAM,OAAN,MAAW;EACT,cAAc;AACZ,QAAI,WAAW,CAAA;AACf,aAAS,QAAQ,SAAS,QAAQ;AAClC,SAAK,YAAY;EACnB;EACA,UAAU;AACR,QAAI,WAAW,KAAK;AACpB,QAAI,QAAQ,SAAS;AACrB,QAAI,UAAU,UAAU;AACtB,aAAO,KAAK;AACZ,aAAO;IACT;EACF;EACA,QAAQ,OAAO;AACb,QAAI,WAAW,KAAK;AACpB,QAAI,MAAM,SAAS,MAAM,OAAO;AAC9B,aAAO,KAAK;IACd;AACA,UAAM,QAAQ,SAAS;AACvB,aAAS,MAAM,QAAQ;AACvB,aAAS,QAAQ;AACjB,UAAM,QAAQ;EAChB;EACA,WAAW;AACT,QAAI,OAAO,CAAA;AACX,QAAI,WAAW,KAAK;AACpB,QAAI,OAAO,SAAS;AACpB,WAAO,SAAS,UAAU;AACxB,WAAK,KAAK,KAAK,UAAU,MAAM,cAAc,CAAC;AAC9C,aAAO,KAAK;IACd;AACA,WAAO,MAAM,KAAK,KAAK,IAAI,IAAI;EACjC;AACF;AAEA,SAAS,OAAO,OAAO;AACrB,QAAM,MAAM,QAAQ,MAAM;AAC1B,QAAM,MAAM,QAAQ,MAAM;AAC1B,SAAO,MAAM;AACb,SAAO,MAAM;AACf;AAEA,SAAS,eAAe,GAAG,GAAG;AAC5B,MAAI,MAAM,WAAW,MAAM,SAAS;AAClC,WAAO;EACT;AACF;ACzCA,IAAI,oBAAoBC,SAAW,CAAC;AAEpC,SAAS,UAAU,GAAG,UAAU;AAC9B,MAAI,EAAE,UAAS,KAAM,GAAG;AACtB,WAAO,CAAA;EACT;AACA,MAAI,QAAQ,WAAW,GAAG,YAAY,iBAAiB;AACvD,MAAI,UAAU,YAAY,MAAM,OAAO,MAAM,SAAS,MAAM,OAAO;AAGnE,SAAOC;IACLC,IAAM,SAAS,SAAU,GAAG;AAC1B,aAAO,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC;IAC5B,CAAC;EACL;AACA;AAEA,SAAS,YAAY,GAAG,SAAS,SAAS;AACxC,MAAI,UAAU,CAAA;AACd,MAAI,UAAU,QAAQ,QAAQ,SAAS,CAAC;AACxC,MAAI,QAAQ,QAAQ,CAAC;AAErB,MAAI;AACJ,SAAO,EAAE,UAAA,GAAa;AACpB,WAAQ,QAAQ,MAAM,QAAA,GAAY;AAChC,iBAAW,GAAG,SAAS,SAAS,KAAK;IACvC;AACA,WAAQ,QAAQ,QAAQ,QAAA,GAAY;AAClC,iBAAW,GAAG,SAAS,SAAS,KAAK;IACvC;AACA,QAAI,EAAE,UAAA,GAAa;AACjB,eAAS,IAAI,QAAQ,SAAS,GAAG,IAAI,GAAG,EAAE,GAAG;AAC3C,gBAAQ,QAAQ,CAAC,EAAE,QAAO;AAC1B,YAAI,OAAO;AACT,oBAAU,QAAQ,OAAO,WAAW,GAAG,SAAS,SAAS,OAAO,IAAI,CAAC;AACrE;QACF;MACF;IACF;EACF;AAEA,SAAO;AACT;AAEA,SAAS,WAAW,GAAG,SAAS,SAAS,OAAO,qBAAqB;AACnE,MAAI,UAAU,sBAAsB,CAAA,IAAK;AAEzCC,UAAU,EAAE,QAAQ,MAAM,CAAC,GAAG,SAAU,MAAM;AAC5C,QAAI,SAAS,EAAE,KAAK,IAAI;AACxB,QAAI,SAAS,EAAE,KAAK,KAAK,CAAC;AAE1B,QAAI,qBAAqB;AACvB,cAAQ,KAAK,EAAE,GAAG,KAAK,GAAG,GAAG,KAAK,EAAA,CAAG;IACvC;AAEA,WAAO,OAAO;AACd,iBAAa,SAAS,SAAS,MAAM;EACvC,CAAC;AAEDA,UAAU,EAAE,SAAS,MAAM,CAAC,GAAG,SAAU,MAAM;AAC7C,QAAI,SAAS,EAAE,KAAK,IAAI;AACxB,QAAI,IAAI,KAAK;AACb,QAAI,SAAS,EAAE,KAAK,CAAC;AACrB,WAAO,IAAI,KAAK;AAChB,iBAAa,SAAS,SAAS,MAAM;EACvC,CAAC;AAED,IAAE,WAAW,MAAM,CAAC;AAEpB,SAAO;AACT;AAEA,SAAS,WAAW,GAAG,UAAU;AAC/B,MAAI,WAAW,IAAI,MAAK;AACxB,MAAI,QAAQ;AACZ,MAAI,SAAS;AAEbA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,aAAS,QAAQ,GAAG,EAAE,GAAM,IAAI,GAAG,KAAK,EAAA,CAAG;EAC7C,CAAC;AAIDA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,aAAa,SAAS,KAAK,EAAE,GAAG,EAAE,CAAC,KAAK;AAC5C,QAAI,SAAS,SAAS,CAAC;AACvB,QAAI,aAAa,aAAa;AAC9B,aAAS,QAAQ,EAAE,GAAG,EAAE,GAAG,UAAU;AACrC,aAAS,KAAK,IAAI,QAAS,SAAS,KAAK,EAAE,CAAC,EAAE,OAAO,MAAM;AAC3D,YAAQ,KAAK,IAAI,OAAQ,SAAS,KAAK,EAAE,CAAC,EAAE,IAAI,KAAK,MAAM;EAC7D,CAAC;AAED,MAAI,UAAUC,MAAQ,SAAS,QAAQ,CAAC,EAAE,IAAI,WAAY;AACxD,WAAO,IAAI,KAAI;EACjB,CAAC;AACD,MAAI,UAAU,QAAQ;AAEtBD,UAAU,SAAS,MAAK,GAAI,SAAU,GAAG;AACvC,iBAAa,SAAS,SAAS,SAAS,KAAK,CAAC,CAAC;EACjD,CAAC;AAED,SAAO,EAAE,OAAO,UAAU,SAAkB,QAAgB;AAC9D;AAEA,SAAS,aAAa,SAAS,SAAS,OAAO;AAC7C,MAAI,CAAC,MAAM,KAAK;AACd,YAAQ,CAAC,EAAE,QAAQ,KAAK;EAC1B,WAAW,CAAC,MAAM,IAAI,GAAG;AACvB,YAAQ,QAAQ,SAAS,CAAC,EAAE,QAAQ,KAAK;EAC3C,OAAO;AACL,YAAQ,MAAM,MAAM,MAAM,IAAI,IAAI,OAAO,EAAE,QAAQ,KAAK;EAC1D;AACF;ACxHA,SAASE,MAAI,GAAG;AACd,MAAI,MAAM,EAAE,MAAK,EAAG,cAAc,WAAW,UAAU,GAAG,SAAS,CAAC,CAAC,IAAI,OAAO,CAAC;AACjFF,UAAU,KAAK,SAAU,GAAG;AAC1B,QAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,MAAE,WAAW,CAAC;AACd,UAAM,cAAc,EAAE;AACtB,UAAM,WAAW;AACjB,MAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,OAAOG,SAAW,KAAK,CAAC;EAC9C,CAAC;AAED,WAAS,SAASC,IAAG;AACnB,WAAO,SAAU,GAAG;AAClB,aAAOA,GAAE,KAAK,CAAC,EAAE;IACnB;EACF;AACF;AAEA,SAAS,OAAO,GAAG;AACjB,MAAI,MAAM,CAAA;AACV,MAAI,QAAQ,CAAA;AACZ,MAAI,UAAU,CAAA;AAEd,WAASC,KAAI,GAAG;AACd,QAAI,OAAO,UAAU,eAAe,KAAK,SAAS,CAAC,GAAG;AACpD;IACF;AACA,YAAQ,CAAC,IAAI;AACb,UAAM,CAAC,IAAI;AACXL,YAAU,EAAE,SAAS,CAAC,GAAG,SAAU,GAAG;AACpC,UAAI,OAAO,UAAU,eAAe,KAAK,OAAO,EAAE,CAAC,GAAG;AACpD,YAAI,KAAK,CAAC;MACZ,OAAO;AACLK,aAAI,EAAE,CAAC;MACT;IACF,CAAC;AACD,WAAO,MAAM,CAAC;EAChB;AAEAL,UAAU,EAAE,MAAK,GAAIK,IAAG;AACxB,SAAO;AACT;AAEA,SAASC,OAAK,GAAG;AACfN,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,QAAI,MAAM,UAAU;AAClB,QAAE,WAAW,CAAC;AAEd,UAAI,cAAc,MAAM;AACxB,aAAO,MAAM;AACb,aAAO,MAAM;AACb,QAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,OAAO,WAAW;IACxC;EACF,CAAC;AACH;ACpCA,SAAS,aAAa,GAAG,MAAM,OAAO,MAAM;AAC1C,MAAI;AACJ,KAAG;AACD,QAAIG,SAAW,IAAI;EACrB,SAAS,EAAE,QAAQ,CAAC;AAEpB,QAAM,QAAQ;AACd,IAAE,QAAQ,GAAG,KAAK;AAClB,SAAO;AACT;AAMA,SAAS,SAAS,GAAG;AACnB,MAAI,aAAa,IAAI,MAAK,EAAG,SAAS,EAAE,MAAA,CAAO;AAC/CH,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,eAAW,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;EACjC,CAAC;AACDA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,cAAc,WAAW,KAAK,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,QAAQ,GAAG,QAAQ,EAAC;AACrE,QAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,eAAW,QAAQ,EAAE,GAAG,EAAE,GAAG;MAC3B,QAAQ,YAAY,SAAS,MAAM;MACnC,QAAQ,KAAK,IAAI,YAAY,QAAQ,MAAM,MAAM;IACvD,CAAK;EACH,CAAC;AACD,SAAO;AACT;AAEA,SAAS,mBAAmB,GAAG;AAC7B,MAAI,aAAa,IAAI,MAAM,EAAE,YAAY,EAAE,aAAY,EAAE,CAAE,EAAE,SAAS,EAAE,MAAK,CAAE;AAC/EA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ;AACzB,iBAAW,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC;EACF,CAAC;AACDA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,eAAW,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;EACjC,CAAC;AACD,SAAO;AACT;AA4BA,SAAS,cAAc,MAAM,OAAO;AAClC,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AAIb,MAAI,KAAK,MAAM,IAAI;AACnB,MAAI,KAAK,MAAM,IAAI;AACnB,MAAI,IAAI,KAAK,QAAQ;AACrB,MAAI,IAAI,KAAK,SAAS;AAEtB,MAAI,CAAC,MAAM,CAAC,IAAI;AACd,UAAM,IAAI,MAAM,2DAA2D;EAC7E;AAEA,MAAI,IAAI;AACR,MAAI,KAAK,IAAI,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,GAAG;AAEvC,QAAI,KAAK,GAAG;AACV,UAAI,CAAC;IACP;AACA,SAAM,IAAI,KAAM;AAChB,SAAK;EACP,OAAO;AAEL,QAAI,KAAK,GAAG;AACV,UAAI,CAAC;IACP;AACA,SAAK;AACL,SAAM,IAAI,KAAM;EAClB;AAEA,SAAO,EAAE,GAAG,IAAI,IAAI,GAAG,IAAI,GAAE;AAC/B;AAMA,SAAS,iBAAiB,GAAG;AAC3B,MAAI,WAAWD,IAAME,MAAQ,QAAQ,CAAC,IAAI,CAAC,GAAG,WAAY;AACxD,WAAO,CAAA;EACT,CAAC;AACDD,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAIO,QAAO,KAAK;AAChB,QAAI,CAACC,YAAcD,KAAI,GAAG;AACxB,eAASA,KAAI,EAAE,KAAK,KAAK,IAAI;IAC/B;EACF,CAAC;AACD,SAAO;AACT;AAMA,SAAS,eAAe,GAAG;AACzB,MAAIE,QAAMC;IACRX,IAAM,EAAE,MAAK,GAAI,SAAU,GAAG;AAC5B,aAAO,EAAE,KAAK,CAAC,EAAE;IACnB,CAAC;EACL;AACEC,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAIW,IAAM,MAAM,MAAM,GAAG;AACvB,WAAK,QAAQF;IACf;EACF,CAAC;AACH;AAEA,SAAS,iBAAiB,GAAG;AAE3B,MAAI,SAASC;IACXX,IAAM,EAAE,MAAK,GAAI,SAAU,GAAG;AAC5B,aAAO,EAAE,KAAK,CAAC,EAAE;IACnB,CAAC;EACL;AAEE,MAAI,SAAS,CAAA;AACbC,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAIO,QAAO,EAAE,KAAK,CAAC,EAAE,OAAO;AAC5B,QAAI,CAAC,OAAOA,KAAI,GAAG;AACjB,aAAOA,KAAI,IAAI,CAAA;IACjB;AACA,WAAOA,KAAI,EAAE,KAAK,CAAC;EACrB,CAAC;AAED,MAAI,QAAQ;AACZ,MAAI,iBAAiB,EAAE,MAAK,EAAG;AAC/BP,UAAU,QAAQ,SAAU,IAAI,GAAG;AACjC,QAAIQ,YAAc,EAAE,KAAK,IAAI,mBAAmB,GAAG;AACjD,QAAE;IACJ,WAAW,OAAO;AAChBR,cAAU,IAAI,SAAU,GAAG;AACzB,UAAE,KAAK,CAAC,EAAE,QAAQ;MACpB,CAAC;IACH;EACF,CAAC;AACH;AAEA,SAASY,gBAAc,GAAG,QAAQL,OAAMb,QAAO;AAC7C,MAAI,OAAO;IACT,OAAO;IACP,QAAQ;EACZ;AACE,MAAI,UAAU,UAAU,GAAG;AACzB,SAAK,OAAOa;AACZ,SAAK,QAAQb;EACf;AACA,SAAO,aAAa,GAAG,UAAU,MAAM,MAAM;AAC/C;AAEA,SAAS,QAAQ,GAAG;AAClB,SAAOmB;IACLd,IAAM,EAAE,MAAK,GAAI,SAAU,GAAG;AAC5B,UAAIQ,QAAO,EAAE,KAAK,CAAC,EAAE;AACrB,UAAI,CAACC,YAAcD,KAAI,GAAG;AACxB,eAAOA;MACT;IACF,CAAC;EACL;AACA;AAOA,SAAS,UAAU,YAAY,IAAI;AACjC,MAAI,SAAS,EAAE,KAAK,CAAA,GAAI,KAAK,CAAA,EAAE;AAC/BP,UAAU,YAAY,SAAU,OAAO;AACrC,QAAI,GAAG,KAAK,GAAG;AACb,aAAO,IAAI,KAAK,KAAK;IACvB,OAAO;AACL,aAAO,IAAI,KAAK,KAAK;IACvB;EACF,CAAC;AACD,SAAO;AACT;AAeA,SAAS,OAAO,MAAM,IAAI;AACxB,SAAO,GAAE;AACX;ACpPA,SAAS,kBAAkB,GAAG;AAC5B,WAASK,KAAI,GAAG;AACd,QAAI,WAAW,EAAE,SAAS,CAAC;AAC3B,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,SAAS,QAAQ;AACnBL,cAAU,UAAUK,IAAG;IACzB;AAEA,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,SAAS,GAAG;AACzD,WAAK,aAAa,CAAA;AAClB,WAAK,cAAc,CAAA;AACnB,eAASE,QAAO,KAAK,SAASO,WAAU,KAAK,UAAU,GAAGP,QAAOO,UAAS,EAAEP,OAAM;AAChF,sBAAc,GAAG,cAAc,OAAO,GAAG,MAAMA,KAAI;AACnD,sBAAc,GAAG,eAAe,OAAO,GAAG,MAAMA,KAAI;MACtD;IACF;EACF;AAEAP,UAAU,EAAE,SAAQ,GAAIK,IAAG;AAC7B;AAEA,SAAS,cAAc,GAAG,MAAM,QAAQ,IAAI,QAAQE,OAAM;AACxD,MAAI,QAAQ,EAAE,OAAO,GAAG,QAAQ,GAAG,MAAMA,OAAM,YAAY,KAAI;AAC/D,MAAI,OAAO,OAAO,IAAI,EAAEA,QAAO,CAAC;AAChC,MAAI,OAAOQ,aAAkB,GAAG,UAAU,OAAO,MAAM;AACvD,SAAO,IAAI,EAAER,KAAI,IAAI;AACrB,IAAE,UAAU,MAAM,EAAE;AACpB,MAAI,MAAM;AACR,MAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,EAAA,CAAG;EACrC;AACF;AC/BA,SAAS,OAAO,GAAG;AACjB,MAAI,UAAU,EAAE,MAAK,EAAG,QAAQ,YAAW;AAC3C,MAAI,YAAY,QAAQ,YAAY,MAAM;AACxC,oBAAgB,CAAC;EACnB;AACF;AAEA,SAASD,OAAK,GAAG;AACf,MAAI,UAAU,EAAE,MAAK,EAAG,QAAQ,YAAW;AAC3C,MAAI,YAAY,QAAQ,YAAY,MAAM;AACxC,aAAS,CAAC;EACZ;AAEA,MAAI,YAAY,QAAQ,YAAY,MAAM;AACxC,WAAO,CAAC;AACR,oBAAgB,CAAC;EACnB;AACF;AAEA,SAAS,gBAAgB,GAAG;AAC1BN,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,uBAAmB,EAAE,KAAK,CAAC,CAAC;EAC9B,CAAC;AACDA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,uBAAmB,EAAE,KAAK,CAAC,CAAC;EAC9B,CAAC;AACH;AAEA,SAAS,mBAAmB,OAAO;AACjC,MAAI,IAAI,MAAM;AACd,QAAM,QAAQ,MAAM;AACpB,QAAM,SAAS;AACjB;AAEA,SAAS,SAAS,GAAG;AACnBA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,gBAAY,EAAE,KAAK,CAAC,CAAC;EACvB,CAAC;AAEDA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnBA,YAAU,KAAK,QAAQ,WAAW;AAClC,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACnD,kBAAY,IAAI;IAClB;EACF,CAAC;AACH;AAEA,SAAS,YAAY,OAAO;AAC1B,QAAM,IAAI,CAAC,MAAM;AACnB;AAEA,SAAS,OAAO,GAAG;AACjBA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,cAAU,EAAE,KAAK,CAAC,CAAC;EACrB,CAAC;AAEDA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnBA,YAAU,KAAK,QAAQ,SAAS;AAChC,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACnD,gBAAU,IAAI;IAChB;EACF,CAAC;AACH;AAEA,SAAS,UAAU,OAAO;AACxB,MAAI,IAAI,MAAM;AACd,QAAM,IAAI,MAAM;AAChB,QAAM,IAAI;AACZ;AChDA,SAASE,MAAI,GAAG;AACd,IAAE,MAAA,EAAQ,cAAc,CAAA;AACxBF,UAAU,EAAE,MAAK,GAAI,SAAU,MAAM;AACnC,kBAAc,GAAG,IAAI;EACvB,CAAC;AACH;AAKA,SAAS,cAAc,GAAG,GAAG;AAC3B,MAAI,IAAI,EAAE;AACV,MAAI,QAAQ,EAAE,KAAK,CAAC,EAAE;AACtB,MAAI,IAAI,EAAE;AACV,MAAI,QAAQ,EAAE,KAAK,CAAC,EAAE;AACtB,MAAI,OAAO,EAAE;AACb,MAAI,YAAY,EAAE,KAAK,CAAC;AACxB,MAAI,YAAY,UAAU;AAE1B,MAAI,UAAU,QAAQ;AAAG;AAEzB,IAAE,WAAW,CAAC;AAcd,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,OAAK,IAAI,GAAG,EAAE,OAAO,QAAQ,OAAO,EAAE,GAAG,EAAE,OAAO;AAChD,cAAU,SAAS,CAAA;AACnB,YAAQ;MACN,OAAO;MACP,QAAQ;MACR;MACA,SAAS;MACT,MAAM;IACZ;AACI,YAAQe,aAAkB,GAAG,QAAQ,OAAO,IAAI;AAChD,QAAI,UAAU,WAAW;AACvB,YAAM,QAAQ,UAAU;AACxB,YAAM,SAAS,UAAU;AACzB,YAAM,QAAQ;AACd,YAAM,WAAW,UAAU;IAC7B;AACA,MAAE,QAAQ,GAAG,OAAO,EAAE,QAAQ,UAAU,OAAM,GAAI,IAAI;AACtD,QAAI,MAAM,GAAG;AACX,QAAE,MAAK,EAAG,YAAY,KAAK,KAAK;IAClC;AACA,QAAI;EACN;AAEA,IAAE,QAAQ,GAAG,GAAG,EAAE,QAAQ,UAAU,OAAM,GAAI,IAAI;AACpD;AAEA,SAAS,KAAK,GAAG;AACff,UAAU,EAAE,MAAK,EAAG,aAAa,SAAU,GAAG;AAC5C,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,YAAY,KAAK;AACrB,QAAI;AACJ,MAAE,QAAQ,KAAK,SAAS,SAAS;AACjC,WAAO,KAAK,OAAO;AACjB,UAAI,EAAE,WAAW,CAAC,EAAE,CAAC;AACrB,QAAE,WAAW,CAAC;AACd,gBAAU,OAAO,KAAK,EAAE,GAAG,KAAK,GAAG,GAAG,KAAK,EAAA,CAAG;AAC9C,UAAI,KAAK,UAAU,cAAc;AAC/B,kBAAU,IAAI,KAAK;AACnB,kBAAU,IAAI,KAAK;AACnB,kBAAU,QAAQ,KAAK;AACvB,kBAAU,SAAS,KAAK;MAC1B;AACA,UAAI;AACJ,aAAO,EAAE,KAAK,CAAC;IACjB;EACF,CAAC;AACH;ACpFA,SAAS,YAAY,GAAG;AACtB,MAAI,UAAU,CAAA;AAEd,WAASK,KAAI,GAAG;AACd,QAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,QAAI,OAAO,UAAU,eAAe,KAAK,SAAS,CAAC,GAAG;AACpD,aAAO,MAAM;IACf;AACA,YAAQ,CAAC,IAAI;AAEb,QAAIE,QAAOG;MACTX,IAAM,EAAE,SAAS,CAAC,GAAG,SAAU,GAAG;AAChC,eAAOM,KAAI,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;MAC9B,CAAC;IACP;AAEI,QACEE,UAAS,OAAO;IAChBA,UAAS;IACTA,UAAS,MACT;AAEAA,cAAO;IACT;AAEA,WAAQ,MAAM,OAAOA;EACvB;AAEAP,UAAU,EAAE,QAAO,GAAIK,IAAG;AAC5B;AAMA,SAAS,MAAM,GAAG,GAAG;AACnB,SAAO,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE;AACzD;AC/BA,SAAS,aAAa,GAAG;AACvB,MAAI,IAAI,IAAI,MAAM,EAAE,UAAU,MAAK,CAAE;AAGrC,MAAI,QAAQ,EAAE,MAAK,EAAG,CAAC;AACvB,MAAI,OAAO,EAAE,UAAS;AACtB,IAAE,QAAQ,OAAO,CAAA,CAAE;AAEnB,MAAI,MAAM;AACV,SAAO,UAAU,GAAG,CAAC,IAAI,MAAM;AAC7B,WAAO,iBAAiB,GAAG,CAAC;AAC5B,YAAQ,EAAE,QAAQ,KAAK,CAAC,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI;AAC3D,eAAW,GAAG,GAAG,KAAK;EACxB;AAEA,SAAO;AACT;AAMA,SAAS,UAAU,GAAG,GAAG;AACvB,WAASA,KAAI,GAAG;AACdL,YAAU,EAAE,UAAU,CAAC,GAAG,SAAU,GAAG;AACrC,UAAI,QAAQ,EAAE,GACZ,IAAI,MAAM,QAAQ,EAAE,IAAI;AAC1B,UAAI,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG;AACjC,UAAE,QAAQ,GAAG,CAAA,CAAE;AACf,UAAE,QAAQ,GAAG,GAAG,CAAA,CAAE;AAClBK,aAAI,CAAC;MACP;IACF,CAAC;EACH;AAEAL,UAAU,EAAE,MAAK,GAAIK,IAAG;AACxB,SAAO,EAAE,UAAS;AACpB;AAMA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,SAAOW,MAAQ,EAAE,MAAK,GAAI,SAAU,GAAG;AACrC,QAAI,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG;AACrC,aAAO,MAAM,GAAG,CAAC;IACnB;EACF,CAAC;AACH;AAEA,SAAS,WAAW,GAAG,GAAG,OAAO;AAC/BhB,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,MAAE,KAAK,CAAC,EAAE,QAAQ;EACpB,CAAC;AACH;ACpDA,SAAS,iBAAiB;AAAC;AAC3B,eAAe,YAAY,IAAI,MAAA;ACvB/B,SAASK,MAAI,GAAG,IAAIX,QAAO;AACzB,MAAI,CAACuB,QAAU,EAAE,GAAG;AAClB,SAAK,CAAC,EAAE;EACV;AAEA,MAAI,cAAc,EAAE,WAAA,IAAe,EAAE,aAAa,EAAE,WAAW,KAAK,CAAC;AAErE,MAAI,MAAM,CAAA;AACV,MAAI,UAAU,CAAA;AACdC,UAAO,IAAI,SAAU,GAAG;AACtB,QAAI,CAAC,EAAE,QAAQ,CAAC,GAAG;AACjB,YAAM,IAAI,MAAM,+BAA+B,CAAC;IAClD;AAEA,UAAM,GAAG,GAAGxB,WAAU,QAAQ,SAAS,YAAY,GAAG;EACxD,CAAC;AACD,SAAO;AACT;AAEA,SAAS,MAAM,GAAG,GAAGyB,YAAW,SAAS,YAAY,KAAK;AACxD,MAAI,CAAC,OAAO,UAAU,eAAe,KAAK,SAAS,CAAC,GAAG;AACrD,YAAQ,CAAC,IAAI;AAEb,QAAI,CAACA,YAAW;AACd,UAAI,KAAK,CAAC;IACZ;AACAD,YAAO,WAAW,CAAC,GAAG,SAAU,GAAG;AACjC,YAAM,GAAG,GAAGC,YAAW,SAAS,YAAY,GAAG;IACjD,CAAC;AACD,QAAIA,YAAW;AACb,UAAI,KAAK,CAAC;IACZ;EACF;AACF;ACzCA,SAASA,YAAU,GAAG,IAAI;AACxB,SAAOd,MAAI,GAAG,IAAI,MAAM;AAC1B;ACFA,SAAS,SAAS,GAAG,IAAI;AACvB,SAAOA,MAAI,GAAG,IAAI,KAAK;AACzB;ACGA,eAAe,mBAAmB;AAClC,eAAe,gBAAgB;AAC/B,eAAe,eAAe;AAC9B,eAAe,YAAY;AAC3B,eAAe,YAAY;AAC3B,eAAe,gBAAgB;AAmC/B,SAAS,eAAe,GAAG;AACzB,MAAI,SAAS,CAAC;AACd,cAAY,CAAC;AACb,MAAI,IAAI,aAAa,CAAC;AACtB,mBAAiB,CAAC;AAClB,gBAAc,GAAG,CAAC;AAElB,MAAI,GAAG;AACP,SAAQ,IAAI,UAAU,CAAC,GAAI;AACzB,QAAI,UAAU,GAAG,GAAG,CAAC;AACrB,kBAAc,GAAG,GAAG,GAAG,CAAC;EAC1B;AACF;AAKA,SAAS,cAAc,GAAG,GAAG;AAC3B,MAAI,KAAKe,YAAc,GAAG,EAAE,MAAK,CAAE;AACnC,OAAK,GAAG,MAAM,GAAG,GAAG,SAAS,CAAC;AAC9BpB,UAAU,IAAI,SAAU,GAAG;AACzB,mBAAe,GAAG,GAAG,CAAC;EACxB,CAAC;AACH;AAEA,SAAS,eAAe,GAAG,GAAG,OAAO;AACnC,MAAI,WAAW,EAAE,KAAK,KAAK;AAC3B,MAAI,SAAS,SAAS;AACtB,IAAE,KAAK,OAAO,MAAM,EAAE,WAAW,aAAa,GAAG,GAAG,KAAK;AAC3D;AAMA,SAAS,aAAa,GAAG,GAAG,OAAO;AACjC,MAAI,WAAW,EAAE,KAAK,KAAK;AAC3B,MAAI,SAAS,SAAS;AAEtB,MAAI,cAAc;AAElB,MAAI,YAAY,EAAE,KAAK,OAAO,MAAM;AAEpC,MAAI,WAAW;AAEf,MAAI,CAAC,WAAW;AACd,kBAAc;AACd,gBAAY,EAAE,KAAK,QAAQ,KAAK;EAClC;AAEA,aAAW,UAAU;AAErBA,UAAU,EAAE,UAAU,KAAK,GAAG,SAAU,GAAG;AACzC,QAAI,YAAY,EAAE,MAAM,OACtB,QAAQ,YAAY,EAAE,IAAI,EAAE;AAE9B,QAAI,UAAU,QAAQ;AACpB,UAAI,eAAe,cAAc,aAC/B,cAAc,EAAE,KAAK,CAAC,EAAE;AAE1B,kBAAY,eAAe,cAAc,CAAC;AAC1C,UAAI,WAAW,GAAG,OAAO,KAAK,GAAG;AAC/B,YAAI,gBAAgB,EAAE,KAAK,OAAO,KAAK,EAAE;AACzC,oBAAY,eAAe,CAAC,gBAAgB;MAC9C;IACF;EACF,CAAC;AAED,SAAO;AACT;AAEA,SAAS,iBAAiB,MAAM,MAAM;AACpC,MAAI,UAAU,SAAS,GAAG;AACxB,WAAO,KAAK,MAAK,EAAG,CAAC;EACvB;AACA,kBAAgB,MAAM,CAAA,GAAI,GAAG,IAAI;AACnC;AAEA,SAAS,gBAAgB,MAAM,SAAS,SAAS,GAAG,QAAQ;AAC1D,MAAI,MAAM;AACV,MAAI,QAAQ,KAAK,KAAK,CAAC;AAEvB,UAAQ,CAAC,IAAI;AACbA,UAAU,KAAK,UAAU,CAAC,GAAG,SAAU,GAAG;AACxC,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,SAAS,CAAC,GAAG;AACrD,gBAAU,gBAAgB,MAAM,SAAS,SAAS,GAAG,CAAC;IACxD;EACF,CAAC;AAED,QAAM,MAAM;AACZ,QAAM,MAAM;AACZ,MAAI,QAAQ;AACV,UAAM,SAAS;EACjB,OAAO;AAEL,WAAO,MAAM;EACf;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,MAAM;AACvB,SAAOqB,KAAO,KAAK,MAAK,GAAI,SAAU,GAAG;AACvC,WAAO,KAAK,KAAK,CAAC,EAAE,WAAW;EACjC,CAAC;AACH;AAEA,SAAS,UAAU,GAAG,GAAG,MAAM;AAC7B,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AAKb,MAAI,CAAC,EAAE,QAAQ,GAAG,CAAC,GAAG;AACpB,QAAI,KAAK;AACT,QAAI,KAAK;EACX;AAEA,MAAI,SAAS,EAAE,KAAK,CAAC;AACrB,MAAI,SAAS,EAAE,KAAK,CAAC;AACrB,MAAI,YAAY;AAChB,MAAI,OAAO;AAIX,MAAI,OAAO,MAAM,OAAO,KAAK;AAC3B,gBAAY;AACZ,WAAO;EACT;AAEA,MAAI,aAAaC,OAAS,EAAE,MAAK,GAAI,SAAUC,OAAM;AACnD,WACE,SAAS,aAAa,GAAG,EAAE,KAAKA,MAAK,CAAC,GAAG,SAAS,KAClD,SAAS,aAAa,GAAG,EAAE,KAAKA,MAAK,CAAC,GAAG,SAAS;EAEtD,CAAC;AAED,SAAOP,MAAQ,YAAY,SAAUO,OAAM;AACzC,WAAO,MAAM,GAAGA,KAAI;EACtB,CAAC;AACH;AAEA,SAAS,cAAc,GAAG,GAAG,GAAG,GAAG;AACjC,MAAI,IAAI,EAAE;AACV,MAAI,IAAI,EAAE;AACV,IAAE,WAAW,GAAG,CAAC;AACjB,IAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAA,CAAE;AACtB,mBAAiB,CAAC;AAClB,gBAAc,GAAG,CAAC;AAClB,cAAY,GAAG,CAAC;AAClB;AAEA,SAAS,YAAY,GAAG,GAAG;AACzB,MAAI,OAAOF,KAAO,EAAE,MAAK,GAAI,SAAU,GAAG;AACxC,WAAO,CAAC,EAAE,KAAK,CAAC,EAAE;EACpB,CAAC;AACD,MAAI,KAAKG,SAAa,GAAG,IAAI;AAC7B,OAAK,GAAG,MAAM,CAAC;AACfxB,UAAU,IAAI,SAAU,GAAG;AACzB,QAAI,SAAS,EAAE,KAAK,CAAC,EAAE,QACrB,OAAO,EAAE,KAAK,GAAG,MAAM,GACvB,UAAU;AAEZ,QAAI,CAAC,MAAM;AACT,aAAO,EAAE,KAAK,QAAQ,CAAC;AACvB,gBAAU;IACZ;AAEA,MAAE,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,MAAM,EAAE,QAAQ,UAAU,KAAK,SAAS,CAAC,KAAK;EACxE,CAAC;AACH;AAKA,SAAS,WAAW,MAAM,GAAG,GAAG;AAC9B,SAAO,KAAK,QAAQ,GAAG,CAAC;AAC1B;AAMA,SAAS,aAAa,MAAM,QAAQ,WAAW;AAC7C,SAAO,UAAU,OAAO,OAAO,OAAO,OAAO,OAAO,UAAU;AAChE;AClNA,SAAS,KAAK,GAAG;AACf,UAAQ,EAAE,MAAK,EAAG,QAAM;IACtB,KAAK;AACH,2BAAqB,CAAC;AACtB;IACF,KAAK;AACH,sBAAgB,CAAC;AACjB;IACF,KAAK;AACH,wBAAkB,CAAC;AACnB;IACF;AACE,2BAAqB,CAAC;EAC5B;AACA;AAGA,IAAI,oBAAoB;AAExB,SAAS,gBAAgB,GAAG;AAC1B,cAAY,CAAC;AACb,eAAa,CAAC;AAChB;AAEA,SAAS,qBAAqB,GAAG;AAC/B,iBAAe,CAAC;AAClB;ACvBA,SAAS,IAAI,GAAG;AACd,MAAI,OAAOe,aAAkB,GAAG,QAAQ,CAAA,GAAI,OAAO;AACnD,MAAI,SAAS,WAAW,CAAC;AACzB,MAAI,SAASF,IAAMY,OAAS,MAAM,CAAC,IAAI;AACvC,MAAI,UAAU,IAAI,SAAS;AAE3B,IAAE,MAAA,EAAQ,cAAc;AAGxBzB,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,MAAE,KAAK,CAAC,EAAE,UAAU;EACtB,CAAC;AAGD,MAAI,SAAS,WAAW,CAAC,IAAI;AAG7BA,UAAU,EAAE,SAAQ,GAAI,SAAU,OAAO;AACvC,QAAI,GAAG,MAAM,SAAS,QAAQ,QAAQ,QAAQ,KAAK;EACrD,CAAC;AAID,IAAE,MAAA,EAAQ,iBAAiB;AAC7B;AAEA,SAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,QAAQ,QAAQ,GAAG;AACxD,MAAI,WAAW,EAAE,SAAS,CAAC;AAC3B,MAAI,CAAC,SAAS,QAAQ;AACpB,QAAI,MAAM,MAAM;AACd,QAAE,QAAQ,MAAM,GAAG,EAAE,QAAQ,GAAG,QAAQ,QAAA,CAAS;IACnD;AACA;EACF;AAEA,MAAI,MAAM0B,gBAAmB,GAAG,KAAK;AACrC,MAAI,SAASA,gBAAmB,GAAG,KAAK;AACxC,MAAI,QAAQ,EAAE,KAAK,CAAC;AAEpB,IAAE,UAAU,KAAK,CAAC;AAClB,QAAM,YAAY;AAClB,IAAE,UAAU,QAAQ,CAAC;AACrB,QAAM,eAAe;AAErB1B,UAAU,UAAU,SAAU,OAAO;AACnC,QAAI,GAAG,MAAM,SAAS,QAAQ,QAAQ,QAAQ,KAAK;AAEnD,QAAI,YAAY,EAAE,KAAK,KAAK;AAC5B,QAAI,WAAW,UAAU,YAAY,UAAU,YAAY;AAC3D,QAAI,cAAc,UAAU,eAAe,UAAU,eAAe;AACpE,QAAI,aAAa,UAAU,YAAY,SAAS,IAAI;AACpD,QAAI,SAAS,aAAa,cAAc,IAAI,SAAS,OAAO,CAAC,IAAI;AAEjE,MAAE,QAAQ,KAAK,UAAU;MACvB,QAAQ;MACR;MACA,aAAa;IACnB,CAAK;AAED,MAAE,QAAQ,aAAa,QAAQ;MAC7B,QAAQ;MACR;MACA,aAAa;IACnB,CAAK;EACH,CAAC;AAED,MAAI,CAAC,EAAE,OAAO,CAAC,GAAG;AAChB,MAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ,GAAG,QAAQ,SAAS,OAAO,CAAC,EAAC,CAAE;EAChE;AACF;AAEA,SAAS,WAAW,GAAG;AACrB,MAAI,SAAS,CAAA;AACb,WAASK,KAAI,GAAG,OAAO;AACrB,QAAI,WAAW,EAAE,SAAS,CAAC;AAC3B,QAAI,YAAY,SAAS,QAAQ;AAC/BL,cAAU,UAAU,SAAU,OAAO;AACnCK,aAAI,OAAO,QAAQ,CAAC;MACtB,CAAC;IACH;AACA,WAAO,CAAC,IAAI;EACd;AACAL,UAAU,EAAE,SAAQ,GAAI,SAAU,GAAG;AACnCK,SAAI,GAAG,CAAC;EACV,CAAC;AACD,SAAO;AACT;AAEA,SAAS,WAAW,GAAG;AACrB,SAAOsB;IACL,EAAE,MAAK;IACP,SAAU,KAAK,GAAG;AAChB,aAAO,MAAM,EAAE,KAAK,CAAC,EAAE;IACzB;IACA;EACJ;AACA;AAEA,SAAS,QAAQ,GAAG;AAClB,MAAI,aAAa,EAAE,MAAK;AACxB,IAAE,WAAW,WAAW,WAAW;AACnC,SAAO,WAAW;AAClB3B,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,KAAK,aAAa;AACpB,QAAE,WAAW,CAAC;IAChB;EACF,CAAC;AACH;ACpIA,SAAS,uBAAuB,GAAG,IAAI,IAAI;AACzC,MAAI,OAAO,CAAA,GACT;AAEFA,UAAU,IAAI,SAAU,GAAG;AACzB,QAAI,QAAQ,EAAE,OAAO,CAAC,GACpB,QACA;AACF,WAAO,OAAO;AACZ,eAAS,EAAE,OAAO,KAAK;AACvB,UAAI,QAAQ;AACV,oBAAY,KAAK,MAAM;AACvB,aAAK,MAAM,IAAI;MACjB,OAAO;AACL,oBAAY;AACZ,mBAAW;MACb;AACA,UAAI,aAAa,cAAc,OAAO;AACpC,WAAG,QAAQ,WAAW,KAAK;AAC3B;MACF;AACA,cAAQ;IACV;EACF,CAAC;AAyBH;ACjBA,SAAS,gBAAgB,GAAGO,OAAM,cAAc;AAC9C,MAAI,OAAO,eAAe,CAAC,GACzB,SAAS,IAAI,MAAM,EAAE,UAAU,KAAI,CAAE,EAClC,SAAS,EAAE,KAAU,CAAE,EACvB,oBAAoB,SAAU,GAAG;AAChC,WAAO,EAAE,KAAK,CAAC;EACjB,CAAC;AAELP,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC,GACjB,SAAS,EAAE,OAAO,CAAC;AAErB,QAAI,KAAK,SAASO,SAAS,KAAK,WAAWA,SAAQA,SAAQ,KAAK,SAAU;AACxE,aAAO,QAAQ,CAAC;AAChB,aAAO,UAAU,GAAG,UAAU,IAAI;AAGlCP,cAAU,EAAE,YAAY,EAAE,CAAC,GAAG,SAAU,GAAG;AACzC,YAAI,IAAI,EAAE,MAAM,IAAI,EAAE,IAAI,EAAE,GAC1B,OAAO,OAAO,KAAK,GAAG,CAAC,GACvB,SAAS,CAACQ,YAAc,IAAI,IAAI,KAAK,SAAS;AAChD,eAAO,QAAQ,GAAG,GAAG,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,SAAS,OAAM,CAAE;MAC5D,CAAC;AAED,UAAI,OAAO,UAAU,eAAe,KAAK,MAAM,SAAS,GAAG;AACzD,eAAO,QAAQ,GAAG;UAChB,YAAY,KAAK,WAAWD,KAAI;UAChC,aAAa,KAAK,YAAYA,KAAI;QAC5C,CAAS;MACH;IACF;EACF,CAAC;AAED,SAAO;AACT;AAEA,SAAS,eAAe,GAAG;AACzB,MAAI;AACJ,SAAO,EAAE,QAAS,IAAIJ,SAAW,OAAO,CAAC;AAAG;AAC5C,SAAO;AACT;ACvDA,SAAS,WAAW,GAAG,UAAU;AAC/B,MAAI,KAAK;AACT,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACxC,UAAM,mBAAmB,GAAG,SAAS,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;EAC1D;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,GAAG,YAAY,YAAY;AAIrD,MAAI,WAAWyB;IACb;IACA7B,IAAM,YAAY,SAAU,GAAG,GAAG;AAChC,aAAO;IACT,CAAC;EACL;AACE,MAAI,eAAeD;IACjBC,IAAM,YAAY,SAAU,GAAG;AAC7B,aAAO8B;QACL9B,IAAM,EAAE,SAAS,CAAC,GAAG,SAAU,GAAG;AAChC,iBAAO,EAAE,KAAK,SAAS,EAAE,CAAC,GAAG,QAAQ,EAAE,KAAK,CAAC,EAAE,OAAM;QACvD,CAAC;QACD;MACR;IACI,CAAC;EACL;AAGE,MAAI,aAAa;AACjB,SAAO,aAAa,WAAW;AAAQ,mBAAe;AACtD,MAAI,WAAW,IAAI,aAAa;AAChC,gBAAc;AACd,MAAI,OAAOA,IAAM,IAAI,MAAM,QAAQ,GAAG,WAAY;AAChD,WAAO;EACT,CAAC;AAGD,MAAI,KAAK;AACTC;;IAEE,aAAa,QAAQ,SAAU,OAAO;AACpC,UAAI,QAAQ,MAAM,MAAM;AACxB,WAAK,KAAK,KAAK,MAAM;AACrB,UAAI,YAAY;AAEhB,aAAO,QAAQ,GAAG;AAEhB,YAAI,QAAQ,GAAG;AACb,uBAAa,KAAK,QAAQ,CAAC;QAC7B;AAEA,gBAAS,QAAQ,KAAM;AACvB,aAAK,KAAK,KAAK,MAAM;MACvB;AACA,YAAM,MAAM,SAAS;IACvB,CAAC;EACL;AAEE,SAAO;AACT;ACpEO,SAAS,UAAU,GAAG;AAC3B,MAAI,UAAU,CAAA;AACd,MAAI,cAAcsB,OAAS,EAAE,MAAK,GAAI,SAAU,GAAG;AACjD,WAAO,CAAC,EAAE,SAAS,CAAC,EAAE;EACxB,CAAC;AACD,MAAIR,WAAUD;IACZd,IAAM,aAAa,SAAU,GAAG;AAC9B,aAAO,EAAE,KAAK,CAAC,EAAE;IACnB,CAAC;EACL;AACE,MAAI,SAASA,IAAME,MAAQa,WAAU,CAAC,GAAG,WAAY;AACnD,WAAO,CAAA;EACT,CAAC;AAED,WAAST,KAAI,GAAG;AACd,QAAIM,IAAM,SAAS,CAAC;AAAG;AACvB,YAAQ,CAAC,IAAI;AACb,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,WAAO,KAAK,IAAI,EAAE,KAAK,CAAC;AACxBX,YAAU,EAAE,WAAW,CAAC,GAAGK,IAAG;EAChC;AAEA,MAAI,YAAYwB,OAAS,aAAa,SAAU,GAAG;AACjD,WAAO,EAAE,KAAK,CAAC,EAAE;EACnB,CAAC;AACD7B,UAAU,WAAWK,IAAG;AAExB,SAAO;AACT;ACrCA,SAAS,WAAW,GAAG,SAAS;AAC9B,SAAON,IAAM,SAAS,SAAU,GAAG;AACjC,QAAI,MAAM,EAAE,QAAQ,CAAC;AACrB,QAAI,CAAC,IAAI,QAAQ;AACf,aAAO,EAAE,EAAI;IACf,OAAO;AACL,UAAI,SAAS4B;QACX;QACA,SAAU,KAAK,GAAG;AAChB,cAAI,OAAO,EAAE,KAAK,CAAC,GACjB,QAAQ,EAAE,KAAK,EAAE,CAAC;AACpB,iBAAO;YACL,KAAK,IAAI,MAAM,KAAK,SAAS,MAAM;YACnC,QAAQ,IAAI,SAAS,KAAK;UACtC;QACQ;QACA,EAAE,KAAK,GAAG,QAAQ,EAAC;MAC3B;AAEM,aAAO;QACL;QACA,YAAY,OAAO,MAAM,OAAO;QAChC,QAAQ,OAAO;MACvB;IACI;EACF,CAAC;AACH;ACDA,SAAS,iBAAiB,SAAS,IAAI;AACrC,MAAI,gBAAgB,CAAA;AACpB3B,UAAU,SAAS,SAAU,OAAO,GAAG;AACrC,QAAI,MAAO,cAAc,MAAM,CAAC,IAAI;MAClC,UAAU;MACV,IAAI,CAAA;MACJ,KAAK,CAAA;MACL,IAAI,CAAC,MAAM,CAAC;MACZ;IACN;AACI,QAAI,CAACQ,YAAc,MAAM,UAAU,GAAG;AAEpC,UAAI,aAAa,MAAM;AAEvB,UAAI,SAAS,MAAM;IACrB;EACF,CAAC;AAEDR,UAAU,GAAG,MAAK,GAAI,SAAU,GAAG;AACjC,QAAI,SAAS,cAAc,EAAE,CAAC;AAC9B,QAAI,SAAS,cAAc,EAAE,CAAC;AAC9B,QAAI,CAACQ,YAAc,MAAM,KAAK,CAACA,YAAc,MAAM,GAAG;AACpD,aAAO;AACP,aAAO,IAAI,KAAK,cAAc,EAAE,CAAC,CAAC;IACpC;EACF,CAAC;AAED,MAAI,YAAYc,OAAS,eAAe,SAAU,OAAO;AAEvD,WAAO,CAAC,MAAM;EAChB,CAAC;AAED,SAAO,mBAAmB,SAAS;AACrC;AAEA,SAAS,mBAAmB,WAAW;AACrC,MAAI,UAAU,CAAA;AAEd,WAAS,SAAS,QAAQ;AACxB,WAAO,SAAU,QAAQ;AACvB,UAAI,OAAO,QAAQ;AACjB;MACF;AACA,UACEd,YAAc,OAAO,UAAU,KAC/BA,YAAc,OAAO,UAAU,KAC/B,OAAO,cAAc,OAAO,YAC5B;AACA,qBAAa,QAAQ,MAAM;MAC7B;IACF;EACF;AAEA,WAAS,UAAU,QAAQ;AACzB,WAAO,SAAU,QAAQ;AACvB,aAAO,IAAI,EAAE,KAAK,MAAM;AACxB,UAAI,EAAE,OAAO,aAAa,GAAG;AAC3B,kBAAU,KAAK,MAAM;MACvB;IACF;EACF;AAEA,SAAO,UAAU,QAAQ;AACvB,QAAI,QAAQ,UAAU,IAAG;AACzB,YAAQ,KAAK,KAAK;AAClBR,YAAU,MAAM,IAAI,EAAE,QAAO,GAAI,SAAS,KAAK,CAAC;AAChDA,YAAU,MAAM,KAAK,UAAU,KAAK,CAAC;EACvC;AAEA,SAAOD;IACLuB,OAAS,SAAS,SAAUQ,QAAO;AACjC,aAAO,CAACA,OAAM;IAChB,CAAC;IACD,SAAUA,QAAO;AACf,aAAOC,KAAOD,QAAO,CAAC,MAAM,KAAK,cAAc,QAAQ,CAAC;IAC1D;EACJ;AACA;AAEA,SAAS,aAAa,QAAQ,QAAQ;AACpC,MAAI,MAAM;AACV,MAAI,SAAS;AAEb,MAAI,OAAO,QAAQ;AACjB,WAAO,OAAO,aAAa,OAAO;AAClC,cAAU,OAAO;EACnB;AAEA,MAAI,OAAO,QAAQ;AACjB,WAAO,OAAO,aAAa,OAAO;AAClC,cAAU,OAAO;EACnB;AAEA,SAAO,KAAK,OAAO,GAAG,OAAO,OAAO,EAAE;AACtC,SAAO,aAAa,MAAM;AAC1B,SAAO,SAAS;AAChB,SAAO,IAAI,KAAK,IAAI,OAAO,GAAG,OAAO,CAAC;AACtC,SAAO,SAAS;AAClB;AC1HA,SAAS,KAAK,SAAS,WAAW;AAChC,MAAI,QAAQE,UAAe,SAAS,SAAU,OAAO;AACnD,WAAO,OAAO,UAAU,eAAe,KAAK,OAAO,YAAY;EACjE,CAAC;AACD,MAAI,WAAW,MAAM,KACnB,aAAaH,OAAS,MAAM,KAAK,SAAU,OAAO;AAChD,WAAO,CAAC,MAAM;EAChB,CAAC,GACD,KAAK,CAAA,GACL,MAAM,GACN,SAAS,GACT,UAAU;AAEZ,WAAS,KAAK,gBAAgB,CAAC,CAAC,SAAS,CAAC;AAE1C,YAAU,kBAAkB,IAAI,YAAY,OAAO;AAEnD7B,UAAU,UAAU,SAAU,OAAO;AACnC,eAAW,MAAM,GAAG;AACpB,OAAG,KAAK,MAAM,EAAE;AAChB,WAAO,MAAM,aAAa,MAAM;AAChC,cAAU,MAAM;AAChB,cAAU,kBAAkB,IAAI,YAAY,OAAO;EACrD,CAAC;AAED,MAAI,SAAS,EAAE,IAAIF,QAAU,EAAE,EAAC;AAChC,MAAI,QAAQ;AACV,WAAO,aAAa,MAAM;AAC1B,WAAO,SAAS;EAClB;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,IAAI,YAAY,OAAO;AAChD,MAAImC;AACJ,SAAO,WAAW,WAAWA,SAAOC,KAAO,UAAU,GAAG,KAAK,OAAO;AAClE,eAAW,IAAG;AACd,OAAG,KAAKD,OAAK,EAAE;AACf;EACF;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAU,QAAQ,QAAQ;AAC/B,QAAI,OAAO,aAAa,OAAO,YAAY;AACzC,aAAO;IACT,WAAW,OAAO,aAAa,OAAO,YAAY;AAChD,aAAO;IACT;AAEA,WAAO,CAAC,OAAO,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO;EACzD;AACF;ACnDA,SAAS,aAAa,GAAG,GAAG,IAAI,WAAW;AACzC,MAAI,UAAU,EAAE,SAAS,CAAC;AAC1B,MAAI,OAAO,EAAE,KAAK,CAAC;AACnB,MAAI,KAAK,OAAO,KAAK,aAAa;AAClC,MAAI,KAAK,OAAO,KAAK,cAAc;AACnC,MAAI,YAAY,CAAA;AAEhB,MAAI,IAAI;AACN,cAAUX,OAAS,SAAS,SAAU,GAAG;AACvC,aAAO,MAAM,MAAM,MAAM;IAC3B,CAAC;EACH;AAEA,MAAI,cAAc,WAAW,GAAG,OAAO;AACvCtB,UAAU,aAAa,SAAU,OAAO;AACtC,QAAI,EAAE,SAAS,MAAM,CAAC,EAAE,QAAQ;AAC9B,UAAI,iBAAiB,aAAa,GAAG,MAAM,GAAG,IAAI,SAAS;AAC3D,gBAAU,MAAM,CAAC,IAAI;AACrB,UAAI,OAAO,UAAU,eAAe,KAAK,gBAAgB,YAAY,GAAG;AACtE,yBAAiB,OAAO,cAAc;MACxC;IACF;EACF,CAAC;AAED,MAAI,UAAU,iBAAiB,aAAa,EAAE;AAC9C,kBAAgB,SAAS,SAAS;AAElC,MAAI,SAAS,KAAK,SAAS,SAAS;AAEpC,MAAI,IAAI;AACN,WAAO,KAAKF,QAAU,CAAC,IAAI,OAAO,IAAI,EAAE,CAAC;AACzC,QAAI,EAAE,aAAa,EAAE,EAAE,QAAQ;AAC7B,UAAI,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC,GACvC,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;AACvC,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,QAAQ,YAAY,GAAG;AAC/D,eAAO,aAAa;AACpB,eAAO,SAAS;MAClB;AACA,aAAO,cACJ,OAAO,aAAa,OAAO,SAAS,OAAO,QAAQ,OAAO,UAAU,OAAO,SAAS;AACvF,aAAO,UAAU;IACnB;EACF;AAEA,SAAO;AACT;AAEA,SAAS,gBAAgB,SAAS,WAAW;AAC3CE,UAAU,SAAS,SAAU,OAAO;AAClC,UAAM,KAAKF;MACT,MAAM,GAAG,IAAI,SAAU,GAAG;AACxB,YAAI,UAAU,CAAC,GAAG;AAChB,iBAAO,UAAU,CAAC,EAAE;QACtB;AACA,eAAO;MACT,CAAC;IACP;EACE,CAAC;AACH;AAEA,SAAS,iBAAiB,QAAQ,OAAO;AACvC,MAAI,CAACU,YAAc,OAAO,UAAU,GAAG;AACrC,WAAO,cACJ,OAAO,aAAa,OAAO,SAAS,MAAM,aAAa,MAAM,WAC7D,OAAO,SAAS,MAAM;AACzB,WAAO,UAAU,MAAM;EACzB,OAAO;AACL,WAAO,aAAa,MAAM;AAC1B,WAAO,SAAS,MAAM;EACxB;AACF;ACnDA,SAAS,MAAM,GAAG;AAChB,MAAIM,YAAUqB,QAAa,CAAC,GAC1B,kBAAkB,iBAAiB,GAAGlC,MAAQ,GAAGa,YAAU,CAAC,GAAG,SAAS,GACxE,gBAAgB,iBAAiB,GAAGb,MAAQa,YAAU,GAAG,IAAI,EAAE,GAAG,UAAU;AAE9E,MAAI,WAAW,UAAU,CAAC;AAC1B,cAAY,GAAG,QAAQ;AAEvB,MAAI,SAAS,OAAO,mBAClB;AAEF,WAAS,IAAI,GAAG,WAAW,GAAG,WAAW,GAAG,EAAE,GAAG,EAAE,UAAU;AAC3D,qBAAiB,IAAI,IAAI,kBAAkB,eAAe,IAAI,KAAK,CAAC;AAEpE,eAAWsB,iBAAsB,CAAC;AAClC,QAAI,KAAK,WAAW,GAAG,QAAQ;AAC/B,QAAI,KAAK,QAAQ;AACf,iBAAW;AACX,aAAOC,UAAY,QAAQ;AAC3B,eAAS;IACX;EACF;AAEA,cAAY,GAAG,IAAI;AACrB;AAEA,SAAS,iBAAiB,GAAG,OAAO,cAAc;AAChD,SAAOtC,IAAM,OAAO,SAAUQ,OAAM;AAClC,WAAO,gBAAgB,GAAGA,OAAM,YAAY;EAC9C,CAAC;AACH;AAEA,SAAS,iBAAiB,aAAa,WAAW;AAChD,MAAI,KAAK,IAAI,MAAK;AAClBP,UAAU,aAAa,SAAU,IAAI;AACnC,QAAI,OAAO,GAAG,MAAK,EAAG;AACtB,QAAI,SAAS,aAAa,IAAI,MAAM,IAAI,SAAS;AACjDA,YAAU,OAAO,IAAI,SAAU,GAAG,GAAG;AACnC,SAAG,KAAK,CAAC,EAAE,QAAQ;IACrB,CAAC;AACD,2BAAuB,IAAI,IAAI,OAAO,EAAE;EAC1C,CAAC;AACH;AAEA,SAAS,YAAY,GAAG,UAAU;AAChCA,UAAU,UAAU,SAAU,OAAO;AACnCA,YAAU,OAAO,SAAU,GAAG,GAAG;AAC/B,QAAE,KAAK,CAAC,EAAE,QAAQ;IACpB,CAAC;EACH,CAAC;AACH;ACxEA,SAAS,kBAAkB,GAAG;AAC5B,MAAI,gBAAgB,UAAU,CAAC;AAE/BA,UAAU,EAAE,MAAK,EAAG,aAAa,SAAU,GAAG;AAC5C,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,UAAU,KAAK;AACnB,QAAI,WAAW,SAAS,GAAG,eAAe,QAAQ,GAAG,QAAQ,CAAC;AAC9D,QAAI,OAAO,SAAS;AACpB,QAAI,MAAM,SAAS;AACnB,QAAI,UAAU;AACd,QAAI,QAAQ,KAAK,OAAO;AACxB,QAAI,YAAY;AAEhB,WAAO,MAAM,QAAQ,GAAG;AACtB,aAAO,EAAE,KAAK,CAAC;AAEf,UAAI,WAAW;AACb,gBAAQ,QAAQ,KAAK,OAAO,OAAO,OAAO,EAAE,KAAK,KAAK,EAAE,UAAU,KAAK,MAAM;AAC3E;QACF;AAEA,YAAI,UAAU,KAAK;AACjB,sBAAY;QACd;MACF;AAEA,UAAI,CAAC,WAAW;AACd,eACE,UAAU,KAAK,SAAS,KACxB,EAAE,KAAM,QAAQ,KAAK,UAAU,CAAC,CAAC,EAAG,WAAW,KAAK,MACpD;AACA;QACF;AACA,gBAAQ,KAAK,OAAO;MACtB;AAEA,QAAE,UAAU,GAAG,KAAK;AACpB,UAAI,EAAE,WAAW,CAAC,EAAE,CAAC;IACvB;EACF,CAAC;AACH;AAIA,SAAS,SAAS,GAAG,eAAe,GAAG,GAAG;AACxC,MAAI,QAAQ,CAAA;AACZ,MAAI,QAAQ,CAAA;AACZ,MAAI,MAAM,KAAK,IAAI,cAAc,CAAC,EAAE,KAAK,cAAc,CAAC,EAAE,GAAG;AAC7D,MAAI,MAAM,KAAK,IAAI,cAAc,CAAC,EAAE,KAAK,cAAc,CAAC,EAAE,GAAG;AAC7D,MAAI;AACJ,MAAI;AAGJ,WAAS;AACT,KAAG;AACD,aAAS,EAAE,OAAO,MAAM;AACxB,UAAM,KAAK,MAAM;EACnB,SAAS,WAAW,cAAc,MAAM,EAAE,MAAM,OAAO,MAAM,cAAc,MAAM,EAAE;AACnF,QAAM;AAGN,WAAS;AACT,UAAQ,SAAS,EAAE,OAAO,MAAM,OAAO,KAAK;AAC1C,UAAM,KAAK,MAAM;EACnB;AAEA,SAAO,EAAE,MAAM,MAAM,OAAO,MAAM,QAAO,CAAE,GAAG,IAAQ;AACxD;AAEA,SAAS,UAAU,GAAG;AACpB,MAAI,SAAS,CAAA;AACb,MAAI,MAAM;AAEV,WAASK,KAAI,GAAG;AACd,QAAI,MAAM;AACVL,YAAU,EAAE,SAAS,CAAC,GAAGK,IAAG;AAC5B,WAAO,CAAC,IAAI,EAAE,KAAU,KAAK,MAAK;EACpC;AACAL,UAAU,EAAE,SAAQ,GAAIK,IAAG;AAE3B,SAAO;AACT;AC9CA,SAAS,mBAAmB,GAAG,UAAU;AACvC,MAAI,YAAY,CAAA;AAEhB,WAAS,WAAW,WAAW,OAAO;AACpC,QAEE,KAAK,GAGL,UAAU,GACV,kBAAkB,UAAU,QAC5B,WAAW6B,KAAO,KAAK;AAEzBlC,YAAU,OAAO,SAAU,GAAG,GAAG;AAC/B,UAAI,IAAI,0BAA0B,GAAG,CAAC,GACpC,KAAK,IAAI,EAAE,KAAK,CAAC,EAAE,QAAQ;AAE7B,UAAI,KAAK,MAAM,UAAU;AACvBA,gBAAU,MAAM,MAAM,SAAS,IAAI,CAAC,GAAG,SAAU,UAAU;AACzDA,kBAAU,EAAE,aAAa,QAAQ,GAAG,SAAU,GAAG;AAC/C,gBAAI,SAAS,EAAE,KAAK,CAAC,GACnB,OAAO,OAAO;AAChB,iBAAK,OAAO,MAAM,KAAK,SAAS,EAAE,OAAO,SAAS,EAAE,KAAK,QAAQ,EAAE,QAAQ;AACzE,0BAAY,WAAW,GAAG,QAAQ;YACpC;UACF,CAAC;QACH,CAAC;AAED,kBAAU,IAAI;AACd,aAAK;MACP;IACF,CAAC;AAED,WAAO;EACT;AAEA2B,SAAS,UAAU,UAAU;AAC7B,SAAO;AACT;AAEA,SAAS,mBAAmB,GAAG,UAAU;AACvC,MAAI,YAAY,CAAA;AAEhB,WAAS,KAAK,OAAO,UAAU,UAAU,iBAAiB,iBAAiB;AACzE,QAAI;AACJ3B,YAAUC,MAAQ,UAAU,QAAQ,GAAG,SAAU,GAAG;AAClD,UAAI,MAAM,CAAC;AACX,UAAI,EAAE,KAAK,CAAC,EAAE,OAAO;AACnBD,gBAAU,EAAE,aAAa,CAAC,GAAG,SAAU,GAAG;AACxC,cAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,cAAI,MAAM,UAAU,MAAM,QAAQ,mBAAmB,MAAM,QAAQ,kBAAkB;AACnF,wBAAY,WAAW,GAAG,CAAC;UAC7B;QACF,CAAC;MACH;IACF,CAAC;EACH;AAEA,WAAS,WAAW,OAAO,OAAO;AAChC,QAAI,eAAe,IACjB,cACA,WAAW;AAEbA,YAAU,OAAO,SAAU,GAAG,gBAAgB;AAC5C,UAAI,EAAE,KAAK,CAAC,EAAE,UAAU,UAAU;AAChC,YAAI,eAAe,EAAE,aAAa,CAAC;AACnC,YAAI,aAAa,QAAQ;AACvB,yBAAe,EAAE,KAAK,aAAa,CAAC,CAAC,EAAE;AACvC,eAAK,OAAO,UAAU,gBAAgB,cAAc,YAAY;AAEhE,qBAAW;AACX,yBAAe;QACjB;MACF;AACA,WAAK,OAAO,UAAU,MAAM,QAAQ,cAAc,MAAM,MAAM;IAChE,CAAC;AAED,WAAO;EACT;AAEA2B,SAAS,UAAU,UAAU;AAC7B,SAAO;AACT;AAEA,SAAS,0BAA0B,GAAG,GAAG;AACvC,MAAI,EAAE,KAAK,CAAC,EAAE,OAAO;AACnB,WAAON,KAAO,EAAE,aAAa,CAAC,GAAG,SAAU,GAAG;AAC5C,aAAO,EAAE,KAAK,CAAC,EAAE;IACnB,CAAC;EACH;AACF;AAEA,SAAS,YAAY,WAAW,GAAG,GAAG;AACpC,MAAI,IAAI,GAAG;AACT,QAAI,MAAM;AACV,QAAI;AACJ,QAAI;EACN;AAEA,MAAI,aAAa,UAAU,CAAC;AAC5B,MAAI,CAAC,YAAY;AACf,cAAU,CAAC,IAAI,aAAa,CAAA;EAC9B;AACA,aAAW,CAAC,IAAI;AAClB;AAEA,SAAS,YAAY,WAAW,GAAG,GAAG;AACpC,MAAI,IAAI,GAAG;AACT,QAAI,MAAM;AACV,QAAI;AACJ,QAAI;EACN;AACA,SAAO,CAAC,CAAC,UAAU,CAAC,KAAK,OAAO,UAAU,eAAe,KAAK,UAAU,CAAC,GAAG,CAAC;AAC/E;AAUA,SAAS,kBAAkB,GAAG,UAAU,WAAW,YAAY;AAC7D,MAAI,OAAO,CAAA,GACT,QAAQ,CAAA,GACR,MAAM,CAAA;AAKRrB,UAAU,UAAU,SAAU,OAAO;AACnCA,YAAU,OAAO,SAAU,GAAGN,QAAO;AACnC,WAAK,CAAC,IAAI;AACV,YAAM,CAAC,IAAI;AACX,UAAI,CAAC,IAAIA;IACX,CAAC;EACH,CAAC;AAEDM,UAAU,UAAU,SAAU,OAAO;AACnC,QAAI,UAAU;AACdA,YAAU,OAAO,SAAU,GAAG;AAC5B,UAAI,KAAK,WAAW,CAAC;AACrB,UAAI,GAAG,QAAQ;AACb,aAAK6B,OAAS,IAAI,SAAUS,IAAG;AAC7B,iBAAO,IAAIA,EAAC;QACd,CAAC;AACD,YAAI,MAAM,GAAG,SAAS,KAAK;AAC3B,iBAAS,IAAI,KAAK,MAAM,EAAE,GAAG,KAAK,KAAK,KAAK,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG;AAC7D,cAAI,IAAI,GAAG,CAAC;AACZ,cAAI,MAAM,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,KAAK,CAAC,YAAY,WAAW,GAAG,CAAC,GAAG;AACvE,kBAAM,CAAC,IAAI;AACX,kBAAM,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC;AAC3B,sBAAU,IAAI,CAAC;UACjB;QACF;MACF;IACF,CAAC;EACH,CAAC;AAED,SAAO,EAAE,MAAY,MAAY;AACnC;AAEA,SAAS,qBAAqB,GAAG,UAAU,MAAM,OAAO,YAAY;AAMlE,MAAI,KAAK,CAAA,GACP,SAAS,gBAAgB,GAAG,UAAU,MAAM,UAAU,GACtD,aAAa,aAAa,eAAe;AAE3C,WAAS,QAAQ,WAAW,eAAe;AACzC,QAAI,QAAQ,OAAO,MAAK;AACxB,QAAI,OAAO,MAAM,IAAG;AACpB,QAAI,UAAU,CAAA;AACd,WAAO,MAAM;AACX,UAAI,QAAQ,IAAI,GAAG;AACjB,kBAAU,IAAI;MAChB,OAAO;AACL,gBAAQ,IAAI,IAAI;AAChB,cAAM,KAAK,IAAI;AACf,gBAAQ,MAAM,OAAO,cAAc,IAAI,CAAC;MAC1C;AAEA,aAAO,MAAM,IAAG;IAClB;EACF;AAGA,WAAS,MAAM,MAAM;AACnB,OAAG,IAAI,IAAI,OAAO,QAAQ,IAAI,EAAE,OAAO,SAAU,KAAK,GAAG;AACvD,aAAO,KAAK,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;IAC/C,GAAG,CAAC;EACN;AAGA,WAAS,MAAM,MAAM;AACnB,QAAI7B,OAAM,OAAO,SAAS,IAAI,EAAE,OAAO,SAAU,KAAK,GAAG;AACvD,aAAO,KAAK,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;IAC/C,GAAG,OAAO,iBAAiB;AAE3B,QAAI,OAAO,EAAE,KAAK,IAAI;AACtB,QAAIA,SAAQ,OAAO,qBAAqB,KAAK,eAAe,YAAY;AACtE,SAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,GAAGA,IAAG;IACnC;EACF;AAEA,UAAQ,OAAO,OAAO,aAAa,KAAK,MAAM,CAAC;AAC/C,UAAQ,OAAO,OAAO,WAAW,KAAK,MAAM,CAAC;AAG7CT,UAAU,OAAO,SAAU,GAAG;AAC5B,OAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;EACpB,CAAC;AAED,SAAO;AACT;AAEA,SAAS,gBAAgB,GAAG,UAAU,MAAM,YAAY;AACtD,MAAI,aAAa,IAAI,MAAK,GACxB,aAAa,EAAE,MAAK,GACpB,QAAQ,IAAI,WAAW,SAAS,WAAW,SAAS,UAAU;AAEhEA,UAAU,UAAU,SAAU,OAAO;AACnC,QAAI;AACJA,YAAU,OAAO,SAAU,GAAG;AAC5B,UAAI,QAAQ,KAAK,CAAC;AAClB,iBAAW,QAAQ,KAAK;AACxB,UAAI,GAAG;AACL,YAAI,QAAQ,KAAK,CAAC,GAChB,UAAU,WAAW,KAAK,OAAO,KAAK;AACxC,mBAAW,QAAQ,OAAO,OAAO,KAAK,IAAI,MAAM,GAAG,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC;MACzE;AACA,UAAI;IACN,CAAC;EACH,CAAC;AAED,SAAO;AACT;AAKA,SAAS,2BAA2B,GAAG,KAAK;AAC1C,SAAOgB,MAAQS,OAAS,GAAG,GAAG,SAAU,IAAI;AAC1C,QAAIc,OAAM,OAAO;AACjB,QAAI9B,OAAM,OAAO;AAEjB+B,UAAQ,IAAI,SAAU,GAAG,GAAG;AAC1B,UAAI,YAAY,MAAM,GAAG,CAAC,IAAI;AAE9BD,aAAM,KAAK,IAAI,IAAI,WAAWA,IAAG;AACjC9B,aAAM,KAAK,IAAI,IAAI,WAAWA,IAAG;IACnC,CAAC;AAED,WAAO8B,OAAM9B;EACf,CAAC;AACH;AASA,SAAS,iBAAiB,KAAK,SAAS;AACtC,MAAI,cAAcgB,OAAS,OAAO,GAChC,aAAaf,IAAM,WAAW,GAC9B,aAAaG,IAAM,WAAW;AAEhCb,UAAU,CAAC,KAAK,GAAG,GAAG,SAAU,MAAM;AACpCA,YAAU,CAAC,KAAK,GAAG,GAAG,SAAU,OAAO;AACrC,UAAI,YAAY,OAAO,OACrB,KAAK,IAAI,SAAS,GAClB;AACF,UAAI,OAAO;AAAS;AAEpB,UAAI,SAASyB,OAAS,EAAE;AACxB,cAAQ,UAAU,MAAM,aAAaf,IAAM,MAAM,IAAI,aAAaG,IAAM,MAAM;AAE9E,UAAI,OAAO;AACT,YAAI,SAAS,IAAI4B,UAAY,IAAI,SAAU,GAAG;AAC5C,iBAAO,IAAI;QACb,CAAC;MACH;IACF,CAAC;EACH,CAAC;AACH;AAEA,SAAS,QAAQ,KAAK,OAAO;AAC3B,SAAOA,UAAY,IAAI,IAAI,SAAU,QAAQ,GAAG;AAC9C,QAAI,OAAO;AACT,aAAO,IAAI,MAAM,YAAW,CAAE,EAAE,CAAC;IACnC,OAAO;AACL,UAAI,KAAKZ,OAAS9B,IAAM,KAAK,CAAC,CAAC;AAC/B,cAAQ,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK;IAC3B;EACF,CAAC;AACH;AAEA,SAAS,UAAU,GAAG;AACpB,MAAI,WAAWqC,iBAAsB,CAAC;AACtC,MAAI,YAAYM,QAAQ,mBAAmB,GAAG,QAAQ,GAAG,mBAAmB,GAAG,QAAQ,CAAC;AAExF,MAAI,MAAM,CAAA;AACV,MAAI;AACJ1C,UAAU,CAAC,KAAK,GAAG,GAAG,SAAU,MAAM;AACpC,uBAAmB,SAAS,MAAM,WAAWyB,OAAS,QAAQ,EAAE,QAAO;AACvEzB,YAAU,CAAC,KAAK,GAAG,GAAG,SAAU,OAAO;AACrC,UAAI,UAAU,KAAK;AACjB,2BAAmBD,IAAM,kBAAkB,SAAU,OAAO;AAC1D,iBAAO0B,OAAS,KAAK,EAAE,QAAO;QAChC,CAAC;MACH;AAEA,UAAI,cAAc,SAAS,MAAM,EAAE,eAAe,EAAE,YAAY,KAAK,CAAC;AACtE,UAAI,QAAQ,kBAAkB,GAAG,kBAAkB,WAAW,UAAU;AACxE,UAAI,KAAK,qBAAqB,GAAG,kBAAkB,MAAM,MAAM,MAAM,OAAO,UAAU,GAAG;AACzF,UAAI,UAAU,KAAK;AACjB,aAAKgB,UAAY,IAAI,SAAU,GAAG;AAChC,iBAAO,CAAC;QACV,CAAC;MACH;AACA,UAAI,OAAO,KAAK,IAAI;IACtB,CAAC;EACH,CAAC;AAED,MAAI,gBAAgB,2BAA2B,GAAG,GAAG;AACrD,mBAAiB,KAAK,aAAa;AACnC,SAAO,QAAQ,KAAK,EAAE,MAAK,EAAG,KAAK;AACrC;AAEA,SAAS,IAAI,SAAS,SAAS,YAAY;AACzC,SAAO,SAAU,GAAG,GAAG,GAAG;AACxB,QAAI,SAAS,EAAE,KAAK,CAAC;AACrB,QAAI,SAAS,EAAE,KAAK,CAAC;AACrB,QAAI,MAAM;AACV,QAAI;AAEJ,WAAO,OAAO,QAAQ;AACtB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,UAAU,GAAG;AAC5D,cAAQ,OAAO,SAAS,YAAW,GAAE;QACnC,KAAK;AACH,kBAAQ,CAAC,OAAO,QAAQ;AACxB;QACF,KAAK;AACH,kBAAQ,OAAO,QAAQ;AACvB;MACV;IACI;AACA,QAAI,OAAO;AACT,aAAO,aAAa,QAAQ,CAAC;IAC/B;AACA,YAAQ;AAER,YAAQ,OAAO,QAAQ,UAAU,WAAW;AAC5C,YAAQ,OAAO,QAAQ,UAAU,WAAW;AAE5C,WAAO,OAAO,QAAQ;AACtB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,UAAU,GAAG;AAC5D,cAAQ,OAAO,SAAS,YAAW,GAAE;QACnC,KAAK;AACH,kBAAQ,OAAO,QAAQ;AACvB;QACF,KAAK;AACH,kBAAQ,CAAC,OAAO,QAAQ;AACxB;MACV;IACI;AACA,QAAI,OAAO;AACT,aAAO,aAAa,QAAQ,CAAC;IAC/B;AACA,YAAQ;AAER,WAAO;EACT;AACF;AAEA,SAAS,MAAM,GAAG,GAAG;AACnB,SAAO,EAAE,KAAK,CAAC,EAAE;AACnB;AChaA,SAAS,SAAS,GAAG;AACnB,MAAIE,mBAAwB,CAAC;AAE7B,YAAU,CAAC;AACXC,SAAS,UAAU,CAAC,GAAG,SAAU,GAAG,GAAG;AACrC,MAAE,KAAK,CAAC,EAAE,IAAI;EAChB,CAAC;AACH;AAEA,SAAS,UAAU,GAAG;AACpB,MAAI,WAAWR,iBAAsB,CAAC;AACtC,MAAI,UAAU,EAAE,MAAK,EAAG;AACxB,MAAI,QAAQ;AACZpC,UAAU,UAAU,SAAU,OAAO;AACnC,QAAI,YAAYa;MACdd,IAAM,OAAO,SAAU,GAAG;AACxB,eAAO,EAAE,KAAK,CAAC,EAAE;MACnB,CAAC;IACP;AACIC,YAAU,OAAO,SAAU,GAAG;AAC5B,QAAE,KAAK,CAAC,EAAE,IAAI,QAAQ,YAAY;IACpC,CAAC;AACD,aAAS,YAAY;EACvB,CAAC;AACH;ACfA,SAAS,OAAO,GAAG,MAAM;AACvB,MAAI,OAA8C6C;AAClD,OAAK,UAAU,MAAM;AACnB,QAAI,cAAc,KAAK,sBAAsB,MAAM,iBAAiB,CAAC,CAAC;AACtE,SAAK,eAAe,MAAM,UAAU,aAAa,IAAI,CAAC;AACtD,SAAK,sBAAsB,MAAM,iBAAiB,GAAG,WAAW,CAAC;EACnE,CAAC;AACH;AAEA,SAAS,UAAU,GAAG,MAAM;AAC1B,OAAK,8BAA8B,MAAM,uBAAuB,CAAC,CAAC;AAClE,OAAK,uBAAuB,MAAM,gBAAgB,CAAC,CAAC;AACpD,OAAK,eAAe,MAAMC,MAAY,CAAC,CAAC;AACxC,OAAK,wBAAwB,MAAMC,IAAiB,CAAC,CAAC;AACtD,OAAK,YAAY,MAAM,KAAKJ,mBAAwB,CAAC,CAAC,CAAC;AACvD,OAAK,8BAA8B,MAAM,uBAAuB,CAAC,CAAC;AAClE,OAAK,wBAAwB,MAAMK,iBAAsB,CAAC,CAAC;AAC3D,OAAK,4BAA4B,MAAMC,QAAqB,CAAC,CAAC;AAC9D,OAAK,sBAAsB,MAAMC,eAAoB,CAAC,CAAC;AACvD,OAAK,wBAAwB,MAAM,iBAAiB,CAAC,CAAC;AACtD,OAAK,8BAA8B,MAAM,uBAAuB,CAAC,CAAC;AAClE,OAAK,qBAAqB,MAAMC,MAAc,CAAC,CAAC;AAChD,OAAK,yBAAyB,MAAM,kBAAkB,CAAC,CAAC;AACxD,OAAK,yBAAyB,MAAM,kBAAkB,CAAC,CAAC;AACxD,OAAK,aAAa,MAAM,MAAM,CAAC,CAAC;AAChC,OAAK,uBAAuB,MAAM,gBAAgB,CAAC,CAAC;AACpD,OAAK,8BAA8B,MAAMC,OAAwB,CAAC,CAAC;AACnE,OAAK,gBAAgB,MAAM,SAAS,CAAC,CAAC;AACtC,OAAK,yBAAyB,MAAM,kBAAkB,CAAC,CAAC;AACxD,OAAK,yBAAyB,MAAM,kBAAkB,CAAC,CAAC;AACxD,OAAK,sBAAsB,MAAMC,KAAe,CAAC,CAAC;AAClD,OAAK,4BAA4B,MAAM,qBAAqB,CAAC,CAAC;AAC9D,OAAK,4BAA4B,MAAMC,OAAsB,CAAC,CAAC;AAC/D,OAAK,sBAAsB,MAAM,eAAe,CAAC,CAAC;AAClD,OAAK,4BAA4B,MAAM,qBAAqB,CAAC,CAAC;AAC9D,OAAK,qBAAqB,MAAM,8BAA8B,CAAC,CAAC;AAChE,OAAK,oBAAoB,MAAMC,OAAa,CAAC,CAAC;AAChD;AAQA,SAAS,iBAAiB,YAAY,aAAa;AACjDvD,UAAU,WAAW,MAAK,GAAI,SAAU,GAAG;AACzC,QAAI,aAAa,WAAW,KAAK,CAAC;AAClC,QAAI,cAAc,YAAY,KAAK,CAAC;AAEpC,QAAI,YAAY;AACd,iBAAW,IAAI,YAAY;AAC3B,iBAAW,IAAI,YAAY;AAE3B,UAAI,YAAY,SAAS,CAAC,EAAE,QAAQ;AAClC,mBAAW,QAAQ,YAAY;AAC/B,mBAAW,SAAS,YAAY;MAClC;IACF;EACF,CAAC;AAEDA,UAAU,WAAW,MAAK,GAAI,SAAU,GAAG;AACzC,QAAI,aAAa,WAAW,KAAK,CAAC;AAClC,QAAI,cAAc,YAAY,KAAK,CAAC;AAEpC,eAAW,SAAS,YAAY;AAChC,QAAI,OAAO,UAAU,eAAe,KAAK,aAAa,GAAG,GAAG;AAC1D,iBAAW,IAAI,YAAY;AAC3B,iBAAW,IAAI,YAAY;IAC7B;EACF,CAAC;AAED,aAAW,MAAK,EAAG,QAAQ,YAAY,MAAK,EAAG;AAC/C,aAAW,MAAK,EAAG,SAAS,YAAY,MAAK,EAAG;AAClD;AAEA,IAAI,gBAAgB,CAAC,WAAW,WAAW,WAAW,WAAW,SAAS;AAC1E,IAAI,gBAAgB,EAAE,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,KAAI;AAC1E,IAAI,aAAa,CAAC,aAAa,UAAU,WAAW,OAAO;AAC3D,IAAI,eAAe,CAAC,SAAS,QAAQ;AACrC,IAAI,eAAe,EAAE,OAAO,GAAG,QAAQ,EAAC;AACxC,IAAI,eAAe,CAAC,UAAU,UAAU,SAAS,UAAU,aAAa;AACxE,IAAI,eAAe;EACjB,QAAQ;EACR,QAAQ;EACR,OAAO;EACP,QAAQ;EACR,aAAa;EACb,UAAU;AACZ;AACA,IAAI,YAAY,CAAC,UAAU;AAQ3B,SAAS,iBAAiB,YAAY;AACpC,MAAI,IAAI,IAAI,MAAM,EAAE,YAAY,MAAM,UAAU,KAAA,CAAM;AACtD,MAAI,QAAQ,aAAa,WAAW,MAAK,CAAE;AAE3C,IAAE;IACA0C,QAAQ,CAAA,GAAI,eAAe,kBAAkB,OAAO,aAAa,GAAGX,KAAO,OAAO,UAAU,CAAC;EACjG;AAEE/B,UAAU,WAAW,MAAK,GAAI,SAAU,GAAG;AACzC,QAAI,OAAO,aAAa,WAAW,KAAK,CAAC,CAAC;AAC1C,MAAE,QAAQ,GAAGwD,SAAW,kBAAkB,MAAM,YAAY,GAAG,YAAY,CAAC;AAC5E,MAAE,UAAU,GAAG,WAAW,OAAO,CAAC,CAAC;EACrC,CAAC;AAEDxD,UAAU,WAAW,MAAK,GAAI,SAAU,GAAG;AACzC,QAAI,OAAO,aAAa,WAAW,KAAK,CAAC,CAAC;AAC1C,MAAE;MACA;MACA0C,QAAQ,CAAA,GAAI,cAAc,kBAAkB,MAAM,YAAY,GAAGX,KAAO,MAAM,SAAS,CAAC;IAC9F;EACE,CAAC;AAED,SAAO;AACT;AAUA,SAAS,uBAAuB,GAAG;AACjC,MAAI,QAAQ,EAAE,MAAK;AACnB,QAAM,WAAW;AACjB/B,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,SAAK,UAAU;AACf,QAAI,KAAK,SAAS,YAAW,MAAO,KAAK;AACvC,UAAI,MAAM,YAAY,QAAQ,MAAM,YAAY,MAAM;AACpD,aAAK,SAAS,KAAK;MACrB,OAAO;AACL,aAAK,UAAU,KAAK;MACtB;IACF;EACF,CAAC;AACH;AAQA,SAAS,uBAAuB,GAAG;AACjCA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,KAAK,SAAS,KAAK,QAAQ;AAC7B,UAAI,IAAI,EAAE,KAAK,EAAE,CAAC;AAClB,UAAI,IAAI,EAAE,KAAK,EAAE,CAAC;AAClB,UAAI,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,IAAI,EAAE,MAAM,EAAI;AACxDe,mBAAkB,GAAG,cAAc,OAAO,KAAK;IACjD;EACF,CAAC;AACH;AAEA,SAAS,iBAAiB,GAAG;AAC3B,MAAID,WAAU;AACdd,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,EAAE,KAAK,KAAK,SAAS,EAAE;AACtC,WAAK,UAAU,EAAE,KAAK,KAAK,YAAY,EAAE;AAEzCc,iBAAUD,IAAMC,UAAS,KAAK,OAAO;IACvC;EACF,CAAC;AACD,IAAE,MAAA,EAAQ,UAAUA;AACtB;AAEA,SAAS,uBAAuB,GAAG;AACjCd,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,KAAK,UAAU,cAAc;AAC/B,QAAE,KAAK,KAAK,CAAC,EAAE,YAAY,KAAK;AAChC,QAAE,WAAW,CAAC;IAChB;EACF,CAAC;AACH;AAEA,SAAS,eAAe,GAAG;AACzB,MAAI,OAAO,OAAO;AAClB,MAAI,OAAO;AACX,MAAI,OAAO,OAAO;AAClB,MAAI,OAAO;AACX,MAAI,aAAa,EAAE,MAAK;AACxB,MAAI,UAAU,WAAW,WAAW;AACpC,MAAI,UAAU,WAAW,WAAW;AAEpC,WAAS,YAAY,OAAO;AAC1B,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM;AACd,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAC/B,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAC/B,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAC/B,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;EACjC;AAEAA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,gBAAY,EAAE,KAAK,CAAC,CAAC;EACvB,CAAC;AACDA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACnD,kBAAY,IAAI;IAClB;EACF,CAAC;AAED,UAAQ;AACR,UAAQ;AAERA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,SAAK,KAAK;AACV,SAAK,KAAK;EACZ,CAAC;AAEDA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnBA,YAAU,KAAK,QAAQ,SAAU,GAAG;AAClC,QAAE,KAAK;AACP,QAAE,KAAK;IACT,CAAC;AACD,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACnD,WAAK,KAAK;IACZ;AACA,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACnD,WAAK,KAAK;IACZ;EACF,CAAC;AAED,aAAW,QAAQ,OAAO,OAAO;AACjC,aAAW,SAAS,OAAO,OAAO;AACpC;AAEA,SAAS,qBAAqB,GAAG;AAC/BA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,QAAQ,EAAE,KAAK,EAAE,CAAC;AACtB,QAAI,QAAQ,EAAE,KAAK,EAAE,CAAC;AACtB,QAAI,IAAI;AACR,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,SAAS,CAAA;AACd,WAAK;AACL,WAAK;IACP,OAAO;AACL,WAAK,KAAK,OAAO,CAAC;AAClB,WAAK,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;IACzC;AACA,SAAK,OAAO,QAAQyD,cAAmB,OAAO,EAAE,CAAC;AACjD,SAAK,OAAO,KAAKA,cAAmB,OAAO,EAAE,CAAC;EAChD,CAAC;AACH;AAEA,SAAS,qBAAqB,GAAG;AAC/BzD,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACnD,UAAI,KAAK,aAAa,OAAO,KAAK,aAAa,KAAK;AAClD,aAAK,SAAS,KAAK;MACrB;AACA,cAAQ,KAAK,UAAQ;QACnB,KAAK;AACH,eAAK,KAAK,KAAK,QAAQ,IAAI,KAAK;AAChC;QACF,KAAK;AACH,eAAK,KAAK,KAAK,QAAQ,IAAI,KAAK;AAChC;MACV;IACI;EACF,CAAC;AACH;AAEA,SAAS,8BAA8B,GAAG;AACxCA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,KAAK,UAAU;AACjB,WAAK,OAAO,QAAO;IACrB;EACF,CAAC;AACH;AAEA,SAAS,kBAAkB,GAAG;AAC5BA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,EAAE,SAAS,CAAC,EAAE,QAAQ;AACxB,UAAI,OAAO,EAAE,KAAK,CAAC;AACnB,UAAI,IAAI,EAAE,KAAK,KAAK,SAAS;AAC7B,UAAI,IAAI,EAAE,KAAK,KAAK,YAAY;AAChC,UAAI,IAAI,EAAE,KAAKkC,KAAO,KAAK,UAAU,CAAC;AACtC,UAAI,IAAI,EAAE,KAAKA,KAAO,KAAK,WAAW,CAAC;AAEvC,WAAK,QAAQ,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC;AAC/B,WAAK,SAAS,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC;AAChC,WAAK,IAAI,EAAE,IAAI,KAAK,QAAQ;AAC5B,WAAK,IAAI,EAAE,IAAI,KAAK,SAAS;IAC/B;EACF,CAAC;AAEDlC,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,EAAE,KAAK,CAAC,EAAE,UAAU,UAAU;AAChC,QAAE,WAAW,CAAC;IAChB;EACF,CAAC;AACH;AAEA,SAAS,gBAAgB,GAAG;AAC1BA,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,EAAE,MAAM,EAAE,GAAG;AACf,UAAI,OAAO,EAAE,KAAK,EAAE,CAAC;AACrB,UAAI,CAAC,KAAK,WAAW;AACnB,aAAK,YAAY,CAAA;MACnB;AACA,WAAK,UAAU,KAAK,EAAE,GAAM,OAAO,EAAE,KAAK,CAAC,EAAA,CAAG;AAC9C,QAAE,WAAW,CAAC;IAChB;EACF,CAAC;AACH;AAEA,SAAS,gBAAgB,GAAG;AAC1B,MAAI,SAASoC,iBAAsB,CAAC;AACpCpC,UAAU,QAAQ,SAAU,OAAO;AACjC,QAAI,aAAa;AACjBA,YAAU,OAAO,SAAU,GAAG,GAAG;AAC/B,UAAI,OAAO,EAAE,KAAK,CAAC;AACnB,WAAK,QAAQ,IAAI;AACjBA,cAAU,KAAK,WAAW,SAAU,UAAU;AAC5Ce;UACE;UACA;UACA;YACE,OAAO,SAAS,MAAM;YACtB,QAAQ,SAAS,MAAM;YACvB,MAAM,KAAK;YACX,OAAO,IAAI,EAAE;YACb,GAAG,SAAS;YACZ,OAAO,SAAS;UAC5B;UACU;QACV;MACM,CAAC;AACD,aAAO,KAAK;IACd,CAAC;EACH,CAAC;AACH;AAEA,SAAS,kBAAkB,GAAG;AAC5Bf,UAAU,EAAE,MAAK,GAAI,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,KAAK,UAAU,YAAY;AAC7B,UAAI,WAAW,EAAE,KAAK,KAAK,EAAE,CAAC;AAC9B,UAAI,IAAI,SAAS,IAAI,SAAS,QAAQ;AACtC,UAAI,IAAI,SAAS;AACjB,UAAI,KAAK,KAAK,IAAI;AAClB,UAAI,KAAK,SAAS,SAAS;AAC3B,QAAE,QAAQ,KAAK,GAAG,KAAK,KAAK;AAC5B,QAAE,WAAW,CAAC;AACd,WAAK,MAAM,SAAS;QAClB,EAAE,GAAG,IAAK,IAAI,KAAM,GAAG,GAAG,IAAI,GAAE;QAChC,EAAE,GAAG,IAAK,IAAI,KAAM,GAAG,GAAG,IAAI,GAAE;QAChC,EAAE,GAAG,IAAI,IAAI,EAAI;QACjB,EAAE,GAAG,IAAK,IAAI,KAAM,GAAG,GAAG,IAAI,GAAE;QAChC,EAAE,GAAG,IAAK,IAAI,KAAM,GAAG,GAAG,IAAI,GAAE;MACxC;AACM,WAAK,MAAM,IAAI,KAAK;AACpB,WAAK,MAAM,IAAI,KAAK;IACtB;EACF,CAAC;AACH;AAEA,SAAS,kBAAkB,KAAK,OAAO;AACrC,SAAOyC,UAAYV,KAAO,KAAK,KAAK,GAAG,MAAM;AAC/C;AAEA,SAAS,aAAa,OAAO;AAC3B,MAAI,WAAW,CAAA;AACf/B,UAAU,OAAO,SAAU,GAAG,GAAG;AAC/B,aAAS,EAAE,YAAW,CAAE,IAAI;EAC9B,CAAC;AACD,SAAO;AACT;", "names": ["object", "order", "collection", "values", "_.constant", "_.flatten", "_.map", "_.for<PERSON>ach", "_.range", "run", "_.uniqueId", "g", "dfs", "undo", "rank", "_.isUndefined", "min", "_.min", "_.has", "addBorderNode", "_.max", "maxRank", "util.addDummyNode", "_.minBy", "_.<PERSON><PERSON><PERSON><PERSON>", "_.each", "postorder", "alg.postorder", "_.find", "_.filter", "edge", "alg.preorder", "_.values", "util.addBorderNode", "_.reduce", "_.zipObject", "_.sortBy", "entry", "_.pick", "util.partition", "last", "_.last", "util.maxRank", "util.buildLayerMatrix", "_.clone<PERSON><PERSON>", "w", "max", "_.forIn", "_.map<PERSON><PERSON><PERSON>", "_.merge", "util.asNonCompoundGraph", "_.forOwn", "util.notime", "acyclic.run", "nestingGraph.run", "util.removeEmptyRanks", "nestingGraph.cleanup", "util.normalizeRanks", "normalize.run", "coordinateSystem.adjust", "normalize.undo", "coordinateSystem.undo", "acyclic.undo", "_.defaults", "util.intersectRect"]}