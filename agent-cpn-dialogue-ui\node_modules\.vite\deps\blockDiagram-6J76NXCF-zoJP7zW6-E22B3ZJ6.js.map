{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/mermaid/dist/chunks/mermaid.core/blockDiagram-6J76NXCF.mjs"], "sourcesContent": ["import {\n  getIconStyles\n} from \"./chunk-E2GYISFI.mjs\";\nimport {\n  getLineFunctionsWithOffset\n} from \"./chunk-MXNHSMXR.mjs\";\nimport {\n  getSubGraphTitleMargins\n} from \"./chunk-AC5SNWB5.mjs\";\nimport {\n  createText,\n  replaceIconSubstring\n} from \"./chunk-QESNASVV.mjs\";\nimport {\n  decodeEntities,\n  getStylesFromArray,\n  utils_default\n} from \"./chunk-55PJQP7W.mjs\";\nimport {\n  __name,\n  clear,\n  common_default,\n  configureSvgSize,\n  evaluate,\n  getConfig,\n  getConfig2,\n  getUrl,\n  log,\n  sanitizeText\n} from \"./chunk-3XYRH5AP.mjs\";\n\n// src/diagrams/block/parser/block.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 7], $V1 = [1, 13], $V2 = [1, 14], $V3 = [1, 15], $V4 = [1, 19], $V5 = [1, 16], $V6 = [1, 17], $V7 = [1, 18], $V8 = [8, 30], $V9 = [8, 21, 28, 29, 30, 31, 32, 40, 44, 47], $Va = [1, 23], $Vb = [1, 24], $Vc = [8, 15, 16, 21, 28, 29, 30, 31, 32, 40, 44, 47], $Vd = [8, 15, 16, 21, 27, 28, 29, 30, 31, 32, 40, 44, 47], $Ve = [1, 49];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"spaceLines\": 3, \"SPACELINE\": 4, \"NL\": 5, \"separator\": 6, \"SPACE\": 7, \"EOF\": 8, \"start\": 9, \"BLOCK_DIAGRAM_KEY\": 10, \"document\": 11, \"stop\": 12, \"statement\": 13, \"link\": 14, \"LINK\": 15, \"START_LINK\": 16, \"LINK_LABEL\": 17, \"STR\": 18, \"nodeStatement\": 19, \"columnsStatement\": 20, \"SPACE_BLOCK\": 21, \"blockStatement\": 22, \"classDefStatement\": 23, \"cssClassStatement\": 24, \"styleStatement\": 25, \"node\": 26, \"SIZE\": 27, \"COLUMNS\": 28, \"id-block\": 29, \"end\": 30, \"block\": 31, \"NODE_ID\": 32, \"nodeShapeNLabel\": 33, \"dirList\": 34, \"DIR\": 35, \"NODE_DSTART\": 36, \"NODE_DEND\": 37, \"BLOCK_ARROW_START\": 38, \"BLOCK_ARROW_END\": 39, \"classDef\": 40, \"CLASSDEF_ID\": 41, \"CLASSDEF_STYLEOPTS\": 42, \"DEFAULT\": 43, \"class\": 44, \"CLASSENTITY_IDS\": 45, \"STYLECLASS\": 46, \"style\": 47, \"STYLE_ENTITY_IDS\": 48, \"STYLE_DEFINITION_DATA\": 49, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SPACELINE\", 5: \"NL\", 7: \"SPACE\", 8: \"EOF\", 10: \"BLOCK_DIAGRAM_KEY\", 15: \"LINK\", 16: \"START_LINK\", 17: \"LINK_LABEL\", 18: \"STR\", 21: \"SPACE_BLOCK\", 27: \"SIZE\", 28: \"COLUMNS\", 29: \"id-block\", 30: \"end\", 31: \"block\", 32: \"NODE_ID\", 35: \"DIR\", 36: \"NODE_DSTART\", 37: \"NODE_DEND\", 38: \"BLOCK_ARROW_START\", 39: \"BLOCK_ARROW_END\", 40: \"classDef\", 41: \"CLASSDEF_ID\", 42: \"CLASSDEF_STYLEOPTS\", 43: \"DEFAULT\", 44: \"class\", 45: \"CLASSENTITY_IDS\", 46: \"STYLECLASS\", 47: \"style\", 48: \"STYLE_ENTITY_IDS\", 49: \"STYLE_DEFINITION_DATA\" },\n    productions_: [0, [3, 1], [3, 2], [3, 2], [6, 1], [6, 1], [6, 1], [9, 3], [12, 1], [12, 1], [12, 2], [12, 2], [11, 1], [11, 2], [14, 1], [14, 4], [13, 1], [13, 1], [13, 1], [13, 1], [13, 1], [13, 1], [13, 1], [19, 3], [19, 2], [19, 1], [20, 1], [22, 4], [22, 3], [26, 1], [26, 2], [34, 1], [34, 2], [33, 3], [33, 4], [23, 3], [23, 3], [24, 3], [25, 3]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 4:\n          yy.getLogger().debug(\"Rule: separator (NL) \");\n          break;\n        case 5:\n          yy.getLogger().debug(\"Rule: separator (Space) \");\n          break;\n        case 6:\n          yy.getLogger().debug(\"Rule: separator (EOF) \");\n          break;\n        case 7:\n          yy.getLogger().debug(\"Rule: hierarchy: \", $$[$0 - 1]);\n          yy.setHierarchy($$[$0 - 1]);\n          break;\n        case 8:\n          yy.getLogger().debug(\"Stop NL \");\n          break;\n        case 9:\n          yy.getLogger().debug(\"Stop EOF \");\n          break;\n        case 10:\n          yy.getLogger().debug(\"Stop NL2 \");\n          break;\n        case 11:\n          yy.getLogger().debug(\"Stop EOF2 \");\n          break;\n        case 12:\n          yy.getLogger().debug(\"Rule: statement: \", $$[$0]);\n          typeof $$[$0].length === \"number\" ? this.$ = $$[$0] : this.$ = [$$[$0]];\n          break;\n        case 13:\n          yy.getLogger().debug(\"Rule: statement #2: \", $$[$0 - 1]);\n          this.$ = [$$[$0 - 1]].concat($$[$0]);\n          break;\n        case 14:\n          yy.getLogger().debug(\"Rule: link: \", $$[$0], yytext);\n          this.$ = { edgeTypeStr: $$[$0], label: \"\" };\n          break;\n        case 15:\n          yy.getLogger().debug(\"Rule: LABEL link: \", $$[$0 - 3], $$[$0 - 1], $$[$0]);\n          this.$ = { edgeTypeStr: $$[$0], label: $$[$0 - 1] };\n          break;\n        case 18:\n          const num = parseInt($$[$0]);\n          const spaceId = yy.generateId();\n          this.$ = { id: spaceId, type: \"space\", label: \"\", width: num, children: [] };\n          break;\n        case 23:\n          yy.getLogger().debug(\"Rule: (nodeStatement link node) \", $$[$0 - 2], $$[$0 - 1], $$[$0], \" typestr: \", $$[$0 - 1].edgeTypeStr);\n          const edgeData = yy.edgeStrToEdgeData($$[$0 - 1].edgeTypeStr);\n          this.$ = [\n            { id: $$[$0 - 2].id, label: $$[$0 - 2].label, type: $$[$0 - 2].type, directions: $$[$0 - 2].directions },\n            { id: $$[$0 - 2].id + \"-\" + $$[$0].id, start: $$[$0 - 2].id, end: $$[$0].id, label: $$[$0 - 1].label, type: \"edge\", directions: $$[$0].directions, arrowTypeEnd: edgeData, arrowTypeStart: \"arrow_open\" },\n            { id: $$[$0].id, label: $$[$0].label, type: yy.typeStr2Type($$[$0].typeStr), directions: $$[$0].directions }\n          ];\n          break;\n        case 24:\n          yy.getLogger().debug(\"Rule: nodeStatement (abc88 node size) \", $$[$0 - 1], $$[$0]);\n          this.$ = { id: $$[$0 - 1].id, label: $$[$0 - 1].label, type: yy.typeStr2Type($$[$0 - 1].typeStr), directions: $$[$0 - 1].directions, widthInColumns: parseInt($$[$0], 10) };\n          break;\n        case 25:\n          yy.getLogger().debug(\"Rule: nodeStatement (node) \", $$[$0]);\n          this.$ = { id: $$[$0].id, label: $$[$0].label, type: yy.typeStr2Type($$[$0].typeStr), directions: $$[$0].directions, widthInColumns: 1 };\n          break;\n        case 26:\n          yy.getLogger().debug(\"APA123\", this ? this : \"na\");\n          yy.getLogger().debug(\"COLUMNS: \", $$[$0]);\n          this.$ = { type: \"column-setting\", columns: $$[$0] === \"auto\" ? -1 : parseInt($$[$0]) };\n          break;\n        case 27:\n          yy.getLogger().debug(\"Rule: id-block statement : \", $$[$0 - 2], $$[$0 - 1]);\n          const id2 = yy.generateId();\n          this.$ = { ...$$[$0 - 2], type: \"composite\", children: $$[$0 - 1] };\n          break;\n        case 28:\n          yy.getLogger().debug(\"Rule: blockStatement : \", $$[$0 - 2], $$[$0 - 1], $$[$0]);\n          const id = yy.generateId();\n          this.$ = { id, type: \"composite\", label: \"\", children: $$[$0 - 1] };\n          break;\n        case 29:\n          yy.getLogger().debug(\"Rule: node (NODE_ID separator): \", $$[$0]);\n          this.$ = { id: $$[$0] };\n          break;\n        case 30:\n          yy.getLogger().debug(\"Rule: node (NODE_ID nodeShapeNLabel separator): \", $$[$0 - 1], $$[$0]);\n          this.$ = { id: $$[$0 - 1], label: $$[$0].label, typeStr: $$[$0].typeStr, directions: $$[$0].directions };\n          break;\n        case 31:\n          yy.getLogger().debug(\"Rule: dirList: \", $$[$0]);\n          this.$ = [$$[$0]];\n          break;\n        case 32:\n          yy.getLogger().debug(\"Rule: dirList: \", $$[$0 - 1], $$[$0]);\n          this.$ = [$$[$0 - 1]].concat($$[$0]);\n          break;\n        case 33:\n          yy.getLogger().debug(\"Rule: nodeShapeNLabel: \", $$[$0 - 2], $$[$0 - 1], $$[$0]);\n          this.$ = { typeStr: $$[$0 - 2] + $$[$0], label: $$[$0 - 1] };\n          break;\n        case 34:\n          yy.getLogger().debug(\"Rule: BLOCK_ARROW nodeShapeNLabel: \", $$[$0 - 3], $$[$0 - 2], \" #3:\", $$[$0 - 1], $$[$0]);\n          this.$ = { typeStr: $$[$0 - 3] + $$[$0], label: $$[$0 - 2], directions: $$[$0 - 1] };\n          break;\n        case 35:\n        case 36:\n          this.$ = { type: \"classDef\", id: $$[$0 - 1].trim(), css: $$[$0].trim() };\n          break;\n        case 37:\n          this.$ = { type: \"applyClass\", id: $$[$0 - 1].trim(), styleClass: $$[$0].trim() };\n          break;\n        case 38:\n          this.$ = { type: \"applyStyles\", id: $$[$0 - 1].trim(), stylesStr: $$[$0].trim() };\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 9: 1, 10: [1, 2] }, { 1: [3] }, { 11: 3, 13: 4, 19: 5, 20: 6, 21: $V0, 22: 8, 23: 9, 24: 10, 25: 11, 26: 12, 28: $V1, 29: $V2, 31: $V3, 32: $V4, 40: $V5, 44: $V6, 47: $V7 }, { 8: [1, 20] }, o($V8, [2, 12], { 13: 4, 19: 5, 20: 6, 22: 8, 23: 9, 24: 10, 25: 11, 26: 12, 11: 21, 21: $V0, 28: $V1, 29: $V2, 31: $V3, 32: $V4, 40: $V5, 44: $V6, 47: $V7 }), o($V9, [2, 16], { 14: 22, 15: $Va, 16: $Vb }), o($V9, [2, 17]), o($V9, [2, 18]), o($V9, [2, 19]), o($V9, [2, 20]), o($V9, [2, 21]), o($V9, [2, 22]), o($Vc, [2, 25], { 27: [1, 25] }), o($V9, [2, 26]), { 19: 26, 26: 12, 32: $V4 }, { 11: 27, 13: 4, 19: 5, 20: 6, 21: $V0, 22: 8, 23: 9, 24: 10, 25: 11, 26: 12, 28: $V1, 29: $V2, 31: $V3, 32: $V4, 40: $V5, 44: $V6, 47: $V7 }, { 41: [1, 28], 43: [1, 29] }, { 45: [1, 30] }, { 48: [1, 31] }, o($Vd, [2, 29], { 33: 32, 36: [1, 33], 38: [1, 34] }), { 1: [2, 7] }, o($V8, [2, 13]), { 26: 35, 32: $V4 }, { 32: [2, 14] }, { 17: [1, 36] }, o($Vc, [2, 24]), { 11: 37, 13: 4, 14: 22, 15: $Va, 16: $Vb, 19: 5, 20: 6, 21: $V0, 22: 8, 23: 9, 24: 10, 25: 11, 26: 12, 28: $V1, 29: $V2, 31: $V3, 32: $V4, 40: $V5, 44: $V6, 47: $V7 }, { 30: [1, 38] }, { 42: [1, 39] }, { 42: [1, 40] }, { 46: [1, 41] }, { 49: [1, 42] }, o($Vd, [2, 30]), { 18: [1, 43] }, { 18: [1, 44] }, o($Vc, [2, 23]), { 18: [1, 45] }, { 30: [1, 46] }, o($V9, [2, 28]), o($V9, [2, 35]), o($V9, [2, 36]), o($V9, [2, 37]), o($V9, [2, 38]), { 37: [1, 47] }, { 34: 48, 35: $Ve }, { 15: [1, 50] }, o($V9, [2, 27]), o($Vd, [2, 33]), { 39: [1, 51] }, { 34: 52, 35: $Ve, 39: [2, 31] }, { 32: [2, 15] }, o($Vd, [2, 34]), { 39: [2, 32] }],\n    defaultActions: { 20: [2, 7], 23: [2, 14], 50: [2, 15], 52: [2, 32] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: {},\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 10;\n            break;\n          case 1:\n            yy.getLogger().debug(\"Found space-block\");\n            return 31;\n            break;\n          case 2:\n            yy.getLogger().debug(\"Found nl-block\");\n            return 31;\n            break;\n          case 3:\n            yy.getLogger().debug(\"Found space-block\");\n            return 29;\n            break;\n          case 4:\n            yy.getLogger().debug(\".\", yy_.yytext);\n            break;\n          case 5:\n            yy.getLogger().debug(\"_\", yy_.yytext);\n            break;\n          case 6:\n            return 5;\n            break;\n          case 7:\n            yy_.yytext = -1;\n            return 28;\n            break;\n          case 8:\n            yy_.yytext = yy_.yytext.replace(/columns\\s+/, \"\");\n            yy.getLogger().debug(\"COLUMNS (LEX)\", yy_.yytext);\n            return 28;\n            break;\n          case 9:\n            this.pushState(\"md_string\");\n            break;\n          case 10:\n            return \"MD_STR\";\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            this.pushState(\"string\");\n            break;\n          case 13:\n            yy.getLogger().debug(\"LEX: POPPING STR:\", yy_.yytext);\n            this.popState();\n            break;\n          case 14:\n            yy.getLogger().debug(\"LEX: STR end:\", yy_.yytext);\n            return \"STR\";\n            break;\n          case 15:\n            yy_.yytext = yy_.yytext.replace(/space\\:/, \"\");\n            yy.getLogger().debug(\"SPACE NUM (LEX)\", yy_.yytext);\n            return 21;\n            break;\n          case 16:\n            yy_.yytext = \"1\";\n            yy.getLogger().debug(\"COLUMNS (LEX)\", yy_.yytext);\n            return 21;\n            break;\n          case 17:\n            return 43;\n            break;\n          case 18:\n            return \"LINKSTYLE\";\n            break;\n          case 19:\n            return \"INTERPOLATE\";\n            break;\n          case 20:\n            this.pushState(\"CLASSDEF\");\n            return 40;\n            break;\n          case 21:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return \"DEFAULT_CLASSDEF_ID\";\n            break;\n          case 22:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return 41;\n            break;\n          case 23:\n            this.popState();\n            return 42;\n            break;\n          case 24:\n            this.pushState(\"CLASS\");\n            return 44;\n            break;\n          case 25:\n            this.popState();\n            this.pushState(\"CLASS_STYLE\");\n            return 45;\n            break;\n          case 26:\n            this.popState();\n            return 46;\n            break;\n          case 27:\n            this.pushState(\"STYLE_STMNT\");\n            return 47;\n            break;\n          case 28:\n            this.popState();\n            this.pushState(\"STYLE_DEFINITION\");\n            return 48;\n            break;\n          case 29:\n            this.popState();\n            return 49;\n            break;\n          case 30:\n            this.pushState(\"acc_title\");\n            return \"acc_title\";\n            break;\n          case 31:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 32:\n            this.pushState(\"acc_descr\");\n            return \"acc_descr\";\n            break;\n          case 33:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 34:\n            this.pushState(\"acc_descr_multiline\");\n            break;\n          case 35:\n            this.popState();\n            break;\n          case 36:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 37:\n            return 30;\n            break;\n          case 38:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ((\");\n            return \"NODE_DEND\";\n            break;\n          case 39:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ((\");\n            return \"NODE_DEND\";\n            break;\n          case 40:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ))\");\n            return \"NODE_DEND\";\n            break;\n          case 41:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ((\");\n            return \"NODE_DEND\";\n            break;\n          case 42:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ((\");\n            return \"NODE_DEND\";\n            break;\n          case 43:\n            this.popState();\n            yy.getLogger().debug(\"Lex: (-\");\n            return \"NODE_DEND\";\n            break;\n          case 44:\n            this.popState();\n            yy.getLogger().debug(\"Lex: -)\");\n            return \"NODE_DEND\";\n            break;\n          case 45:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ((\");\n            return \"NODE_DEND\";\n            break;\n          case 46:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ]]\");\n            return \"NODE_DEND\";\n            break;\n          case 47:\n            this.popState();\n            yy.getLogger().debug(\"Lex: (\");\n            return \"NODE_DEND\";\n            break;\n          case 48:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ])\");\n            return \"NODE_DEND\";\n            break;\n          case 49:\n            this.popState();\n            yy.getLogger().debug(\"Lex: /]\");\n            return \"NODE_DEND\";\n            break;\n          case 50:\n            this.popState();\n            yy.getLogger().debug(\"Lex: /]\");\n            return \"NODE_DEND\";\n            break;\n          case 51:\n            this.popState();\n            yy.getLogger().debug(\"Lex: )]\");\n            return \"NODE_DEND\";\n            break;\n          case 52:\n            this.popState();\n            yy.getLogger().debug(\"Lex: )\");\n            return \"NODE_DEND\";\n            break;\n          case 53:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ]>\");\n            return \"NODE_DEND\";\n            break;\n          case 54:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ]\");\n            return \"NODE_DEND\";\n            break;\n          case 55:\n            yy.getLogger().debug(\"Lexa: -)\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 56:\n            yy.getLogger().debug(\"Lexa: (-\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 57:\n            yy.getLogger().debug(\"Lexa: ))\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 58:\n            yy.getLogger().debug(\"Lexa: )\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 59:\n            yy.getLogger().debug(\"Lex: (((\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 60:\n            yy.getLogger().debug(\"Lexa: )\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 61:\n            yy.getLogger().debug(\"Lexa: )\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 62:\n            yy.getLogger().debug(\"Lexa: )\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 63:\n            yy.getLogger().debug(\"Lexc: >\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 64:\n            yy.getLogger().debug(\"Lexa: ([\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 65:\n            yy.getLogger().debug(\"Lexa: )\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 66:\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 67:\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 68:\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 69:\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 70:\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 71:\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 72:\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 73:\n            yy.getLogger().debug(\"Lexa: [\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 74:\n            this.pushState(\"BLOCK_ARROW\");\n            yy.getLogger().debug(\"LEX ARR START\");\n            return 38;\n            break;\n          case 75:\n            yy.getLogger().debug(\"Lex: NODE_ID\", yy_.yytext);\n            return 32;\n            break;\n          case 76:\n            yy.getLogger().debug(\"Lex: EOF\", yy_.yytext);\n            return 8;\n            break;\n          case 77:\n            this.pushState(\"md_string\");\n            break;\n          case 78:\n            this.pushState(\"md_string\");\n            break;\n          case 79:\n            return \"NODE_DESCR\";\n            break;\n          case 80:\n            this.popState();\n            break;\n          case 81:\n            yy.getLogger().debug(\"Lex: Starting string\");\n            this.pushState(\"string\");\n            break;\n          case 82:\n            yy.getLogger().debug(\"LEX ARR: Starting string\");\n            this.pushState(\"string\");\n            break;\n          case 83:\n            yy.getLogger().debug(\"LEX: NODE_DESCR:\", yy_.yytext);\n            return \"NODE_DESCR\";\n            break;\n          case 84:\n            yy.getLogger().debug(\"LEX POPPING\");\n            this.popState();\n            break;\n          case 85:\n            yy.getLogger().debug(\"Lex: =>BAE\");\n            this.pushState(\"ARROW_DIR\");\n            break;\n          case 86:\n            yy_.yytext = yy_.yytext.replace(/^,\\s*/, \"\");\n            yy.getLogger().debug(\"Lex (right): dir:\", yy_.yytext);\n            return \"DIR\";\n            break;\n          case 87:\n            yy_.yytext = yy_.yytext.replace(/^,\\s*/, \"\");\n            yy.getLogger().debug(\"Lex (left):\", yy_.yytext);\n            return \"DIR\";\n            break;\n          case 88:\n            yy_.yytext = yy_.yytext.replace(/^,\\s*/, \"\");\n            yy.getLogger().debug(\"Lex (x):\", yy_.yytext);\n            return \"DIR\";\n            break;\n          case 89:\n            yy_.yytext = yy_.yytext.replace(/^,\\s*/, \"\");\n            yy.getLogger().debug(\"Lex (y):\", yy_.yytext);\n            return \"DIR\";\n            break;\n          case 90:\n            yy_.yytext = yy_.yytext.replace(/^,\\s*/, \"\");\n            yy.getLogger().debug(\"Lex (up):\", yy_.yytext);\n            return \"DIR\";\n            break;\n          case 91:\n            yy_.yytext = yy_.yytext.replace(/^,\\s*/, \"\");\n            yy.getLogger().debug(\"Lex (down):\", yy_.yytext);\n            return \"DIR\";\n            break;\n          case 92:\n            yy_.yytext = \"]>\";\n            yy.getLogger().debug(\"Lex (ARROW_DIR end):\", yy_.yytext);\n            this.popState();\n            this.popState();\n            return \"BLOCK_ARROW_END\";\n            break;\n          case 93:\n            yy.getLogger().debug(\"Lex: LINK\", \"#\" + yy_.yytext + \"#\");\n            return 15;\n            break;\n          case 94:\n            yy.getLogger().debug(\"Lex: LINK\", yy_.yytext);\n            return 15;\n            break;\n          case 95:\n            yy.getLogger().debug(\"Lex: LINK\", yy_.yytext);\n            return 15;\n            break;\n          case 96:\n            yy.getLogger().debug(\"Lex: LINK\", yy_.yytext);\n            return 15;\n            break;\n          case 97:\n            yy.getLogger().debug(\"Lex: START_LINK\", yy_.yytext);\n            this.pushState(\"LLABEL\");\n            return 16;\n            break;\n          case 98:\n            yy.getLogger().debug(\"Lex: START_LINK\", yy_.yytext);\n            this.pushState(\"LLABEL\");\n            return 16;\n            break;\n          case 99:\n            yy.getLogger().debug(\"Lex: START_LINK\", yy_.yytext);\n            this.pushState(\"LLABEL\");\n            return 16;\n            break;\n          case 100:\n            this.pushState(\"md_string\");\n            break;\n          case 101:\n            yy.getLogger().debug(\"Lex: Starting string\");\n            this.pushState(\"string\");\n            return \"LINK_LABEL\";\n            break;\n          case 102:\n            this.popState();\n            yy.getLogger().debug(\"Lex: LINK\", \"#\" + yy_.yytext + \"#\");\n            return 15;\n            break;\n          case 103:\n            this.popState();\n            yy.getLogger().debug(\"Lex: LINK\", yy_.yytext);\n            return 15;\n            break;\n          case 104:\n            this.popState();\n            yy.getLogger().debug(\"Lex: LINK\", yy_.yytext);\n            return 15;\n            break;\n          case 105:\n            yy.getLogger().debug(\"Lex: COLON\", yy_.yytext);\n            yy_.yytext = yy_.yytext.slice(1);\n            return 27;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:block-beta\\b)/, /^(?:block\\s+)/, /^(?:block\\n+)/, /^(?:block:)/, /^(?:[\\s]+)/, /^(?:[\\n]+)/, /^(?:((\\u000D\\u000A)|(\\u000A)))/, /^(?:columns\\s+auto\\b)/, /^(?:columns\\s+[\\d]+)/, /^(?:[\"][`])/, /^(?:[^`\"]+)/, /^(?:[`][\"])/, /^(?:[\"])/, /^(?:[\"])/, /^(?:[^\"]*)/, /^(?:space[:]\\d+)/, /^(?:space\\b)/, /^(?:default\\b)/, /^(?:linkStyle\\b)/, /^(?:interpolate\\b)/, /^(?:classDef\\s+)/, /^(?:DEFAULT\\s+)/, /^(?:\\w+\\s+)/, /^(?:[^\\n]*)/, /^(?:class\\s+)/, /^(?:(\\w+)+((,\\s*\\w+)*))/, /^(?:[^\\n]*)/, /^(?:style\\s+)/, /^(?:(\\w+)+((,\\s*\\w+)*))/, /^(?:[^\\n]*)/, /^(?:accTitle\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*\\{\\s*)/, /^(?:[\\}])/, /^(?:[^\\}]*)/, /^(?:end\\b\\s*)/, /^(?:\\(\\(\\()/, /^(?:\\)\\)\\))/, /^(?:[\\)]\\))/, /^(?:\\}\\})/, /^(?:\\})/, /^(?:\\(-)/, /^(?:-\\))/, /^(?:\\(\\()/, /^(?:\\]\\])/, /^(?:\\()/, /^(?:\\]\\))/, /^(?:\\\\\\])/, /^(?:\\/\\])/, /^(?:\\)\\])/, /^(?:[\\)])/, /^(?:\\]>)/, /^(?:[\\]])/, /^(?:-\\))/, /^(?:\\(-)/, /^(?:\\)\\))/, /^(?:\\))/, /^(?:\\(\\(\\()/, /^(?:\\(\\()/, /^(?:\\{\\{)/, /^(?:\\{)/, /^(?:>)/, /^(?:\\(\\[)/, /^(?:\\()/, /^(?:\\[\\[)/, /^(?:\\[\\|)/, /^(?:\\[\\()/, /^(?:\\)\\)\\))/, /^(?:\\[\\\\)/, /^(?:\\[\\/)/, /^(?:\\[\\\\)/, /^(?:\\[)/, /^(?:<\\[)/, /^(?:[^\\(\\[\\n\\-\\)\\{\\}\\s\\<\\>:]+)/, /^(?:$)/, /^(?:[\"][`])/, /^(?:[\"][`])/, /^(?:[^`\"]+)/, /^(?:[`][\"])/, /^(?:[\"])/, /^(?:[\"])/, /^(?:[^\"]+)/, /^(?:[\"])/, /^(?:\\]>\\s*\\()/, /^(?:,?\\s*right\\s*)/, /^(?:,?\\s*left\\s*)/, /^(?:,?\\s*x\\s*)/, /^(?:,?\\s*y\\s*)/, /^(?:,?\\s*up\\s*)/, /^(?:,?\\s*down\\s*)/, /^(?:\\)\\s*)/, /^(?:\\s*[xo<]?--+[-xo>]\\s*)/, /^(?:\\s*[xo<]?==+[=xo>]\\s*)/, /^(?:\\s*[xo<]?-?\\.+-[xo>]?\\s*)/, /^(?:\\s*~~[\\~]+\\s*)/, /^(?:\\s*[xo<]?--\\s*)/, /^(?:\\s*[xo<]?==\\s*)/, /^(?:\\s*[xo<]?-\\.\\s*)/, /^(?:[\"][`])/, /^(?:[\"])/, /^(?:\\s*[xo<]?--+[-xo>]\\s*)/, /^(?:\\s*[xo<]?==+[=xo>]\\s*)/, /^(?:\\s*[xo<]?-?\\.+-[xo>]?\\s*)/, /^(?::\\d+)/],\n      conditions: { \"STYLE_DEFINITION\": { \"rules\": [29], \"inclusive\": false }, \"STYLE_STMNT\": { \"rules\": [28], \"inclusive\": false }, \"CLASSDEFID\": { \"rules\": [23], \"inclusive\": false }, \"CLASSDEF\": { \"rules\": [21, 22], \"inclusive\": false }, \"CLASS_STYLE\": { \"rules\": [26], \"inclusive\": false }, \"CLASS\": { \"rules\": [25], \"inclusive\": false }, \"LLABEL\": { \"rules\": [100, 101, 102, 103, 104], \"inclusive\": false }, \"ARROW_DIR\": { \"rules\": [86, 87, 88, 89, 90, 91, 92], \"inclusive\": false }, \"BLOCK_ARROW\": { \"rules\": [77, 82, 85], \"inclusive\": false }, \"NODE\": { \"rules\": [38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 78, 81], \"inclusive\": false }, \"md_string\": { \"rules\": [10, 11, 79, 80], \"inclusive\": false }, \"space\": { \"rules\": [], \"inclusive\": false }, \"string\": { \"rules\": [13, 14, 83, 84], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [35, 36], \"inclusive\": false }, \"acc_descr\": { \"rules\": [33], \"inclusive\": false }, \"acc_title\": { \"rules\": [31], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 12, 15, 16, 17, 18, 19, 20, 24, 27, 30, 32, 34, 37, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 93, 94, 95, 96, 97, 98, 99, 105], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar block_default = parser;\n\n// src/diagrams/block/blockDB.ts\nimport clone from \"lodash-es/clone.js\";\nvar blockDatabase = /* @__PURE__ */ new Map();\nvar edgeList = [];\nvar edgeCount = /* @__PURE__ */ new Map();\nvar COLOR_KEYWORD = \"color\";\nvar FILL_KEYWORD = \"fill\";\nvar BG_FILL = \"bgFill\";\nvar STYLECLASS_SEP = \",\";\nvar config = getConfig2();\nvar classes = /* @__PURE__ */ new Map();\nvar sanitizeText2 = /* @__PURE__ */ __name((txt) => common_default.sanitizeText(txt, config), \"sanitizeText\");\nvar addStyleClass = /* @__PURE__ */ __name(function(id, styleAttributes = \"\") {\n  let foundClass = classes.get(id);\n  if (!foundClass) {\n    foundClass = { id, styles: [], textStyles: [] };\n    classes.set(id, foundClass);\n  }\n  if (styleAttributes !== void 0 && styleAttributes !== null) {\n    styleAttributes.split(STYLECLASS_SEP).forEach((attrib) => {\n      const fixedAttrib = attrib.replace(/([^;]*);/, \"$1\").trim();\n      if (RegExp(COLOR_KEYWORD).exec(attrib)) {\n        const newStyle1 = fixedAttrib.replace(FILL_KEYWORD, BG_FILL);\n        const newStyle2 = newStyle1.replace(COLOR_KEYWORD, FILL_KEYWORD);\n        foundClass.textStyles.push(newStyle2);\n      }\n      foundClass.styles.push(fixedAttrib);\n    });\n  }\n}, \"addStyleClass\");\nvar addStyle2Node = /* @__PURE__ */ __name(function(id, styles = \"\") {\n  const foundBlock = blockDatabase.get(id);\n  if (styles !== void 0 && styles !== null) {\n    foundBlock.styles = styles.split(STYLECLASS_SEP);\n  }\n}, \"addStyle2Node\");\nvar setCssClass = /* @__PURE__ */ __name(function(itemIds, cssClassName) {\n  itemIds.split(\",\").forEach(function(id) {\n    let foundBlock = blockDatabase.get(id);\n    if (foundBlock === void 0) {\n      const trimmedId = id.trim();\n      foundBlock = { id: trimmedId, type: \"na\", children: [] };\n      blockDatabase.set(trimmedId, foundBlock);\n    }\n    if (!foundBlock.classes) {\n      foundBlock.classes = [];\n    }\n    foundBlock.classes.push(cssClassName);\n  });\n}, \"setCssClass\");\nvar populateBlockDatabase = /* @__PURE__ */ __name((_blockList, parent) => {\n  const blockList = _blockList.flat();\n  const children = [];\n  for (const block of blockList) {\n    if (block.label) {\n      block.label = sanitizeText2(block.label);\n    }\n    if (block.type === \"classDef\") {\n      addStyleClass(block.id, block.css);\n      continue;\n    }\n    if (block.type === \"applyClass\") {\n      setCssClass(block.id, block?.styleClass ?? \"\");\n      continue;\n    }\n    if (block.type === \"applyStyles\") {\n      if (block?.stylesStr) {\n        addStyle2Node(block.id, block?.stylesStr);\n      }\n      continue;\n    }\n    if (block.type === \"column-setting\") {\n      parent.columns = block.columns ?? -1;\n    } else if (block.type === \"edge\") {\n      const count = (edgeCount.get(block.id) ?? 0) + 1;\n      edgeCount.set(block.id, count);\n      block.id = count + \"-\" + block.id;\n      edgeList.push(block);\n    } else {\n      if (!block.label) {\n        if (block.type === \"composite\") {\n          block.label = \"\";\n        } else {\n          block.label = block.id;\n        }\n      }\n      const existingBlock = blockDatabase.get(block.id);\n      if (existingBlock === void 0) {\n        blockDatabase.set(block.id, block);\n      } else {\n        if (block.type !== \"na\") {\n          existingBlock.type = block.type;\n        }\n        if (block.label !== block.id) {\n          existingBlock.label = block.label;\n        }\n      }\n      if (block.children) {\n        populateBlockDatabase(block.children, block);\n      }\n      if (block.type === \"space\") {\n        const w = block.width ?? 1;\n        for (let j = 0; j < w; j++) {\n          const newBlock = clone(block);\n          newBlock.id = newBlock.id + \"-\" + j;\n          blockDatabase.set(newBlock.id, newBlock);\n          children.push(newBlock);\n        }\n      } else if (existingBlock === void 0) {\n        children.push(block);\n      }\n    }\n  }\n  parent.children = children;\n}, \"populateBlockDatabase\");\nvar blocks = [];\nvar rootBlock = { id: \"root\", type: \"composite\", children: [], columns: -1 };\nvar clear2 = /* @__PURE__ */ __name(() => {\n  log.debug(\"Clear called\");\n  clear();\n  rootBlock = { id: \"root\", type: \"composite\", children: [], columns: -1 };\n  blockDatabase = /* @__PURE__ */ new Map([[\"root\", rootBlock]]);\n  blocks = [];\n  classes = /* @__PURE__ */ new Map();\n  edgeList = [];\n  edgeCount = /* @__PURE__ */ new Map();\n}, \"clear\");\nfunction typeStr2Type(typeStr) {\n  log.debug(\"typeStr2Type\", typeStr);\n  switch (typeStr) {\n    case \"[]\":\n      return \"square\";\n    case \"()\":\n      log.debug(\"we have a round\");\n      return \"round\";\n    case \"(())\":\n      return \"circle\";\n    case \">]\":\n      return \"rect_left_inv_arrow\";\n    case \"{}\":\n      return \"diamond\";\n    case \"{{}}\":\n      return \"hexagon\";\n    case \"([])\":\n      return \"stadium\";\n    case \"[[]]\":\n      return \"subroutine\";\n    case \"[()]\":\n      return \"cylinder\";\n    case \"((()))\":\n      return \"doublecircle\";\n    case \"[//]\":\n      return \"lean_right\";\n    case \"[\\\\\\\\]\":\n      return \"lean_left\";\n    case \"[/\\\\]\":\n      return \"trapezoid\";\n    case \"[\\\\/]\":\n      return \"inv_trapezoid\";\n    case \"<[]>\":\n      return \"block_arrow\";\n    default:\n      return \"na\";\n  }\n}\n__name(typeStr2Type, \"typeStr2Type\");\nfunction edgeTypeStr2Type(typeStr) {\n  log.debug(\"typeStr2Type\", typeStr);\n  switch (typeStr) {\n    case \"==\":\n      return \"thick\";\n    default:\n      return \"normal\";\n  }\n}\n__name(edgeTypeStr2Type, \"edgeTypeStr2Type\");\nfunction edgeStrToEdgeData(typeStr) {\n  switch (typeStr.trim()) {\n    case \"--x\":\n      return \"arrow_cross\";\n    case \"--o\":\n      return \"arrow_circle\";\n    default:\n      return \"arrow_point\";\n  }\n}\n__name(edgeStrToEdgeData, \"edgeStrToEdgeData\");\nvar cnt = 0;\nvar generateId = /* @__PURE__ */ __name(() => {\n  cnt++;\n  return \"id-\" + Math.random().toString(36).substr(2, 12) + \"-\" + cnt;\n}, \"generateId\");\nvar setHierarchy = /* @__PURE__ */ __name((block) => {\n  rootBlock.children = block;\n  populateBlockDatabase(block, rootBlock);\n  blocks = rootBlock.children;\n}, \"setHierarchy\");\nvar getColumns = /* @__PURE__ */ __name((blockId) => {\n  const block = blockDatabase.get(blockId);\n  if (!block) {\n    return -1;\n  }\n  if (block.columns) {\n    return block.columns;\n  }\n  if (!block.children) {\n    return -1;\n  }\n  return block.children.length;\n}, \"getColumns\");\nvar getBlocksFlat = /* @__PURE__ */ __name(() => {\n  return [...blockDatabase.values()];\n}, \"getBlocksFlat\");\nvar getBlocks = /* @__PURE__ */ __name(() => {\n  return blocks || [];\n}, \"getBlocks\");\nvar getEdges = /* @__PURE__ */ __name(() => {\n  return edgeList;\n}, \"getEdges\");\nvar getBlock = /* @__PURE__ */ __name((id) => {\n  return blockDatabase.get(id);\n}, \"getBlock\");\nvar setBlock = /* @__PURE__ */ __name((block) => {\n  blockDatabase.set(block.id, block);\n}, \"setBlock\");\nvar getLogger = /* @__PURE__ */ __name(() => log, \"getLogger\");\nvar getClasses = /* @__PURE__ */ __name(function() {\n  return classes;\n}, \"getClasses\");\nvar db = {\n  getConfig: /* @__PURE__ */ __name(() => getConfig().block, \"getConfig\"),\n  typeStr2Type,\n  edgeTypeStr2Type,\n  edgeStrToEdgeData,\n  getLogger,\n  getBlocksFlat,\n  getBlocks,\n  getEdges,\n  setHierarchy,\n  getBlock,\n  setBlock,\n  getColumns,\n  getClasses,\n  clear: clear2,\n  generateId\n};\nvar blockDB_default = db;\n\n// src/diagrams/block/styles.ts\nimport * as khroma from \"khroma\";\nvar fade = /* @__PURE__ */ __name((color, opacity) => {\n  const channel2 = khroma.channel;\n  const r = channel2(color, \"r\");\n  const g = channel2(color, \"g\");\n  const b = channel2(color, \"b\");\n  return khroma.rgba(r, g, b, opacity);\n}, \"fade\");\nvar getStyles = /* @__PURE__ */ __name((options) => `.label {\n    font-family: ${options.fontFamily};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n  .cluster-label text {\n    fill: ${options.titleColor};\n  }\n  .cluster-label span,p {\n    color: ${options.titleColor};\n  }\n\n\n\n  .label text,span,p {\n    fill: ${options.nodeTextColor || options.textColor};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n  .flowchart-label text {\n    text-anchor: middle;\n  }\n  // .flowchart-label .text-outer-tspan {\n  //   text-anchor: middle;\n  // }\n  // .flowchart-label .text-inner-tspan {\n  //   text-anchor: start;\n  // }\n\n  .node .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n  .arrowheadPath {\n    fill: ${options.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${options.lineColor};\n    stroke-width: 2.0px;\n  }\n\n  .flowchart-link {\n    stroke: ${options.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${options.edgeLabelBackground};\n    rect {\n      opacity: 0.5;\n      background-color: ${options.edgeLabelBackground};\n      fill: ${options.edgeLabelBackground};\n    }\n    text-align: center;\n  }\n\n  /* For html labels only */\n  .labelBkg {\n    background-color: ${fade(options.edgeLabelBackground, 0.5)};\n    // background-color:\n  }\n\n  .node .cluster {\n    // fill: ${fade(options.mainBkg, 0.5)};\n    fill: ${fade(options.clusterBkg, 0.5)};\n    stroke: ${fade(options.clusterBorder, 0.2)};\n    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;\n    stroke-width: 1px;\n  }\n\n  .cluster text {\n    fill: ${options.titleColor};\n  }\n\n  .cluster span,p {\n    color: ${options.titleColor};\n  }\n  /* .cluster div {\n    color: ${options.titleColor};\n  } */\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: ${options.fontFamily};\n    font-size: 12px;\n    background: ${options.tertiaryColor};\n    border: 1px solid ${options.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .flowchartTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.textColor};\n  }\n  ${getIconStyles()}\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/block/blockRenderer.ts\nimport { select as d3select } from \"d3\";\n\n// src/dagre-wrapper/markers.js\nvar insertMarkers = /* @__PURE__ */ __name((elem, markerArray, type, id) => {\n  markerArray.forEach((markerName) => {\n    markers[markerName](elem, type, id);\n  });\n}, \"insertMarkers\");\nvar extension = /* @__PURE__ */ __name((elem, type, id) => {\n  log.trace(\"Making markers for \", id);\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-extensionStart\").attr(\"class\", \"marker extension \" + type).attr(\"refX\", 18).attr(\"refY\", 7).attr(\"markerWidth\", 190).attr(\"markerHeight\", 240).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 1,7 L18,13 V 1 Z\");\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-extensionEnd\").attr(\"class\", \"marker extension \" + type).attr(\"refX\", 1).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 1,1 V 13 L18,7 Z\");\n}, \"extension\");\nvar composition = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-compositionStart\").attr(\"class\", \"marker composition \" + type).attr(\"refX\", 18).attr(\"refY\", 7).attr(\"markerWidth\", 190).attr(\"markerHeight\", 240).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L1,7 L9,1 Z\");\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-compositionEnd\").attr(\"class\", \"marker composition \" + type).attr(\"refX\", 1).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L1,7 L9,1 Z\");\n}, \"composition\");\nvar aggregation = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-aggregationStart\").attr(\"class\", \"marker aggregation \" + type).attr(\"refX\", 18).attr(\"refY\", 7).attr(\"markerWidth\", 190).attr(\"markerHeight\", 240).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L1,7 L9,1 Z\");\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-aggregationEnd\").attr(\"class\", \"marker aggregation \" + type).attr(\"refX\", 1).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L1,7 L9,1 Z\");\n}, \"aggregation\");\nvar dependency = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-dependencyStart\").attr(\"class\", \"marker dependency \" + type).attr(\"refX\", 6).attr(\"refY\", 7).attr(\"markerWidth\", 190).attr(\"markerHeight\", 240).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 5,7 L9,13 L1,7 L9,1 Z\");\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-dependencyEnd\").attr(\"class\", \"marker dependency \" + type).attr(\"refX\", 13).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L14,7 L9,1 Z\");\n}, \"dependency\");\nvar lollipop = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-lollipopStart\").attr(\"class\", \"marker lollipop \" + type).attr(\"refX\", 13).attr(\"refY\", 7).attr(\"markerWidth\", 190).attr(\"markerHeight\", 240).attr(\"orient\", \"auto\").append(\"circle\").attr(\"stroke\", \"black\").attr(\"fill\", \"transparent\").attr(\"cx\", 7).attr(\"cy\", 7).attr(\"r\", 6);\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-lollipopEnd\").attr(\"class\", \"marker lollipop \" + type).attr(\"refX\", 1).attr(\"refY\", 7).attr(\"markerWidth\", 190).attr(\"markerHeight\", 240).attr(\"orient\", \"auto\").append(\"circle\").attr(\"stroke\", \"black\").attr(\"fill\", \"transparent\").attr(\"cx\", 7).attr(\"cy\", 7).attr(\"r\", 6);\n}, \"lollipop\");\nvar point = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"marker\").attr(\"id\", id + \"_\" + type + \"-pointEnd\").attr(\"class\", \"marker \" + type).attr(\"viewBox\", \"0 0 10 10\").attr(\"refX\", 6).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0 0 L 10 5 L 0 10 z\").attr(\"class\", \"arrowMarkerPath\").style(\"stroke-width\", 1).style(\"stroke-dasharray\", \"1,0\");\n  elem.append(\"marker\").attr(\"id\", id + \"_\" + type + \"-pointStart\").attr(\"class\", \"marker \" + type).attr(\"viewBox\", \"0 0 10 10\").attr(\"refX\", 4.5).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0 5 L 10 10 L 10 0 z\").attr(\"class\", \"arrowMarkerPath\").style(\"stroke-width\", 1).style(\"stroke-dasharray\", \"1,0\");\n}, \"point\");\nvar circle = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"marker\").attr(\"id\", id + \"_\" + type + \"-circleEnd\").attr(\"class\", \"marker \" + type).attr(\"viewBox\", \"0 0 10 10\").attr(\"refX\", 11).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 11).attr(\"markerHeight\", 11).attr(\"orient\", \"auto\").append(\"circle\").attr(\"cx\", \"5\").attr(\"cy\", \"5\").attr(\"r\", \"5\").attr(\"class\", \"arrowMarkerPath\").style(\"stroke-width\", 1).style(\"stroke-dasharray\", \"1,0\");\n  elem.append(\"marker\").attr(\"id\", id + \"_\" + type + \"-circleStart\").attr(\"class\", \"marker \" + type).attr(\"viewBox\", \"0 0 10 10\").attr(\"refX\", -1).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 11).attr(\"markerHeight\", 11).attr(\"orient\", \"auto\").append(\"circle\").attr(\"cx\", \"5\").attr(\"cy\", \"5\").attr(\"r\", \"5\").attr(\"class\", \"arrowMarkerPath\").style(\"stroke-width\", 1).style(\"stroke-dasharray\", \"1,0\");\n}, \"circle\");\nvar cross = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"marker\").attr(\"id\", id + \"_\" + type + \"-crossEnd\").attr(\"class\", \"marker cross \" + type).attr(\"viewBox\", \"0 0 11 11\").attr(\"refX\", 12).attr(\"refY\", 5.2).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 11).attr(\"markerHeight\", 11).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 1,1 l 9,9 M 10,1 l -9,9\").attr(\"class\", \"arrowMarkerPath\").style(\"stroke-width\", 2).style(\"stroke-dasharray\", \"1,0\");\n  elem.append(\"marker\").attr(\"id\", id + \"_\" + type + \"-crossStart\").attr(\"class\", \"marker cross \" + type).attr(\"viewBox\", \"0 0 11 11\").attr(\"refX\", -1).attr(\"refY\", 5.2).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 11).attr(\"markerHeight\", 11).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 1,1 l 9,9 M 10,1 l -9,9\").attr(\"class\", \"arrowMarkerPath\").style(\"stroke-width\", 2).style(\"stroke-dasharray\", \"1,0\");\n}, \"cross\");\nvar barb = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-barbEnd\").attr(\"refX\", 19).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 14).attr(\"markerUnits\", \"strokeWidth\").attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 19,7 L9,13 L14,7 L9,1 Z\");\n}, \"barb\");\nvar markers = {\n  extension,\n  composition,\n  aggregation,\n  dependency,\n  lollipop,\n  point,\n  circle,\n  cross,\n  barb\n};\nvar markers_default = insertMarkers;\n\n// src/diagrams/block/layout.ts\nvar padding = getConfig2()?.block?.padding ?? 8;\nfunction calculateBlockPosition(columns, position) {\n  if (columns === 0 || !Number.isInteger(columns)) {\n    throw new Error(\"Columns must be an integer !== 0.\");\n  }\n  if (position < 0 || !Number.isInteger(position)) {\n    throw new Error(\"Position must be a non-negative integer.\" + position);\n  }\n  if (columns < 0) {\n    return { px: position, py: 0 };\n  }\n  if (columns === 1) {\n    return { px: 0, py: position };\n  }\n  const px = position % columns;\n  const py = Math.floor(position / columns);\n  return { px, py };\n}\n__name(calculateBlockPosition, \"calculateBlockPosition\");\nvar getMaxChildSize = /* @__PURE__ */ __name((block) => {\n  let maxWidth = 0;\n  let maxHeight = 0;\n  for (const child of block.children) {\n    const { width, height, x, y } = child.size ?? { width: 0, height: 0, x: 0, y: 0 };\n    log.debug(\n      \"getMaxChildSize abc95 child:\",\n      child.id,\n      \"width:\",\n      width,\n      \"height:\",\n      height,\n      \"x:\",\n      x,\n      \"y:\",\n      y,\n      child.type\n    );\n    if (child.type === \"space\") {\n      continue;\n    }\n    if (width > maxWidth) {\n      maxWidth = width / (block.widthInColumns ?? 1);\n    }\n    if (height > maxHeight) {\n      maxHeight = height;\n    }\n  }\n  return { width: maxWidth, height: maxHeight };\n}, \"getMaxChildSize\");\nfunction setBlockSizes(block, db2, siblingWidth = 0, siblingHeight = 0) {\n  log.debug(\n    \"setBlockSizes abc95 (start)\",\n    block.id,\n    block?.size?.x,\n    \"block width =\",\n    block?.size,\n    \"siblingWidth\",\n    siblingWidth\n  );\n  if (!block?.size?.width) {\n    block.size = {\n      width: siblingWidth,\n      height: siblingHeight,\n      x: 0,\n      y: 0\n    };\n  }\n  let maxWidth = 0;\n  let maxHeight = 0;\n  if (block.children?.length > 0) {\n    for (const child of block.children) {\n      setBlockSizes(child, db2);\n    }\n    const childSize = getMaxChildSize(block);\n    maxWidth = childSize.width;\n    maxHeight = childSize.height;\n    log.debug(\"setBlockSizes abc95 maxWidth of\", block.id, \":s children is \", maxWidth, maxHeight);\n    for (const child of block.children) {\n      if (child.size) {\n        log.debug(\n          `abc95 Setting size of children of ${block.id} id=${child.id} ${maxWidth} ${maxHeight} ${JSON.stringify(child.size)}`\n        );\n        child.size.width = maxWidth * (child.widthInColumns ?? 1) + padding * ((child.widthInColumns ?? 1) - 1);\n        child.size.height = maxHeight;\n        child.size.x = 0;\n        child.size.y = 0;\n        log.debug(\n          `abc95 updating size of ${block.id} children child:${child.id} maxWidth:${maxWidth} maxHeight:${maxHeight}`\n        );\n      }\n    }\n    for (const child of block.children) {\n      setBlockSizes(child, db2, maxWidth, maxHeight);\n    }\n    const columns = block.columns ?? -1;\n    let numItems = 0;\n    for (const child of block.children) {\n      numItems += child.widthInColumns ?? 1;\n    }\n    let xSize = block.children.length;\n    if (columns > 0 && columns < numItems) {\n      xSize = columns;\n    }\n    const ySize = Math.ceil(numItems / xSize);\n    let width = xSize * (maxWidth + padding) + padding;\n    let height = ySize * (maxHeight + padding) + padding;\n    if (width < siblingWidth) {\n      log.debug(\n        `Detected to small sibling: abc95 ${block.id} siblingWidth ${siblingWidth} siblingHeight ${siblingHeight} width ${width}`\n      );\n      width = siblingWidth;\n      height = siblingHeight;\n      const childWidth = (siblingWidth - xSize * padding - padding) / xSize;\n      const childHeight = (siblingHeight - ySize * padding - padding) / ySize;\n      log.debug(\"Size indata abc88\", block.id, \"childWidth\", childWidth, \"maxWidth\", maxWidth);\n      log.debug(\"Size indata abc88\", block.id, \"childHeight\", childHeight, \"maxHeight\", maxHeight);\n      log.debug(\"Size indata abc88 xSize\", xSize, \"padding\", padding);\n      for (const child of block.children) {\n        if (child.size) {\n          child.size.width = childWidth;\n          child.size.height = childHeight;\n          child.size.x = 0;\n          child.size.y = 0;\n        }\n      }\n    }\n    log.debug(\n      `abc95 (finale calc) ${block.id} xSize ${xSize} ySize ${ySize} columns ${columns}${block.children.length} width=${Math.max(width, block.size?.width || 0)}`\n    );\n    if (width < (block?.size?.width || 0)) {\n      width = block?.size?.width || 0;\n      const num = columns > 0 ? Math.min(block.children.length, columns) : block.children.length;\n      if (num > 0) {\n        const childWidth = (width - num * padding - padding) / num;\n        log.debug(\"abc95 (growing to fit) width\", block.id, width, block.size?.width, childWidth);\n        for (const child of block.children) {\n          if (child.size) {\n            child.size.width = childWidth;\n          }\n        }\n      }\n    }\n    block.size = {\n      width,\n      height,\n      x: 0,\n      y: 0\n    };\n  }\n  log.debug(\n    \"setBlockSizes abc94 (done)\",\n    block.id,\n    block?.size?.x,\n    block?.size?.width,\n    block?.size?.y,\n    block?.size?.height\n  );\n}\n__name(setBlockSizes, \"setBlockSizes\");\nfunction layoutBlocks(block, db2) {\n  log.debug(\n    `abc85 layout blocks (=>layoutBlocks) ${block.id} x: ${block?.size?.x} y: ${block?.size?.y} width: ${block?.size?.width}`\n  );\n  const columns = block.columns ?? -1;\n  log.debug(\"layoutBlocks columns abc95\", block.id, \"=>\", columns, block);\n  if (block.children && // find max width of children\n  block.children.length > 0) {\n    const width = block?.children[0]?.size?.width ?? 0;\n    const widthOfChildren = block.children.length * width + (block.children.length - 1) * padding;\n    log.debug(\"widthOfChildren 88\", widthOfChildren, \"posX\");\n    let columnPos = 0;\n    log.debug(\"abc91 block?.size?.x\", block.id, block?.size?.x);\n    let startingPosX = block?.size?.x ? block?.size?.x + (-block?.size?.width / 2 || 0) : -padding;\n    let rowPos = 0;\n    for (const child of block.children) {\n      const parent = block;\n      if (!child.size) {\n        continue;\n      }\n      const { width: width2, height } = child.size;\n      const { px, py } = calculateBlockPosition(columns, columnPos);\n      if (py != rowPos) {\n        rowPos = py;\n        startingPosX = block?.size?.x ? block?.size?.x + (-block?.size?.width / 2 || 0) : -padding;\n        log.debug(\"New row in layout for block\", block.id, \" and child \", child.id, rowPos);\n      }\n      log.debug(\n        `abc89 layout blocks (child) id: ${child.id} Pos: ${columnPos} (px, py) ${px},${py} (${parent?.size?.x},${parent?.size?.y}) parent: ${parent.id} width: ${width2}${padding}`\n      );\n      if (parent.size) {\n        const halfWidth = width2 / 2;\n        child.size.x = startingPosX + padding + halfWidth;\n        log.debug(\n          `abc91 layout blocks (calc) px, pyid:${child.id} startingPos=X${startingPosX} new startingPosX${child.size.x} ${halfWidth} padding=${padding} width=${width2} halfWidth=${halfWidth} => x:${child.size.x} y:${child.size.y} ${child.widthInColumns} (width * (child?.w || 1)) / 2 ${width2 * (child?.widthInColumns ?? 1) / 2}`\n        );\n        startingPosX = child.size.x + halfWidth;\n        child.size.y = parent.size.y - parent.size.height / 2 + py * (height + padding) + height / 2 + padding;\n        log.debug(\n          `abc88 layout blocks (calc) px, pyid:${child.id}startingPosX${startingPosX}${padding}${halfWidth}=>x:${child.size.x}y:${child.size.y}${child.widthInColumns}(width * (child?.w || 1)) / 2${width2 * (child?.widthInColumns ?? 1) / 2}`\n        );\n      }\n      if (child.children) {\n        layoutBlocks(child, db2);\n      }\n      columnPos += child?.widthInColumns ?? 1;\n      log.debug(\"abc88 columnsPos\", child, columnPos);\n    }\n  }\n  log.debug(\n    `layout blocks (<==layoutBlocks) ${block.id} x: ${block?.size?.x} y: ${block?.size?.y} width: ${block?.size?.width}`\n  );\n}\n__name(layoutBlocks, \"layoutBlocks\");\nfunction findBounds(block, { minX, minY, maxX, maxY } = { minX: 0, minY: 0, maxX: 0, maxY: 0 }) {\n  if (block.size && block.id !== \"root\") {\n    const { x, y, width, height } = block.size;\n    if (x - width / 2 < minX) {\n      minX = x - width / 2;\n    }\n    if (y - height / 2 < minY) {\n      minY = y - height / 2;\n    }\n    if (x + width / 2 > maxX) {\n      maxX = x + width / 2;\n    }\n    if (y + height / 2 > maxY) {\n      maxY = y + height / 2;\n    }\n  }\n  if (block.children) {\n    for (const child of block.children) {\n      ({ minX, minY, maxX, maxY } = findBounds(child, { minX, minY, maxX, maxY }));\n    }\n  }\n  return { minX, minY, maxX, maxY };\n}\n__name(findBounds, \"findBounds\");\nfunction layout(db2) {\n  const root = db2.getBlock(\"root\");\n  if (!root) {\n    return;\n  }\n  setBlockSizes(root, db2, 0, 0);\n  layoutBlocks(root, db2);\n  log.debug(\"getBlocks\", JSON.stringify(root, null, 2));\n  const { minX, minY, maxX, maxY } = findBounds(root);\n  const height = maxY - minY;\n  const width = maxX - minX;\n  return { x: minX, y: minY, width, height };\n}\n__name(layout, \"layout\");\n\n// src/diagrams/block/renderHelpers.ts\nimport * as graphlib from \"dagre-d3-es/src/graphlib/index.js\";\n\n// src/dagre-wrapper/createLabel.js\nimport { select } from \"d3\";\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr(\"style\", styleFn);\n  }\n}\n__name(applyStyle, \"applyStyle\");\nfunction addHtmlLabel(node) {\n  const fo = select(document.createElementNS(\"http://www.w3.org/2000/svg\", \"foreignObject\"));\n  const div = fo.append(\"xhtml:div\");\n  const label = node.label;\n  const labelClass = node.isNode ? \"nodeLabel\" : \"edgeLabel\";\n  const span = div.append(\"span\");\n  span.html(label);\n  applyStyle(span, node.labelStyle);\n  span.attr(\"class\", labelClass);\n  applyStyle(div, node.labelStyle);\n  div.style(\"display\", \"inline-block\");\n  div.style(\"white-space\", \"nowrap\");\n  div.attr(\"xmlns\", \"http://www.w3.org/1999/xhtml\");\n  return fo.node();\n}\n__name(addHtmlLabel, \"addHtmlLabel\");\nvar createLabel = /* @__PURE__ */ __name(async (_vertexText, style, isTitle, isNode) => {\n  let vertexText = _vertexText || \"\";\n  if (typeof vertexText === \"object\") {\n    vertexText = vertexText[0];\n  }\n  if (evaluate(getConfig2().flowchart.htmlLabels)) {\n    vertexText = vertexText.replace(/\\\\n|\\n/g, \"<br />\");\n    log.debug(\"vertexText\" + vertexText);\n    const label = await replaceIconSubstring(decodeEntities(vertexText));\n    const node = {\n      isNode,\n      label,\n      labelStyle: style.replace(\"fill:\", \"color:\")\n    };\n    let vertexNode = addHtmlLabel(node);\n    return vertexNode;\n  } else {\n    const svgLabel = document.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n    svgLabel.setAttribute(\"style\", style.replace(\"color:\", \"fill:\"));\n    let rows = [];\n    if (typeof vertexText === \"string\") {\n      rows = vertexText.split(/\\\\n|\\n|<br\\s*\\/?>/gi);\n    } else if (Array.isArray(vertexText)) {\n      rows = vertexText;\n    } else {\n      rows = [];\n    }\n    for (const row of rows) {\n      const tspan = document.createElementNS(\"http://www.w3.org/2000/svg\", \"tspan\");\n      tspan.setAttributeNS(\"http://www.w3.org/XML/1998/namespace\", \"xml:space\", \"preserve\");\n      tspan.setAttribute(\"dy\", \"1em\");\n      tspan.setAttribute(\"x\", \"0\");\n      if (isTitle) {\n        tspan.setAttribute(\"class\", \"title-row\");\n      } else {\n        tspan.setAttribute(\"class\", \"row\");\n      }\n      tspan.textContent = row.trim();\n      svgLabel.appendChild(tspan);\n    }\n    return svgLabel;\n  }\n}, \"createLabel\");\nvar createLabel_default = createLabel;\n\n// src/dagre-wrapper/edges.js\nimport { line, curveBasis, select as select2 } from \"d3\";\n\n// src/dagre-wrapper/edgeMarker.ts\nvar addEdgeMarkers = /* @__PURE__ */ __name((svgPath, edge, url, id, diagramType) => {\n  if (edge.arrowTypeStart) {\n    addEdgeMarker(svgPath, \"start\", edge.arrowTypeStart, url, id, diagramType);\n  }\n  if (edge.arrowTypeEnd) {\n    addEdgeMarker(svgPath, \"end\", edge.arrowTypeEnd, url, id, diagramType);\n  }\n}, \"addEdgeMarkers\");\nvar arrowTypesMap = {\n  arrow_cross: \"cross\",\n  arrow_point: \"point\",\n  arrow_barb: \"barb\",\n  arrow_circle: \"circle\",\n  aggregation: \"aggregation\",\n  extension: \"extension\",\n  composition: \"composition\",\n  dependency: \"dependency\",\n  lollipop: \"lollipop\"\n};\nvar addEdgeMarker = /* @__PURE__ */ __name((svgPath, position, arrowType, url, id, diagramType) => {\n  const endMarkerType = arrowTypesMap[arrowType];\n  if (!endMarkerType) {\n    log.warn(`Unknown arrow type: ${arrowType}`);\n    return;\n  }\n  const suffix = position === \"start\" ? \"Start\" : \"End\";\n  svgPath.attr(`marker-${position}`, `url(${url}#${id}_${diagramType}-${endMarkerType}${suffix})`);\n}, \"addEdgeMarker\");\n\n// src/dagre-wrapper/edges.js\nvar edgeLabels = {};\nvar terminalLabels = {};\nvar insertEdgeLabel = /* @__PURE__ */ __name(async (elem, edge) => {\n  const config2 = getConfig2();\n  const useHtmlLabels = evaluate(config2.flowchart.htmlLabels);\n  const labelElement = edge.labelType === \"markdown\" ? createText(\n    elem,\n    edge.label,\n    {\n      style: edge.labelStyle,\n      useHtmlLabels,\n      addSvgBackground: true\n    },\n    config2\n  ) : await createLabel_default(edge.label, edge.labelStyle);\n  const edgeLabel = elem.insert(\"g\").attr(\"class\", \"edgeLabel\");\n  const label = edgeLabel.insert(\"g\").attr(\"class\", \"label\");\n  label.node().appendChild(labelElement);\n  let bbox = labelElement.getBBox();\n  if (useHtmlLabels) {\n    const div = labelElement.children[0];\n    const dv = select2(labelElement);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  label.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + -bbox.height / 2 + \")\");\n  edgeLabels[edge.id] = edgeLabel;\n  edge.width = bbox.width;\n  edge.height = bbox.height;\n  let fo;\n  if (edge.startLabelLeft) {\n    const startLabelElement = await createLabel_default(edge.startLabelLeft, edge.labelStyle);\n    const startEdgeLabelLeft = elem.insert(\"g\").attr(\"class\", \"edgeTerminals\");\n    const inner = startEdgeLabelLeft.insert(\"g\").attr(\"class\", \"inner\");\n    fo = inner.node().appendChild(startLabelElement);\n    const slBox = startLabelElement.getBBox();\n    inner.attr(\"transform\", \"translate(\" + -slBox.width / 2 + \", \" + -slBox.height / 2 + \")\");\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].startLeft = startEdgeLabelLeft;\n    setTerminalWidth(fo, edge.startLabelLeft);\n  }\n  if (edge.startLabelRight) {\n    const startLabelElement = await createLabel_default(edge.startLabelRight, edge.labelStyle);\n    const startEdgeLabelRight = elem.insert(\"g\").attr(\"class\", \"edgeTerminals\");\n    const inner = startEdgeLabelRight.insert(\"g\").attr(\"class\", \"inner\");\n    fo = startEdgeLabelRight.node().appendChild(startLabelElement);\n    inner.node().appendChild(startLabelElement);\n    const slBox = startLabelElement.getBBox();\n    inner.attr(\"transform\", \"translate(\" + -slBox.width / 2 + \", \" + -slBox.height / 2 + \")\");\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].startRight = startEdgeLabelRight;\n    setTerminalWidth(fo, edge.startLabelRight);\n  }\n  if (edge.endLabelLeft) {\n    const endLabelElement = await createLabel_default(edge.endLabelLeft, edge.labelStyle);\n    const endEdgeLabelLeft = elem.insert(\"g\").attr(\"class\", \"edgeTerminals\");\n    const inner = endEdgeLabelLeft.insert(\"g\").attr(\"class\", \"inner\");\n    fo = inner.node().appendChild(endLabelElement);\n    const slBox = endLabelElement.getBBox();\n    inner.attr(\"transform\", \"translate(\" + -slBox.width / 2 + \", \" + -slBox.height / 2 + \")\");\n    endEdgeLabelLeft.node().appendChild(endLabelElement);\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].endLeft = endEdgeLabelLeft;\n    setTerminalWidth(fo, edge.endLabelLeft);\n  }\n  if (edge.endLabelRight) {\n    const endLabelElement = await createLabel_default(edge.endLabelRight, edge.labelStyle);\n    const endEdgeLabelRight = elem.insert(\"g\").attr(\"class\", \"edgeTerminals\");\n    const inner = endEdgeLabelRight.insert(\"g\").attr(\"class\", \"inner\");\n    fo = inner.node().appendChild(endLabelElement);\n    const slBox = endLabelElement.getBBox();\n    inner.attr(\"transform\", \"translate(\" + -slBox.width / 2 + \", \" + -slBox.height / 2 + \")\");\n    endEdgeLabelRight.node().appendChild(endLabelElement);\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].endRight = endEdgeLabelRight;\n    setTerminalWidth(fo, edge.endLabelRight);\n  }\n  return labelElement;\n}, \"insertEdgeLabel\");\nfunction setTerminalWidth(fo, value) {\n  if (getConfig2().flowchart.htmlLabels && fo) {\n    fo.style.width = value.length * 9 + \"px\";\n    fo.style.height = \"12px\";\n  }\n}\n__name(setTerminalWidth, \"setTerminalWidth\");\nvar positionEdgeLabel = /* @__PURE__ */ __name((edge, paths) => {\n  log.debug(\"Moving label abc88 \", edge.id, edge.label, edgeLabels[edge.id], paths);\n  let path = paths.updatedPath ? paths.updatedPath : paths.originalPath;\n  const siteConfig = getConfig2();\n  const { subGraphTitleTotalMargin } = getSubGraphTitleMargins(siteConfig);\n  if (edge.label) {\n    const el = edgeLabels[edge.id];\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils_default.calcLabelPosition(path);\n      log.debug(\n        \"Moving label \" + edge.label + \" from (\",\n        x,\n        \",\",\n        y,\n        \") to (\",\n        pos.x,\n        \",\",\n        pos.y,\n        \") abc88\"\n      );\n      if (paths.updatedPath) {\n        x = pos.x;\n        y = pos.y;\n      }\n    }\n    el.attr(\"transform\", `translate(${x}, ${y + subGraphTitleTotalMargin / 2})`);\n  }\n  if (edge.startLabelLeft) {\n    const el = terminalLabels[edge.id].startLeft;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils_default.calcTerminalLabelPosition(edge.arrowTypeStart ? 10 : 0, \"start_left\", path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr(\"transform\", `translate(${x}, ${y})`);\n  }\n  if (edge.startLabelRight) {\n    const el = terminalLabels[edge.id].startRight;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils_default.calcTerminalLabelPosition(\n        edge.arrowTypeStart ? 10 : 0,\n        \"start_right\",\n        path\n      );\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr(\"transform\", `translate(${x}, ${y})`);\n  }\n  if (edge.endLabelLeft) {\n    const el = terminalLabels[edge.id].endLeft;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils_default.calcTerminalLabelPosition(edge.arrowTypeEnd ? 10 : 0, \"end_left\", path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr(\"transform\", `translate(${x}, ${y})`);\n  }\n  if (edge.endLabelRight) {\n    const el = terminalLabels[edge.id].endRight;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils_default.calcTerminalLabelPosition(edge.arrowTypeEnd ? 10 : 0, \"end_right\", path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr(\"transform\", `translate(${x}, ${y})`);\n  }\n}, \"positionEdgeLabel\");\nvar outsideNode = /* @__PURE__ */ __name((node, point2) => {\n  const x = node.x;\n  const y = node.y;\n  const dx = Math.abs(point2.x - x);\n  const dy = Math.abs(point2.y - y);\n  const w = node.width / 2;\n  const h = node.height / 2;\n  if (dx >= w || dy >= h) {\n    return true;\n  }\n  return false;\n}, \"outsideNode\");\nvar intersection = /* @__PURE__ */ __name((node, outsidePoint, insidePoint) => {\n  log.debug(`intersection calc abc89:\n  outsidePoint: ${JSON.stringify(outsidePoint)}\n  insidePoint : ${JSON.stringify(insidePoint)}\n  node        : x:${node.x} y:${node.y} w:${node.width} h:${node.height}`);\n  const x = node.x;\n  const y = node.y;\n  const dx = Math.abs(x - insidePoint.x);\n  const w = node.width / 2;\n  let r = insidePoint.x < outsidePoint.x ? w - dx : w + dx;\n  const h = node.height / 2;\n  const Q = Math.abs(outsidePoint.y - insidePoint.y);\n  const R = Math.abs(outsidePoint.x - insidePoint.x);\n  if (Math.abs(y - outsidePoint.y) * w > Math.abs(x - outsidePoint.x) * h) {\n    let q = insidePoint.y < outsidePoint.y ? outsidePoint.y - h - y : y - h - outsidePoint.y;\n    r = R * q / Q;\n    const res = {\n      x: insidePoint.x < outsidePoint.x ? insidePoint.x + r : insidePoint.x - R + r,\n      y: insidePoint.y < outsidePoint.y ? insidePoint.y + Q - q : insidePoint.y - Q + q\n    };\n    if (r === 0) {\n      res.x = outsidePoint.x;\n      res.y = outsidePoint.y;\n    }\n    if (R === 0) {\n      res.x = outsidePoint.x;\n    }\n    if (Q === 0) {\n      res.y = outsidePoint.y;\n    }\n    log.debug(`abc89 topp/bott calc, Q ${Q}, q ${q}, R ${R}, r ${r}`, res);\n    return res;\n  } else {\n    if (insidePoint.x < outsidePoint.x) {\n      r = outsidePoint.x - w - x;\n    } else {\n      r = x - w - outsidePoint.x;\n    }\n    let q = Q * r / R;\n    let _x = insidePoint.x < outsidePoint.x ? insidePoint.x + R - r : insidePoint.x - R + r;\n    let _y = insidePoint.y < outsidePoint.y ? insidePoint.y + q : insidePoint.y - q;\n    log.debug(`sides calc abc89, Q ${Q}, q ${q}, R ${R}, r ${r}`, { _x, _y });\n    if (r === 0) {\n      _x = outsidePoint.x;\n      _y = outsidePoint.y;\n    }\n    if (R === 0) {\n      _x = outsidePoint.x;\n    }\n    if (Q === 0) {\n      _y = outsidePoint.y;\n    }\n    return { x: _x, y: _y };\n  }\n}, \"intersection\");\nvar cutPathAtIntersect = /* @__PURE__ */ __name((_points, boundaryNode) => {\n  log.debug(\"abc88 cutPathAtIntersect\", _points, boundaryNode);\n  let points = [];\n  let lastPointOutside = _points[0];\n  let isInside = false;\n  _points.forEach((point2) => {\n    if (!outsideNode(boundaryNode, point2) && !isInside) {\n      const inter = intersection(boundaryNode, lastPointOutside, point2);\n      let pointPresent = false;\n      points.forEach((p) => {\n        pointPresent = pointPresent || p.x === inter.x && p.y === inter.y;\n      });\n      if (!points.some((e) => e.x === inter.x && e.y === inter.y)) {\n        points.push(inter);\n      }\n      isInside = true;\n    } else {\n      lastPointOutside = point2;\n      if (!isInside) {\n        points.push(point2);\n      }\n    }\n  });\n  return points;\n}, \"cutPathAtIntersect\");\nvar insertEdge = /* @__PURE__ */ __name(function(elem, e, edge, clusterDb, diagramType, graph, id) {\n  let points = edge.points;\n  log.debug(\"abc88 InsertEdge: edge=\", edge, \"e=\", e);\n  let pointsHasChanged = false;\n  const tail = graph.node(e.v);\n  var head = graph.node(e.w);\n  if (head?.intersect && tail?.intersect) {\n    points = points.slice(1, edge.points.length - 1);\n    points.unshift(tail.intersect(points[0]));\n    points.push(head.intersect(points[points.length - 1]));\n  }\n  if (edge.toCluster) {\n    log.debug(\"to cluster abc88\", clusterDb[edge.toCluster]);\n    points = cutPathAtIntersect(edge.points, clusterDb[edge.toCluster].node);\n    pointsHasChanged = true;\n  }\n  if (edge.fromCluster) {\n    log.debug(\"from cluster abc88\", clusterDb[edge.fromCluster]);\n    points = cutPathAtIntersect(points.reverse(), clusterDb[edge.fromCluster].node).reverse();\n    pointsHasChanged = true;\n  }\n  const lineData = points.filter((p) => !Number.isNaN(p.y));\n  let curve = curveBasis;\n  if (edge.curve && (diagramType === \"graph\" || diagramType === \"flowchart\")) {\n    curve = edge.curve;\n  }\n  const { x, y } = getLineFunctionsWithOffset(edge);\n  const lineFunction = line().x(x).y(y).curve(curve);\n  let strokeClasses;\n  switch (edge.thickness) {\n    case \"normal\":\n      strokeClasses = \"edge-thickness-normal\";\n      break;\n    case \"thick\":\n      strokeClasses = \"edge-thickness-thick\";\n      break;\n    case \"invisible\":\n      strokeClasses = \"edge-thickness-thick\";\n      break;\n    default:\n      strokeClasses = \"\";\n  }\n  switch (edge.pattern) {\n    case \"solid\":\n      strokeClasses += \" edge-pattern-solid\";\n      break;\n    case \"dotted\":\n      strokeClasses += \" edge-pattern-dotted\";\n      break;\n    case \"dashed\":\n      strokeClasses += \" edge-pattern-dashed\";\n      break;\n  }\n  const svgPath = elem.append(\"path\").attr(\"d\", lineFunction(lineData)).attr(\"id\", edge.id).attr(\"class\", \" \" + strokeClasses + (edge.classes ? \" \" + edge.classes : \"\")).attr(\"style\", edge.style);\n  let url = \"\";\n  if (getConfig2().flowchart.arrowMarkerAbsolute || getConfig2().state.arrowMarkerAbsolute) {\n    url = getUrl(true);\n  }\n  addEdgeMarkers(svgPath, edge, url, id, diagramType);\n  let paths = {};\n  if (pointsHasChanged) {\n    paths.updatedPath = points;\n  }\n  paths.originalPath = edge.points;\n  return paths;\n}, \"insertEdge\");\n\n// src/dagre-wrapper/nodes.js\nimport { select as select4 } from \"d3\";\n\n// src/dagre-wrapper/blockArrowHelper.ts\nvar expandAndDeduplicateDirections = /* @__PURE__ */ __name((directions) => {\n  const uniqueDirections = /* @__PURE__ */ new Set();\n  for (const direction of directions) {\n    switch (direction) {\n      case \"x\":\n        uniqueDirections.add(\"right\");\n        uniqueDirections.add(\"left\");\n        break;\n      case \"y\":\n        uniqueDirections.add(\"up\");\n        uniqueDirections.add(\"down\");\n        break;\n      default:\n        uniqueDirections.add(direction);\n        break;\n    }\n  }\n  return uniqueDirections;\n}, \"expandAndDeduplicateDirections\");\nvar getArrowPoints = /* @__PURE__ */ __name((duplicatedDirections, bbox, node) => {\n  const directions = expandAndDeduplicateDirections(duplicatedDirections);\n  const f = 2;\n  const height = bbox.height + 2 * node.padding;\n  const midpoint = height / f;\n  const width = bbox.width + 2 * midpoint + node.padding;\n  const padding2 = node.padding / 2;\n  if (directions.has(\"right\") && directions.has(\"left\") && directions.has(\"up\") && directions.has(\"down\")) {\n    return [\n      // Bottom\n      { x: 0, y: 0 },\n      { x: midpoint, y: 0 },\n      { x: width / 2, y: 2 * padding2 },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: 0 },\n      // Right\n      { x: width, y: -height / 3 },\n      { x: width + 2 * padding2, y: -height / 2 },\n      { x: width, y: -2 * height / 3 },\n      { x: width, y: -height },\n      // Top\n      { x: width - midpoint, y: -height },\n      { x: width / 2, y: -height - 2 * padding2 },\n      { x: midpoint, y: -height },\n      // Left\n      { x: 0, y: -height },\n      { x: 0, y: -2 * height / 3 },\n      { x: -2 * padding2, y: -height / 2 },\n      { x: 0, y: -height / 3 }\n    ];\n  }\n  if (directions.has(\"right\") && directions.has(\"left\") && directions.has(\"up\")) {\n    return [\n      { x: midpoint, y: 0 },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: -height / 2 },\n      { x: width - midpoint, y: -height },\n      { x: midpoint, y: -height },\n      { x: 0, y: -height / 2 }\n    ];\n  }\n  if (directions.has(\"right\") && directions.has(\"left\") && directions.has(\"down\")) {\n    return [\n      { x: 0, y: 0 },\n      { x: midpoint, y: -height },\n      { x: width - midpoint, y: -height },\n      { x: width, y: 0 }\n    ];\n  }\n  if (directions.has(\"right\") && directions.has(\"up\") && directions.has(\"down\")) {\n    return [\n      { x: 0, y: 0 },\n      { x: width, y: -midpoint },\n      { x: width, y: -height + midpoint },\n      { x: 0, y: -height }\n    ];\n  }\n  if (directions.has(\"left\") && directions.has(\"up\") && directions.has(\"down\")) {\n    return [\n      { x: width, y: 0 },\n      { x: 0, y: -midpoint },\n      { x: 0, y: -height + midpoint },\n      { x: width, y: -height }\n    ];\n  }\n  if (directions.has(\"right\") && directions.has(\"left\")) {\n    return [\n      { x: midpoint, y: 0 },\n      { x: midpoint, y: -padding2 },\n      { x: width - midpoint, y: -padding2 },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: -height / 2 },\n      { x: width - midpoint, y: -height },\n      { x: width - midpoint, y: -height + padding2 },\n      { x: midpoint, y: -height + padding2 },\n      { x: midpoint, y: -height },\n      { x: 0, y: -height / 2 }\n    ];\n  }\n  if (directions.has(\"up\") && directions.has(\"down\")) {\n    return [\n      // Bottom center\n      { x: width / 2, y: 0 },\n      // Left pont of bottom arrow\n      { x: 0, y: -padding2 },\n      { x: midpoint, y: -padding2 },\n      // Left top over vertical section\n      { x: midpoint, y: -height + padding2 },\n      { x: 0, y: -height + padding2 },\n      // Top of arrow\n      { x: width / 2, y: -height },\n      { x: width, y: -height + padding2 },\n      // Top of right vertical bar\n      { x: width - midpoint, y: -height + padding2 },\n      { x: width - midpoint, y: -padding2 },\n      { x: width, y: -padding2 }\n    ];\n  }\n  if (directions.has(\"right\") && directions.has(\"up\")) {\n    return [\n      { x: 0, y: 0 },\n      { x: width, y: -midpoint },\n      { x: 0, y: -height }\n    ];\n  }\n  if (directions.has(\"right\") && directions.has(\"down\")) {\n    return [\n      { x: 0, y: 0 },\n      { x: width, y: 0 },\n      { x: 0, y: -height }\n    ];\n  }\n  if (directions.has(\"left\") && directions.has(\"up\")) {\n    return [\n      { x: width, y: 0 },\n      { x: 0, y: -midpoint },\n      { x: width, y: -height }\n    ];\n  }\n  if (directions.has(\"left\") && directions.has(\"down\")) {\n    return [\n      { x: width, y: 0 },\n      { x: 0, y: 0 },\n      { x: width, y: -height }\n    ];\n  }\n  if (directions.has(\"right\")) {\n    return [\n      { x: midpoint, y: -padding2 },\n      { x: midpoint, y: -padding2 },\n      { x: width - midpoint, y: -padding2 },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: -height / 2 },\n      { x: width - midpoint, y: -height },\n      { x: width - midpoint, y: -height + padding2 },\n      // top left corner of arrow\n      { x: midpoint, y: -height + padding2 },\n      { x: midpoint, y: -height + padding2 }\n    ];\n  }\n  if (directions.has(\"left\")) {\n    return [\n      { x: midpoint, y: 0 },\n      { x: midpoint, y: -padding2 },\n      // Two points, the right corners\n      { x: width - midpoint, y: -padding2 },\n      { x: width - midpoint, y: -height + padding2 },\n      { x: midpoint, y: -height + padding2 },\n      { x: midpoint, y: -height },\n      { x: 0, y: -height / 2 }\n    ];\n  }\n  if (directions.has(\"up\")) {\n    return [\n      // Bottom center\n      { x: midpoint, y: -padding2 },\n      // Left top over vertical section\n      { x: midpoint, y: -height + padding2 },\n      { x: 0, y: -height + padding2 },\n      // Top of arrow\n      { x: width / 2, y: -height },\n      { x: width, y: -height + padding2 },\n      // Top of right vertical bar\n      { x: width - midpoint, y: -height + padding2 },\n      { x: width - midpoint, y: -padding2 }\n    ];\n  }\n  if (directions.has(\"down\")) {\n    return [\n      // Bottom center\n      { x: width / 2, y: 0 },\n      // Left pont of bottom arrow\n      { x: 0, y: -padding2 },\n      { x: midpoint, y: -padding2 },\n      // Left top over vertical section\n      { x: midpoint, y: -height + padding2 },\n      { x: width - midpoint, y: -height + padding2 },\n      { x: width - midpoint, y: -padding2 },\n      { x: width, y: -padding2 }\n    ];\n  }\n  return [{ x: 0, y: 0 }];\n}, \"getArrowPoints\");\n\n// src/dagre-wrapper/intersect/intersect-node.js\nfunction intersectNode(node, point2) {\n  return node.intersect(point2);\n}\n__name(intersectNode, \"intersectNode\");\nvar intersect_node_default = intersectNode;\n\n// src/dagre-wrapper/intersect/intersect-ellipse.js\nfunction intersectEllipse(node, rx, ry, point2) {\n  var cx = node.x;\n  var cy = node.y;\n  var px = cx - point2.x;\n  var py = cy - point2.y;\n  var det = Math.sqrt(rx * rx * py * py + ry * ry * px * px);\n  var dx = Math.abs(rx * ry * px / det);\n  if (point2.x < cx) {\n    dx = -dx;\n  }\n  var dy = Math.abs(rx * ry * py / det);\n  if (point2.y < cy) {\n    dy = -dy;\n  }\n  return { x: cx + dx, y: cy + dy };\n}\n__name(intersectEllipse, \"intersectEllipse\");\nvar intersect_ellipse_default = intersectEllipse;\n\n// src/dagre-wrapper/intersect/intersect-circle.js\nfunction intersectCircle(node, rx, point2) {\n  return intersect_ellipse_default(node, rx, rx, point2);\n}\n__name(intersectCircle, \"intersectCircle\");\nvar intersect_circle_default = intersectCircle;\n\n// src/dagre-wrapper/intersect/intersect-line.js\nfunction intersectLine(p1, p2, q1, q2) {\n  var a1, a2, b1, b2, c1, c2;\n  var r1, r2, r3, r4;\n  var denom, offset, num;\n  var x, y;\n  a1 = p2.y - p1.y;\n  b1 = p1.x - p2.x;\n  c1 = p2.x * p1.y - p1.x * p2.y;\n  r3 = a1 * q1.x + b1 * q1.y + c1;\n  r4 = a1 * q2.x + b1 * q2.y + c1;\n  if (r3 !== 0 && r4 !== 0 && sameSign(r3, r4)) {\n    return;\n  }\n  a2 = q2.y - q1.y;\n  b2 = q1.x - q2.x;\n  c2 = q2.x * q1.y - q1.x * q2.y;\n  r1 = a2 * p1.x + b2 * p1.y + c2;\n  r2 = a2 * p2.x + b2 * p2.y + c2;\n  if (r1 !== 0 && r2 !== 0 && sameSign(r1, r2)) {\n    return;\n  }\n  denom = a1 * b2 - a2 * b1;\n  if (denom === 0) {\n    return;\n  }\n  offset = Math.abs(denom / 2);\n  num = b1 * c2 - b2 * c1;\n  x = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n  num = a2 * c1 - a1 * c2;\n  y = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n  return { x, y };\n}\n__name(intersectLine, \"intersectLine\");\nfunction sameSign(r1, r2) {\n  return r1 * r2 > 0;\n}\n__name(sameSign, \"sameSign\");\nvar intersect_line_default = intersectLine;\n\n// src/dagre-wrapper/intersect/intersect-polygon.js\nvar intersect_polygon_default = intersectPolygon;\nfunction intersectPolygon(node, polyPoints, point2) {\n  var x1 = node.x;\n  var y1 = node.y;\n  var intersections = [];\n  var minX = Number.POSITIVE_INFINITY;\n  var minY = Number.POSITIVE_INFINITY;\n  if (typeof polyPoints.forEach === \"function\") {\n    polyPoints.forEach(function(entry) {\n      minX = Math.min(minX, entry.x);\n      minY = Math.min(minY, entry.y);\n    });\n  } else {\n    minX = Math.min(minX, polyPoints.x);\n    minY = Math.min(minY, polyPoints.y);\n  }\n  var left = x1 - node.width / 2 - minX;\n  var top = y1 - node.height / 2 - minY;\n  for (var i = 0; i < polyPoints.length; i++) {\n    var p1 = polyPoints[i];\n    var p2 = polyPoints[i < polyPoints.length - 1 ? i + 1 : 0];\n    var intersect = intersect_line_default(\n      node,\n      point2,\n      { x: left + p1.x, y: top + p1.y },\n      { x: left + p2.x, y: top + p2.y }\n    );\n    if (intersect) {\n      intersections.push(intersect);\n    }\n  }\n  if (!intersections.length) {\n    return node;\n  }\n  if (intersections.length > 1) {\n    intersections.sort(function(p, q) {\n      var pdx = p.x - point2.x;\n      var pdy = p.y - point2.y;\n      var distp = Math.sqrt(pdx * pdx + pdy * pdy);\n      var qdx = q.x - point2.x;\n      var qdy = q.y - point2.y;\n      var distq = Math.sqrt(qdx * qdx + qdy * qdy);\n      return distp < distq ? -1 : distp === distq ? 0 : 1;\n    });\n  }\n  return intersections[0];\n}\n__name(intersectPolygon, \"intersectPolygon\");\n\n// src/dagre-wrapper/intersect/intersect-rect.js\nvar intersectRect = /* @__PURE__ */ __name((node, point2) => {\n  var x = node.x;\n  var y = node.y;\n  var dx = point2.x - x;\n  var dy = point2.y - y;\n  var w = node.width / 2;\n  var h = node.height / 2;\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = dy === 0 ? 0 : h * dx / dy;\n    sy = h;\n  } else {\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = dx === 0 ? 0 : w * dy / dx;\n  }\n  return { x: x + sx, y: y + sy };\n}, \"intersectRect\");\nvar intersect_rect_default = intersectRect;\n\n// src/dagre-wrapper/intersect/index.js\nvar intersect_default = {\n  node: intersect_node_default,\n  circle: intersect_circle_default,\n  ellipse: intersect_ellipse_default,\n  polygon: intersect_polygon_default,\n  rect: intersect_rect_default\n};\n\n// src/dagre-wrapper/shapes/util.js\nimport { select as select3 } from \"d3\";\nvar labelHelper = /* @__PURE__ */ __name(async (parent, node, _classes, isNode) => {\n  const config2 = getConfig2();\n  let classes2;\n  const useHtmlLabels = node.useHtmlLabels || evaluate(config2.flowchart.htmlLabels);\n  if (!_classes) {\n    classes2 = \"node default\";\n  } else {\n    classes2 = _classes;\n  }\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", classes2).attr(\"id\", node.domId || node.id);\n  const label = shapeSvg.insert(\"g\").attr(\"class\", \"label\").attr(\"style\", node.labelStyle);\n  let labelText;\n  if (node.labelText === void 0) {\n    labelText = \"\";\n  } else {\n    labelText = typeof node.labelText === \"string\" ? node.labelText : node.labelText[0];\n  }\n  const textNode = label.node();\n  let text;\n  if (node.labelType === \"markdown\") {\n    text = createText(\n      label,\n      sanitizeText(decodeEntities(labelText), config2),\n      {\n        useHtmlLabels,\n        width: node.width || config2.flowchart.wrappingWidth,\n        classes: \"markdown-node-label\"\n      },\n      config2\n    );\n  } else {\n    text = textNode.appendChild(\n      await createLabel_default(\n        sanitizeText(decodeEntities(labelText), config2),\n        node.labelStyle,\n        false,\n        isNode\n      )\n    );\n  }\n  let bbox = text.getBBox();\n  const halfPadding = node.padding / 2;\n  if (evaluate(config2.flowchart.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select3(text);\n    const images = div.getElementsByTagName(\"img\");\n    if (images) {\n      const noImgText = labelText.replace(/<img[^>]*>/g, \"\").trim() === \"\";\n      await Promise.all(\n        [...images].map(\n          (img) => new Promise((res) => {\n            function setupImage() {\n              img.style.display = \"flex\";\n              img.style.flexDirection = \"column\";\n              if (noImgText) {\n                const bodyFontSize = config2.fontSize ? config2.fontSize : window.getComputedStyle(document.body).fontSize;\n                const enlargingFactor = 5;\n                const width = parseInt(bodyFontSize, 10) * enlargingFactor + \"px\";\n                img.style.minWidth = width;\n                img.style.maxWidth = width;\n              } else {\n                img.style.width = \"100%\";\n              }\n              res(img);\n            }\n            __name(setupImage, \"setupImage\");\n            setTimeout(() => {\n              if (img.complete) {\n                setupImage();\n              }\n            });\n            img.addEventListener(\"error\", setupImage);\n            img.addEventListener(\"load\", setupImage);\n          })\n        )\n      );\n    }\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  if (useHtmlLabels) {\n    label.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + -bbox.height / 2 + \")\");\n  } else {\n    label.attr(\"transform\", \"translate(0, \" + -bbox.height / 2 + \")\");\n  }\n  if (node.centerLabel) {\n    label.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + -bbox.height / 2 + \")\");\n  }\n  label.insert(\"rect\", \":first-child\");\n  return { shapeSvg, bbox, halfPadding, label };\n}, \"labelHelper\");\nvar updateNodeBounds = /* @__PURE__ */ __name((node, element) => {\n  const bbox = element.node().getBBox();\n  node.width = bbox.width;\n  node.height = bbox.height;\n}, \"updateNodeBounds\");\nfunction insertPolygonShape(parent, w, h, points) {\n  return parent.insert(\"polygon\", \":first-child\").attr(\n    \"points\",\n    points.map(function(d) {\n      return d.x + \",\" + d.y;\n    }).join(\" \")\n  ).attr(\"class\", \"label-container\").attr(\"transform\", \"translate(\" + -w / 2 + \",\" + h / 2 + \")\");\n}\n__name(insertPolygonShape, \"insertPolygonShape\");\n\n// src/dagre-wrapper/shapes/note.js\nvar note = /* @__PURE__ */ __name(async (parent, node) => {\n  const useHtmlLabels = node.useHtmlLabels || getConfig2().flowchart.htmlLabels;\n  if (!useHtmlLabels) {\n    node.centerLabel = true;\n  }\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    \"node \" + node.classes,\n    true\n  );\n  log.info(\"Classes = \", node.classes);\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  rect2.attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"x\", -bbox.width / 2 - halfPadding).attr(\"y\", -bbox.height / 2 - halfPadding).attr(\"width\", bbox.width + node.padding).attr(\"height\", bbox.height + node.padding);\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"note\");\nvar note_default = note;\n\n// src/dagre-wrapper/nodes.js\nvar formatClass = /* @__PURE__ */ __name((str) => {\n  if (str) {\n    return \" \" + str;\n  }\n  return \"\";\n}, \"formatClass\");\nvar getClassesFromNode = /* @__PURE__ */ __name((node, otherClasses) => {\n  return `${otherClasses ? otherClasses : \"node default\"}${formatClass(node.classes)} ${formatClass(\n    node.class\n  )}`;\n}, \"getClassesFromNode\");\nvar question = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const s = w + h;\n  const points = [\n    { x: s / 2, y: 0 },\n    { x: s, y: -s / 2 },\n    { x: s / 2, y: -s },\n    { x: 0, y: -s / 2 }\n  ];\n  log.info(\"Question main (Circle)\");\n  const questionElem = insertPolygonShape(shapeSvg, s, s, points);\n  questionElem.attr(\"style\", node.style);\n  updateNodeBounds(node, questionElem);\n  node.intersect = function(point2) {\n    log.warn(\"Intersect called\");\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"question\");\nvar choice = /* @__PURE__ */ __name((parent, node) => {\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"node default\").attr(\"id\", node.domId || node.id);\n  const s = 28;\n  const points = [\n    { x: 0, y: s / 2 },\n    { x: s / 2, y: 0 },\n    { x: 0, y: -s / 2 },\n    { x: -s / 2, y: 0 }\n  ];\n  const choice2 = shapeSvg.insert(\"polygon\", \":first-child\").attr(\n    \"points\",\n    points.map(function(d) {\n      return d.x + \",\" + d.y;\n    }).join(\" \")\n  );\n  choice2.attr(\"class\", \"state-start\").attr(\"r\", 7).attr(\"width\", 28).attr(\"height\", 28);\n  node.width = 28;\n  node.height = 28;\n  node.intersect = function(point2) {\n    return intersect_default.circle(node, 14, point2);\n  };\n  return shapeSvg;\n}, \"choice\");\nvar hexagon = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const f = 4;\n  const h = bbox.height + node.padding;\n  const m = h / f;\n  const w = bbox.width + 2 * m + node.padding;\n  const points = [\n    { x: m, y: 0 },\n    { x: w - m, y: 0 },\n    { x: w, y: -h / 2 },\n    { x: w - m, y: -h },\n    { x: m, y: -h },\n    { x: 0, y: -h / 2 }\n  ];\n  const hex = insertPolygonShape(shapeSvg, w, h, points);\n  hex.attr(\"style\", node.style);\n  updateNodeBounds(node, hex);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"hexagon\");\nvar block_arrow = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(parent, node, void 0, true);\n  const f = 2;\n  const h = bbox.height + 2 * node.padding;\n  const m = h / f;\n  const w = bbox.width + 2 * m + node.padding;\n  const points = getArrowPoints(node.directions, bbox, node);\n  const blockArrow = insertPolygonShape(shapeSvg, w, h, points);\n  blockArrow.attr(\"style\", node.style);\n  updateNodeBounds(node, blockArrow);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"block_arrow\");\nvar rect_left_inv_arrow = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: -h / 2, y: 0 },\n    { x: w, y: 0 },\n    { x: w, y: -h },\n    { x: -h / 2, y: -h },\n    { x: 0, y: -h / 2 }\n  ];\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr(\"style\", node.style);\n  node.width = w + h;\n  node.height = h;\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"rect_left_inv_arrow\");\nvar lean_right = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node), true);\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: -2 * h / 6, y: 0 },\n    { x: w - h / 6, y: 0 },\n    { x: w + 2 * h / 6, y: -h },\n    { x: h / 6, y: -h }\n  ];\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr(\"style\", node.style);\n  updateNodeBounds(node, el);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"lean_right\");\nvar lean_left = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: 2 * h / 6, y: 0 },\n    { x: w + h / 6, y: 0 },\n    { x: w - 2 * h / 6, y: -h },\n    { x: -h / 6, y: -h }\n  ];\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr(\"style\", node.style);\n  updateNodeBounds(node, el);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"lean_left\");\nvar trapezoid = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: -2 * h / 6, y: 0 },\n    { x: w + 2 * h / 6, y: 0 },\n    { x: w - h / 6, y: -h },\n    { x: h / 6, y: -h }\n  ];\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr(\"style\", node.style);\n  updateNodeBounds(node, el);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"trapezoid\");\nvar inv_trapezoid = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: h / 6, y: 0 },\n    { x: w - h / 6, y: 0 },\n    { x: w + 2 * h / 6, y: -h },\n    { x: -2 * h / 6, y: -h }\n  ];\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr(\"style\", node.style);\n  updateNodeBounds(node, el);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"inv_trapezoid\");\nvar rect_right_inv_arrow = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: 0, y: 0 },\n    { x: w + h / 2, y: 0 },\n    { x: w, y: -h / 2 },\n    { x: w + h / 2, y: -h },\n    { x: 0, y: -h }\n  ];\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr(\"style\", node.style);\n  updateNodeBounds(node, el);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"rect_right_inv_arrow\");\nvar cylinder = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const rx = w / 2;\n  const ry = rx / (2.5 + w / 50);\n  const h = bbox.height + ry + node.padding;\n  const shape = \"M 0,\" + ry + \" a \" + rx + \",\" + ry + \" 0,0,0 \" + w + \" 0 a \" + rx + \",\" + ry + \" 0,0,0 \" + -w + \" 0 l 0,\" + h + \" a \" + rx + \",\" + ry + \" 0,0,0 \" + w + \" 0 l 0,\" + -h;\n  const el = shapeSvg.attr(\"label-offset-y\", ry).insert(\"path\", \":first-child\").attr(\"style\", node.style).attr(\"d\", shape).attr(\"transform\", \"translate(\" + -w / 2 + \",\" + -(h / 2 + ry) + \")\");\n  updateNodeBounds(node, el);\n  node.intersect = function(point2) {\n    const pos = intersect_default.rect(node, point2);\n    const x = pos.x - node.x;\n    if (rx != 0 && (Math.abs(x) < node.width / 2 || Math.abs(x) == node.width / 2 && Math.abs(pos.y - node.y) > node.height / 2 - ry)) {\n      let y = ry * ry * (1 - x * x / (rx * rx));\n      if (y != 0) {\n        y = Math.sqrt(y);\n      }\n      y = ry - y;\n      if (point2.y - node.y > 0) {\n        y = -y;\n      }\n      pos.y += y;\n    }\n    return pos;\n  };\n  return shapeSvg;\n}, \"cylinder\");\nvar rect = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    \"node \" + node.classes + \" \" + node.class,\n    true\n  );\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  const totalWidth = node.positioned ? node.width : bbox.width + node.padding;\n  const totalHeight = node.positioned ? node.height : bbox.height + node.padding;\n  const x = node.positioned ? -totalWidth / 2 : -bbox.width / 2 - halfPadding;\n  const y = node.positioned ? -totalHeight / 2 : -bbox.height / 2 - halfPadding;\n  rect2.attr(\"class\", \"basic label-container\").attr(\"style\", node.style).attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"x\", x).attr(\"y\", y).attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  if (node.props) {\n    const propKeys = new Set(Object.keys(node.props));\n    if (node.props.borders) {\n      applyNodePropertyBorders(rect2, node.props.borders, totalWidth, totalHeight);\n      propKeys.delete(\"borders\");\n    }\n    propKeys.forEach((propKey) => {\n      log.warn(`Unknown node property ${propKey}`);\n    });\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"rect\");\nvar composite = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    \"node \" + node.classes,\n    true\n  );\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  const totalWidth = node.positioned ? node.width : bbox.width + node.padding;\n  const totalHeight = node.positioned ? node.height : bbox.height + node.padding;\n  const x = node.positioned ? -totalWidth / 2 : -bbox.width / 2 - halfPadding;\n  const y = node.positioned ? -totalHeight / 2 : -bbox.height / 2 - halfPadding;\n  rect2.attr(\"class\", \"basic cluster composite label-container\").attr(\"style\", node.style).attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"x\", x).attr(\"y\", y).attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  if (node.props) {\n    const propKeys = new Set(Object.keys(node.props));\n    if (node.props.borders) {\n      applyNodePropertyBorders(rect2, node.props.borders, totalWidth, totalHeight);\n      propKeys.delete(\"borders\");\n    }\n    propKeys.forEach((propKey) => {\n      log.warn(`Unknown node property ${propKey}`);\n    });\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"composite\");\nvar labelRect = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg } = await labelHelper(parent, node, \"label\", true);\n  log.trace(\"Classes = \", node.class);\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  const totalWidth = 0;\n  const totalHeight = 0;\n  rect2.attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  shapeSvg.attr(\"class\", \"label edgeLabel\");\n  if (node.props) {\n    const propKeys = new Set(Object.keys(node.props));\n    if (node.props.borders) {\n      applyNodePropertyBorders(rect2, node.props.borders, totalWidth, totalHeight);\n      propKeys.delete(\"borders\");\n    }\n    propKeys.forEach((propKey) => {\n      log.warn(`Unknown node property ${propKey}`);\n    });\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"labelRect\");\nfunction applyNodePropertyBorders(rect2, borders, totalWidth, totalHeight) {\n  const strokeDashArray = [];\n  const addBorder = /* @__PURE__ */ __name((length) => {\n    strokeDashArray.push(length, 0);\n  }, \"addBorder\");\n  const skipBorder = /* @__PURE__ */ __name((length) => {\n    strokeDashArray.push(0, length);\n  }, \"skipBorder\");\n  if (borders.includes(\"t\")) {\n    log.debug(\"add top border\");\n    addBorder(totalWidth);\n  } else {\n    skipBorder(totalWidth);\n  }\n  if (borders.includes(\"r\")) {\n    log.debug(\"add right border\");\n    addBorder(totalHeight);\n  } else {\n    skipBorder(totalHeight);\n  }\n  if (borders.includes(\"b\")) {\n    log.debug(\"add bottom border\");\n    addBorder(totalWidth);\n  } else {\n    skipBorder(totalWidth);\n  }\n  if (borders.includes(\"l\")) {\n    log.debug(\"add left border\");\n    addBorder(totalHeight);\n  } else {\n    skipBorder(totalHeight);\n  }\n  rect2.attr(\"stroke-dasharray\", strokeDashArray.join(\" \"));\n}\n__name(applyNodePropertyBorders, \"applyNodePropertyBorders\");\nvar rectWithTitle = /* @__PURE__ */ __name(async (parent, node) => {\n  let classes2;\n  if (!node.classes) {\n    classes2 = \"node default\";\n  } else {\n    classes2 = \"node \" + node.classes;\n  }\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", classes2).attr(\"id\", node.domId || node.id);\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  const innerLine = shapeSvg.insert(\"line\");\n  const label = shapeSvg.insert(\"g\").attr(\"class\", \"label\");\n  const text2 = node.labelText.flat ? node.labelText.flat() : node.labelText;\n  let title = \"\";\n  if (typeof text2 === \"object\") {\n    title = text2[0];\n  } else {\n    title = text2;\n  }\n  log.info(\"Label text abc79\", title, text2, typeof text2 === \"object\");\n  const text = label.node().appendChild(await createLabel_default(title, node.labelStyle, true, true));\n  let bbox = { width: 0, height: 0 };\n  if (evaluate(getConfig2().flowchart.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select4(text);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  log.info(\"Text 2\", text2);\n  const textRows = text2.slice(1, text2.length);\n  let titleBox = text.getBBox();\n  const descr = label.node().appendChild(\n    await createLabel_default(\n      textRows.join ? textRows.join(\"<br/>\") : textRows,\n      node.labelStyle,\n      true,\n      true\n    )\n  );\n  if (evaluate(getConfig2().flowchart.htmlLabels)) {\n    const div = descr.children[0];\n    const dv = select4(descr);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  const halfPadding = node.padding / 2;\n  select4(descr).attr(\n    \"transform\",\n    \"translate( \" + // (titleBox.width - bbox.width) / 2 +\n    (bbox.width > titleBox.width ? 0 : (titleBox.width - bbox.width) / 2) + \", \" + (titleBox.height + halfPadding + 5) + \")\"\n  );\n  select4(text).attr(\n    \"transform\",\n    \"translate( \" + // (titleBox.width - bbox.width) / 2 +\n    (bbox.width < titleBox.width ? 0 : -(titleBox.width - bbox.width) / 2) + \", 0)\"\n  );\n  bbox = label.node().getBBox();\n  label.attr(\n    \"transform\",\n    \"translate(\" + -bbox.width / 2 + \", \" + (-bbox.height / 2 - halfPadding + 3) + \")\"\n  );\n  rect2.attr(\"class\", \"outer title-state\").attr(\"x\", -bbox.width / 2 - halfPadding).attr(\"y\", -bbox.height / 2 - halfPadding).attr(\"width\", bbox.width + node.padding).attr(\"height\", bbox.height + node.padding);\n  innerLine.attr(\"class\", \"divider\").attr(\"x1\", -bbox.width / 2 - halfPadding).attr(\"x2\", bbox.width / 2 + halfPadding).attr(\"y1\", -bbox.height / 2 - halfPadding + titleBox.height + halfPadding).attr(\"y2\", -bbox.height / 2 - halfPadding + titleBox.height + halfPadding);\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"rectWithTitle\");\nvar stadium = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const h = bbox.height + node.padding;\n  const w = bbox.width + h / 4 + node.padding;\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\").attr(\"style\", node.style).attr(\"rx\", h / 2).attr(\"ry\", h / 2).attr(\"x\", -w / 2).attr(\"y\", -h / 2).attr(\"width\", w).attr(\"height\", h);\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"stadium\");\nvar circle2 = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const circle3 = shapeSvg.insert(\"circle\", \":first-child\");\n  circle3.attr(\"style\", node.style).attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"r\", bbox.width / 2 + halfPadding).attr(\"width\", bbox.width + node.padding).attr(\"height\", bbox.height + node.padding);\n  log.info(\"Circle main\");\n  updateNodeBounds(node, circle3);\n  node.intersect = function(point2) {\n    log.info(\"Circle intersect\", node, bbox.width / 2 + halfPadding, point2);\n    return intersect_default.circle(node, bbox.width / 2 + halfPadding, point2);\n  };\n  return shapeSvg;\n}, \"circle\");\nvar doublecircle = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const gap = 5;\n  const circleGroup = shapeSvg.insert(\"g\", \":first-child\");\n  const outerCircle = circleGroup.insert(\"circle\");\n  const innerCircle = circleGroup.insert(\"circle\");\n  circleGroup.attr(\"class\", node.class);\n  outerCircle.attr(\"style\", node.style).attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"r\", bbox.width / 2 + halfPadding + gap).attr(\"width\", bbox.width + node.padding + gap * 2).attr(\"height\", bbox.height + node.padding + gap * 2);\n  innerCircle.attr(\"style\", node.style).attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"r\", bbox.width / 2 + halfPadding).attr(\"width\", bbox.width + node.padding).attr(\"height\", bbox.height + node.padding);\n  log.info(\"DoubleCircle main\");\n  updateNodeBounds(node, outerCircle);\n  node.intersect = function(point2) {\n    log.info(\"DoubleCircle intersect\", node, bbox.width / 2 + halfPadding + gap, point2);\n    return intersect_default.circle(node, bbox.width / 2 + halfPadding + gap, point2);\n  };\n  return shapeSvg;\n}, \"doublecircle\");\nvar subroutine = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: 0, y: 0 },\n    { x: w, y: 0 },\n    { x: w, y: -h },\n    { x: 0, y: -h },\n    { x: 0, y: 0 },\n    { x: -8, y: 0 },\n    { x: w + 8, y: 0 },\n    { x: w + 8, y: -h },\n    { x: -8, y: -h },\n    { x: -8, y: 0 }\n  ];\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr(\"style\", node.style);\n  updateNodeBounds(node, el);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"subroutine\");\nvar start = /* @__PURE__ */ __name((parent, node) => {\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"node default\").attr(\"id\", node.domId || node.id);\n  const circle3 = shapeSvg.insert(\"circle\", \":first-child\");\n  circle3.attr(\"class\", \"state-start\").attr(\"r\", 7).attr(\"width\", 14).attr(\"height\", 14);\n  updateNodeBounds(node, circle3);\n  node.intersect = function(point2) {\n    return intersect_default.circle(node, 7, point2);\n  };\n  return shapeSvg;\n}, \"start\");\nvar forkJoin = /* @__PURE__ */ __name((parent, node, dir) => {\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"node default\").attr(\"id\", node.domId || node.id);\n  let width = 70;\n  let height = 10;\n  if (dir === \"LR\") {\n    width = 10;\n    height = 70;\n  }\n  const shape = shapeSvg.append(\"rect\").attr(\"x\", -1 * width / 2).attr(\"y\", -1 * height / 2).attr(\"width\", width).attr(\"height\", height).attr(\"class\", \"fork-join\");\n  updateNodeBounds(node, shape);\n  node.height = node.height + node.padding / 2;\n  node.width = node.width + node.padding / 2;\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"forkJoin\");\nvar end = /* @__PURE__ */ __name((parent, node) => {\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"node default\").attr(\"id\", node.domId || node.id);\n  const innerCircle = shapeSvg.insert(\"circle\", \":first-child\");\n  const circle3 = shapeSvg.insert(\"circle\", \":first-child\");\n  circle3.attr(\"class\", \"state-start\").attr(\"r\", 7).attr(\"width\", 14).attr(\"height\", 14);\n  innerCircle.attr(\"class\", \"state-end\").attr(\"r\", 5).attr(\"width\", 10).attr(\"height\", 10);\n  updateNodeBounds(node, circle3);\n  node.intersect = function(point2) {\n    return intersect_default.circle(node, 7, point2);\n  };\n  return shapeSvg;\n}, \"end\");\nvar class_box = /* @__PURE__ */ __name(async (parent, node) => {\n  const halfPadding = node.padding / 2;\n  const rowPadding = 4;\n  const lineHeight = 8;\n  let classes2;\n  if (!node.classes) {\n    classes2 = \"node default\";\n  } else {\n    classes2 = \"node \" + node.classes;\n  }\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", classes2).attr(\"id\", node.domId || node.id);\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  const topLine = shapeSvg.insert(\"line\");\n  const bottomLine = shapeSvg.insert(\"line\");\n  let maxWidth = 0;\n  let maxHeight = rowPadding;\n  const labelContainer = shapeSvg.insert(\"g\").attr(\"class\", \"label\");\n  let verticalPos = 0;\n  const hasInterface = node.classData.annotations?.[0];\n  const interfaceLabelText = node.classData.annotations[0] ? \"\\xAB\" + node.classData.annotations[0] + \"\\xBB\" : \"\";\n  const interfaceLabel = labelContainer.node().appendChild(await createLabel_default(interfaceLabelText, node.labelStyle, true, true));\n  let interfaceBBox = interfaceLabel.getBBox();\n  if (evaluate(getConfig2().flowchart.htmlLabels)) {\n    const div = interfaceLabel.children[0];\n    const dv = select4(interfaceLabel);\n    interfaceBBox = div.getBoundingClientRect();\n    dv.attr(\"width\", interfaceBBox.width);\n    dv.attr(\"height\", interfaceBBox.height);\n  }\n  if (node.classData.annotations[0]) {\n    maxHeight += interfaceBBox.height + rowPadding;\n    maxWidth += interfaceBBox.width;\n  }\n  let classTitleString = node.classData.label;\n  if (node.classData.type !== void 0 && node.classData.type !== \"\") {\n    if (getConfig2().flowchart.htmlLabels) {\n      classTitleString += \"&lt;\" + node.classData.type + \"&gt;\";\n    } else {\n      classTitleString += \"<\" + node.classData.type + \">\";\n    }\n  }\n  const classTitleLabel = labelContainer.node().appendChild(await createLabel_default(classTitleString, node.labelStyle, true, true));\n  select4(classTitleLabel).attr(\"class\", \"classTitle\");\n  let classTitleBBox = classTitleLabel.getBBox();\n  if (evaluate(getConfig2().flowchart.htmlLabels)) {\n    const div = classTitleLabel.children[0];\n    const dv = select4(classTitleLabel);\n    classTitleBBox = div.getBoundingClientRect();\n    dv.attr(\"width\", classTitleBBox.width);\n    dv.attr(\"height\", classTitleBBox.height);\n  }\n  maxHeight += classTitleBBox.height + rowPadding;\n  if (classTitleBBox.width > maxWidth) {\n    maxWidth = classTitleBBox.width;\n  }\n  const classAttributes = [];\n  node.classData.members.forEach(async (member) => {\n    const parsedInfo = member.getDisplayDetails();\n    let parsedText = parsedInfo.displayText;\n    if (getConfig2().flowchart.htmlLabels) {\n      parsedText = parsedText.replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    const lbl = labelContainer.node().appendChild(\n      await createLabel_default(\n        parsedText,\n        parsedInfo.cssStyle ? parsedInfo.cssStyle : node.labelStyle,\n        true,\n        true\n      )\n    );\n    let bbox = lbl.getBBox();\n    if (evaluate(getConfig2().flowchart.htmlLabels)) {\n      const div = lbl.children[0];\n      const dv = select4(lbl);\n      bbox = div.getBoundingClientRect();\n      dv.attr(\"width\", bbox.width);\n      dv.attr(\"height\", bbox.height);\n    }\n    if (bbox.width > maxWidth) {\n      maxWidth = bbox.width;\n    }\n    maxHeight += bbox.height + rowPadding;\n    classAttributes.push(lbl);\n  });\n  maxHeight += lineHeight;\n  const classMethods = [];\n  node.classData.methods.forEach(async (member) => {\n    const parsedInfo = member.getDisplayDetails();\n    let displayText = parsedInfo.displayText;\n    if (getConfig2().flowchart.htmlLabels) {\n      displayText = displayText.replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    const lbl = labelContainer.node().appendChild(\n      await createLabel_default(\n        displayText,\n        parsedInfo.cssStyle ? parsedInfo.cssStyle : node.labelStyle,\n        true,\n        true\n      )\n    );\n    let bbox = lbl.getBBox();\n    if (evaluate(getConfig2().flowchart.htmlLabels)) {\n      const div = lbl.children[0];\n      const dv = select4(lbl);\n      bbox = div.getBoundingClientRect();\n      dv.attr(\"width\", bbox.width);\n      dv.attr(\"height\", bbox.height);\n    }\n    if (bbox.width > maxWidth) {\n      maxWidth = bbox.width;\n    }\n    maxHeight += bbox.height + rowPadding;\n    classMethods.push(lbl);\n  });\n  maxHeight += lineHeight;\n  if (hasInterface) {\n    let diffX2 = (maxWidth - interfaceBBox.width) / 2;\n    select4(interfaceLabel).attr(\n      \"transform\",\n      \"translate( \" + (-1 * maxWidth / 2 + diffX2) + \", \" + -1 * maxHeight / 2 + \")\"\n    );\n    verticalPos = interfaceBBox.height + rowPadding;\n  }\n  let diffX = (maxWidth - classTitleBBox.width) / 2;\n  select4(classTitleLabel).attr(\n    \"transform\",\n    \"translate( \" + (-1 * maxWidth / 2 + diffX) + \", \" + (-1 * maxHeight / 2 + verticalPos) + \")\"\n  );\n  verticalPos += classTitleBBox.height + rowPadding;\n  topLine.attr(\"class\", \"divider\").attr(\"x1\", -maxWidth / 2 - halfPadding).attr(\"x2\", maxWidth / 2 + halfPadding).attr(\"y1\", -maxHeight / 2 - halfPadding + lineHeight + verticalPos).attr(\"y2\", -maxHeight / 2 - halfPadding + lineHeight + verticalPos);\n  verticalPos += lineHeight;\n  classAttributes.forEach((lbl) => {\n    select4(lbl).attr(\n      \"transform\",\n      \"translate( \" + -maxWidth / 2 + \", \" + (-1 * maxHeight / 2 + verticalPos + lineHeight / 2) + \")\"\n    );\n    const memberBBox = lbl?.getBBox();\n    verticalPos += (memberBBox?.height ?? 0) + rowPadding;\n  });\n  verticalPos += lineHeight;\n  bottomLine.attr(\"class\", \"divider\").attr(\"x1\", -maxWidth / 2 - halfPadding).attr(\"x2\", maxWidth / 2 + halfPadding).attr(\"y1\", -maxHeight / 2 - halfPadding + lineHeight + verticalPos).attr(\"y2\", -maxHeight / 2 - halfPadding + lineHeight + verticalPos);\n  verticalPos += lineHeight;\n  classMethods.forEach((lbl) => {\n    select4(lbl).attr(\n      \"transform\",\n      \"translate( \" + -maxWidth / 2 + \", \" + (-1 * maxHeight / 2 + verticalPos) + \")\"\n    );\n    const memberBBox = lbl?.getBBox();\n    verticalPos += (memberBBox?.height ?? 0) + rowPadding;\n  });\n  rect2.attr(\"style\", node.style).attr(\"class\", \"outer title-state\").attr(\"x\", -maxWidth / 2 - halfPadding).attr(\"y\", -(maxHeight / 2) - halfPadding).attr(\"width\", maxWidth + node.padding).attr(\"height\", maxHeight + node.padding);\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"class_box\");\nvar shapes = {\n  rhombus: question,\n  composite,\n  question,\n  rect,\n  labelRect,\n  rectWithTitle,\n  choice,\n  circle: circle2,\n  doublecircle,\n  stadium,\n  hexagon,\n  block_arrow,\n  rect_left_inv_arrow,\n  lean_right,\n  lean_left,\n  trapezoid,\n  inv_trapezoid,\n  rect_right_inv_arrow,\n  cylinder,\n  start,\n  end,\n  note: note_default,\n  subroutine,\n  fork: forkJoin,\n  join: forkJoin,\n  class_box\n};\nvar nodeElems = {};\nvar insertNode = /* @__PURE__ */ __name(async (elem, node, renderOptions) => {\n  let newEl;\n  let el;\n  if (node.link) {\n    let target;\n    if (getConfig2().securityLevel === \"sandbox\") {\n      target = \"_top\";\n    } else if (node.linkTarget) {\n      target = node.linkTarget || \"_blank\";\n    }\n    newEl = elem.insert(\"svg:a\").attr(\"xlink:href\", node.link).attr(\"target\", target);\n    el = await shapes[node.shape](newEl, node, renderOptions);\n  } else {\n    el = await shapes[node.shape](elem, node, renderOptions);\n    newEl = el;\n  }\n  if (node.tooltip) {\n    el.attr(\"title\", node.tooltip);\n  }\n  if (node.class) {\n    el.attr(\"class\", \"node default \" + node.class);\n  }\n  nodeElems[node.id] = newEl;\n  if (node.haveCallback) {\n    nodeElems[node.id].attr(\"class\", nodeElems[node.id].attr(\"class\") + \" clickable\");\n  }\n  return newEl;\n}, \"insertNode\");\nvar positionNode = /* @__PURE__ */ __name((node) => {\n  const el = nodeElems[node.id];\n  log.trace(\n    \"Transforming node\",\n    node.diff,\n    node,\n    \"translate(\" + (node.x - node.width / 2 - 5) + \", \" + node.width / 2 + \")\"\n  );\n  const padding2 = 8;\n  const diff = node.diff || 0;\n  if (node.clusterNode) {\n    el.attr(\n      \"transform\",\n      \"translate(\" + (node.x + diff - node.width / 2) + \", \" + (node.y - node.height / 2 - padding2) + \")\"\n    );\n  } else {\n    el.attr(\"transform\", \"translate(\" + node.x + \", \" + node.y + \")\");\n  }\n  return diff;\n}, \"positionNode\");\n\n// src/diagrams/block/renderHelpers.ts\nfunction getNodeFromBlock(block, db2, positioned = false) {\n  const vertex = block;\n  let classStr = \"default\";\n  if ((vertex?.classes?.length || 0) > 0) {\n    classStr = (vertex?.classes ?? []).join(\" \");\n  }\n  classStr = classStr + \" flowchart-label\";\n  let radius = 0;\n  let shape = \"\";\n  let padding2;\n  switch (vertex.type) {\n    case \"round\":\n      radius = 5;\n      shape = \"rect\";\n      break;\n    case \"composite\":\n      radius = 0;\n      shape = \"composite\";\n      padding2 = 0;\n      break;\n    case \"square\":\n      shape = \"rect\";\n      break;\n    case \"diamond\":\n      shape = \"question\";\n      break;\n    case \"hexagon\":\n      shape = \"hexagon\";\n      break;\n    case \"block_arrow\":\n      shape = \"block_arrow\";\n      break;\n    case \"odd\":\n      shape = \"rect_left_inv_arrow\";\n      break;\n    case \"lean_right\":\n      shape = \"lean_right\";\n      break;\n    case \"lean_left\":\n      shape = \"lean_left\";\n      break;\n    case \"trapezoid\":\n      shape = \"trapezoid\";\n      break;\n    case \"inv_trapezoid\":\n      shape = \"inv_trapezoid\";\n      break;\n    case \"rect_left_inv_arrow\":\n      shape = \"rect_left_inv_arrow\";\n      break;\n    case \"circle\":\n      shape = \"circle\";\n      break;\n    case \"ellipse\":\n      shape = \"ellipse\";\n      break;\n    case \"stadium\":\n      shape = \"stadium\";\n      break;\n    case \"subroutine\":\n      shape = \"subroutine\";\n      break;\n    case \"cylinder\":\n      shape = \"cylinder\";\n      break;\n    case \"group\":\n      shape = \"rect\";\n      break;\n    case \"doublecircle\":\n      shape = \"doublecircle\";\n      break;\n    default:\n      shape = \"rect\";\n  }\n  const styles = getStylesFromArray(vertex?.styles ?? []);\n  const vertexText = vertex.label;\n  const bounds = vertex.size ?? { width: 0, height: 0, x: 0, y: 0 };\n  const node = {\n    labelStyle: styles.labelStyle,\n    shape,\n    labelText: vertexText,\n    rx: radius,\n    ry: radius,\n    class: classStr,\n    style: styles.style,\n    id: vertex.id,\n    directions: vertex.directions,\n    width: bounds.width,\n    height: bounds.height,\n    x: bounds.x,\n    y: bounds.y,\n    positioned,\n    intersect: void 0,\n    type: vertex.type,\n    padding: padding2 ?? getConfig()?.block?.padding ?? 0\n  };\n  return node;\n}\n__name(getNodeFromBlock, \"getNodeFromBlock\");\nasync function calculateBlockSize(elem, block, db2) {\n  const node = getNodeFromBlock(block, db2, false);\n  if (node.type === \"group\") {\n    return;\n  }\n  const config2 = getConfig();\n  const nodeEl = await insertNode(elem, node, { config: config2 });\n  const boundingBox = nodeEl.node().getBBox();\n  const obj = db2.getBlock(node.id);\n  obj.size = { width: boundingBox.width, height: boundingBox.height, x: 0, y: 0, node: nodeEl };\n  db2.setBlock(obj);\n  nodeEl.remove();\n}\n__name(calculateBlockSize, \"calculateBlockSize\");\nasync function insertBlockPositioned(elem, block, db2) {\n  const node = getNodeFromBlock(block, db2, true);\n  const obj = db2.getBlock(node.id);\n  if (obj.type !== \"space\") {\n    const config2 = getConfig();\n    await insertNode(elem, node, { config: config2 });\n    block.intersect = node?.intersect;\n    positionNode(node);\n  }\n}\n__name(insertBlockPositioned, \"insertBlockPositioned\");\nasync function performOperations(elem, blocks2, db2, operation) {\n  for (const block of blocks2) {\n    await operation(elem, block, db2);\n    if (block.children) {\n      await performOperations(elem, block.children, db2, operation);\n    }\n  }\n}\n__name(performOperations, \"performOperations\");\nasync function calculateBlockSizes(elem, blocks2, db2) {\n  await performOperations(elem, blocks2, db2, calculateBlockSize);\n}\n__name(calculateBlockSizes, \"calculateBlockSizes\");\nasync function insertBlocks(elem, blocks2, db2) {\n  await performOperations(elem, blocks2, db2, insertBlockPositioned);\n}\n__name(insertBlocks, \"insertBlocks\");\nasync function insertEdges(elem, edges, blocks2, db2, id) {\n  const g = new graphlib.Graph({\n    multigraph: true,\n    compound: true\n  });\n  g.setGraph({\n    rankdir: \"TB\",\n    nodesep: 10,\n    ranksep: 10,\n    marginx: 8,\n    marginy: 8\n  });\n  for (const block of blocks2) {\n    if (block.size) {\n      g.setNode(block.id, {\n        width: block.size.width,\n        height: block.size.height,\n        intersect: block.intersect\n      });\n    }\n  }\n  for (const edge of edges) {\n    if (edge.start && edge.end) {\n      const startBlock = db2.getBlock(edge.start);\n      const endBlock = db2.getBlock(edge.end);\n      if (startBlock?.size && endBlock?.size) {\n        const start2 = startBlock.size;\n        const end2 = endBlock.size;\n        const points = [\n          { x: start2.x, y: start2.y },\n          { x: start2.x + (end2.x - start2.x) / 2, y: start2.y + (end2.y - start2.y) / 2 },\n          { x: end2.x, y: end2.y }\n        ];\n        insertEdge(\n          elem,\n          { v: edge.start, w: edge.end, name: edge.id },\n          {\n            ...edge,\n            arrowTypeEnd: edge.arrowTypeEnd,\n            arrowTypeStart: edge.arrowTypeStart,\n            points,\n            classes: \"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1\"\n          },\n          void 0,\n          \"block\",\n          g,\n          id\n        );\n        if (edge.label) {\n          await insertEdgeLabel(elem, {\n            ...edge,\n            label: edge.label,\n            labelStyle: \"stroke: #333; stroke-width: 1.5px;fill:none;\",\n            arrowTypeEnd: edge.arrowTypeEnd,\n            arrowTypeStart: edge.arrowTypeStart,\n            points,\n            classes: \"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1\"\n          });\n          positionEdgeLabel(\n            { ...edge, x: points[1].x, y: points[1].y },\n            {\n              originalPath: points\n            }\n          );\n        }\n      }\n    }\n  }\n}\n__name(insertEdges, \"insertEdges\");\n\n// src/diagrams/block/blockRenderer.ts\nvar getClasses2 = /* @__PURE__ */ __name(function(text, diagObj) {\n  return diagObj.db.getClasses();\n}, \"getClasses\");\nvar draw = /* @__PURE__ */ __name(async function(text, id, _version, diagObj) {\n  const { securityLevel, block: conf } = getConfig();\n  const db2 = diagObj.db;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = d3select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? d3select(sandboxElement.nodes()[0].contentDocument.body) : d3select(\"body\");\n  const svg = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : d3select(`[id=\"${id}\"]`);\n  const markers2 = [\"point\", \"circle\", \"cross\"];\n  markers_default(svg, markers2, diagObj.type, id);\n  const bl = db2.getBlocks();\n  const blArr = db2.getBlocksFlat();\n  const edges = db2.getEdges();\n  const nodes = svg.insert(\"g\").attr(\"class\", \"block\");\n  await calculateBlockSizes(nodes, bl, db2);\n  const bounds = layout(db2);\n  await insertBlocks(nodes, bl, db2);\n  await insertEdges(nodes, edges, blArr, db2, id);\n  if (bounds) {\n    const bounds2 = bounds;\n    const magicFactor = Math.max(1, Math.round(0.125 * (bounds2.width / bounds2.height)));\n    const height = bounds2.height + magicFactor + 10;\n    const width = bounds2.width + 10;\n    const { useMaxWidth } = conf;\n    configureSvgSize(svg, height, width, !!useMaxWidth);\n    log.debug(\"Here Bounds\", bounds, bounds2);\n    svg.attr(\n      \"viewBox\",\n      `${bounds2.x - 5} ${bounds2.y - 5} ${bounds2.width + 10} ${bounds2.height + 10}`\n    );\n  }\n}, \"draw\");\nvar blockRenderer_default = {\n  draw,\n  getClasses: getClasses2\n};\n\n// src/diagrams/block/blockDiagram.ts\nvar diagram = {\n  parser: block_default,\n  db: blockDB_default,\n  renderer: blockRenderer_default,\n  styles: styles_default\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,IAAI,SAAS,WAAW;AACtB,MAAI,IAAoB,OAAO,SAAS,GAAG,GAAG,IAAI,GAAG;AACnD,SAAK,KAAK,MAAM,CAAA,GAAI,IAAI,EAAE,QAAQ,KAAK,GAAG,EAAE,CAAC,CAAC,IAAI;AAAG;AACrD,WAAO;EACT,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE;AAC1V,MAAI,UAAU;IACZ,OAAuB,OAAO,SAAS,QAAQ;IAC/C,GAAG,OAAO;IACV,IAAI,CAAA;IACJ,UAAU,EAAE,SAAS,GAAG,cAAc,GAAG,aAAa,GAAG,MAAM,GAAG,aAAa,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,GAAG,qBAAqB,IAAI,YAAY,IAAI,QAAQ,IAAI,aAAa,IAAI,QAAQ,IAAI,QAAQ,IAAI,cAAc,IAAI,cAAc,IAAI,OAAO,IAAI,iBAAiB,IAAI,oBAAoB,IAAI,eAAe,IAAI,kBAAkB,IAAI,qBAAqB,IAAI,qBAAqB,IAAI,kBAAkB,IAAI,QAAQ,IAAI,QAAQ,IAAI,WAAW,IAAI,YAAY,IAAI,OAAO,IAAI,SAAS,IAAI,WAAW,IAAI,mBAAmB,IAAI,WAAW,IAAI,OAAO,IAAI,eAAe,IAAI,aAAa,IAAI,qBAAqB,IAAI,mBAAmB,IAAI,YAAY,IAAI,eAAe,IAAI,sBAAsB,IAAI,WAAW,IAAI,SAAS,IAAI,mBAAmB,IAAI,cAAc,IAAI,SAAS,IAAI,oBAAoB,IAAI,yBAAyB,IAAI,WAAW,GAAG,QAAQ,EAAC;IAC71B,YAAY,EAAE,GAAG,SAAS,GAAG,aAAa,GAAG,MAAM,GAAG,SAAS,GAAG,OAAO,IAAI,qBAAqB,IAAI,QAAQ,IAAI,cAAc,IAAI,cAAc,IAAI,OAAO,IAAI,eAAe,IAAI,QAAQ,IAAI,WAAW,IAAI,YAAY,IAAI,OAAO,IAAI,SAAS,IAAI,WAAW,IAAI,OAAO,IAAI,eAAe,IAAI,aAAa,IAAI,qBAAqB,IAAI,mBAAmB,IAAI,YAAY,IAAI,eAAe,IAAI,sBAAsB,IAAI,WAAW,IAAI,SAAS,IAAI,mBAAmB,IAAI,cAAc,IAAI,SAAS,IAAI,oBAAoB,IAAI,wBAAuB;IACniB,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/V,eAA+B,OAAO,SAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAS,IAAI,IAAI;AACtG,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAO;QACb,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,uBAAuB;AAC5C;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,0BAA0B;AAC/C;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,wBAAwB;AAC7C;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,qBAAqB,GAAG,KAAK,CAAC,CAAC;AACpD,aAAG,aAAa,GAAG,KAAK,CAAC,CAAC;AAC1B;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,UAAU;AAC/B;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,WAAW;AAChC;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,WAAW;AAChC;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,YAAY;AACjC;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,qBAAqB,GAAG,EAAE,CAAC;AAChD,iBAAO,GAAG,EAAE,EAAE,WAAW,WAAW,KAAK,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AACtE;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,wBAAwB,GAAG,KAAK,CAAC,CAAC;AACvD,eAAK,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,OAAO,GAAG,EAAE,CAAC;AACnC;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,gBAAgB,GAAG,EAAE,GAAG,MAAM;AACnD,eAAK,IAAI,EAAE,aAAa,GAAG,EAAE,GAAG,OAAO,GAAE;AACzC;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,sBAAsB,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;AACzE,eAAK,IAAI,EAAE,aAAa,GAAG,EAAE,GAAG,OAAO,GAAG,KAAK,CAAC,EAAC;AACjD;QACF,KAAK;AACH,gBAAM,MAAM,SAAS,GAAG,EAAE,CAAC;AAC3B,gBAAM,UAAU,GAAG,WAAU;AAC7B,eAAK,IAAI,EAAE,IAAI,SAAS,MAAM,SAAS,OAAO,IAAI,OAAO,KAAK,UAAU,CAAA,EAAE;AAC1E;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,oCAAoC,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,GAAG,cAAc,GAAG,KAAK,CAAC,EAAE,WAAW;AAC7H,gBAAM,WAAW,GAAG,kBAAkB,GAAG,KAAK,CAAC,EAAE,WAAW;AAC5D,eAAK,IAAI;YACP,EAAE,IAAI,GAAG,KAAK,CAAC,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,MAAM,GAAG,KAAK,CAAC,EAAE,MAAM,YAAY,GAAG,KAAK,CAAC,EAAE,WAAU;YACtG,EAAE,IAAI,GAAG,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,EAAE,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,EAAE,IAAI,KAAK,GAAG,EAAE,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,MAAM,QAAQ,YAAY,GAAG,EAAE,EAAE,YAAY,cAAc,UAAU,gBAAgB,aAAY;YACvM,EAAE,IAAI,GAAG,EAAE,EAAE,IAAI,OAAO,GAAG,EAAE,EAAE,OAAO,MAAM,GAAG,aAAa,GAAG,EAAE,EAAE,OAAO,GAAG,YAAY,GAAG,EAAE,EAAE,WAAU;UACtH;AACU;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,0CAA0C,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;AACjF,eAAK,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,MAAM,GAAG,aAAa,GAAG,KAAK,CAAC,EAAE,OAAO,GAAG,YAAY,GAAG,KAAK,CAAC,EAAE,YAAY,gBAAgB,SAAS,GAAG,EAAE,GAAG,EAAE,EAAC;AACzK;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,+BAA+B,GAAG,EAAE,CAAC;AAC1D,eAAK,IAAI,EAAE,IAAI,GAAG,EAAE,EAAE,IAAI,OAAO,GAAG,EAAE,EAAE,OAAO,MAAM,GAAG,aAAa,GAAG,EAAE,EAAE,OAAO,GAAG,YAAY,GAAG,EAAE,EAAE,YAAY,gBAAgB,EAAC;AACtI;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,UAAU,OAAO,OAAO,IAAI;AACjD,aAAG,UAAS,EAAG,MAAM,aAAa,GAAG,EAAE,CAAC;AACxC,eAAK,IAAI,EAAE,MAAM,kBAAkB,SAAS,GAAG,EAAE,MAAM,SAAS,KAAK,SAAS,GAAG,EAAE,CAAC,EAAC;AACrF;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,+BAA+B,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;AAC9D,aAAG,WAAU;AACzB,eAAK,IAAI,EAAE,GAAG,GAAG,KAAK,CAAC,GAAG,MAAM,aAAa,UAAU,GAAG,KAAK,CAAC,EAAC;AACjE;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,2BAA2B,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;AAC9E,gBAAM,KAAK,GAAG,WAAU;AACxB,eAAK,IAAI,EAAE,IAAI,MAAM,aAAa,OAAO,IAAI,UAAU,GAAG,KAAK,CAAC,EAAC;AACjE;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,oCAAoC,GAAG,EAAE,CAAC;AAC/D,eAAK,IAAI,EAAE,IAAI,GAAG,EAAE,EAAC;AACrB;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,oDAAoD,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;AAC3F,eAAK,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,GAAG,EAAE,EAAE,OAAO,SAAS,GAAG,EAAE,EAAE,SAAS,YAAY,GAAG,EAAE,EAAE,WAAU;AACtG;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,mBAAmB,GAAG,EAAE,CAAC;AAC9C,eAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AAChB;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,mBAAmB,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;AAC1D,eAAK,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,OAAO,GAAG,EAAE,CAAC;AACnC;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,2BAA2B,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;AAC9E,eAAK,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC,IAAI,GAAG,EAAE,GAAG,OAAO,GAAG,KAAK,CAAC,EAAC;AAC1D;QACF,KAAK;AACH,aAAG,UAAS,EAAG,MAAM,uCAAuC,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,QAAQ,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;AAC9G,eAAK,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC,IAAI,GAAG,EAAE,GAAG,OAAO,GAAG,KAAK,CAAC,GAAG,YAAY,GAAG,KAAK,CAAC,EAAC;AAClF;QACF,KAAK;QACL,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,YAAY,IAAI,GAAG,KAAK,CAAC,EAAE,KAAI,GAAI,KAAK,GAAG,EAAE,EAAE,KAAA,EAAM;AACtE;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,cAAc,IAAI,GAAG,KAAK,CAAC,EAAE,KAAI,GAAI,YAAY,GAAG,EAAE,EAAE,KAAA,EAAM;AAC/E;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,eAAe,IAAI,GAAG,KAAK,CAAC,EAAE,KAAI,GAAI,WAAW,GAAG,EAAE,EAAE,KAAA,EAAM;AAC/E;MACV;IACI,GAAG,WAAW;IACd,OAAO,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,EAAC,GAAI,EAAE,GAAG,CAAC,CAAC,EAAC,GAAI,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAG,GAAI,EAAE,GAAG,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAG,CAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI,IAAI,KAAK,IAAI,IAAG,CAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,CAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAG,GAAI,EAAE,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAG,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAC,CAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,IAAG,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAG,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAA,GAAK,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAA,GAAK,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,IAAI,IAAI,IAAG,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAA,GAAK,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,CAAE;IACjjD,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAC;IACnE,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,UAAI,KAAK,aAAa;AACpB,aAAK,MAAM,GAAG;MAChB,OAAO;AACL,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;MACR;IACF,GAAG,YAAY;IACf,OAAuB,OAAO,SAAS,MAAM,OAAO;AAC/C,UAAC,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAA,GAAI,SAAS,CAAC,IAAI,GAAG,SAAS,CAAA,GAAI,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAmB,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAI,SAAS,OAAO,OAAO,KAAK,KAAK;AACrC,UAAI,cAAc,EAAE,IAAI,CAAA,EAAE;AAC1B,eAAS,KAAK,KAAK,IAAI;AACrB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AACpD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;QAC/B;MACF;AACA,aAAO,SAAS,OAAO,YAAY,EAAE;AACrC,kBAAY,GAAG,QAAQ;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAO,OAAO,UAAU,aAAa;AACvC,eAAO,SAAS,CAAA;MAClB;AACA,UAAI,QAAQ,OAAO;AACnB,aAAO,KAAK,KAAK;AACjB,UAAI,SAAS,OAAO,WAAW,OAAO,QAAQ;AAC9C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACnD,aAAK,aAAa,YAAY,GAAG;MACnC,OAAO;AACL,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;MAChD;AACA,eAAS,SAAS,GAAG;AACnB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;MAClC;AACA,aAAO,UAAU,UAAU;AAC3B,eAAS,MAAM;AACb,YAAI;AACJ,gBAAQ,OAAO,IAAG,KAAM,OAAO,IAAG,KAAM;AACxC,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,iBAAiB,OAAO;AAC1B,qBAAS;AACT,oBAAQ,OAAO,IAAG;UACpB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;QAClC;AACA,eAAO;MACT;AACA,aAAO,KAAK,KAAK;AACd,UAAC,QAAwB,OAAO,QAAW,GAAG,QAAQ,CAAA,GAAI,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACX,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC9B,mBAAS,KAAK,eAAe,KAAK;QACpC,OAAO;AACL,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACnD,qBAAS,IAAG;UACd;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;QAC9C;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AACjE,cAAI,SAAS;AACb,qBAAW,CAAA;AACX,eAAK,KAAK,MAAM,KAAK,GAAG;AACtB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AACpC,uBAAS,KAAK,MAAM,KAAK,WAAW,CAAC,IAAI,GAAG;YAC9C;UACF;AACA,cAAI,OAAO,cAAc;AACvB,qBAAS,0BAA0B,WAAW,KAAK,QAAQ,OAAO,aAAY,IAAK,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAa,KAAK,WAAW,MAAM,KAAK,UAAU;UAC9K,OAAO;AACL,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAO,KAAK,WAAW,MAAM,KAAK,UAAU;UACrJ;AACA,eAAK,WAAW,QAAQ;YACtB,MAAM,OAAO;YACb,OAAO,KAAK,WAAW,MAAM,KAAK;YAClC,MAAM,OAAO;YACb,KAAK;YACL;UACZ,CAAW;QACH;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACnD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;QACpG;AACA,gBAAQ,OAAO,CAAC,GAAC;UACf,KAAK;AACH,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAK,OAAO,MAAM;AACzB,mBAAO,KAAK,OAAO,MAAM;AACzB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACY;AACnB,uBAAS,OAAO;AAChB,uBAAS,OAAO;AAChB,yBAAW,OAAO;AAClB,sBAAQ,OAAO;YAIjB;AAIA;UACF,KAAK;AACH,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;cACT,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;YACrD;AACY,gBAAI,QAAQ;AACV,oBAAM,GAAG,QAAQ;gBACf,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;cACjD;YACY;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;cAClC;cACA;cACA;cACA,YAAY;cACZ,OAAO,CAAC;cACR;cACA;YACd,EAAc,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC5B,qBAAO;YACT;AACA,gBAAI,KAAK;AACP,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;YACnC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;UACF,KAAK;AACH,mBAAO;QACnB;MACM;AACA,aAAO;IACT,GAAG,OAAO;EACd;AACE,MAAI,QAAwB,WAAW;AACrC,QAAI,SAAS;MACX,KAAK;MACL,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,YAAI,KAAK,GAAG,QAAQ;AAClB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;QACrC,OAAO;AACL,gBAAM,IAAI,MAAM,GAAG;QACrB;MACF,GAAG,YAAY;;MAEf,UAA0B,OAAO,SAAS,OAAO,IAAI;AACnD,aAAK,KAAK,MAAM,KAAK,MAAM,CAAA;AAC3B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;UACZ,YAAY;UACZ,cAAc;UACd,WAAW;UACX,aAAa;QACvB;AACQ,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,GAAG,CAAC;QAC3B;AACA,aAAK,SAAS;AACd,eAAO;MACT,GAAG,UAAU;;MAEb,OAAuB,OAAO,WAAW;AACvC,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACT,eAAK;AACL,eAAK,OAAO;QACd,OAAO;AACL,eAAK,OAAO;QACd;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,MAAM,CAAC;QACrB;AACA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;MACT,GAAG,OAAO;;MAEV,OAAuB,OAAO,SAAS,IAAI;AACzC,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AACpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAC5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAC7D,YAAI,MAAM,SAAS,GAAG;AACpB,eAAK,YAAY,MAAM,SAAS;QAClC;AACA,YAAI,IAAI,KAAK,OAAO;AACpB,aAAK,SAAS;UACZ,YAAY,KAAK,OAAO;UACxB,WAAW,KAAK,WAAW;UAC3B,cAAc,KAAK,OAAO;UAC1B,aAAa,SAAS,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAAK,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS,KAAK,OAAO,eAAe;QAClM;AACQ,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;QACrD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;MACT,GAAG,OAAO;;MAEV,MAAsB,OAAO,WAAW;AACtC,aAAK,QAAQ;AACb,eAAO;MACT,GAAG,MAAM;;MAET,QAAwB,OAAO,WAAW;AACxC,YAAI,KAAK,QAAQ,iBAAiB;AAChC,eAAK,aAAa;QACpB,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAA,GAAgB;YAChO,MAAM;YACN,OAAO;YACP,MAAM,KAAK;UACvB,CAAW;QACH;AACA,eAAO;MACT,GAAG,QAAQ;;MAEX,MAAsB,OAAO,SAAS,GAAG;AACvC,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;MAChC,GAAG,MAAM;;MAET,WAA2B,OAAO,WAAW;AAC3C,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAQ,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;MAC7E,GAAG,WAAW;;MAEd,eAA+B,OAAO,WAAW;AAC/C,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AACpB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAK,KAAK,MAAM;QAChD;AACA,gBAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;MACjF,GAAG,eAAe;;MAElB,cAA8B,OAAO,WAAW;AAC9C,YAAI,MAAM,KAAK,UAAS;AACxB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAa,IAAK,OAAO,IAAI;MACjD,GAAG,cAAc;;MAEjB,YAA4B,OAAO,SAAS,OAAO,cAAc;AAC/D,YAAI,OAAO,OAAO;AAClB,YAAI,KAAK,QAAQ,iBAAiB;AAChC,mBAAS;YACP,UAAU,KAAK;YACf,QAAQ;cACN,YAAY,KAAK,OAAO;cACxB,WAAW,KAAK;cAChB,cAAc,KAAK,OAAO;cAC1B,aAAa,KAAK,OAAO;YACvC;YACY,QAAQ,KAAK;YACb,OAAO,KAAK;YACZ,SAAS,KAAK;YACd,SAAS,KAAK;YACd,QAAQ,KAAK;YACb,QAAQ,KAAK;YACb,OAAO,KAAK;YACZ,QAAQ,KAAK;YACb,IAAI,KAAK;YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;YAC3C,MAAM,KAAK;UACvB;AACU,cAAI,KAAK,QAAQ,QAAQ;AACvB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;UACjD;QACF;AACA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACT,eAAK,YAAY,MAAM;QACzB;AACA,aAAK,SAAS;UACZ,YAAY,KAAK,OAAO;UACxB,WAAW,KAAK,WAAW;UAC3B,cAAc,KAAK,OAAO;UAC1B,aAAa,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAAS,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;QACvJ;AACQ,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;QAC9D;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC5B,eAAK,OAAO;QACd;AACA,YAAI,OAAO;AACT,iBAAO;QACT,WAAW,KAAK,YAAY;AAC1B,mBAAS,KAAK,QAAQ;AACpB,iBAAK,CAAC,IAAI,OAAO,CAAC;UACpB;AACA,iBAAO;QACT;AACA,eAAO;MACT,GAAG,YAAY;;MAEf,MAAsB,OAAO,WAAW;AACtC,YAAI,KAAK,MAAM;AACb,iBAAO,KAAK;QACd;AACA,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,OAAO;QACd;AACA,YAAI,OAAO,OAAO,WAAW;AAC7B,YAAI,CAAC,KAAK,OAAO;AACf,eAAK,SAAS;AACd,eAAK,QAAQ;QACf;AACA,YAAI,QAAQ,KAAK,cAAa;AAC9B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAClE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAChC,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACnB,uBAAO;cACT,WAAW,KAAK,YAAY;AAC1B,wBAAQ;AACR;cACF,OAAO;AACL,uBAAO;cACT;YACF,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC7B;YACF;UACF;QACF;AACA,YAAI,OAAO;AACT,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACnB,mBAAO;UACT;AACA,iBAAO;QACT;AACA,YAAI,KAAK,WAAW,IAAI;AACtB,iBAAO,KAAK;QACd,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAA,GAAgB;YACtH,MAAM;YACN,OAAO;YACP,MAAM,KAAK;UACvB,CAAW;QACH;MACF,GAAG,MAAM;;MAET,KAAqB,OAAO,SAAS,MAAM;AACzC,YAAI,IAAI,KAAK,KAAI;AACjB,YAAI,GAAG;AACL,iBAAO;QACT,OAAO;AACL,iBAAO,KAAK,IAAG;QACjB;MACF,GAAG,KAAK;;MAER,OAAuB,OAAO,SAAS,MAAM,WAAW;AACtD,aAAK,eAAe,KAAK,SAAS;MACpC,GAAG,OAAO;;MAEV,UAA0B,OAAO,SAAS,WAAW;AACnD,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACT,iBAAO,KAAK,eAAe,IAAG;QAChC,OAAO;AACL,iBAAO,KAAK,eAAe,CAAC;QAC9B;MACF,GAAG,UAAU;;MAEb,eAA+B,OAAO,SAAS,gBAAgB;AAC7D,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACrF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;QAC9E,OAAO;AACL,iBAAO,KAAK,WAAW,SAAS,EAAE;QACpC;MACF,GAAG,eAAe;;MAElB,UAA0B,OAAO,SAAS,SAAS,GAAG;AACpD,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACV,iBAAO,KAAK,eAAe,CAAC;QAC9B,OAAO;AACL,iBAAO;QACT;MACF,GAAG,UAAU;;MAEb,WAA2B,OAAO,SAAS,UAAU,WAAW;AAC9D,aAAK,MAAM,SAAS;MACtB,GAAG,WAAW;;MAEd,gBAAgC,OAAO,SAAS,iBAAiB;AAC/D,eAAO,KAAK,eAAe;MAC7B,GAAG,gBAAgB;MACnB,SAAS,CAAA;MACT,eAA+B,OAAO,SAAS,UAAU,IAAI,KAAK,2BAA2B,UAAU;AAErG,gBAAQ,2BAAyB;UAC/B,KAAK;AACH,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,mBAAmB;AACxC,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,gBAAgB;AACrC,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,mBAAmB;AACxC,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,KAAK,IAAI,MAAM;AACpC;UACF,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,KAAK,IAAI,MAAM;AACpC;UACF,KAAK;AACH,mBAAO;UAET,KAAK;AACH,gBAAI,SAAS;AACb,mBAAO;UAET,KAAK;AACH,gBAAI,SAAS,IAAI,OAAO,QAAQ,cAAc,EAAE;AAChD,eAAG,UAAS,EAAG,MAAM,iBAAiB,IAAI,MAAM;AAChD,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,WAAW;AAC1B;UACF,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb;UACF,KAAK;AACH,iBAAK,UAAU,QAAQ;AACvB;UACF,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,qBAAqB,IAAI,MAAM;AACpD,iBAAK,SAAQ;AACb;UACF,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,iBAAiB,IAAI,MAAM;AAChD,mBAAO;UAET,KAAK;AACH,gBAAI,SAAS,IAAI,OAAO,QAAQ,WAAW,EAAE;AAC7C,eAAG,UAAS,EAAG,MAAM,mBAAmB,IAAI,MAAM;AAClD,mBAAO;UAET,KAAK;AACH,gBAAI,SAAS;AACb,eAAG,UAAS,EAAG,MAAM,iBAAiB,IAAI,MAAM;AAChD,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,UAAU;AACzB,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,iBAAK,UAAU,YAAY;AAC3B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,iBAAK,UAAU,YAAY;AAC3B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,OAAO;AACtB,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,iBAAK,UAAU,aAAa;AAC5B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,aAAa;AAC5B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,iBAAK,UAAU,kBAAkB;AACjC,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,WAAW;AAC1B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,WAAW;AAC1B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,qBAAqB;AACpC;UACF,KAAK;AACH,iBAAK,SAAQ;AACb;UACF,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,QAAQ;AAC7B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,QAAQ;AAC7B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,QAAQ;AAC7B,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,UAAU;AAC/B,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,UAAU;AAC/B,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,UAAU;AAC/B,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,UAAU;AAC/B,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,UAAU;AAC/B,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,SAAS;AAC9B,iBAAK,UAAU,MAAM;AACrB,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,aAAa;AAC5B,eAAG,UAAS,EAAG,MAAM,eAAe;AACpC,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,gBAAgB,IAAI,MAAM;AAC/C,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,YAAY,IAAI,MAAM;AAC3C,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,WAAW;AAC1B;UACF,KAAK;AACH,iBAAK,UAAU,WAAW;AAC1B;UACF,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb;UACF,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,sBAAsB;AAC3C,iBAAK,UAAU,QAAQ;AACvB;UACF,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,0BAA0B;AAC/C,iBAAK,UAAU,QAAQ;AACvB;UACF,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,oBAAoB,IAAI,MAAM;AACnD,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,aAAa;AAClC,iBAAK,SAAQ;AACb;UACF,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,YAAY;AACjC,iBAAK,UAAU,WAAW;AAC1B;UACF,KAAK;AACH,gBAAI,SAAS,IAAI,OAAO,QAAQ,SAAS,EAAE;AAC3C,eAAG,UAAS,EAAG,MAAM,qBAAqB,IAAI,MAAM;AACpD,mBAAO;UAET,KAAK;AACH,gBAAI,SAAS,IAAI,OAAO,QAAQ,SAAS,EAAE;AAC3C,eAAG,UAAS,EAAG,MAAM,eAAe,IAAI,MAAM;AAC9C,mBAAO;UAET,KAAK;AACH,gBAAI,SAAS,IAAI,OAAO,QAAQ,SAAS,EAAE;AAC3C,eAAG,UAAS,EAAG,MAAM,YAAY,IAAI,MAAM;AAC3C,mBAAO;UAET,KAAK;AACH,gBAAI,SAAS,IAAI,OAAO,QAAQ,SAAS,EAAE;AAC3C,eAAG,UAAS,EAAG,MAAM,YAAY,IAAI,MAAM;AAC3C,mBAAO;UAET,KAAK;AACH,gBAAI,SAAS,IAAI,OAAO,QAAQ,SAAS,EAAE;AAC3C,eAAG,UAAS,EAAG,MAAM,aAAa,IAAI,MAAM;AAC5C,mBAAO;UAET,KAAK;AACH,gBAAI,SAAS,IAAI,OAAO,QAAQ,SAAS,EAAE;AAC3C,eAAG,UAAS,EAAG,MAAM,eAAe,IAAI,MAAM;AAC9C,mBAAO;UAET,KAAK;AACH,gBAAI,SAAS;AACb,eAAG,UAAS,EAAG,MAAM,wBAAwB,IAAI,MAAM;AACvD,iBAAK,SAAQ;AACb,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,aAAa,MAAM,IAAI,SAAS,GAAG;AACxD,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,aAAa,IAAI,MAAM;AAC5C,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,aAAa,IAAI,MAAM;AAC5C,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,aAAa,IAAI,MAAM;AAC5C,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,mBAAmB,IAAI,MAAM;AAClD,iBAAK,UAAU,QAAQ;AACvB,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,mBAAmB,IAAI,MAAM;AAClD,iBAAK,UAAU,QAAQ;AACvB,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,mBAAmB,IAAI,MAAM;AAClD,iBAAK,UAAU,QAAQ;AACvB,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,WAAW;AAC1B;UACF,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,sBAAsB;AAC3C,iBAAK,UAAU,QAAQ;AACvB,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,aAAa,MAAM,IAAI,SAAS,GAAG;AACxD,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,aAAa,IAAI,MAAM;AAC5C,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,eAAG,UAAS,EAAG,MAAM,aAAa,IAAI,MAAM;AAC5C,mBAAO;UAET,KAAK;AACH,eAAG,UAAS,EAAG,MAAM,cAAc,IAAI,MAAM;AAC7C,gBAAI,SAAS,IAAI,OAAO,MAAM,CAAC;AAC/B,mBAAO;QAEnB;MACM,GAAG,WAAW;MACd,OAAO,CAAC,qBAAqB,iBAAiB,iBAAiB,eAAe,cAAc,cAAc,kCAAkC,yBAAyB,wBAAwB,eAAe,eAAe,eAAe,YAAY,YAAY,cAAc,oBAAoB,gBAAgB,kBAAkB,oBAAoB,sBAAsB,oBAAoB,mBAAmB,eAAe,eAAe,iBAAiB,2BAA2B,eAAe,iBAAiB,2BAA2B,eAAe,wBAAwB,wBAAwB,wBAAwB,wBAAwB,yBAAyB,aAAa,eAAe,iBAAiB,eAAe,eAAe,eAAe,aAAa,WAAW,YAAY,YAAY,aAAa,aAAa,WAAW,aAAa,aAAa,aAAa,aAAa,aAAa,YAAY,aAAa,YAAY,YAAY,aAAa,WAAW,eAAe,aAAa,aAAa,WAAW,UAAU,aAAa,WAAW,aAAa,aAAa,aAAa,eAAe,aAAa,aAAa,aAAa,WAAW,YAAY,kCAAkC,UAAU,eAAe,eAAe,eAAe,eAAe,YAAY,YAAY,cAAc,YAAY,iBAAiB,sBAAsB,qBAAqB,kBAAkB,kBAAkB,mBAAmB,qBAAqB,cAAc,8BAA8B,8BAA8B,iCAAiC,sBAAsB,uBAAuB,uBAAuB,wBAAwB,eAAe,YAAY,8BAA8B,8BAA8B,iCAAiC,WAAW;MACnxD,YAAY,EAAE,oBAAoB,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,eAAe,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,cAAc,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAA,GAAS,YAAY,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,eAAe,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,SAAS,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,UAAU,EAAE,SAAS,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,aAAa,MAAA,GAAS,aAAa,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAA,GAAS,eAAe,EAAE,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,QAAQ,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,aAAa,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,SAAS,EAAE,SAAS,CAAA,GAAI,aAAa,MAAK,GAAI,UAAU,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,uBAAuB,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,aAAa,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,aAAa,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAA,GAAS,WAAW,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,aAAa,KAAI,EAAE;IACzuC;AACI,WAAO;EACT,EAAC;AACD,UAAQ,QAAQ;AAChB,WAAS,SAAS;AAChB,SAAK,KAAK,CAAA;EACZ;AACA,SAAO,QAAQ,QAAQ;AACvB,SAAO,YAAY;AACnB,UAAQ,SAAS;AACjB,SAAO,IAAI,OAAM;AACnB,EAAC;AACD,OAAO,SAAS;AAChB,IAAI,gBAAgB;AAIpB,IAAI,gBAAgC,oBAAI,IAAG;AAC3C,IAAI,WAAW,CAAA;AACf,IAAI,YAA4B,oBAAI,IAAG;AACvC,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,UAAU;AACd,IAAI,iBAAiB;AACrB,IAAI,SAAS,WAAU;AACvB,IAAI,UAA0B,oBAAI,IAAG;AACrC,IAAI,gBAAgC,OAAO,CAAC,QAAQ,eAAe,aAAa,KAAK,MAAM,GAAG,cAAc;AAC5G,IAAI,gBAAgC,OAAO,SAAS,IAAI,kBAAkB,IAAI;AAC5E,MAAI,aAAa,QAAQ,IAAI,EAAE;AAC/B,MAAI,CAAC,YAAY;AACf,iBAAa,EAAE,IAAI,QAAQ,CAAA,GAAI,YAAY,CAAA,EAAE;AAC7C,YAAQ,IAAI,IAAI,UAAU;EAC5B;AACA,MAAI,oBAAoB,UAAU,oBAAoB,MAAM;AAC1D,oBAAgB,MAAM,cAAc,EAAE,QAAQ,CAAC,WAAW;AACxD,YAAM,cAAc,OAAO,QAAQ,YAAY,IAAI,EAAE,KAAI;AACzD,UAAI,OAAO,aAAa,EAAE,KAAK,MAAM,GAAG;AACtC,cAAM,YAAY,YAAY,QAAQ,cAAc,OAAO;AAC3D,cAAM,YAAY,UAAU,QAAQ,eAAe,YAAY;AAC/D,mBAAW,WAAW,KAAK,SAAS;MACtC;AACA,iBAAW,OAAO,KAAK,WAAW;IACpC,CAAC;EACH;AACF,GAAG,eAAe;AAClB,IAAI,gBAAgC,OAAO,SAAS,IAAI,SAAS,IAAI;AACnE,QAAM,aAAa,cAAc,IAAI,EAAE;AACvC,MAAI,WAAW,UAAU,WAAW,MAAM;AACxC,eAAW,SAAS,OAAO,MAAM,cAAc;EACjD;AACF,GAAG,eAAe;AAClB,IAAI,cAA8B,OAAO,SAAS,SAAS,cAAc;AACvE,UAAQ,MAAM,GAAG,EAAE,QAAQ,SAAS,IAAI;AACtC,QAAI,aAAa,cAAc,IAAI,EAAE;AACrC,QAAI,eAAe,QAAQ;AACzB,YAAM,YAAY,GAAG,KAAI;AACzB,mBAAa,EAAE,IAAI,WAAW,MAAM,MAAM,UAAU,CAAA,EAAE;AACtD,oBAAc,IAAI,WAAW,UAAU;IACzC;AACA,QAAI,CAAC,WAAW,SAAS;AACvB,iBAAW,UAAU,CAAA;IACvB;AACA,eAAW,QAAQ,KAAK,YAAY;EACtC,CAAC;AACH,GAAG,aAAa;AAChB,IAAI,wBAAwC,OAAO,CAAC,YAAY,WAAW;AACzE,QAAM,YAAY,WAAW,KAAI;AACjC,QAAM,WAAW,CAAA;AACjB,aAAW,SAAS,WAAW;AAC7B,QAAI,MAAM,OAAO;AACf,YAAM,QAAQ,cAAc,MAAM,KAAK;IACzC;AACA,QAAI,MAAM,SAAS,YAAY;AAC7B,oBAAc,MAAM,IAAI,MAAM,GAAG;AACjC;IACF;AACA,QAAI,MAAM,SAAS,cAAc;AAC/B,kBAAY,MAAM,KAAI,SAAA,OAAA,SAAA,MAAO,eAAc,EAAE;AAC7C;IACF;AACA,QAAI,MAAM,SAAS,eAAe;AAChC,UAAI,SAAA,OAAA,SAAA,MAAO,WAAW;AACpB,sBAAc,MAAM,IAAI,SAAA,OAAA,SAAA,MAAO,SAAS;MAC1C;AACA;IACF;AACA,QAAI,MAAM,SAAS,kBAAkB;AACnC,aAAO,UAAU,MAAM,WAAW;IACpC,WAAW,MAAM,SAAS,QAAQ;AAChC,YAAM,SAAS,UAAU,IAAI,MAAM,EAAE,KAAK,KAAK;AAC/C,gBAAU,IAAI,MAAM,IAAI,KAAK;AAC7B,YAAM,KAAK,QAAQ,MAAM,MAAM;AAC/B,eAAS,KAAK,KAAK;IACrB,OAAO;AACL,UAAI,CAAC,MAAM,OAAO;AAChB,YAAI,MAAM,SAAS,aAAa;AAC9B,gBAAM,QAAQ;QAChB,OAAO;AACL,gBAAM,QAAQ,MAAM;QACtB;MACF;AACA,YAAM,gBAAgB,cAAc,IAAI,MAAM,EAAE;AAChD,UAAI,kBAAkB,QAAQ;AAC5B,sBAAc,IAAI,MAAM,IAAI,KAAK;MACnC,OAAO;AACL,YAAI,MAAM,SAAS,MAAM;AACvB,wBAAc,OAAO,MAAM;QAC7B;AACA,YAAI,MAAM,UAAU,MAAM,IAAI;AAC5B,wBAAc,QAAQ,MAAM;QAC9B;MACF;AACA,UAAI,MAAM,UAAU;AAClB,8BAAsB,MAAM,UAAU,KAAK;MAC7C;AACA,UAAI,MAAM,SAAS,SAAS;AAC1B,cAAM,IAAI,MAAM,SAAS;AACzB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAM,WAAW,MAAM,KAAK;AAC5B,mBAAS,KAAK,SAAS,KAAK,MAAM;AAClC,wBAAc,IAAI,SAAS,IAAI,QAAQ;AACvC,mBAAS,KAAK,QAAQ;QACxB;MACF,WAAW,kBAAkB,QAAQ;AACnC,iBAAS,KAAK,KAAK;MACrB;IACF;EACF;AACA,SAAO,WAAW;AACpB,GAAG,uBAAuB;AAC1B,IAAI,SAAS,CAAA;AACb,IAAI,YAAY,EAAE,IAAI,QAAQ,MAAM,aAAa,UAAU,CAAA,GAAI,SAAS,GAAE;AAC1E,IAAI,SAAyB,OAAO,MAAM;AACxC,MAAI,MAAM,cAAc;AACxB,UAAK;AACL,cAAY,EAAE,IAAI,QAAQ,MAAM,aAAa,UAAU,CAAA,GAAI,SAAS,GAAE;AACtE,kBAAgC,oBAAI,IAAI,CAAC,CAAC,QAAQ,SAAS,CAAC,CAAC;AAC7D,WAAS,CAAA;AACT,YAA0B,oBAAI,IAAG;AACjC,aAAW,CAAA;AACX,cAA4B,oBAAI,IAAG;AACrC,GAAG,OAAO;AACV,SAAS,aAAa,SAAS;AAC7B,MAAI,MAAM,gBAAgB,OAAO;AACjC,UAAQ,SAAO;IACb,KAAK;AACH,aAAO;IACT,KAAK;AACH,UAAI,MAAM,iBAAiB;AAC3B,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;AACE,aAAO;EACb;AACA;AACA,OAAO,cAAc,cAAc;AACnC,SAAS,iBAAiB,SAAS;AACjC,MAAI,MAAM,gBAAgB,OAAO;AACjC,UAAQ,SAAO;IACb,KAAK;AACH,aAAO;IACT;AACE,aAAO;EACb;AACA;AACA,OAAO,kBAAkB,kBAAkB;AAC3C,SAAS,kBAAkB,SAAS;AAClC,UAAQ,QAAQ,KAAI,GAAE;IACpB,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;AACE,aAAO;EACb;AACA;AACA,OAAO,mBAAmB,mBAAmB;AAC7C,IAAI,MAAM;AACV,IAAI,aAA6B,OAAO,MAAM;AAC5C;AACA,SAAO,QAAQ,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,OAAO,GAAG,EAAE,IAAI,MAAM;AAClE,GAAG,YAAY;AACf,IAAI,eAA+B,OAAO,CAAC,UAAU;AACnD,YAAU,WAAW;AACrB,wBAAsB,OAAO,SAAS;AACtC,WAAS,UAAU;AACrB,GAAG,cAAc;AACjB,IAAI,aAA6B,OAAO,CAAC,YAAY;AACnD,QAAM,QAAQ,cAAc,IAAI,OAAO;AACvC,MAAI,CAAC,OAAO;AACV,WAAO;EACT;AACA,MAAI,MAAM,SAAS;AACjB,WAAO,MAAM;EACf;AACA,MAAI,CAAC,MAAM,UAAU;AACnB,WAAO;EACT;AACA,SAAO,MAAM,SAAS;AACxB,GAAG,YAAY;AACf,IAAI,gBAAgC,OAAO,MAAM;AAC/C,SAAO,CAAC,GAAG,cAAc,OAAA,CAAQ;AACnC,GAAG,eAAe;AAClB,IAAI,YAA4B,OAAO,MAAM;AAC3C,SAAO,UAAU,CAAA;AACnB,GAAG,WAAW;AACd,IAAI,WAA2B,OAAO,MAAM;AAC1C,SAAO;AACT,GAAG,UAAU;AACb,IAAI,WAA2B,OAAO,CAAC,OAAO;AAC5C,SAAO,cAAc,IAAI,EAAE;AAC7B,GAAG,UAAU;AACb,IAAI,WAA2B,OAAO,CAAC,UAAU;AAC/C,gBAAc,IAAI,MAAM,IAAI,KAAK;AACnC,GAAG,UAAU;AACb,IAAI,YAA4B,OAAO,MAAM,KAAK,WAAW;AAC7D,IAAI,aAA6B,OAAO,WAAW;AACjD,SAAO;AACT,GAAG,YAAY;AACf,IAAI,KAAK;EACP,WAA2B,OAAO,MAAM,UAAS,EAAG,OAAO,WAAW;EACtE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAO;EACP;AACF;AACA,IAAI,kBAAkB;AAItB,IAAI,OAAuB,OAAO,CAAC,OAAO,YAAY;AACpD,QAAM,WAAWA;AACjB,QAAM,IAAI,SAAS,OAAO,GAAG;AAC7B,QAAM,IAAI,SAAS,OAAO,GAAG;AAC7B,QAAM,IAAI,SAAS,OAAO,GAAG;AAC7B,SAAOC,OAAY,GAAG,GAAG,GAAG,OAAO;AACrC,GAAG,MAAM;AACT,IAAI,YAA4B,OAAO,CAAC,YAAY;mBACjC,QAAQ,UAAU;aACxB,QAAQ,iBAAiB,QAAQ,SAAS;;;YAG3C,QAAQ,UAAU;;;aAGjB,QAAQ,UAAU;;;;;;YAMnB,QAAQ,iBAAiB,QAAQ,SAAS;aACzC,QAAQ,iBAAiB,QAAQ,SAAS;;;;;;;;YAQ3C,QAAQ,OAAO;cACb,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;;;YAqBpB,QAAQ,cAAc;;;;cAIpB,QAAQ,SAAS;;;;;cAKjB,QAAQ,SAAS;;;;;wBAKP,QAAQ,mBAAmB;;;0BAGzB,QAAQ,mBAAmB;cACvC,QAAQ,mBAAmB;;;;;;;wBAOjB,KAAK,QAAQ,qBAAqB,GAAG,CAAC;;;;;eAK/C,KAAK,QAAQ,SAAS,GAAG,CAAC;YAC7B,KAAK,QAAQ,YAAY,GAAG,CAAC;cAC3B,KAAK,QAAQ,eAAe,GAAG,CAAC;;;;;;YAMlC,QAAQ,UAAU;;;;aAIjB,QAAQ,UAAU;;;aAGlB,QAAQ,UAAU;;;;;;;;mBAQZ,QAAQ,UAAU;;kBAEnB,QAAQ,aAAa;wBACf,QAAQ,OAAO;;;;;;;;;YAS3B,QAAQ,SAAS;;IAEzB,cAAa,CAAE;GAChB,WAAW;AACd,IAAI,iBAAiB;AAMrB,IAAI,gBAAgC,OAAO,CAAC,MAAM,aAAa,MAAM,OAAO;AAC1E,cAAY,QAAQ,CAAC,eAAe;AAClC,YAAQ,UAAU,EAAE,MAAM,MAAM,EAAE;EACpC,CAAC;AACH,GAAG,eAAe;AAClB,IAAI,YAA4B,OAAO,CAAC,MAAM,MAAM,OAAO;AACzD,MAAI,MAAM,uBAAuB,EAAE;AACnC,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,iBAAiB,EAAE,KAAK,SAAS,sBAAsB,IAAI,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,GAAG,EAAE,KAAK,gBAAgB,GAAG,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,oBAAoB;AACvR,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,eAAe,EAAE,KAAK,SAAS,sBAAsB,IAAI,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,oBAAoB;AACpR,GAAG,WAAW;AACd,IAAI,cAA8B,OAAO,CAAC,MAAM,MAAM,OAAO;AAC3D,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,mBAAmB,EAAE,KAAK,SAAS,wBAAwB,IAAI,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,GAAG,EAAE,KAAK,gBAAgB,GAAG,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,0BAA0B;AACjS,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,iBAAiB,EAAE,KAAK,SAAS,wBAAwB,IAAI,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,0BAA0B;AAC9R,GAAG,aAAa;AAChB,IAAI,cAA8B,OAAO,CAAC,MAAM,MAAM,OAAO;AAC3D,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,mBAAmB,EAAE,KAAK,SAAS,wBAAwB,IAAI,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,GAAG,EAAE,KAAK,gBAAgB,GAAG,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,0BAA0B;AACjS,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,iBAAiB,EAAE,KAAK,SAAS,wBAAwB,IAAI,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,0BAA0B;AAC9R,GAAG,aAAa;AAChB,IAAI,aAA6B,OAAO,CAAC,MAAM,MAAM,OAAO;AAC1D,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,kBAAkB,EAAE,KAAK,SAAS,uBAAuB,IAAI,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,GAAG,EAAE,KAAK,gBAAgB,GAAG,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,yBAAyB;AAC7R,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,gBAAgB,EAAE,KAAK,SAAS,uBAAuB,IAAI,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,2BAA2B;AAC9R,GAAG,YAAY;AACf,IAAI,WAA2B,OAAO,CAAC,MAAM,MAAM,OAAO;AACxD,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,gBAAgB,EAAE,KAAK,SAAS,qBAAqB,IAAI,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,GAAG,EAAE,KAAK,gBAAgB,GAAG,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,UAAU,OAAO,EAAE,KAAK,QAAQ,aAAa,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,KAAK,CAAC;AACpV,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,cAAc,EAAE,KAAK,SAAS,qBAAqB,IAAI,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,GAAG,EAAE,KAAK,gBAAgB,GAAG,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,UAAU,OAAO,EAAE,KAAK,QAAQ,aAAa,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,KAAK,CAAC;AACnV,GAAG,UAAU;AACb,IAAI,QAAwB,OAAO,CAAC,MAAM,MAAM,OAAO;AACrD,OAAK,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,WAAW,EAAE,KAAK,SAAS,YAAY,IAAI,EAAE,KAAK,WAAW,WAAW,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,gBAAgB,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,uBAAuB,EAAE,KAAK,SAAS,iBAAiB,EAAE,MAAM,gBAAgB,CAAC,EAAE,MAAM,oBAAoB,KAAK;AACtZ,OAAK,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,aAAa,EAAE,KAAK,SAAS,YAAY,IAAI,EAAE,KAAK,WAAW,WAAW,EAAE,KAAK,QAAQ,GAAG,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,gBAAgB,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,wBAAwB,EAAE,KAAK,SAAS,iBAAiB,EAAE,MAAM,gBAAgB,CAAC,EAAE,MAAM,oBAAoB,KAAK;AAC7Z,GAAG,OAAO;AACV,IAAI,SAAyB,OAAO,CAAC,MAAM,MAAM,OAAO;AACtD,OAAK,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,YAAY,EAAE,KAAK,SAAS,YAAY,IAAI,EAAE,KAAK,WAAW,WAAW,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,gBAAgB,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,GAAG,EAAE,KAAK,MAAM,GAAG,EAAE,KAAK,KAAK,GAAG,EAAE,KAAK,SAAS,iBAAiB,EAAE,MAAM,gBAAgB,CAAC,EAAE,MAAM,oBAAoB,KAAK;AACta,OAAK,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,cAAc,EAAE,KAAK,SAAS,YAAY,IAAI,EAAE,KAAK,WAAW,WAAW,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,gBAAgB,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,GAAG,EAAE,KAAK,MAAM,GAAG,EAAE,KAAK,KAAK,GAAG,EAAE,KAAK,SAAS,iBAAiB,EAAE,MAAM,gBAAgB,CAAC,EAAE,MAAM,oBAAoB,KAAK;AAC1a,GAAG,QAAQ;AACX,IAAI,QAAwB,OAAO,CAAC,MAAM,MAAM,OAAO;AACrD,OAAK,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,WAAW,EAAE,KAAK,SAAS,kBAAkB,IAAI,EAAE,KAAK,WAAW,WAAW,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,GAAG,EAAE,KAAK,eAAe,gBAAgB,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,2BAA2B,EAAE,KAAK,SAAS,iBAAiB,EAAE,MAAM,gBAAgB,CAAC,EAAE,MAAM,oBAAoB,KAAK;AACna,OAAK,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,aAAa,EAAE,KAAK,SAAS,kBAAkB,IAAI,EAAE,KAAK,WAAW,WAAW,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,GAAG,EAAE,KAAK,eAAe,gBAAgB,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,2BAA2B,EAAE,KAAK,SAAS,iBAAiB,EAAE,MAAM,gBAAgB,CAAC,EAAE,MAAM,oBAAoB,KAAK;AACva,GAAG,OAAO;AACV,IAAI,OAAuB,OAAO,CAAC,MAAM,MAAM,OAAO;AACpD,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,KAAK,MAAM,OAAO,UAAU,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,eAAe,aAAa,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,2BAA2B;AAChR,GAAG,MAAM;AACT,IAAI,UAAU;EACZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AACA,IAAI,kBAAkB;AAGtB,IAAI,YAAU,MAAA,KAAA,WAAU,MAAV,OAAA,SAAA,GAAc,UAAd,OAAA,SAAA,GAAqB,YAAW;AAC9C,SAAS,uBAAuB,SAAS,UAAU;AACjD,MAAI,YAAY,KAAK,CAAC,OAAO,UAAU,OAAO,GAAG;AAC/C,UAAM,IAAI,MAAM,mCAAmC;EACrD;AACA,MAAI,WAAW,KAAK,CAAC,OAAO,UAAU,QAAQ,GAAG;AAC/C,UAAM,IAAI,MAAM,6CAA6C,QAAQ;EACvE;AACA,MAAI,UAAU,GAAG;AACf,WAAO,EAAE,IAAI,UAAU,IAAI,EAAC;EAC9B;AACA,MAAI,YAAY,GAAG;AACjB,WAAO,EAAE,IAAI,GAAG,IAAI,SAAQ;EAC9B;AACA,QAAM,KAAK,WAAW;AACtB,QAAM,KAAK,KAAK,MAAM,WAAW,OAAO;AACxC,SAAO,EAAE,IAAI,GAAE;AACjB;AACA,OAAO,wBAAwB,wBAAwB;AACvD,IAAI,kBAAkC,OAAO,CAAC,UAAU;AACtD,MAAI,WAAW;AACf,MAAI,YAAY;AAChB,aAAW,SAAS,MAAM,UAAU;AAClC,UAAM,EAAE,OAAO,QAAQ,GAAG,EAAC,IAAK,MAAM,QAAQ,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,GAAG,GAAG,EAAC;AAC/E,QAAI;MACF;MACA,MAAM;MACN;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAM;IACZ;AACI,QAAI,MAAM,SAAS,SAAS;AAC1B;IACF;AACA,QAAI,QAAQ,UAAU;AACpB,iBAAW,SAAS,MAAM,kBAAkB;IAC9C;AACA,QAAI,SAAS,WAAW;AACtB,kBAAY;IACd;EACF;AACA,SAAO,EAAE,OAAO,UAAU,QAAQ,UAAS;AAC7C,GAAG,iBAAiB;AACpB,SAAS,cAAc,OAAO,KAAK,eAAe,GAAG,gBAAgB,GAAG;;AACtE,MAAI;IACF;IACA,MAAM;KACNC,MAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAAA,IAAa;IACb;IACA,SAAA,OAAA,SAAA,MAAO;IACP;IACA;EACJ;AACE,MAAI,GAACC,MAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAAA,IAAa,QAAO;AACvB,UAAM,OAAO;MACX,OAAO;MACP,QAAQ;MACR,GAAG;MACH,GAAG;IACT;EACE;AACA,MAAI,WAAW;AACf,MAAI,YAAY;AAChB,QAAI,KAAA,MAAM,aAAN,OAAA,SAAA,GAAgB,UAAS,GAAG;AAC9B,eAAW,SAAS,MAAM,UAAU;AAClC,oBAAc,OAAO,GAAG;IAC1B;AACA,UAAM,YAAY,gBAAgB,KAAK;AACvC,eAAW,UAAU;AACrB,gBAAY,UAAU;AACtB,QAAI,MAAM,mCAAmC,MAAM,IAAI,mBAAmB,UAAU,SAAS;AAC7F,eAAW,SAAS,MAAM,UAAU;AAClC,UAAI,MAAM,MAAM;AACd,YAAI;UACF,qCAAqC,MAAM,EAAE,OAAO,MAAM,EAAE,IAAI,QAAQ,IAAI,SAAS,IAAI,KAAK,UAAU,MAAM,IAAI,CAAC;QAC7H;AACQ,cAAM,KAAK,QAAQ,YAAY,MAAM,kBAAkB,KAAK,YAAY,MAAM,kBAAkB,KAAK;AACrG,cAAM,KAAK,SAAS;AACpB,cAAM,KAAK,IAAI;AACf,cAAM,KAAK,IAAI;AACf,YAAI;UACF,0BAA0B,MAAM,EAAE,mBAAmB,MAAM,EAAE,aAAa,QAAQ,cAAc,SAAS;QACnH;MACM;IACF;AACA,eAAW,SAAS,MAAM,UAAU;AAClC,oBAAc,OAAO,KAAK,UAAU,SAAS;IAC/C;AACA,UAAM,UAAU,MAAM,WAAW;AACjC,QAAI,WAAW;AACf,eAAW,SAAS,MAAM,UAAU;AAClC,kBAAY,MAAM,kBAAkB;IACtC;AACA,QAAI,QAAQ,MAAM,SAAS;AAC3B,QAAI,UAAU,KAAK,UAAU,UAAU;AACrC,cAAQ;IACV;AACA,UAAM,QAAQ,KAAK,KAAK,WAAW,KAAK;AACxC,QAAI,QAAQ,SAAS,WAAW,WAAW;AAC3C,QAAI,SAAS,SAAS,YAAY,WAAW;AAC7C,QAAI,QAAQ,cAAc;AACxB,UAAI;QACF,oCAAoC,MAAM,EAAE,iBAAiB,YAAY,kBAAkB,aAAa,UAAU,KAAK;MAC/H;AACM,cAAQ;AACR,eAAS;AACT,YAAM,cAAc,eAAe,QAAQ,UAAU,WAAW;AAChE,YAAM,eAAe,gBAAgB,QAAQ,UAAU,WAAW;AAClE,UAAI,MAAM,qBAAqB,MAAM,IAAI,cAAc,YAAY,YAAY,QAAQ;AACvF,UAAI,MAAM,qBAAqB,MAAM,IAAI,eAAe,aAAa,aAAa,SAAS;AAC3F,UAAI,MAAM,2BAA2B,OAAO,WAAW,OAAO;AAC9D,iBAAW,SAAS,MAAM,UAAU;AAClC,YAAI,MAAM,MAAM;AACd,gBAAM,KAAK,QAAQ;AACnB,gBAAM,KAAK,SAAS;AACpB,gBAAM,KAAK,IAAI;AACf,gBAAM,KAAK,IAAI;QACjB;MACF;IACF;AACA,QAAI;MACF,uBAAuB,MAAM,EAAE,UAAU,KAAK,UAAU,KAAK,YAAY,OAAO,GAAG,MAAM,SAAS,MAAM,UAAU,KAAK,IAAI,SAAO,KAAA,MAAM,SAAN,OAAA,SAAA,GAAY,UAAS,CAAC,CAAC;IAC/J;AACI,QAAI,WAAS,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa,UAAS,IAAI;AACrC,gBAAQ,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa,UAAS;AAC9B,YAAM,MAAM,UAAU,IAAI,KAAK,IAAI,MAAM,SAAS,QAAQ,OAAO,IAAI,MAAM,SAAS;AACpF,UAAI,MAAM,GAAG;AACX,cAAM,cAAc,QAAQ,MAAM,UAAU,WAAW;AACvD,YAAI,MAAM,gCAAgC,MAAM,IAAI,QAAO,KAAA,MAAM,SAAN,OAAA,SAAA,GAAY,OAAO,UAAU;AACxF,mBAAW,SAAS,MAAM,UAAU;AAClC,cAAI,MAAM,MAAM;AACd,kBAAM,KAAK,QAAQ;UACrB;QACF;MACF;IACF;AACA,UAAM,OAAO;MACX;MACA;MACA,GAAG;MACH,GAAG;IACT;EACE;AACA,MAAI;IACF;IACA,MAAM;KACN,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa;KACb,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa;KACb,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa;KACb,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa;EACjB;AACA;AACA,OAAO,eAAe,eAAe;AACrC,SAAS,aAAa,OAAO,KAAK;;AAChC,MAAI;IACF,wCAAwC,MAAM,EAAE,QAAOD,MAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAAA,IAAa,CAAC,QAAOC,MAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAAA,IAAa,CAAC,YAAW,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa,KAAK;EAC3H;AACE,QAAM,UAAU,MAAM,WAAW;AACjC,MAAI,MAAM,8BAA8B,MAAM,IAAI,MAAM,SAAS,KAAK;AACtE,MAAI,MAAM;EACV,MAAM,SAAS,SAAS,GAAG;AACzB,UAAM,UAAQ,MAAA,KAAA,SAAA,OAAA,SAAA,MAAO,SAAS,CAAA,MAAhB,OAAA,SAAA,GAAoB,SAApB,OAAA,SAAA,GAA0B,UAAS;AACjD,UAAM,kBAAkB,MAAM,SAAS,SAAS,SAAS,MAAM,SAAS,SAAS,KAAK;AACtF,QAAI,MAAM,sBAAsB,iBAAiB,MAAM;AACvD,QAAI,YAAY;AAChB,QAAI,MAAM,wBAAwB,MAAM,KAAI,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa,CAAC;AAC1D,QAAI,iBAAe,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa,OAAI,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa,MAAK,GAAC,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa,SAAQ,KAAK,KAAK,CAAC;AACvF,QAAI,SAAS;AACb,eAAW,SAAS,MAAM,UAAU;AAClC,YAAM,SAAS;AACf,UAAI,CAAC,MAAM,MAAM;AACf;MACF;AACA,YAAM,EAAE,OAAO,QAAQ,OAAM,IAAK,MAAM;AACxC,YAAM,EAAE,IAAI,GAAE,IAAK,uBAAuB,SAAS,SAAS;AAC5D,UAAI,MAAM,QAAQ;AAChB,iBAAS;AACT,yBAAe,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa,OAAI,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa,MAAK,GAAC,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa,SAAQ,KAAK,KAAK,CAAC;AACnF,YAAI,MAAM,+BAA+B,MAAM,IAAI,eAAe,MAAM,IAAI,MAAM;MACpF;AACA,UAAI;QACF,mCAAmC,MAAM,EAAE,SAAS,SAAS,aAAa,EAAE,IAAI,EAAE,MAAK,KAAA,UAAA,OAAA,SAAA,OAAQ,SAAR,OAAA,SAAA,GAAc,CAAC,KAAI,KAAA,UAAA,OAAA,SAAA,OAAQ,SAAR,OAAA,SAAA,GAAc,CAAC,aAAa,OAAO,EAAE,WAAW,MAAM,GAAG,OAAO;MAClL;AACM,UAAI,OAAO,MAAM;AACf,cAAM,YAAY,SAAS;AAC3B,cAAM,KAAK,IAAI,eAAe,UAAU;AACxC,YAAI;UACF,uCAAuC,MAAM,EAAE,iBAAiB,YAAY,oBAAoB,MAAM,KAAK,CAAC,IAAI,SAAS,YAAY,OAAO,UAAU,MAAM,cAAc,SAAS,SAAS,MAAM,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,IAAI,MAAM,cAAc,kCAAkC,WAAU,SAAA,OAAA,SAAA,MAAO,mBAAkB,KAAK,CAAC;QACvU;AACQ,uBAAe,MAAM,KAAK,IAAI;AAC9B,cAAM,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,SAAS,IAAI,MAAM,SAAS,WAAW,SAAS,IAAI;AAC/F,YAAI;UACF,uCAAuC,MAAM,EAAE,eAAe,YAAY,GAAG,OAAO,GAAG,SAAS,OAAO,MAAM,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,GAAG,MAAM,cAAc,gCAAgC,WAAU,SAAA,OAAA,SAAA,MAAO,mBAAkB,KAAK,CAAC;QAC9O;MACM;AACA,UAAI,MAAM,UAAU;AAClB,qBAAa,KAAU;MACzB;AACA,oBAAa,SAAA,OAAA,SAAA,MAAO,mBAAkB;AACtC,UAAI,MAAM,oBAAoB,OAAO,SAAS;IAChD;EACF;AACA,MAAI;IACF,mCAAmC,MAAM,EAAE,QAAO,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa,CAAC,QAAO,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa,CAAC,YAAW,KAAA,SAAA,OAAA,SAAA,MAAO,SAAP,OAAA,SAAA,GAAa,KAAK;EACtH;AACA;AACA,OAAO,cAAc,cAAc;AACnC,SAAS,WAAW,OAAO,EAAE,MAAM,MAAM,MAAM,KAAA,IAAS,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,EAAA,GAAK;AAC9F,MAAI,MAAM,QAAQ,MAAM,OAAO,QAAQ;AACrC,UAAM,EAAE,GAAG,GAAG,OAAO,OAAM,IAAK,MAAM;AACtC,QAAI,IAAI,QAAQ,IAAI,MAAM;AACxB,aAAO,IAAI,QAAQ;IACrB;AACA,QAAI,IAAI,SAAS,IAAI,MAAM;AACzB,aAAO,IAAI,SAAS;IACtB;AACA,QAAI,IAAI,QAAQ,IAAI,MAAM;AACxB,aAAO,IAAI,QAAQ;IACrB;AACA,QAAI,IAAI,SAAS,IAAI,MAAM;AACzB,aAAO,IAAI,SAAS;IACtB;EACF;AACA,MAAI,MAAM,UAAU;AAClB,eAAW,SAAS,MAAM,UAAU;AAClC,OAAC,EAAE,MAAM,MAAM,MAAM,KAAI,IAAK,WAAW,OAAO,EAAE,MAAM,MAAM,MAAM,KAAI,CAAE;IAC5E;EACF;AACA,SAAO,EAAE,MAAM,MAAM,MAAM,KAAI;AACjC;AACA,OAAO,YAAY,YAAY;AAC/B,SAAS,OAAO,KAAK;AACnB,QAAM,OAAO,IAAI,SAAS,MAAM;AAChC,MAAI,CAAC,MAAM;AACT;EACF;AACA,gBAAc,MAAM,KAAK,GAAG,CAAC;AAC7B,eAAa,IAAS;AACtB,MAAI,MAAM,aAAa,KAAK,UAAU,MAAM,MAAM,CAAC,CAAC;AACpD,QAAM,EAAE,MAAM,MAAM,MAAM,KAAI,IAAK,WAAW,IAAI;AAClD,QAAM,SAAS,OAAO;AACtB,QAAM,QAAQ,OAAO;AACrB,SAAO,EAAE,GAAG,MAAM,GAAG,MAAM,OAAO,OAAM;AAC1C;AACA,OAAO,QAAQ,QAAQ;AAOvB,SAAS,WAAW,KAAK,SAAS;AAChC,MAAI,SAAS;AACX,QAAI,KAAK,SAAS,OAAO;EAC3B;AACF;AACA,OAAO,YAAY,YAAY;AAC/B,SAAS,aAAa,MAAM;AAC1B,QAAM,KAAK,OAAO,SAAS,gBAAgB,8BAA8B,eAAe,CAAC;AACzF,QAAM,MAAM,GAAG,OAAO,WAAW;AACjC,QAAM,QAAQ,KAAK;AACnB,QAAM,aAAa,KAAK,SAAS,cAAc;AAC/C,QAAM,OAAO,IAAI,OAAO,MAAM;AAC9B,OAAK,KAAK,KAAK;AACf,aAAW,MAAM,KAAK,UAAU;AAChC,OAAK,KAAK,SAAS,UAAU;AAC7B,aAAW,KAAK,KAAK,UAAU;AAC/B,MAAI,MAAM,WAAW,cAAc;AACnC,MAAI,MAAM,eAAe,QAAQ;AACjC,MAAI,KAAK,SAAS,8BAA8B;AAChD,SAAO,GAAG,KAAI;AAChB;AACA,OAAO,cAAc,cAAc;AACnC,IAAI,cAA8B,OAAO,OAAO,aAAa,OAAO,SAAS,WAAW;AACtF,MAAI,aAAa,eAAe;AAChC,MAAI,OAAO,eAAe,UAAU;AAClC,iBAAa,WAAW,CAAC;EAC3B;AACA,MAAI,SAAS,WAAU,EAAG,UAAU,UAAU,GAAG;AAC/C,iBAAa,WAAW,QAAQ,WAAW,QAAQ;AACnD,QAAI,MAAM,eAAe,UAAU;AACnC,UAAM,QAAQ,MAAM,qBAAqB,eAAe,UAAU,CAAC;AACnE,UAAM,OAAO;MACX;MACA;MACA,YAAY,MAAM,QAAQ,SAAS,QAAQ;IACjD;AACI,QAAI,aAAa,aAAa,IAAI;AAClC,WAAO;EACT,OAAO;AACL,UAAM,WAAW,SAAS,gBAAgB,8BAA8B,MAAM;AAC9E,aAAS,aAAa,SAAS,MAAM,QAAQ,UAAU,OAAO,CAAC;AAC/D,QAAI,OAAO,CAAA;AACX,QAAI,OAAO,eAAe,UAAU;AAClC,aAAO,WAAW,MAAM,qBAAqB;IAC/C,WAAW,MAAM,QAAQ,UAAU,GAAG;AACpC,aAAO;IACT,OAAO;AACL,aAAO,CAAA;IACT;AACA,eAAW,OAAO,MAAM;AACtB,YAAM,QAAQ,SAAS,gBAAgB,8BAA8B,OAAO;AAC5E,YAAM,eAAe,wCAAwC,aAAa,UAAU;AACpF,YAAM,aAAa,MAAM,KAAK;AAC9B,YAAM,aAAa,KAAK,GAAG;AAC3B,UAAI,SAAS;AACX,cAAM,aAAa,SAAS,WAAW;MACzC,OAAO;AACL,cAAM,aAAa,SAAS,KAAK;MACnC;AACA,YAAM,cAAc,IAAI,KAAI;AAC5B,eAAS,YAAY,KAAK;IAC5B;AACA,WAAO;EACT;AACF,GAAG,aAAa;AAChB,IAAI,sBAAsB;AAM1B,IAAI,iBAAiC,OAAO,CAAC,SAAS,MAAM,KAAK,IAAI,gBAAgB;AACnF,MAAI,KAAK,gBAAgB;AACvB,kBAAc,SAAS,SAAS,KAAK,gBAAgB,KAAK,IAAI,WAAW;EAC3E;AACA,MAAI,KAAK,cAAc;AACrB,kBAAc,SAAS,OAAO,KAAK,cAAc,KAAK,IAAI,WAAW;EACvE;AACF,GAAG,gBAAgB;AACnB,IAAI,gBAAgB;EAClB,aAAa;EACb,aAAa;EACb,YAAY;EACZ,cAAc;EACd,aAAa;EACb,WAAW;EACX,aAAa;EACb,YAAY;EACZ,UAAU;AACZ;AACA,IAAI,gBAAgC,OAAO,CAAC,SAAS,UAAU,WAAW,KAAK,IAAI,gBAAgB;AACjG,QAAM,gBAAgB,cAAc,SAAS;AAC7C,MAAI,CAAC,eAAe;AAClB,QAAI,KAAK,uBAAuB,SAAS,EAAE;AAC3C;EACF;AACA,QAAM,SAAS,aAAa,UAAU,UAAU;AAChD,UAAQ,KAAK,UAAU,QAAQ,IAAI,OAAO,GAAG,IAAI,EAAE,IAAI,WAAW,IAAI,aAAa,GAAG,MAAM,GAAG;AACjG,GAAG,eAAe;AAGlB,IAAI,aAAa,CAAA;AACjB,IAAI,iBAAiB,CAAA;AACrB,IAAI,kBAAkC,OAAO,OAAO,MAAM,SAAS;AACjE,QAAM,UAAU,WAAU;AAC1B,QAAM,gBAAgB,SAAS,QAAQ,UAAU,UAAU;AAC3D,QAAM,eAAe,KAAK,cAAc,aAAa;IACnD;IACA,KAAK;IACL;MACE,OAAO,KAAK;MACZ;MACA,kBAAkB;IACxB;IACI;EACJ,IAAM,MAAM,oBAAoB,KAAK,OAAO,KAAK,UAAU;AACzD,QAAM,YAAY,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,WAAW;AAC5D,QAAM,QAAQ,UAAU,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AACzD,QAAM,KAAI,EAAG,YAAY,YAAY;AACrC,MAAI,OAAO,aAAa,QAAO;AAC/B,MAAI,eAAe;AACjB,UAAM,MAAM,aAAa,SAAS,CAAC;AACnC,UAAM,KAAKC,OAAQ,YAAY;AAC/B,WAAO,IAAI,sBAAqB;AAChC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;EAC/B;AACA,QAAM,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AACtF,aAAW,KAAK,EAAE,IAAI;AACtB,OAAK,QAAQ,KAAK;AAClB,OAAK,SAAS,KAAK;AACnB,MAAI;AACJ,MAAI,KAAK,gBAAgB;AACvB,UAAM,oBAAoB,MAAM,oBAAoB,KAAK,gBAAgB,KAAK,UAAU;AACxF,UAAM,qBAAqB,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AACzE,UAAM,QAAQ,mBAAmB,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AAClE,SAAK,MAAM,KAAA,EAAO,YAAY,iBAAiB;AAC/C,UAAM,QAAQ,kBAAkB,QAAO;AACvC,UAAM,KAAK,aAAa,eAAe,CAAC,MAAM,QAAQ,IAAI,OAAO,CAAC,MAAM,SAAS,IAAI,GAAG;AACxF,QAAI,CAAC,eAAe,KAAK,EAAE,GAAG;AAC5B,qBAAe,KAAK,EAAE,IAAI,CAAA;IAC5B;AACA,mBAAe,KAAK,EAAE,EAAE,YAAY;AACpC,qBAAiB,IAAI,KAAK,cAAc;EAC1C;AACA,MAAI,KAAK,iBAAiB;AACxB,UAAM,oBAAoB,MAAM,oBAAoB,KAAK,iBAAiB,KAAK,UAAU;AACzF,UAAM,sBAAsB,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AAC1E,UAAM,QAAQ,oBAAoB,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AACnE,SAAK,oBAAoB,KAAA,EAAO,YAAY,iBAAiB;AAC7D,UAAM,KAAI,EAAG,YAAY,iBAAiB;AAC1C,UAAM,QAAQ,kBAAkB,QAAO;AACvC,UAAM,KAAK,aAAa,eAAe,CAAC,MAAM,QAAQ,IAAI,OAAO,CAAC,MAAM,SAAS,IAAI,GAAG;AACxF,QAAI,CAAC,eAAe,KAAK,EAAE,GAAG;AAC5B,qBAAe,KAAK,EAAE,IAAI,CAAA;IAC5B;AACA,mBAAe,KAAK,EAAE,EAAE,aAAa;AACrC,qBAAiB,IAAI,KAAK,eAAe;EAC3C;AACA,MAAI,KAAK,cAAc;AACrB,UAAM,kBAAkB,MAAM,oBAAoB,KAAK,cAAc,KAAK,UAAU;AACpF,UAAM,mBAAmB,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AACvE,UAAM,QAAQ,iBAAiB,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AAChE,SAAK,MAAM,KAAA,EAAO,YAAY,eAAe;AAC7C,UAAM,QAAQ,gBAAgB,QAAO;AACrC,UAAM,KAAK,aAAa,eAAe,CAAC,MAAM,QAAQ,IAAI,OAAO,CAAC,MAAM,SAAS,IAAI,GAAG;AACxF,qBAAiB,KAAI,EAAG,YAAY,eAAe;AACnD,QAAI,CAAC,eAAe,KAAK,EAAE,GAAG;AAC5B,qBAAe,KAAK,EAAE,IAAI,CAAA;IAC5B;AACA,mBAAe,KAAK,EAAE,EAAE,UAAU;AAClC,qBAAiB,IAAI,KAAK,YAAY;EACxC;AACA,MAAI,KAAK,eAAe;AACtB,UAAM,kBAAkB,MAAM,oBAAoB,KAAK,eAAe,KAAK,UAAU;AACrF,UAAM,oBAAoB,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AACxE,UAAM,QAAQ,kBAAkB,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AACjE,SAAK,MAAM,KAAA,EAAO,YAAY,eAAe;AAC7C,UAAM,QAAQ,gBAAgB,QAAO;AACrC,UAAM,KAAK,aAAa,eAAe,CAAC,MAAM,QAAQ,IAAI,OAAO,CAAC,MAAM,SAAS,IAAI,GAAG;AACxF,sBAAkB,KAAI,EAAG,YAAY,eAAe;AACpD,QAAI,CAAC,eAAe,KAAK,EAAE,GAAG;AAC5B,qBAAe,KAAK,EAAE,IAAI,CAAA;IAC5B;AACA,mBAAe,KAAK,EAAE,EAAE,WAAW;AACnC,qBAAiB,IAAI,KAAK,aAAa;EACzC;AACA,SAAO;AACT,GAAG,iBAAiB;AACpB,SAAS,iBAAiB,IAAI,OAAO;AACnC,MAAI,WAAU,EAAG,UAAU,cAAc,IAAI;AAC3C,OAAG,MAAM,QAAQ,MAAM,SAAS,IAAI;AACpC,OAAG,MAAM,SAAS;EACpB;AACF;AACA,OAAO,kBAAkB,kBAAkB;AAC3C,IAAI,oBAAoC,OAAO,CAAC,MAAM,UAAU;AAC9D,MAAI,MAAM,uBAAuB,KAAK,IAAI,KAAK,OAAO,WAAW,KAAK,EAAE,GAAG,KAAK;AAChF,MAAI,OAAO,MAAM,cAAc,MAAM,cAAc,MAAM;AACzD,QAAM,aAAa,WAAU;AAC7B,QAAM,EAAE,yBAAwB,IAAK,wBAAwB,UAAU;AACvE,MAAI,KAAK,OAAO;AACd,UAAM,KAAK,WAAW,KAAK,EAAE;AAC7B,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AACR,YAAM,MAAM,cAAc,kBAAkB,IAAI;AAChD,UAAI;QACF,kBAAkB,KAAK,QAAQ;QAC/B;QACA;QACA;QACA;QACA,IAAI;QACJ;QACA,IAAI;QACJ;MACR;AACM,UAAI,MAAM,aAAa;AACrB,YAAI,IAAI;AACR,YAAI,IAAI;MACV;IACF;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,IAAI,2BAA2B,CAAC,GAAG;EAC7E;AACA,MAAI,KAAK,gBAAgB;AACvB,UAAM,KAAK,eAAe,KAAK,EAAE,EAAE;AACnC,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AACR,YAAM,MAAM,cAAc,0BAA0B,KAAK,iBAAiB,KAAK,GAAG,cAAc,IAAI;AACpG,UAAI,IAAI;AACR,UAAI,IAAI;IACV;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,CAAC,GAAG;EAC9C;AACA,MAAI,KAAK,iBAAiB;AACxB,UAAM,KAAK,eAAe,KAAK,EAAE,EAAE;AACnC,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AACR,YAAM,MAAM,cAAc;QACxB,KAAK,iBAAiB,KAAK;QAC3B;QACA;MACR;AACM,UAAI,IAAI;AACR,UAAI,IAAI;IACV;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,CAAC,GAAG;EAC9C;AACA,MAAI,KAAK,cAAc;AACrB,UAAM,KAAK,eAAe,KAAK,EAAE,EAAE;AACnC,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AACR,YAAM,MAAM,cAAc,0BAA0B,KAAK,eAAe,KAAK,GAAG,YAAY,IAAI;AAChG,UAAI,IAAI;AACR,UAAI,IAAI;IACV;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,CAAC,GAAG;EAC9C;AACA,MAAI,KAAK,eAAe;AACtB,UAAM,KAAK,eAAe,KAAK,EAAE,EAAE;AACnC,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AACR,YAAM,MAAM,cAAc,0BAA0B,KAAK,eAAe,KAAK,GAAG,aAAa,IAAI;AACjG,UAAI,IAAI;AACR,UAAI,IAAI;IACV;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,CAAC,GAAG;EAC9C;AACF,GAAG,mBAAmB;AACtB,IAAI,cAA8B,OAAO,CAAC,MAAM,WAAW;AACzD,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,QAAM,KAAK,KAAK,IAAI,OAAO,IAAI,CAAC;AAChC,QAAM,KAAK,KAAK,IAAI,OAAO,IAAI,CAAC;AAChC,QAAM,IAAI,KAAK,QAAQ;AACvB,QAAM,IAAI,KAAK,SAAS;AACxB,MAAI,MAAM,KAAK,MAAM,GAAG;AACtB,WAAO;EACT;AACA,SAAO;AACT,GAAG,aAAa;AAChB,IAAI,eAA+B,OAAO,CAAC,MAAM,cAAc,gBAAgB;AAC7E,MAAI,MAAM;kBACM,KAAK,UAAU,YAAY,CAAC;kBAC5B,KAAK,UAAU,WAAW,CAAC;oBACzB,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,KAAK,KAAK,MAAM,KAAK,MAAM,EAAE;AACvE,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,QAAM,KAAK,KAAK,IAAI,IAAI,YAAY,CAAC;AACrC,QAAM,IAAI,KAAK,QAAQ;AACvB,MAAI,IAAI,YAAY,IAAI,aAAa,IAAI,IAAI,KAAK,IAAI;AACtD,QAAM,IAAI,KAAK,SAAS;AACxB,QAAM,IAAI,KAAK,IAAI,aAAa,IAAI,YAAY,CAAC;AACjD,QAAM,IAAI,KAAK,IAAI,aAAa,IAAI,YAAY,CAAC;AACjD,MAAI,KAAK,IAAI,IAAI,aAAa,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,aAAa,CAAC,IAAI,GAAG;AACvE,QAAI,IAAI,YAAY,IAAI,aAAa,IAAI,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,aAAa;AACvF,QAAI,IAAI,IAAI;AACZ,UAAM,MAAM;MACV,GAAG,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,IAAI,YAAY,IAAI,IAAI;MAC5E,GAAG,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI;IACtF;AACI,QAAI,MAAM,GAAG;AACX,UAAI,IAAI,aAAa;AACrB,UAAI,IAAI,aAAa;IACvB;AACA,QAAI,MAAM,GAAG;AACX,UAAI,IAAI,aAAa;IACvB;AACA,QAAI,MAAM,GAAG;AACX,UAAI,IAAI,aAAa;IACvB;AACA,QAAI,MAAM,2BAA2B,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG;AACrE,WAAO;EACT,OAAO;AACL,QAAI,YAAY,IAAI,aAAa,GAAG;AAClC,UAAI,aAAa,IAAI,IAAI;IAC3B,OAAO;AACL,UAAI,IAAI,IAAI,aAAa;IAC3B;AACA,QAAI,IAAI,IAAI,IAAI;AAChB,QAAI,KAAK,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI;AACtF,QAAI,KAAK,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,IAAI,YAAY,IAAI;AAC9E,QAAI,MAAM,uBAAuB,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,GAAE,CAAE;AACxE,QAAI,MAAM,GAAG;AACX,WAAK,aAAa;AAClB,WAAK,aAAa;IACpB;AACA,QAAI,MAAM,GAAG;AACX,WAAK,aAAa;IACpB;AACA,QAAI,MAAM,GAAG;AACX,WAAK,aAAa;IACpB;AACA,WAAO,EAAE,GAAG,IAAI,GAAG,GAAE;EACvB;AACF,GAAG,cAAc;AACjB,IAAI,qBAAqC,OAAO,CAAC,SAAS,iBAAiB;AACzE,MAAI,MAAM,4BAA4B,SAAS,YAAY;AAC3D,MAAI,SAAS,CAAA;AACb,MAAI,mBAAmB,QAAQ,CAAC;AAChC,MAAI,WAAW;AACf,UAAQ,QAAQ,CAAC,WAAW;AAC1B,QAAI,CAAC,YAAY,cAAc,MAAM,KAAK,CAAC,UAAU;AACnD,YAAM,QAAQ,aAAa,cAAc,kBAAkB,MAAM;AACjE,UAAI,eAAe;AACnB,aAAO,QAAQ,CAAC,MAAM;AACpB,uBAAe,gBAAgB,EAAE,MAAM,MAAM,KAAK,EAAE,MAAM,MAAM;MAClE,CAAC;AACD,UAAI,CAAC,OAAO,KAAK,CAAC,MAAM,EAAE,MAAM,MAAM,KAAK,EAAE,MAAM,MAAM,CAAC,GAAG;AAC3D,eAAO,KAAK,KAAK;MACnB;AACA,iBAAW;IACb,OAAO;AACL,yBAAmB;AACnB,UAAI,CAAC,UAAU;AACb,eAAO,KAAK,MAAM;MACpB;IACF;EACF,CAAC;AACD,SAAO;AACT,GAAG,oBAAoB;AACvB,IAAI,aAA6B,OAAO,SAAS,MAAM,GAAG,MAAM,WAAW,aAAa,OAAO,IAAI;AACjG,MAAI,SAAS,KAAK;AAClB,MAAI,MAAM,2BAA2B,MAAM,MAAM,CAAC;AAClD,MAAI,mBAAmB;AACvB,QAAM,OAAO,MAAM,KAAK,EAAE,CAAC;AAC3B,MAAI,OAAO,MAAM,KAAK,EAAE,CAAC;AACzB,OAAI,QAAA,OAAA,SAAA,KAAM,eAAa,QAAA,OAAA,SAAA,KAAM,YAAW;AACtC,aAAS,OAAO,MAAM,GAAG,KAAK,OAAO,SAAS,CAAC;AAC/C,WAAO,QAAQ,KAAK,UAAU,OAAO,CAAC,CAAC,CAAC;AACxC,WAAO,KAAK,KAAK,UAAU,OAAO,OAAO,SAAS,CAAC,CAAC,CAAC;EACvD;AACA,MAAI,KAAK,WAAW;AAClB,QAAI,MAAM,oBAAoB,UAAU,KAAK,SAAS,CAAC;AACvD,aAAS,mBAAmB,KAAK,QAAQ,UAAU,KAAK,SAAS,EAAE,IAAI;AACvE,uBAAmB;EACrB;AACA,MAAI,KAAK,aAAa;AACpB,QAAI,MAAM,sBAAsB,UAAU,KAAK,WAAW,CAAC;AAC3D,aAAS,mBAAmB,OAAO,QAAA,GAAW,UAAU,KAAK,WAAW,EAAE,IAAI,EAAE,QAAO;AACvF,uBAAmB;EACrB;AACA,QAAM,WAAW,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,MAAM,EAAE,CAAC,CAAC;AACxD,MAAI,QAAQ;AACZ,MAAI,KAAK,UAAU,gBAAgB,WAAW,gBAAgB,cAAc;AAC1E,YAAQ,KAAK;EACf;AACA,QAAM,EAAE,GAAG,EAAA,IAAM,2BAA2B,IAAI;AAChD,QAAM,eAAe,OAAI,EAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,KAAK;AACjD,MAAI;AACJ,UAAQ,KAAK,WAAS;IACpB,KAAK;AACH,sBAAgB;AAChB;IACF,KAAK;AACH,sBAAgB;AAChB;IACF,KAAK;AACH,sBAAgB;AAChB;IACF;AACE,sBAAgB;EACtB;AACE,UAAQ,KAAK,SAAO;IAClB,KAAK;AACH,uBAAiB;AACjB;IACF,KAAK;AACH,uBAAiB;AACjB;IACF,KAAK;AACH,uBAAiB;AACjB;EACN;AACE,QAAM,UAAU,KAAK,OAAO,MAAM,EAAE,KAAK,KAAK,aAAa,QAAQ,CAAC,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,SAAS,MAAM,iBAAiB,KAAK,UAAU,MAAM,KAAK,UAAU,GAAG,EAAE,KAAK,SAAS,KAAK,KAAK;AAChM,MAAI,MAAM;AACV,MAAI,WAAU,EAAG,UAAU,uBAAuB,WAAU,EAAG,MAAM,qBAAqB;AACxF,UAAM,OAAO,IAAI;EACnB;AACA,iBAAe,SAAS,MAAM,KAAK,IAAI,WAAW;AAClD,MAAI,QAAQ,CAAA;AACZ,MAAI,kBAAkB;AACpB,UAAM,cAAc;EACtB;AACA,QAAM,eAAe,KAAK;AAC1B,SAAO;AACT,GAAG,YAAY;AAMf,IAAI,iCAAiD,OAAO,CAAC,eAAe;AAC1E,QAAM,mBAAmC,oBAAI,IAAG;AAChD,aAAW,aAAa,YAAY;AAClC,YAAQ,WAAS;MACf,KAAK;AACH,yBAAiB,IAAI,OAAO;AAC5B,yBAAiB,IAAI,MAAM;AAC3B;MACF,KAAK;AACH,yBAAiB,IAAI,IAAI;AACzB,yBAAiB,IAAI,MAAM;AAC3B;MACF;AACE,yBAAiB,IAAI,SAAS;AAC9B;IACR;EACE;AACA,SAAO;AACT,GAAG,gCAAgC;AACnC,IAAI,iBAAiC,OAAO,CAAC,sBAAsB,MAAM,SAAS;AAChF,QAAM,aAAa,+BAA+B,oBAAoB;AACtE,QAAM,IAAI;AACV,QAAM,SAAS,KAAK,SAAS,IAAI,KAAK;AACtC,QAAM,WAAW,SAAS;AAC1B,QAAM,QAAQ,KAAK,QAAQ,IAAI,WAAW,KAAK;AAC/C,QAAM,WAAW,KAAK,UAAU;AAChC,MAAI,WAAW,IAAI,OAAO,KAAK,WAAW,IAAI,MAAM,KAAK,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,MAAM,GAAG;AACvG,WAAO;;MAEL,EAAE,GAAG,GAAG,GAAG,EAAC;MACZ,EAAE,GAAG,UAAU,GAAG,EAAC;MACnB,EAAE,GAAG,QAAQ,GAAG,GAAG,IAAI,SAAQ;MAC/B,EAAE,GAAG,QAAQ,UAAU,GAAG,EAAC;MAC3B,EAAE,GAAG,OAAO,GAAG,EAAC;;MAEhB,EAAE,GAAG,OAAO,GAAG,CAAC,SAAS,EAAC;MAC1B,EAAE,GAAG,QAAQ,IAAI,UAAU,GAAG,CAAC,SAAS,EAAC;MACzC,EAAE,GAAG,OAAO,GAAG,KAAK,SAAS,EAAC;MAC9B,EAAE,GAAG,OAAO,GAAG,CAAC,OAAM;;MAEtB,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,OAAM;MACjC,EAAE,GAAG,QAAQ,GAAG,GAAG,CAAC,SAAS,IAAI,SAAQ;MACzC,EAAE,GAAG,UAAU,GAAG,CAAC,OAAM;;MAEzB,EAAE,GAAG,GAAG,GAAG,CAAC,OAAM;MAClB,EAAE,GAAG,GAAG,GAAG,KAAK,SAAS,EAAC;MAC1B,EAAE,GAAG,KAAK,UAAU,GAAG,CAAC,SAAS,EAAC;MAClC,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,EAAC;IAC5B;EACE;AACA,MAAI,WAAW,IAAI,OAAO,KAAK,WAAW,IAAI,MAAM,KAAK,WAAW,IAAI,IAAI,GAAG;AAC7E,WAAO;MACL,EAAE,GAAG,UAAU,GAAG,EAAC;MACnB,EAAE,GAAG,QAAQ,UAAU,GAAG,EAAC;MAC3B,EAAE,GAAG,OAAO,GAAG,CAAC,SAAS,EAAC;MAC1B,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,OAAM;MACjC,EAAE,GAAG,UAAU,GAAG,CAAC,OAAM;MACzB,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,EAAC;IAC5B;EACE;AACA,MAAI,WAAW,IAAI,OAAO,KAAK,WAAW,IAAI,MAAM,KAAK,WAAW,IAAI,MAAM,GAAG;AAC/E,WAAO;MACL,EAAE,GAAG,GAAG,GAAG,EAAC;MACZ,EAAE,GAAG,UAAU,GAAG,CAAC,OAAM;MACzB,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,OAAM;MACjC,EAAE,GAAG,OAAO,GAAG,EAAC;IACtB;EACE;AACA,MAAI,WAAW,IAAI,OAAO,KAAK,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,MAAM,GAAG;AAC7E,WAAO;MACL,EAAE,GAAG,GAAG,GAAG,EAAC;MACZ,EAAE,GAAG,OAAO,GAAG,CAAC,SAAQ;MACxB,EAAE,GAAG,OAAO,GAAG,CAAC,SAAS,SAAQ;MACjC,EAAE,GAAG,GAAG,GAAG,CAAC,OAAM;IACxB;EACE;AACA,MAAI,WAAW,IAAI,MAAM,KAAK,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,MAAM,GAAG;AAC5E,WAAO;MACL,EAAE,GAAG,OAAO,GAAG,EAAC;MAChB,EAAE,GAAG,GAAG,GAAG,CAAC,SAAQ;MACpB,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,SAAQ;MAC7B,EAAE,GAAG,OAAO,GAAG,CAAC,OAAM;IAC5B;EACE;AACA,MAAI,WAAW,IAAI,OAAO,KAAK,WAAW,IAAI,MAAM,GAAG;AACrD,WAAO;MACL,EAAE,GAAG,UAAU,GAAG,EAAC;MACnB,EAAE,GAAG,UAAU,GAAG,CAAC,SAAQ;MAC3B,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAQ;MACnC,EAAE,GAAG,QAAQ,UAAU,GAAG,EAAC;MAC3B,EAAE,GAAG,OAAO,GAAG,CAAC,SAAS,EAAC;MAC1B,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,OAAM;MACjC,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAS,SAAQ;MAC5C,EAAE,GAAG,UAAU,GAAG,CAAC,SAAS,SAAQ;MACpC,EAAE,GAAG,UAAU,GAAG,CAAC,OAAM;MACzB,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,EAAC;IAC5B;EACE;AACA,MAAI,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,MAAM,GAAG;AAClD,WAAO;;MAEL,EAAE,GAAG,QAAQ,GAAG,GAAG,EAAC;;MAEpB,EAAE,GAAG,GAAG,GAAG,CAAC,SAAQ;MACpB,EAAE,GAAG,UAAU,GAAG,CAAC,SAAQ;;MAE3B,EAAE,GAAG,UAAU,GAAG,CAAC,SAAS,SAAQ;MACpC,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,SAAQ;;MAE7B,EAAE,GAAG,QAAQ,GAAG,GAAG,CAAC,OAAM;MAC1B,EAAE,GAAG,OAAO,GAAG,CAAC,SAAS,SAAQ;;MAEjC,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAS,SAAQ;MAC5C,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAQ;MACnC,EAAE,GAAG,OAAO,GAAG,CAAC,SAAQ;IAC9B;EACE;AACA,MAAI,WAAW,IAAI,OAAO,KAAK,WAAW,IAAI,IAAI,GAAG;AACnD,WAAO;MACL,EAAE,GAAG,GAAG,GAAG,EAAC;MACZ,EAAE,GAAG,OAAO,GAAG,CAAC,SAAQ;MACxB,EAAE,GAAG,GAAG,GAAG,CAAC,OAAM;IACxB;EACE;AACA,MAAI,WAAW,IAAI,OAAO,KAAK,WAAW,IAAI,MAAM,GAAG;AACrD,WAAO;MACL,EAAE,GAAG,GAAG,GAAG,EAAC;MACZ,EAAE,GAAG,OAAO,GAAG,EAAC;MAChB,EAAE,GAAG,GAAG,GAAG,CAAC,OAAM;IACxB;EACE;AACA,MAAI,WAAW,IAAI,MAAM,KAAK,WAAW,IAAI,IAAI,GAAG;AAClD,WAAO;MACL,EAAE,GAAG,OAAO,GAAG,EAAC;MAChB,EAAE,GAAG,GAAG,GAAG,CAAC,SAAQ;MACpB,EAAE,GAAG,OAAO,GAAG,CAAC,OAAM;IAC5B;EACE;AACA,MAAI,WAAW,IAAI,MAAM,KAAK,WAAW,IAAI,MAAM,GAAG;AACpD,WAAO;MACL,EAAE,GAAG,OAAO,GAAG,EAAC;MAChB,EAAE,GAAG,GAAG,GAAG,EAAC;MACZ,EAAE,GAAG,OAAO,GAAG,CAAC,OAAM;IAC5B;EACE;AACA,MAAI,WAAW,IAAI,OAAO,GAAG;AAC3B,WAAO;MACL,EAAE,GAAG,UAAU,GAAG,CAAC,SAAQ;MAC3B,EAAE,GAAG,UAAU,GAAG,CAAC,SAAQ;MAC3B,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAQ;MACnC,EAAE,GAAG,QAAQ,UAAU,GAAG,EAAC;MAC3B,EAAE,GAAG,OAAO,GAAG,CAAC,SAAS,EAAC;MAC1B,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,OAAM;MACjC,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAS,SAAQ;;MAE5C,EAAE,GAAG,UAAU,GAAG,CAAC,SAAS,SAAQ;MACpC,EAAE,GAAG,UAAU,GAAG,CAAC,SAAS,SAAQ;IAC1C;EACE;AACA,MAAI,WAAW,IAAI,MAAM,GAAG;AAC1B,WAAO;MACL,EAAE,GAAG,UAAU,GAAG,EAAC;MACnB,EAAE,GAAG,UAAU,GAAG,CAAC,SAAQ;;MAE3B,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAQ;MACnC,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAS,SAAQ;MAC5C,EAAE,GAAG,UAAU,GAAG,CAAC,SAAS,SAAQ;MACpC,EAAE,GAAG,UAAU,GAAG,CAAC,OAAM;MACzB,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,EAAC;IAC5B;EACE;AACA,MAAI,WAAW,IAAI,IAAI,GAAG;AACxB,WAAO;;MAEL,EAAE,GAAG,UAAU,GAAG,CAAC,SAAQ;;MAE3B,EAAE,GAAG,UAAU,GAAG,CAAC,SAAS,SAAQ;MACpC,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,SAAQ;;MAE7B,EAAE,GAAG,QAAQ,GAAG,GAAG,CAAC,OAAM;MAC1B,EAAE,GAAG,OAAO,GAAG,CAAC,SAAS,SAAQ;;MAEjC,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAS,SAAQ;MAC5C,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAQ;IACzC;EACE;AACA,MAAI,WAAW,IAAI,MAAM,GAAG;AAC1B,WAAO;;MAEL,EAAE,GAAG,QAAQ,GAAG,GAAG,EAAC;;MAEpB,EAAE,GAAG,GAAG,GAAG,CAAC,SAAQ;MACpB,EAAE,GAAG,UAAU,GAAG,CAAC,SAAQ;;MAE3B,EAAE,GAAG,UAAU,GAAG,CAAC,SAAS,SAAQ;MACpC,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAS,SAAQ;MAC5C,EAAE,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAQ;MACnC,EAAE,GAAG,OAAO,GAAG,CAAC,SAAQ;IAC9B;EACE;AACA,SAAO,CAAC,EAAE,GAAG,GAAG,GAAG,EAAC,CAAE;AACxB,GAAG,gBAAgB;AAGnB,SAAS,cAAc,MAAM,QAAQ;AACnC,SAAO,KAAK,UAAU,MAAM;AAC9B;AACA,OAAO,eAAe,eAAe;AACrC,IAAI,yBAAyB;AAG7B,SAAS,iBAAiB,MAAM,IAAI,IAAI,QAAQ;AAC9C,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK,OAAO;AACrB,MAAI,KAAK,KAAK,OAAO;AACrB,MAAI,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACzD,MAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG;AACpC,MAAI,OAAO,IAAI,IAAI;AACjB,SAAK,CAAC;EACR;AACA,MAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG;AACpC,MAAI,OAAO,IAAI,IAAI;AACjB,SAAK,CAAC;EACR;AACA,SAAO,EAAE,GAAG,KAAK,IAAI,GAAG,KAAK,GAAE;AACjC;AACA,OAAO,kBAAkB,kBAAkB;AAC3C,IAAI,4BAA4B;AAGhC,SAAS,gBAAgB,MAAM,IAAI,QAAQ;AACzC,SAAO,0BAA0B,MAAM,IAAI,IAAI,MAAM;AACvD;AACA,OAAO,iBAAiB,iBAAiB;AACzC,IAAI,2BAA2B;AAG/B,SAAS,cAAc,IAAI,IAAI,IAAI,IAAI;AACrC,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,MAAI,IAAI,IAAI,IAAI;AAChB,MAAI,OAAO,QAAQ;AACnB,MAAI,GAAG;AACP,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,MAAI,OAAO,KAAK,OAAO,KAAK,SAAS,IAAI,EAAE,GAAG;AAC5C;EACF;AACA,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,MAAI,OAAO,KAAK,OAAO,KAAK,SAAS,IAAI,EAAE,GAAG;AAC5C;EACF;AACA,UAAQ,KAAK,KAAK,KAAK;AACvB,MAAI,UAAU,GAAG;AACf;EACF;AACA,WAAS,KAAK,IAAI,QAAQ,CAAC;AAC3B,QAAM,KAAK,KAAK,KAAK;AACrB,MAAI,MAAM,KAAK,MAAM,UAAU,SAAS,MAAM,UAAU;AACxD,QAAM,KAAK,KAAK,KAAK;AACrB,MAAI,MAAM,KAAK,MAAM,UAAU,SAAS,MAAM,UAAU;AACxD,SAAO,EAAE,GAAG,EAAC;AACf;AACA,OAAO,eAAe,eAAe;AACrC,SAAS,SAAS,IAAI,IAAI;AACxB,SAAO,KAAK,KAAK;AACnB;AACA,OAAO,UAAU,UAAU;AAC3B,IAAI,yBAAyB;AAG7B,IAAI,4BAA4B;AAChC,SAAS,iBAAiB,MAAM,YAAY,QAAQ;AAClD,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AACd,MAAI,gBAAgB,CAAA;AACpB,MAAI,OAAO,OAAO;AAClB,MAAI,OAAO,OAAO;AAClB,MAAI,OAAO,WAAW,YAAY,YAAY;AAC5C,eAAW,QAAQ,SAAS,OAAO;AACjC,aAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,aAAO,KAAK,IAAI,MAAM,MAAM,CAAC;IAC/B,CAAC;EACH,OAAO;AACL,WAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAClC,WAAO,KAAK,IAAI,MAAM,WAAW,CAAC;EACpC;AACA,MAAI,OAAO,KAAK,KAAK,QAAQ,IAAI;AACjC,MAAI,MAAM,KAAK,KAAK,SAAS,IAAI;AACjC,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,QAAI,KAAK,WAAW,CAAC;AACrB,QAAI,KAAK,WAAW,IAAI,WAAW,SAAS,IAAI,IAAI,IAAI,CAAC;AACzD,QAAI,YAAY;MACd;MACA;MACA,EAAE,GAAG,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,EAAC;MAC/B,EAAE,GAAG,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,EAAC;IACrC;AACI,QAAI,WAAW;AACb,oBAAc,KAAK,SAAS;IAC9B;EACF;AACA,MAAI,CAAC,cAAc,QAAQ;AACzB,WAAO;EACT;AACA,MAAI,cAAc,SAAS,GAAG;AAC5B,kBAAc,KAAK,SAAS,GAAG,GAAG;AAChC,UAAI,MAAM,EAAE,IAAI,OAAO;AACvB,UAAI,MAAM,EAAE,IAAI,OAAO;AACvB,UAAI,QAAQ,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAC3C,UAAI,MAAM,EAAE,IAAI,OAAO;AACvB,UAAI,MAAM,EAAE,IAAI,OAAO;AACvB,UAAI,QAAQ,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAC3C,aAAO,QAAQ,QAAQ,KAAK,UAAU,QAAQ,IAAI;IACpD,CAAC;EACH;AACA,SAAO,cAAc,CAAC;AACxB;AACA,OAAO,kBAAkB,kBAAkB;AAG3C,IAAI,gBAAgC,OAAO,CAAC,MAAM,WAAW;AAC3D,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AACb,MAAI,KAAK,OAAO,IAAI;AACpB,MAAI,KAAK,OAAO,IAAI;AACpB,MAAI,IAAI,KAAK,QAAQ;AACrB,MAAI,IAAI,KAAK,SAAS;AACtB,MAAI,IAAI;AACR,MAAI,KAAK,IAAI,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,GAAG;AACvC,QAAI,KAAK,GAAG;AACV,UAAI,CAAC;IACP;AACA,SAAK,OAAO,IAAI,IAAI,IAAI,KAAK;AAC7B,SAAK;EACP,OAAO;AACL,QAAI,KAAK,GAAG;AACV,UAAI,CAAC;IACP;AACA,SAAK;AACL,SAAK,OAAO,IAAI,IAAI,IAAI,KAAK;EAC/B;AACA,SAAO,EAAE,GAAG,IAAI,IAAI,GAAG,IAAI,GAAE;AAC/B,GAAG,eAAe;AAClB,IAAI,yBAAyB;AAG7B,IAAI,oBAAoB;EACtB,MAAM;EACN,QAAQ;EACR,SAAS;EACT,SAAS;EACT,MAAM;AACR;AAIA,IAAI,cAA8B,OAAO,OAAO,QAAQ,MAAM,UAAU,WAAW;AACjF,QAAM,UAAU,WAAU;AAC1B,MAAI;AACJ,QAAM,gBAAgB,KAAK,iBAAiB,SAAS,QAAQ,UAAU,UAAU;AACjF,MAAI,CAAC,UAAU;AACb,eAAW;EACb,OAAO;AACL,eAAW;EACb;AACA,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,QAAQ,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAC5F,QAAM,QAAQ,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,KAAK,UAAU;AACvF,MAAI;AACJ,MAAI,KAAK,cAAc,QAAQ;AAC7B,gBAAY;EACd,OAAO;AACL,gBAAY,OAAO,KAAK,cAAc,WAAW,KAAK,YAAY,KAAK,UAAU,CAAC;EACpF;AACA,QAAM,WAAW,MAAM,KAAI;AAC3B,MAAI;AACJ,MAAI,KAAK,cAAc,YAAY;AACjC,WAAO;MACL;MACA,aAAa,eAAe,SAAS,GAAG,OAAO;MAC/C;QACE;QACA,OAAO,KAAK,SAAS,QAAQ,UAAU;QACvC,SAAS;MACjB;MACM;IACN;EACE,OAAO;AACL,WAAO,SAAS;MACd,MAAM;QACJ,aAAa,eAAe,SAAS,GAAG,OAAO;QAC/C,KAAK;QACL;QACA;MACR;IACA;EACE;AACA,MAAI,OAAO,KAAK,QAAO;AACvB,QAAM,cAAc,KAAK,UAAU;AACnC,MAAI,SAAS,QAAQ,UAAU,UAAU,GAAG;AAC1C,UAAM,MAAM,KAAK,SAAS,CAAC;AAC3B,UAAM,KAAKC,OAAQ,IAAI;AACvB,UAAM,SAAS,IAAI,qBAAqB,KAAK;AAC7C,QAAI,QAAQ;AACV,YAAM,YAAY,UAAU,QAAQ,eAAe,EAAE,EAAE,KAAI,MAAO;AAClE,YAAM,QAAQ;QACZ,CAAC,GAAG,MAAM,EAAE;UACV,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ;AAC5B,qBAAS,aAAa;AACpB,kBAAI,MAAM,UAAU;AACpB,kBAAI,MAAM,gBAAgB;AAC1B,kBAAI,WAAW;AACb,sBAAM,eAAe,QAAQ,WAAW,QAAQ,WAAW,OAAO,iBAAiB,SAAS,IAAI,EAAE;AAClG,sBAAM,kBAAkB;AACxB,sBAAM,QAAQ,SAAS,cAAc,EAAE,IAAI,kBAAkB;AAC7D,oBAAI,MAAM,WAAW;AACrB,oBAAI,MAAM,WAAW;cACvB,OAAO;AACL,oBAAI,MAAM,QAAQ;cACpB;AACA,kBAAI,GAAG;YACT;AACA,mBAAO,YAAY,YAAY;AAC/B,uBAAW,MAAM;AACf,kBAAI,IAAI,UAAU;AAChB,2BAAU;cACZ;YACF,CAAC;AACD,gBAAI,iBAAiB,SAAS,UAAU;AACxC,gBAAI,iBAAiB,QAAQ,UAAU;UACzC,CAAC;QACX;MACA;IACI;AACA,WAAO,IAAI,sBAAqB;AAChC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;EAC/B;AACA,MAAI,eAAe;AACjB,UAAM,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;EACxF,OAAO;AACL,UAAM,KAAK,aAAa,kBAAkB,CAAC,KAAK,SAAS,IAAI,GAAG;EAClE;AACA,MAAI,KAAK,aAAa;AACpB,UAAM,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;EACxF;AACA,QAAM,OAAO,QAAQ,cAAc;AACnC,SAAO,EAAE,UAAU,MAAM,aAAa,MAAK;AAC7C,GAAG,aAAa;AAChB,IAAI,mBAAmC,OAAO,CAAC,MAAM,YAAY;AAC/D,QAAM,OAAO,QAAQ,KAAI,EAAG,QAAO;AACnC,OAAK,QAAQ,KAAK;AAClB,OAAK,SAAS,KAAK;AACrB,GAAG,kBAAkB;AACrB,SAAS,mBAAmB,QAAQ,GAAG,GAAG,QAAQ;AAChD,SAAO,OAAO,OAAO,WAAW,cAAc,EAAE;IAC9C;IACA,OAAO,IAAI,SAAS,GAAG;AACrB,aAAO,EAAE,IAAI,MAAM,EAAE;IACvB,CAAC,EAAE,KAAK,GAAG;EACf,EAAI,KAAK,SAAS,iBAAiB,EAAE,KAAK,aAAa,eAAe,CAAC,IAAI,IAAI,MAAM,IAAI,IAAI,GAAG;AAChG;AACA,OAAO,oBAAoB,oBAAoB;AAG/C,IAAI,OAAuB,OAAO,OAAO,QAAQ,SAAS;AACxD,QAAM,gBAAgB,KAAK,iBAAiB,WAAU,EAAG,UAAU;AACnE,MAAI,CAAC,eAAe;AAClB,SAAK,cAAc;EACrB;AACA,QAAM,EAAE,UAAU,MAAM,YAAW,IAAK,MAAM;IAC5C;IACA;IACA,UAAU,KAAK;IACf;EACJ;AACE,MAAI,KAAK,cAAc,KAAK,OAAO;AACnC,QAAM,QAAQ,SAAS,OAAO,QAAQ,cAAc;AACpD,QAAM,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,KAAK,CAAC,KAAK,QAAQ,IAAI,WAAW,EAAE,KAAK,KAAK,CAAC,KAAK,SAAS,IAAI,WAAW,EAAE,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,EAAE,KAAK,UAAU,KAAK,SAAS,KAAK,OAAO;AACnN,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,KAAK,MAAM,MAAM;EAC5C;AACA,SAAO;AACT,GAAG,MAAM;AACT,IAAI,eAAe;AAGnB,IAAI,cAA8B,OAAO,CAAC,QAAQ;AAChD,MAAI,KAAK;AACP,WAAO,MAAM;EACf;AACA,SAAO;AACT,GAAG,aAAa;AAChB,IAAI,qBAAqC,OAAO,CAAC,MAAM,iBAAiB;AACtE,SAAO,GAAG,eAAe,eAAe,cAAc,GAAG,YAAY,KAAK,OAAO,CAAC,IAAI;IACpF,KAAK;EACT,CAAG;AACH,GAAG,oBAAoB;AACvB,IAAI,WAA2B,OAAO,OAAO,QAAQ,SAAS;AAC5D,QAAM,EAAE,UAAU,KAAI,IAAK,MAAM;IAC/B;IACA;IACA,mBAAmB,MAAM,MAAM;IAC/B;EACJ;AACE,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,IAAI,IAAI;AACd,QAAM,SAAS;IACb,EAAE,GAAG,IAAI,GAAG,GAAG,EAAC;IAChB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAC;IACjB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAC;IACjB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAC;EACrB;AACE,MAAI,KAAK,wBAAwB;AACjC,QAAM,eAAe,mBAAmB,UAAU,GAAG,GAAG,MAAM;AAC9D,eAAa,KAAK,SAAS,KAAK,KAAK;AACrC,mBAAiB,MAAM,YAAY;AACnC,OAAK,YAAY,SAAS,QAAQ;AAChC,QAAI,KAAK,kBAAkB;AAC3B,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,MAAM;EACvD;AACA,SAAO;AACT,GAAG,UAAU;AACb,IAAI,SAAyB,OAAO,CAAC,QAAQ,SAAS;AACpD,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAClG,QAAM,IAAI;AACV,QAAM,SAAS;IACb,EAAE,GAAG,GAAG,GAAG,IAAI,EAAC;IAChB,EAAE,GAAG,IAAI,GAAG,GAAG,EAAC;IAChB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAC;IACjB,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,EAAC;EACrB;AACE,QAAM,UAAU,SAAS,OAAO,WAAW,cAAc,EAAE;IACzD;IACA,OAAO,IAAI,SAAS,GAAG;AACrB,aAAO,EAAE,IAAI,MAAM,EAAE;IACvB,CAAC,EAAE,KAAK,GAAG;EACf;AACE,UAAQ,KAAK,SAAS,aAAa,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE;AACrF,OAAK,QAAQ;AACb,OAAK,SAAS;AACd,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,OAAO,MAAM,IAAI,MAAM;EAClD;AACA,SAAO;AACT,GAAG,QAAQ;AACX,IAAI,UAA0B,OAAO,OAAO,QAAQ,SAAS;AAC3D,QAAM,EAAE,UAAU,KAAI,IAAK,MAAM;IAC/B;IACA;IACA,mBAAmB,MAAM,MAAM;IAC/B;EACJ;AACE,QAAM,IAAI;AACV,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,IAAI,IAAI;AACd,QAAM,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK;AACpC,QAAM,SAAS;IACb,EAAE,GAAG,GAAG,GAAG,EAAC;IACZ,EAAE,GAAG,IAAI,GAAG,GAAG,EAAC;IAChB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAC;IACjB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAC;IACjB,EAAE,GAAG,GAAG,GAAG,CAAC,EAAC;IACb,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAC;EACrB;AACE,QAAM,MAAM,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACrD,MAAI,KAAK,SAAS,KAAK,KAAK;AAC5B,mBAAiB,MAAM,GAAG;AAC1B,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,MAAM;EACvD;AACA,SAAO;AACT,GAAG,SAAS;AACZ,IAAI,cAA8B,OAAO,OAAO,QAAQ,SAAS;AAC/D,QAAM,EAAE,UAAU,KAAA,IAAS,MAAM,YAAY,QAAQ,MAAM,QAAQ,IAAI;AACvE,QAAM,IAAI;AACV,QAAM,IAAI,KAAK,SAAS,IAAI,KAAK;AACjC,QAAM,IAAI,IAAI;AACd,QAAM,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK;AACpC,QAAM,SAAS,eAAe,KAAK,YAAY,MAAM,IAAI;AACzD,QAAM,aAAa,mBAAmB,UAAU,GAAG,GAAG,MAAM;AAC5D,aAAW,KAAK,SAAS,KAAK,KAAK;AACnC,mBAAiB,MAAM,UAAU;AACjC,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,MAAM;EACvD;AACA,SAAO;AACT,GAAG,aAAa;AAChB,IAAI,sBAAsC,OAAO,OAAO,QAAQ,SAAS;AACvE,QAAM,EAAE,UAAU,KAAI,IAAK,MAAM;IAC/B;IACA;IACA,mBAAmB,MAAM,MAAM;IAC/B;EACJ;AACE,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;IACb,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,EAAC;IACjB,EAAE,GAAG,GAAG,GAAG,EAAC;IACZ,EAAE,GAAG,GAAG,GAAG,CAAC,EAAC;IACb,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,EAAC;IAClB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAC;EACrB;AACE,QAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAK,QAAQ,IAAI;AACjB,OAAK,SAAS;AACd,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,MAAM;EACvD;AACA,SAAO;AACT,GAAG,qBAAqB;AACxB,IAAI,aAA6B,OAAO,OAAO,QAAQ,SAAS;AAC9D,QAAM,EAAE,UAAU,KAAI,IAAK,MAAM,YAAY,QAAQ,MAAM,mBAAmB,IAAI,GAAG,IAAI;AACzF,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;IACb,EAAE,GAAG,KAAK,IAAI,GAAG,GAAG,EAAC;IACrB,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,EAAC;IACpB,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,EAAC;IACzB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAC;EACrB;AACE,QAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,mBAAiB,MAAM,EAAE;AACzB,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,MAAM;EACvD;AACA,SAAO;AACT,GAAG,YAAY;AACf,IAAI,YAA4B,OAAO,OAAO,QAAQ,SAAS;AAC7D,QAAM,EAAE,UAAU,KAAI,IAAK,MAAM;IAC/B;IACA;IACA,mBAAmB,MAAM,MAAM;IAC/B;EACJ;AACE,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;IACb,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,EAAC;IACpB,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,EAAC;IACpB,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,EAAC;IACzB,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,EAAC;EACtB;AACE,QAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,mBAAiB,MAAM,EAAE;AACzB,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,MAAM;EACvD;AACA,SAAO;AACT,GAAG,WAAW;AACd,IAAI,YAA4B,OAAO,OAAO,QAAQ,SAAS;AAC7D,QAAM,EAAE,UAAU,KAAI,IAAK,MAAM;IAC/B;IACA;IACA,mBAAmB,MAAM,MAAM;IAC/B;EACJ;AACE,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;IACb,EAAE,GAAG,KAAK,IAAI,GAAG,GAAG,EAAC;IACrB,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,EAAC;IACxB,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC,EAAC;IACrB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAC;EACrB;AACE,QAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,mBAAiB,MAAM,EAAE;AACzB,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,MAAM;EACvD;AACA,SAAO;AACT,GAAG,WAAW;AACd,IAAI,gBAAgC,OAAO,OAAO,QAAQ,SAAS;AACjE,QAAM,EAAE,UAAU,KAAI,IAAK,MAAM;IAC/B;IACA;IACA,mBAAmB,MAAM,MAAM;IAC/B;EACJ;AACE,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;IACb,EAAE,GAAG,IAAI,GAAG,GAAG,EAAC;IAChB,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,EAAC;IACpB,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,EAAC;IACzB,EAAE,GAAG,KAAK,IAAI,GAAG,GAAG,CAAC,EAAC;EAC1B;AACE,QAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,mBAAiB,MAAM,EAAE;AACzB,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,MAAM;EACvD;AACA,SAAO;AACT,GAAG,eAAe;AAClB,IAAI,uBAAuC,OAAO,OAAO,QAAQ,SAAS;AACxE,QAAM,EAAE,UAAU,KAAI,IAAK,MAAM;IAC/B;IACA;IACA,mBAAmB,MAAM,MAAM;IAC/B;EACJ;AACE,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;IACb,EAAE,GAAG,GAAG,GAAG,EAAC;IACZ,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,EAAC;IACpB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAC;IACjB,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC,EAAC;IACrB,EAAE,GAAG,GAAG,GAAG,CAAC,EAAC;EACjB;AACE,QAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,mBAAiB,MAAM,EAAE;AACzB,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,MAAM;EACvD;AACA,SAAO;AACT,GAAG,sBAAsB;AACzB,IAAI,WAA2B,OAAO,OAAO,QAAQ,SAAS;AAC5D,QAAM,EAAE,UAAU,KAAI,IAAK,MAAM;IAC/B;IACA;IACA,mBAAmB,MAAM,MAAM;IAC/B;EACJ;AACE,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,KAAK,IAAI;AACf,QAAM,KAAK,MAAM,MAAM,IAAI;AAC3B,QAAM,IAAI,KAAK,SAAS,KAAK,KAAK;AAClC,QAAM,QAAQ,SAAS,KAAK,QAAQ,KAAK,MAAM,KAAK,YAAY,IAAI,UAAU,KAAK,MAAM,KAAK,YAAY,CAAC,IAAI,YAAY,IAAI,QAAQ,KAAK,MAAM,KAAK,YAAY,IAAI,YAAY,CAAC;AACpL,QAAM,KAAK,SAAS,KAAK,kBAAkB,EAAE,EAAE,OAAO,QAAQ,cAAc,EAAE,KAAK,SAAS,KAAK,KAAK,EAAE,KAAK,KAAK,KAAK,EAAE,KAAK,aAAa,eAAe,CAAC,IAAI,IAAI,MAAM,EAAE,IAAI,IAAI,MAAM,GAAG;AAC5L,mBAAiB,MAAM,EAAE;AACzB,OAAK,YAAY,SAAS,QAAQ;AAChC,UAAM,MAAM,kBAAkB,KAAK,MAAM,MAAM;AAC/C,UAAM,IAAI,IAAI,IAAI,KAAK;AACvB,QAAI,MAAM,MAAM,KAAK,IAAI,CAAC,IAAI,KAAK,QAAQ,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,QAAQ,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK;AACjI,UAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,KAAK;AACrC,UAAI,KAAK,GAAG;AACV,YAAI,KAAK,KAAK,CAAC;MACjB;AACA,UAAI,KAAK;AACT,UAAI,OAAO,IAAI,KAAK,IAAI,GAAG;AACzB,YAAI,CAAC;MACP;AACA,UAAI,KAAK;IACX;AACA,WAAO;EACT;AACA,SAAO;AACT,GAAG,UAAU;AACb,IAAI,OAAuB,OAAO,OAAO,QAAQ,SAAS;AACxD,QAAM,EAAE,UAAU,MAAM,YAAW,IAAK,MAAM;IAC5C;IACA;IACA,UAAU,KAAK,UAAU,MAAM,KAAK;IACpC;EACJ;AACE,QAAM,QAAQ,SAAS,OAAO,QAAQ,cAAc;AACpD,QAAM,aAAa,KAAK,aAAa,KAAK,QAAQ,KAAK,QAAQ,KAAK;AACpE,QAAM,cAAc,KAAK,aAAa,KAAK,SAAS,KAAK,SAAS,KAAK;AACvE,QAAM,IAAI,KAAK,aAAa,CAAC,aAAa,IAAI,CAAC,KAAK,QAAQ,IAAI;AAChE,QAAM,IAAI,KAAK,aAAa,CAAC,cAAc,IAAI,CAAC,KAAK,SAAS,IAAI;AAClE,QAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,KAAK,KAAK,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,UAAU,WAAW;AAC7L,MAAI,KAAK,OAAO;AACd,UAAM,WAAW,IAAI,IAAI,OAAO,KAAK,KAAK,KAAK,CAAC;AAChD,QAAI,KAAK,MAAM,SAAS;AACtB,+BAAyB,OAAO,KAAK,MAAM,SAAS,YAAY,WAAW;AAC3E,eAAS,OAAO,SAAS;IAC3B;AACA,aAAS,QAAQ,CAAC,YAAY;AAC5B,UAAI,KAAK,yBAAyB,OAAO,EAAE;IAC7C,CAAC;EACH;AACA,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,KAAK,MAAM,MAAM;EAC5C;AACA,SAAO;AACT,GAAG,MAAM;AACT,IAAI,YAA4B,OAAO,OAAO,QAAQ,SAAS;AAC7D,QAAM,EAAE,UAAU,MAAM,YAAW,IAAK,MAAM;IAC5C;IACA;IACA,UAAU,KAAK;IACf;EACJ;AACE,QAAM,QAAQ,SAAS,OAAO,QAAQ,cAAc;AACpD,QAAM,aAAa,KAAK,aAAa,KAAK,QAAQ,KAAK,QAAQ,KAAK;AACpE,QAAM,cAAc,KAAK,aAAa,KAAK,SAAS,KAAK,SAAS,KAAK;AACvE,QAAM,IAAI,KAAK,aAAa,CAAC,aAAa,IAAI,CAAC,KAAK,QAAQ,IAAI;AAChE,QAAM,IAAI,KAAK,aAAa,CAAC,cAAc,IAAI,CAAC,KAAK,SAAS,IAAI;AAClE,QAAM,KAAK,SAAS,yCAAyC,EAAE,KAAK,SAAS,KAAK,KAAK,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,UAAU,WAAW;AAC/M,MAAI,KAAK,OAAO;AACd,UAAM,WAAW,IAAI,IAAI,OAAO,KAAK,KAAK,KAAK,CAAC;AAChD,QAAI,KAAK,MAAM,SAAS;AACtB,+BAAyB,OAAO,KAAK,MAAM,SAAS,YAAY,WAAW;AAC3E,eAAS,OAAO,SAAS;IAC3B;AACA,aAAS,QAAQ,CAAC,YAAY;AAC5B,UAAI,KAAK,yBAAyB,OAAO,EAAE;IAC7C,CAAC;EACH;AACA,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,KAAK,MAAM,MAAM;EAC5C;AACA,SAAO;AACT,GAAG,WAAW;AACd,IAAI,YAA4B,OAAO,OAAO,QAAQ,SAAS;AAC7D,QAAM,EAAE,SAAQ,IAAK,MAAM,YAAY,QAAQ,MAAM,SAAS,IAAI;AAClE,MAAI,MAAM,cAAc,KAAK,KAAK;AAClC,QAAM,QAAQ,SAAS,OAAO,QAAQ,cAAc;AACpD,QAAM,aAAa;AACnB,QAAM,cAAc;AACpB,QAAM,KAAK,SAAS,UAAU,EAAE,KAAK,UAAU,WAAW;AAC1D,WAAS,KAAK,SAAS,iBAAiB;AACxC,MAAI,KAAK,OAAO;AACd,UAAM,WAAW,IAAI,IAAI,OAAO,KAAK,KAAK,KAAK,CAAC;AAChD,QAAI,KAAK,MAAM,SAAS;AACtB,+BAAyB,OAAO,KAAK,MAAM,SAAS,YAAY,WAAW;AAC3E,eAAS,OAAO,SAAS;IAC3B;AACA,aAAS,QAAQ,CAAC,YAAY;AAC5B,UAAI,KAAK,yBAAyB,OAAO,EAAE;IAC7C,CAAC;EACH;AACA,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,KAAK,MAAM,MAAM;EAC5C;AACA,SAAO;AACT,GAAG,WAAW;AACd,SAAS,yBAAyB,OAAO,SAAS,YAAY,aAAa;AACzE,QAAM,kBAAkB,CAAA;AACxB,QAAM,YAA4B,OAAO,CAAC,WAAW;AACnD,oBAAgB,KAAK,QAAQ,CAAC;EAChC,GAAG,WAAW;AACd,QAAM,aAA6B,OAAO,CAAC,WAAW;AACpD,oBAAgB,KAAK,GAAG,MAAM;EAChC,GAAG,YAAY;AACf,MAAI,QAAQ,SAAS,GAAG,GAAG;AACzB,QAAI,MAAM,gBAAgB;AAC1B,cAAU,UAAU;EACtB,OAAO;AACL,eAAW,UAAU;EACvB;AACA,MAAI,QAAQ,SAAS,GAAG,GAAG;AACzB,QAAI,MAAM,kBAAkB;AAC5B,cAAU,WAAW;EACvB,OAAO;AACL,eAAW,WAAW;EACxB;AACA,MAAI,QAAQ,SAAS,GAAG,GAAG;AACzB,QAAI,MAAM,mBAAmB;AAC7B,cAAU,UAAU;EACtB,OAAO;AACL,eAAW,UAAU;EACvB;AACA,MAAI,QAAQ,SAAS,GAAG,GAAG;AACzB,QAAI,MAAM,iBAAiB;AAC3B,cAAU,WAAW;EACvB,OAAO;AACL,eAAW,WAAW;EACxB;AACA,QAAM,KAAK,oBAAoB,gBAAgB,KAAK,GAAG,CAAC;AAC1D;AACA,OAAO,0BAA0B,0BAA0B;AAC3D,IAAI,gBAAgC,OAAO,OAAO,QAAQ,SAAS;AACjE,MAAI;AACJ,MAAI,CAAC,KAAK,SAAS;AACjB,eAAW;EACb,OAAO;AACL,eAAW,UAAU,KAAK;EAC5B;AACA,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,QAAQ,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAC5F,QAAM,QAAQ,SAAS,OAAO,QAAQ,cAAc;AACpD,QAAM,YAAY,SAAS,OAAO,MAAM;AACxC,QAAM,QAAQ,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AACxD,QAAM,QAAQ,KAAK,UAAU,OAAO,KAAK,UAAU,KAAA,IAAS,KAAK;AACjE,MAAI,QAAQ;AACZ,MAAI,OAAO,UAAU,UAAU;AAC7B,YAAQ,MAAM,CAAC;EACjB,OAAO;AACL,YAAQ;EACV;AACA,MAAI,KAAK,oBAAoB,OAAO,OAAO,OAAO,UAAU,QAAQ;AACpE,QAAM,OAAO,MAAM,KAAI,EAAG,YAAY,MAAM,oBAAoB,OAAO,KAAK,YAAY,MAAM,IAAI,CAAC;AACnG,MAAI,OAAO,EAAE,OAAO,GAAG,QAAQ,EAAC;AAChC,MAAI,SAAS,WAAU,EAAG,UAAU,UAAU,GAAG;AAC/C,UAAM,MAAM,KAAK,SAAS,CAAC;AAC3B,UAAM,KAAKC,OAAQ,IAAI;AACvB,WAAO,IAAI,sBAAqB;AAChC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;EAC/B;AACA,MAAI,KAAK,UAAU,KAAK;AACxB,QAAM,WAAW,MAAM,MAAM,GAAG,MAAM,MAAM;AAC5C,MAAI,WAAW,KAAK,QAAO;AAC3B,QAAM,QAAQ,MAAM,KAAI,EAAG;IACzB,MAAM;MACJ,SAAS,OAAO,SAAS,KAAK,OAAO,IAAI;MACzC,KAAK;MACL;MACA;IACN;EACA;AACE,MAAI,SAAS,WAAU,EAAG,UAAU,UAAU,GAAG;AAC/C,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAKA,OAAQ,KAAK;AACxB,WAAO,IAAI,sBAAqB;AAChC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;EAC/B;AACA,QAAM,cAAc,KAAK,UAAU;AACnCA,SAAQ,KAAK,EAAE;IACb;IACA;KACC,KAAK,QAAQ,SAAS,QAAQ,KAAK,SAAS,QAAQ,KAAK,SAAS,KAAK,QAAQ,SAAS,SAAS,cAAc,KAAK;EACzH;AACEA,SAAQ,IAAI,EAAE;IACZ;IACA;KACC,KAAK,QAAQ,SAAS,QAAQ,IAAI,EAAE,SAAS,QAAQ,KAAK,SAAS,KAAK;EAC7E;AACE,SAAO,MAAM,KAAI,EAAG,QAAO;AAC3B,QAAM;IACJ;IACA,eAAe,CAAC,KAAK,QAAQ,IAAI,QAAQ,CAAC,KAAK,SAAS,IAAI,cAAc,KAAK;EACnF;AACE,QAAM,KAAK,SAAS,mBAAmB,EAAE,KAAK,KAAK,CAAC,KAAK,QAAQ,IAAI,WAAW,EAAE,KAAK,KAAK,CAAC,KAAK,SAAS,IAAI,WAAW,EAAE,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,EAAE,KAAK,UAAU,KAAK,SAAS,KAAK,OAAO;AAC9M,YAAU,KAAK,SAAS,SAAS,EAAE,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,WAAW,EAAE,KAAK,MAAM,KAAK,QAAQ,IAAI,WAAW,EAAE,KAAK,MAAM,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS,WAAW,EAAE,KAAK,MAAM,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS,WAAW;AAC1Q,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,KAAK,MAAM,MAAM;EAC5C;AACA,SAAO;AACT,GAAG,eAAe;AAClB,IAAI,UAA0B,OAAO,OAAO,QAAQ,SAAS;AAC3D,QAAM,EAAE,UAAU,KAAI,IAAK,MAAM;IAC/B;IACA;IACA,mBAAmB,MAAM,MAAM;IAC/B;EACJ;AACE,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK;AACpC,QAAM,QAAQ,SAAS,OAAO,QAAQ,cAAc,EAAE,KAAK,SAAS,KAAK,KAAK,EAAE,KAAK,MAAM,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,EAAE,KAAK,UAAU,CAAC;AACzL,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,KAAK,MAAM,MAAM;EAC5C;AACA,SAAO;AACT,GAAG,SAAS;AACZ,IAAI,UAA0B,OAAO,OAAO,QAAQ,SAAS;AAC3D,QAAM,EAAE,UAAU,MAAM,YAAW,IAAK,MAAM;IAC5C;IACA;IACA,mBAAmB,MAAM,MAAM;IAC/B;EACJ;AACE,QAAM,UAAU,SAAS,OAAO,UAAU,cAAc;AACxD,UAAQ,KAAK,SAAS,KAAK,KAAK,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,KAAK,KAAK,QAAQ,IAAI,WAAW,EAAE,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,EAAE,KAAK,UAAU,KAAK,SAAS,KAAK,OAAO;AACpM,MAAI,KAAK,aAAa;AACtB,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,QAAQ;AAChC,QAAI,KAAK,oBAAoB,MAAM,KAAK,QAAQ,IAAI,aAAa,MAAM;AACvE,WAAO,kBAAkB,OAAO,MAAM,KAAK,QAAQ,IAAI,aAAa,MAAM;EAC5E;AACA,SAAO;AACT,GAAG,QAAQ;AACX,IAAI,eAA+B,OAAO,OAAO,QAAQ,SAAS;AAChE,QAAM,EAAE,UAAU,MAAM,YAAW,IAAK,MAAM;IAC5C;IACA;IACA,mBAAmB,MAAM,MAAM;IAC/B;EACJ;AACE,QAAM,MAAM;AACZ,QAAM,cAAc,SAAS,OAAO,KAAK,cAAc;AACvD,QAAM,cAAc,YAAY,OAAO,QAAQ;AAC/C,QAAM,cAAc,YAAY,OAAO,QAAQ;AAC/C,cAAY,KAAK,SAAS,KAAK,KAAK;AACpC,cAAY,KAAK,SAAS,KAAK,KAAK,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,KAAK,KAAK,QAAQ,IAAI,cAAc,GAAG,EAAE,KAAK,SAAS,KAAK,QAAQ,KAAK,UAAU,MAAM,CAAC,EAAE,KAAK,UAAU,KAAK,SAAS,KAAK,UAAU,MAAM,CAAC;AAClO,cAAY,KAAK,SAAS,KAAK,KAAK,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,KAAK,KAAK,QAAQ,IAAI,WAAW,EAAE,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,EAAE,KAAK,UAAU,KAAK,SAAS,KAAK,OAAO;AACxM,MAAI,KAAK,mBAAmB;AAC5B,mBAAiB,MAAM,WAAW;AAClC,OAAK,YAAY,SAAS,QAAQ;AAChC,QAAI,KAAK,0BAA0B,MAAM,KAAK,QAAQ,IAAI,cAAc,KAAK,MAAM;AACnF,WAAO,kBAAkB,OAAO,MAAM,KAAK,QAAQ,IAAI,cAAc,KAAK,MAAM;EAClF;AACA,SAAO;AACT,GAAG,cAAc;AACjB,IAAI,aAA6B,OAAO,OAAO,QAAQ,SAAS;AAC9D,QAAM,EAAE,UAAU,KAAI,IAAK,MAAM;IAC/B;IACA;IACA,mBAAmB,MAAM,MAAM;IAC/B;EACJ;AACE,QAAM,IAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;IACb,EAAE,GAAG,GAAG,GAAG,EAAC;IACZ,EAAE,GAAG,GAAG,GAAG,EAAC;IACZ,EAAE,GAAG,GAAG,GAAG,CAAC,EAAC;IACb,EAAE,GAAG,GAAG,GAAG,CAAC,EAAC;IACb,EAAE,GAAG,GAAG,GAAG,EAAC;IACZ,EAAE,GAAG,IAAI,GAAG,EAAC;IACb,EAAE,GAAG,IAAI,GAAG,GAAG,EAAC;IAChB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAC;IACjB,EAAE,GAAG,IAAI,GAAG,CAAC,EAAC;IACd,EAAE,GAAG,IAAI,GAAG,EAAC;EACjB;AACE,QAAM,KAAK,mBAAmB,UAAU,GAAG,GAAG,MAAM;AACpD,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,mBAAiB,MAAM,EAAE;AACzB,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,MAAM;EACvD;AACA,SAAO;AACT,GAAG,YAAY;AACf,IAAI,QAAwB,OAAO,CAAC,QAAQ,SAAS;AACnD,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAClG,QAAM,UAAU,SAAS,OAAO,UAAU,cAAc;AACxD,UAAQ,KAAK,SAAS,aAAa,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE;AACrF,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,OAAO,MAAM,GAAG,MAAM;EACjD;AACA,SAAO;AACT,GAAG,OAAO;AACV,IAAI,WAA2B,OAAO,CAAC,QAAQ,MAAM,QAAQ;AAC3D,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAClG,MAAI,QAAQ;AACZ,MAAI,SAAS;AACb,MAAI,QAAQ,MAAM;AAChB,YAAQ;AACR,aAAS;EACX;AACA,QAAM,QAAQ,SAAS,OAAO,MAAM,EAAE,KAAK,KAAK,KAAK,QAAQ,CAAC,EAAE,KAAK,KAAK,KAAK,SAAS,CAAC,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,SAAS,WAAW;AAChK,mBAAiB,MAAM,KAAK;AAC5B,OAAK,SAAS,KAAK,SAAS,KAAK,UAAU;AAC3C,OAAK,QAAQ,KAAK,QAAQ,KAAK,UAAU;AACzC,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,KAAK,MAAM,MAAM;EAC5C;AACA,SAAO;AACT,GAAG,UAAU;AACb,IAAI,MAAsB,OAAO,CAAC,QAAQ,SAAS;AACjD,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAClG,QAAM,cAAc,SAAS,OAAO,UAAU,cAAc;AAC5D,QAAM,UAAU,SAAS,OAAO,UAAU,cAAc;AACxD,UAAQ,KAAK,SAAS,aAAa,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE;AACrF,cAAY,KAAK,SAAS,WAAW,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE;AACvF,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,OAAO,MAAM,GAAG,MAAM;EACjD;AACA,SAAO;AACT,GAAG,KAAK;AACR,IAAI,YAA4B,OAAO,OAAO,QAAQ,SAAS;;AAC7D,QAAM,cAAc,KAAK,UAAU;AACnC,QAAM,aAAa;AACnB,QAAM,aAAa;AACnB,MAAI;AACJ,MAAI,CAAC,KAAK,SAAS;AACjB,eAAW;EACb,OAAO;AACL,eAAW,UAAU,KAAK;EAC5B;AACA,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,QAAQ,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAC5F,QAAM,QAAQ,SAAS,OAAO,QAAQ,cAAc;AACpD,QAAM,UAAU,SAAS,OAAO,MAAM;AACtC,QAAM,aAAa,SAAS,OAAO,MAAM;AACzC,MAAI,WAAW;AACf,MAAI,YAAY;AAChB,QAAM,iBAAiB,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AACjE,MAAI,cAAc;AAClB,QAAM,gBAAeJ,MAAA,KAAK,UAAU,gBAAf,OAAA,SAAAA,IAA6B,CAAA;AAClD,QAAM,qBAAqB,KAAK,UAAU,YAAY,CAAC,IAAI,MAAS,KAAK,UAAU,YAAY,CAAC,IAAI,MAAS;AAC7G,QAAM,iBAAiB,eAAe,KAAI,EAAG,YAAY,MAAM,oBAAoB,oBAAoB,KAAK,YAAY,MAAM,IAAI,CAAC;AACnI,MAAI,gBAAgB,eAAe,QAAO;AAC1C,MAAI,SAAS,WAAU,EAAG,UAAU,UAAU,GAAG;AAC/C,UAAM,MAAM,eAAe,SAAS,CAAC;AACrC,UAAM,KAAKI,OAAQ,cAAc;AACjC,oBAAgB,IAAI,sBAAqB;AACzC,OAAG,KAAK,SAAS,cAAc,KAAK;AACpC,OAAG,KAAK,UAAU,cAAc,MAAM;EACxC;AACA,MAAI,KAAK,UAAU,YAAY,CAAC,GAAG;AACjC,iBAAa,cAAc,SAAS;AACpC,gBAAY,cAAc;EAC5B;AACA,MAAI,mBAAmB,KAAK,UAAU;AACtC,MAAI,KAAK,UAAU,SAAS,UAAU,KAAK,UAAU,SAAS,IAAI;AAChE,QAAI,WAAU,EAAG,UAAU,YAAY;AACrC,0BAAoB,SAAS,KAAK,UAAU,OAAO;IACrD,OAAO;AACL,0BAAoB,MAAM,KAAK,UAAU,OAAO;IAClD;EACF;AACA,QAAM,kBAAkB,eAAe,KAAI,EAAG,YAAY,MAAM,oBAAoB,kBAAkB,KAAK,YAAY,MAAM,IAAI,CAAC;AAClIA,SAAQ,eAAe,EAAE,KAAK,SAAS,YAAY;AACnD,MAAI,iBAAiB,gBAAgB,QAAO;AAC5C,MAAI,SAAS,WAAU,EAAG,UAAU,UAAU,GAAG;AAC/C,UAAM,MAAM,gBAAgB,SAAS,CAAC;AACtC,UAAM,KAAKA,OAAQ,eAAe;AAClC,qBAAiB,IAAI,sBAAqB;AAC1C,OAAG,KAAK,SAAS,eAAe,KAAK;AACrC,OAAG,KAAK,UAAU,eAAe,MAAM;EACzC;AACA,eAAa,eAAe,SAAS;AACrC,MAAI,eAAe,QAAQ,UAAU;AACnC,eAAW,eAAe;EAC5B;AACA,QAAM,kBAAkB,CAAA;AACxB,OAAK,UAAU,QAAQ,QAAQ,OAAO,WAAW;AAC/C,UAAM,aAAa,OAAO,kBAAiB;AAC3C,QAAI,aAAa,WAAW;AAC5B,QAAI,WAAU,EAAG,UAAU,YAAY;AACrC,mBAAa,WAAW,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM;IACpE;AACA,UAAM,MAAM,eAAe,KAAI,EAAG;MAChC,MAAM;QACJ;QACA,WAAW,WAAW,WAAW,WAAW,KAAK;QACjD;QACA;MACR;IACA;AACI,QAAI,OAAO,IAAI,QAAO;AACtB,QAAI,SAAS,WAAU,EAAG,UAAU,UAAU,GAAG;AAC/C,YAAM,MAAM,IAAI,SAAS,CAAC;AAC1B,YAAM,KAAKA,OAAQ,GAAG;AACtB,aAAO,IAAI,sBAAqB;AAChC,SAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,SAAG,KAAK,UAAU,KAAK,MAAM;IAC/B;AACA,QAAI,KAAK,QAAQ,UAAU;AACzB,iBAAW,KAAK;IAClB;AACA,iBAAa,KAAK,SAAS;AAC3B,oBAAgB,KAAK,GAAG;EAC1B,CAAC;AACD,eAAa;AACb,QAAM,eAAe,CAAA;AACrB,OAAK,UAAU,QAAQ,QAAQ,OAAO,WAAW;AAC/C,UAAM,aAAa,OAAO,kBAAiB;AAC3C,QAAI,cAAc,WAAW;AAC7B,QAAI,WAAU,EAAG,UAAU,YAAY;AACrC,oBAAc,YAAY,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM;IACtE;AACA,UAAM,MAAM,eAAe,KAAI,EAAG;MAChC,MAAM;QACJ;QACA,WAAW,WAAW,WAAW,WAAW,KAAK;QACjD;QACA;MACR;IACA;AACI,QAAI,OAAO,IAAI,QAAO;AACtB,QAAI,SAAS,WAAU,EAAG,UAAU,UAAU,GAAG;AAC/C,YAAM,MAAM,IAAI,SAAS,CAAC;AAC1B,YAAM,KAAKA,OAAQ,GAAG;AACtB,aAAO,IAAI,sBAAqB;AAChC,SAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,SAAG,KAAK,UAAU,KAAK,MAAM;IAC/B;AACA,QAAI,KAAK,QAAQ,UAAU;AACzB,iBAAW,KAAK;IAClB;AACA,iBAAa,KAAK,SAAS;AAC3B,iBAAa,KAAK,GAAG;EACvB,CAAC;AACD,eAAa;AACb,MAAI,cAAc;AAChB,QAAI,UAAU,WAAW,cAAc,SAAS;AAChDA,WAAQ,cAAc,EAAE;MACtB;MACA,iBAAiB,KAAK,WAAW,IAAI,UAAU,OAAO,KAAK,YAAY,IAAI;IACjF;AACI,kBAAc,cAAc,SAAS;EACvC;AACA,MAAI,SAAS,WAAW,eAAe,SAAS;AAChDA,SAAQ,eAAe,EAAE;IACvB;IACA,iBAAiB,KAAK,WAAW,IAAI,SAAS,QAAQ,KAAK,YAAY,IAAI,eAAe;EAC9F;AACE,iBAAe,eAAe,SAAS;AACvC,UAAQ,KAAK,SAAS,SAAS,EAAE,KAAK,MAAM,CAAC,WAAW,IAAI,WAAW,EAAE,KAAK,MAAM,WAAW,IAAI,WAAW,EAAE,KAAK,MAAM,CAAC,YAAY,IAAI,cAAc,aAAa,WAAW,EAAE,KAAK,MAAM,CAAC,YAAY,IAAI,cAAc,aAAa,WAAW;AACtP,iBAAe;AACf,kBAAgB,QAAQ,CAAC,QAAQ;AAC/BA,WAAQ,GAAG,EAAE;MACX;MACA,gBAAgB,CAAC,WAAW,IAAI,QAAQ,KAAK,YAAY,IAAI,cAAc,aAAa,KAAK;IACnG;AACI,UAAM,aAAa,OAAA,OAAA,SAAA,IAAK,QAAA;AACxB,qBAAgB,cAAA,OAAA,SAAA,WAAY,WAAU,KAAK;EAC7C,CAAC;AACD,iBAAe;AACf,aAAW,KAAK,SAAS,SAAS,EAAE,KAAK,MAAM,CAAC,WAAW,IAAI,WAAW,EAAE,KAAK,MAAM,WAAW,IAAI,WAAW,EAAE,KAAK,MAAM,CAAC,YAAY,IAAI,cAAc,aAAa,WAAW,EAAE,KAAK,MAAM,CAAC,YAAY,IAAI,cAAc,aAAa,WAAW;AACzP,iBAAe;AACf,eAAa,QAAQ,CAAC,QAAQ;AAC5BA,WAAQ,GAAG,EAAE;MACX;MACA,gBAAgB,CAAC,WAAW,IAAI,QAAQ,KAAK,YAAY,IAAI,eAAe;IAClF;AACI,UAAM,aAAa,OAAA,OAAA,SAAA,IAAK,QAAA;AACxB,qBAAgB,cAAA,OAAA,SAAA,WAAY,WAAU,KAAK;EAC7C,CAAC;AACD,QAAM,KAAK,SAAS,KAAK,KAAK,EAAE,KAAK,SAAS,mBAAmB,EAAE,KAAK,KAAK,CAAC,WAAW,IAAI,WAAW,EAAE,KAAK,KAAK,EAAE,YAAY,KAAK,WAAW,EAAE,KAAK,SAAS,WAAW,KAAK,OAAO,EAAE,KAAK,UAAU,YAAY,KAAK,OAAO;AAClO,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,QAAQ;AAChC,WAAO,kBAAkB,KAAK,MAAM,MAAM;EAC5C;AACA,SAAO;AACT,GAAG,WAAW;AACd,IAAI,SAAS;EACX,SAAS;EACT;EACA;EACA;EACA;EACA;EACA;EACA,QAAQ;EACR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAM;EACN;EACA,MAAM;EACN,MAAM;EACN;AACF;AACA,IAAI,YAAY,CAAA;AAChB,IAAI,aAA6B,OAAO,OAAO,MAAM,MAAM,kBAAkB;AAC3E,MAAI;AACJ,MAAI;AACJ,MAAI,KAAK,MAAM;AACb,QAAI;AACJ,QAAI,WAAU,EAAG,kBAAkB,WAAW;AAC5C,eAAS;IACX,WAAW,KAAK,YAAY;AAC1B,eAAS,KAAK,cAAc;IAC9B;AACA,YAAQ,KAAK,OAAO,OAAO,EAAE,KAAK,cAAc,KAAK,IAAI,EAAE,KAAK,UAAU,MAAM;AAChF,SAAK,MAAM,OAAO,KAAK,KAAK,EAAE,OAAO,MAAM,aAAa;EAC1D,OAAO;AACL,SAAK,MAAM,OAAO,KAAK,KAAK,EAAE,MAAM,MAAM,aAAa;AACvD,YAAQ;EACV;AACA,MAAI,KAAK,SAAS;AAChB,OAAG,KAAK,SAAS,KAAK,OAAO;EAC/B;AACA,MAAI,KAAK,OAAO;AACd,OAAG,KAAK,SAAS,kBAAkB,KAAK,KAAK;EAC/C;AACA,YAAU,KAAK,EAAE,IAAI;AACrB,MAAI,KAAK,cAAc;AACrB,cAAU,KAAK,EAAE,EAAE,KAAK,SAAS,UAAU,KAAK,EAAE,EAAE,KAAK,OAAO,IAAI,YAAY;EAClF;AACA,SAAO;AACT,GAAG,YAAY;AACf,IAAI,eAA+B,OAAO,CAAC,SAAS;AAClD,QAAM,KAAK,UAAU,KAAK,EAAE;AAC5B,MAAI;IACF;IACA,KAAK;IACL;IACA,gBAAgB,KAAK,IAAI,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK,QAAQ,IAAI;EAC3E;AACE,QAAM,WAAW;AACjB,QAAM,OAAO,KAAK,QAAQ;AAC1B,MAAI,KAAK,aAAa;AACpB,OAAG;MACD;MACA,gBAAgB,KAAK,IAAI,OAAO,KAAK,QAAQ,KAAK,QAAQ,KAAK,IAAI,KAAK,SAAS,IAAI,YAAY;IACvG;EACE,OAAO;AACL,OAAG,KAAK,aAAa,eAAe,KAAK,IAAI,OAAO,KAAK,IAAI,GAAG;EAClE;AACA,SAAO;AACT,GAAG,cAAc;AAGjB,SAAS,iBAAiB,OAAO,KAAK,aAAa,OAAO;;AACxD,QAAM,SAAS;AACf,MAAI,WAAW;AACf,SAAKJ,MAAA,UAAA,OAAA,SAAA,OAAQ,YAAR,OAAA,SAAAA,IAAiB,WAAU,KAAK,GAAG;AACtC,iBAAY,UAAA,OAAA,SAAA,OAAQ,YAAW,CAAA,GAAI,KAAK,GAAG;EAC7C;AACA,aAAW,WAAW;AACtB,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI;AACJ,UAAQ,OAAO,MAAI;IACjB,KAAK;AACH,eAAS;AACT,cAAQ;AACR;IACF,KAAK;AACH,eAAS;AACT,cAAQ;AACR,iBAAW;AACX;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF,KAAK;AACH,cAAQ;AACR;IACF;AACE,cAAQ;EACd;AACE,QAAM,SAAS,oBAAmB,UAAA,OAAA,SAAA,OAAQ,WAAU,CAAA,CAAE;AACtD,QAAM,aAAa,OAAO;AAC1B,QAAM,SAAS,OAAO,QAAQ,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,GAAG,GAAG,EAAC;AAC/D,QAAM,OAAO;IACX,YAAY,OAAO;IACnB;IACA,WAAW;IACX,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,OAAO,OAAO;IACd,IAAI,OAAO;IACX,YAAY,OAAO;IACnB,OAAO,OAAO;IACd,QAAQ,OAAO;IACf,GAAG,OAAO;IACV,GAAG,OAAO;IACV;IACA,WAAW;IACX,MAAM,OAAO;IACb,SAAS,cAAY,MAAAC,MAAA,UAAA,MAAA,OAAA,SAAAA,IAAa,UAAb,OAAA,SAAA,GAAoB,YAAW;EACxD;AACE,SAAO;AACT;AACA,OAAO,kBAAkB,kBAAkB;AAC3C,eAAe,mBAAmB,MAAM,OAAO,KAAK;AAClD,QAAM,OAAO,iBAAiB,OAAO,KAAK,KAAK;AAC/C,MAAI,KAAK,SAAS,SAAS;AACzB;EACF;AACA,QAAM,UAAU,UAAS;AACzB,QAAM,SAAS,MAAM,WAAW,MAAM,MAAM,EAAE,QAAQ,QAAA,CAAS;AAC/D,QAAM,cAAc,OAAO,KAAI,EAAG,QAAO;AACzC,QAAM,MAAM,IAAI,SAAS,KAAK,EAAE;AAChC,MAAI,OAAO,EAAE,OAAO,YAAY,OAAO,QAAQ,YAAY,QAAQ,GAAG,GAAG,GAAG,GAAG,MAAM,OAAM;AAC3F,MAAI,SAAS,GAAG;AAChB,SAAO,OAAM;AACf;AACA,OAAO,oBAAoB,oBAAoB;AAC/C,eAAe,sBAAsB,MAAM,OAAO,KAAK;AACrD,QAAM,OAAO,iBAAiB,OAAO,KAAK,IAAI;AAC9C,QAAM,MAAM,IAAI,SAAS,KAAK,EAAE;AAChC,MAAI,IAAI,SAAS,SAAS;AACxB,UAAM,UAAU,UAAS;AACzB,UAAM,WAAW,MAAM,MAAM,EAAE,QAAQ,QAAO,CAAE;AAChD,UAAM,YAAY,QAAA,OAAA,SAAA,KAAM;AACxB,iBAAa,IAAI;EACnB;AACF;AACA,OAAO,uBAAuB,uBAAuB;AACrD,eAAe,kBAAkB,MAAM,SAAS,KAAK,WAAW;AAC9D,aAAW,SAAS,SAAS;AAC3B,UAAM,UAAU,MAAM,OAAO,GAAG;AAChC,QAAI,MAAM,UAAU;AAClB,YAAM,kBAAkB,MAAM,MAAM,UAAU,KAAK,SAAS;IAC9D;EACF;AACF;AACA,OAAO,mBAAmB,mBAAmB;AAC7C,eAAe,oBAAoB,MAAM,SAAS,KAAK;AACrD,QAAM,kBAAkB,MAAM,SAAS,KAAK,kBAAkB;AAChE;AACA,OAAO,qBAAqB,qBAAqB;AACjD,eAAe,aAAa,MAAM,SAAS,KAAK;AAC9C,QAAM,kBAAkB,MAAM,SAAS,KAAK,qBAAqB;AACnE;AACA,OAAO,cAAc,cAAc;AACnC,eAAe,YAAY,MAAM,OAAO,SAAS,KAAK,IAAI;AACxD,QAAM,IAAI,IAAII,MAAe;IAC3B,YAAY;IACZ,UAAU;EACd,CAAG;AACD,IAAE,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;EACb,CAAG;AACD,aAAW,SAAS,SAAS;AAC3B,QAAI,MAAM,MAAM;AACd,QAAE,QAAQ,MAAM,IAAI;QAClB,OAAO,MAAM,KAAK;QAClB,QAAQ,MAAM,KAAK;QACnB,WAAW,MAAM;MACzB,CAAO;IACH;EACF;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,KAAK,SAAS,KAAK,KAAK;AAC1B,YAAM,aAAa,IAAI,SAAS,KAAK,KAAK;AAC1C,YAAM,WAAW,IAAI,SAAS,KAAK,GAAG;AACtC,WAAI,cAAA,OAAA,SAAA,WAAY,UAAQ,YAAA,OAAA,SAAA,SAAU,OAAM;AACtC,cAAM,SAAS,WAAW;AAC1B,cAAM,OAAO,SAAS;AACtB,cAAM,SAAS;UACb,EAAE,GAAG,OAAO,GAAG,GAAG,OAAO,EAAC;UAC1B,EAAE,GAAG,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,GAAG,GAAG,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,EAAC;UAC9E,EAAE,GAAG,KAAK,GAAG,GAAG,KAAK,EAAC;QAChC;AACQ;UACE;UACA,EAAE,GAAG,KAAK,OAAO,GAAG,KAAK,KAAK,MAAM,KAAK,GAAE;UAC3C;YACE,GAAG;YACH,cAAc,KAAK;YACnB,gBAAgB,KAAK;YACrB;YACA,SAAS;UACrB;UACU;UACA;UACA;UACA;QACV;AACQ,YAAI,KAAK,OAAO;AACd,gBAAM,gBAAgB,MAAM;YAC1B,GAAG;YACH,OAAO,KAAK;YACZ,YAAY;YACZ,cAAc,KAAK;YACnB,gBAAgB,KAAK;YACrB;YACA,SAAS;UACrB,CAAW;AACD;YACE,EAAE,GAAG,MAAM,GAAG,OAAO,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,EAAE,EAAC;YACzC;cACE,cAAc;YAC5B;UACA;QACQ;MACF;IACF;EACF;AACF;AACA,OAAO,aAAa,aAAa;AAGjC,IAAI,cAA8B,OAAO,SAAS,MAAM,SAAS;AAC/D,SAAO,QAAQ,GAAG,WAAU;AAC9B,GAAG,YAAY;AACf,IAAI,OAAuB,OAAO,eAAe,MAAM,IAAI,UAAU,SAAS;AAC5E,QAAM,EAAE,eAAe,OAAO,KAAI,IAAK,UAAS;AAChD,QAAM,MAAM,QAAQ;AACpB,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiBC,OAAS,OAAO,EAAE;EACrC;AACA,QAAM,OAAO,kBAAkB,YAAYA,OAAS,eAAe,MAAA,EAAQ,CAAC,EAAE,gBAAgB,IAAI,IAAIA,OAAS,MAAM;AACrH,QAAM,MAAM,kBAAkB,YAAY,KAAK,OAAO,QAAQ,EAAE,IAAI,IAAIA,OAAS,QAAQ,EAAE,IAAI;AAC/F,QAAM,WAAW,CAAC,SAAS,UAAU,OAAO;AAC5C,kBAAgB,KAAK,UAAU,QAAQ,MAAM,EAAE;AAC/C,QAAM,KAAK,IAAI,UAAS;AACxB,QAAM,QAAQ,IAAI,cAAa;AAC/B,QAAM,QAAQ,IAAI,SAAQ;AAC1B,QAAM,QAAQ,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AACnD,QAAM,oBAAoB,OAAO,IAAI,GAAG;AACxC,QAAM,SAAS,OAAO,GAAG;AACzB,QAAM,aAAa,OAAO,IAAI,GAAG;AACjC,QAAM,YAAY,OAAO,OAAO,OAAO,KAAK,EAAE;AAC9C,MAAI,QAAQ;AACV,UAAM,UAAU;AAChB,UAAM,cAAc,KAAK,IAAI,GAAG,KAAK,MAAM,SAAS,QAAQ,QAAQ,QAAQ,OAAO,CAAC;AACpF,UAAM,SAAS,QAAQ,SAAS,cAAc;AAC9C,UAAM,QAAQ,QAAQ,QAAQ;AAC9B,UAAM,EAAE,YAAW,IAAK;AACxB,qBAAiB,KAAK,QAAQ,OAAO,CAAC,CAAC,WAAW;AAClD,QAAI,MAAM,eAAe,QAAQ,OAAO;AACxC,QAAI;MACF;MACA,GAAG,QAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,QAAQ,EAAE,IAAI,QAAQ,SAAS,EAAE;IACpF;EACE;AACF,GAAG,MAAM;AACT,IAAI,wBAAwB;EAC1B;EACA,YAAY;AACd;AAGG,IAAC,UAAU;EACZ,QAAQ;EACR,IAAI;EACJ,UAAU;EACV,QAAQ;AACV;", "names": ["khroma.channel", "khroma.rgba", "_a", "_b", "select2", "select3", "select4", "graphlib.Graph", "d3select"]}