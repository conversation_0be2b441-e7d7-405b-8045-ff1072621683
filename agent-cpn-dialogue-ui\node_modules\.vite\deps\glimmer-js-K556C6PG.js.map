{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/glimmer-js.mjs"], "sourcesContent": ["import javascript from './javascript.mjs';\nimport handlebars from './handlebars.mjs';\nimport './html.mjs';\nimport './css.mjs';\nimport './yaml.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Glimmer JS\", \"injections\": { \"L:source.gjs -comment -string\": { \"patterns\": [{ \"begin\": \"\\\\s*(<)(template)\\\\s*(>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.other.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.html\" } }, \"end\": \"(</)(template)(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.other.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.html\" } }, \"name\": \"meta.js.embeddedTemplateWithoutArgs\", \"patterns\": [{ \"include\": \"text.html.handlebars\" }] }, { \"begin\": \"(<)(template)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.other.html\" } }, \"end\": \"(</)(template)(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.other.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.html\" } }, \"name\": \"meta.js.embeddedTemplateWithArgs\", \"patterns\": [{ \"begin\": \"(?<=\\\\<template)\", \"end\": \"(?=\\\\>)\", \"patterns\": [{ \"include\": \"text.html.handlebars#tag-stuff\" }] }, { \"begin\": \"(>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.js\" } }, \"contentName\": \"meta.html.embedded.block\", \"end\": \"(?=</template>)\", \"patterns\": [{ \"include\": \"text.html.handlebars\" }] }] }] } }, \"name\": \"glimmer-js\", \"patterns\": [{ \"include\": \"source.js\" }], \"scopeName\": \"source.gjs\", \"embeddedLangs\": [\"javascript\", \"handlebars\"], \"aliases\": [\"gjs\"] });\nvar glimmerJs = [\n  ...javascript,\n  ...handlebars,\n  lang\n];\n\nexport { glimmerJs as default };\n"], "mappings": ";;;;;;;;;;;;AAMA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,cAAc,cAAc,EAAE,iCAAiC,EAAE,YAAY,CAAC,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,qBAAqB,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,QAAQ,uCAAuC,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,qBAAqB,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,QAAQ,oCAAoC,YAAY,CAAC,EAAE,SAAS,oBAAoB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,eAAe,4BAA4B,OAAO,mBAAmB,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,QAAQ,cAAc,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,GAAG,aAAa,cAAc,iBAAiB,CAAC,cAAc,YAAY,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;AAC7/C,IAAI,YAAY;AAAA,EACd,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}