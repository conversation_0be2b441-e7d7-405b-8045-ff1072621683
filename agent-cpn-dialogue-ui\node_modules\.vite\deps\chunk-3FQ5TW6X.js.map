{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/vue.mjs"], "sourcesContent": ["import html from './html.mjs';\nimport markdown from './markdown.mjs';\nimport pug from './pug.mjs';\nimport stylus from './stylus.mjs';\nimport sass from './sass.mjs';\nimport css from './css.mjs';\nimport scss from './scss.mjs';\nimport less from './less.mjs';\nimport javascript from './javascript.mjs';\nimport typescript from './typescript.mjs';\nimport jsx from './jsx.mjs';\nimport tsx from './tsx.mjs';\nimport json from './json.mjs';\nimport jsonc from './jsonc.mjs';\nimport json5 from './json5.mjs';\nimport yaml from './yaml.mjs';\nimport toml from './toml.mjs';\nimport graphql from './graphql.mjs';\nimport './coffee.mjs';\n\nconst lang$4 = Object.freeze({ \"fileTypes\": [], \"injectTo\": [\"text.html.markdown\"], \"injectionSelector\": \"L:text.html.markdown\", \"name\": \"markdown-vue\", \"patterns\": [{ \"include\": \"#vue-code-block\" }], \"repository\": { \"vue-code-block\": { \"begin\": \"(^|\\\\G)(\\\\s*)(`{3,}|~{3,})\\\\s*(?i:(vue)((\\\\s+|:|,|\\\\{|\\\\?)[^`~]*)?$)\", \"beginCaptures\": { \"3\": { \"name\": \"punctuation.definition.markdown\" }, \"4\": { \"name\": \"fenced_code.block.language.markdown\" }, \"5\": { \"name\": \"fenced_code.block.language.attributes.markdown\" } }, \"end\": \"(^|\\\\G)(\\\\2|\\\\s{0,3})(\\\\3)\\\\s*$\", \"endCaptures\": { \"3\": { \"name\": \"punctuation.definition.markdown\" } }, \"name\": \"markup.fenced_code.block.markdown\", \"patterns\": [{ \"include\": \"source.vue\" }] } }, \"scopeName\": \"markdown.vue.codeblock\" });\nvar markdown_vue = [\n  lang$4\n];\n\nconst lang$3 = Object.freeze({ \"fileTypes\": [], \"injectTo\": [\"source.vue\", \"text.html.markdown\", \"text.html.derivative\", \"text.pug\"], \"injectionSelector\": \"L:meta.tag -meta.attribute -entity.name.tag.pug -attribute_value -source.tsx -source.js.jsx, L:meta.element -meta.attribute\", \"name\": \"vue-directives\", \"patterns\": [{ \"include\": \"source.vue#vue-directives\" }], \"scopeName\": \"vue.directives\" });\nvar vue_directives = [\n  lang$3\n];\n\nconst lang$2 = Object.freeze({ \"fileTypes\": [], \"injectTo\": [\"source.vue\", \"text.html.markdown\", \"text.html.derivative\", \"text.pug\"], \"injectionSelector\": \"L:text.pug -comment -string.comment, L:text.html.derivative -comment.block, L:text.html.markdown -comment.block\", \"name\": \"vue-interpolations\", \"patterns\": [{ \"include\": \"source.vue#vue-interpolations\" }], \"scopeName\": \"vue.interpolations\" });\nvar vue_interpolations = [\n  lang$2\n];\n\nconst lang$1 = Object.freeze({ \"fileTypes\": [], \"injectTo\": [\"source.vue\"], \"injectionSelector\": \"L:source.css -comment, L:source.postcss -comment, L:source.sass -comment, L:source.stylus -comment\", \"name\": \"vue-sfc-style-variable-injection\", \"patterns\": [{ \"include\": \"#vue-sfc-style-variable-injection\" }], \"repository\": { \"vue-sfc-style-variable-injection\": { \"begin\": \"\\\\b(v-bind)\\\\s*\\\\(\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function\" } }, \"end\": \"\\\\)\", \"name\": \"vue.sfc.style.variable.injection.v-bind\", \"patterns\": [{ \"begin\": `('|\")`, \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" } }, \"end\": \"(\\\\1)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"source.ts.embedded.html.vue\", \"patterns\": [{ \"include\": \"source.js\" }] }, { \"include\": \"source.js\" }] } }, \"scopeName\": \"vue.sfc.style.variable.injection\", \"embeddedLangs\": [\"javascript\"] });\nvar vue_sfc_style_variable_injection = [\n  ...javascript,\n  lang$1\n];\n\nconst lang = Object.freeze({ \"displayName\": \"Vue\", \"name\": \"vue\", \"patterns\": [{ \"include\": \"text.html.basic#comment\" }, { \"include\": \"#self-closing-tag\" }, { \"begin\": \"(<)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" } }, \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html.vue\" } }, \"patterns\": [{ \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)md\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"text.html.markdown\", \"patterns\": [{ \"include\": \"text.html.markdown\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)html\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"text.html.derivative\", \"patterns\": [{ \"include\": \"#html-stuff\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)pug\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"text.pug\", \"patterns\": [{ \"include\": \"text.pug\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)stylus\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.stylus\", \"patterns\": [{ \"include\": \"source.stylus\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)postcss\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.postcss\", \"patterns\": [{ \"include\": \"source.postcss\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)sass\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.sass\", \"patterns\": [{ \"include\": \"source.sass\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)css\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.css\", \"patterns\": [{ \"include\": \"source.css\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)scss\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.css.scss\", \"patterns\": [{ \"include\": \"source.css.scss\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)less\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.css.less\", \"patterns\": [{ \"include\": \"source.css.less\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)js\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.js\", \"patterns\": [{ \"include\": \"source.js\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)ts\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.ts\", \"patterns\": [{ \"include\": \"source.ts\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)jsx\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.js.jsx\", \"patterns\": [{ \"include\": \"source.js.jsx\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)tsx\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.tsx\", \"patterns\": [{ \"include\": \"source.tsx\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)json\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.json\", \"patterns\": [{ \"include\": \"source.json\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)jsonc\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.json.comments\", \"patterns\": [{ \"include\": \"source.json.comments\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)json5\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.json5\", \"patterns\": [{ \"include\": \"source.json5\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)yaml\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.yaml\", \"patterns\": [{ \"include\": \"source.yaml\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)toml\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.toml\", \"patterns\": [{ \"include\": \"source.toml\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)(gql|graphql)\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.graphql\", \"patterns\": [{ \"include\": \"source.graphql\" }] }] }, { \"begin\": `([a-zA-Z0-9:-]+)\\\\b(?=[^>]*\\\\blang\\\\s*=\\\\s*(['\"]?)vue\\\\b\\\\2)`, \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"source.vue\", \"patterns\": [{ \"include\": \"source.vue\" }] }] }, { \"begin\": \"(template)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/template\\\\b)\", \"name\": \"text.html.derivative\", \"patterns\": [{ \"include\": \"#html-stuff\" }] }] }, { \"begin\": \"(script)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/script\\\\b)\", \"name\": \"source.js\", \"patterns\": [{ \"include\": \"source.js\" }] }] }, { \"begin\": \"(style)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/style\\\\b)\", \"name\": \"source.css\", \"patterns\": [{ \"include\": \"source.css\" }] }] }, { \"begin\": \"([a-zA-Z0-9:-]+)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.$1.html.vue\" } }, \"end\": \"(</)(\\\\1)\\\\s*(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"begin\": \"(?<=>)\", \"end\": \"(?=<\\\\/)\", \"name\": \"text\" }] }] }], \"repository\": { \"html-stuff\": { \"patterns\": [{ \"include\": \"#template-tag\" }, { \"include\": \"text.html.derivative\" }, { \"include\": \"text.html.basic\" }] }, \"self-closing-tag\": { \"begin\": \"(<)([a-zA-Z0-9:-]+)(?=([^>]+/>))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"end\": \"(/>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html.vue\" } }, \"name\": \"self-closing-tag\", \"patterns\": [{ \"include\": \"#tag-stuff\" }] }, \"tag-stuff\": { \"begin\": \"\\\\G\", \"end\": \"(?=/>)|(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html.vue\" } }, \"name\": \"meta.tag-stuff\", \"patterns\": [{ \"include\": \"#vue-directives\" }, { \"include\": \"text.html.basic#attribute\" }] }, \"template-tag\": { \"patterns\": [{ \"include\": \"#template-tag-1\" }, { \"include\": \"#template-tag-2\" }] }, \"template-tag-1\": { \"begin\": \"(<)(template)\\\\b(>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.html.vue\" } }, \"end\": \"(/?>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html.vue\" } }, \"name\": \"meta.template-tag.start\", \"patterns\": [{ \"begin\": \"\\\\G\", \"end\": \"(?=/>)|((</)(template)\\\\b)\", \"endCaptures\": { \"2\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"3\": { \"name\": \"entity.name.tag.$3.html.vue\" } }, \"name\": \"meta.template-tag.end\", \"patterns\": [{ \"include\": \"#html-stuff\" }] }] }, \"template-tag-2\": { \"begin\": \"(<)(template)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"2\": { \"name\": \"entity.name.tag.$2.html.vue\" } }, \"end\": \"(/?>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html.vue\" } }, \"name\": \"meta.template-tag.start\", \"patterns\": [{ \"begin\": \"\\\\G\", \"end\": \"(?=/>)|((</)(template)\\\\b)\", \"endCaptures\": { \"2\": { \"name\": \"punctuation.definition.tag.begin.html.vue\" }, \"3\": { \"name\": \"entity.name.tag.$3.html.vue\" } }, \"name\": \"meta.template-tag.end\", \"patterns\": [{ \"include\": \"#tag-stuff\" }, { \"include\": \"#html-stuff\" }] }] }, \"vue-directives\": { \"patterns\": [{ \"include\": \"#vue-directives-control\" }, { \"include\": \"#vue-directives-style-attr\" }, { \"include\": \"#vue-directives-original\" }, { \"include\": \"#vue-directives-generic-attr\" }] }, \"vue-directives-control\": { \"begin\": \"(v-for)|(v-if|v-else-if|v-else)\", \"captures\": { \"1\": { \"name\": \"keyword.control.loop.vue\" }, \"2\": { \"name\": \"keyword.control.conditional.vue\" } }, \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.attribute.directive.control.vue\", \"patterns\": [{ \"include\": \"#vue-directives-expression\" }] }, \"vue-directives-expression\": { \"patterns\": [{ \"begin\": \"(=)\\\\s*('|\\\"|`)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.key-value.html.vue\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.html.vue\" } }, \"end\": \"(\\\\2)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.html.vue\" } }, \"patterns\": [{ \"begin\": \"(?<=('|\\\"|`))\", \"end\": \"(?=\\\\1)\", \"name\": \"source.ts.embedded.html.vue\", \"patterns\": [{ \"include\": \"source.ts\" }] }] }, { \"begin\": \"(=)\\\\s*(?=[^'\\\"`])\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.key-value.html.vue\" } }, \"end\": \"(?=(\\\\s|>|\\\\/>))\", \"patterns\": [{ \"begin\": \"(?=[^'\\\"`])\", \"end\": \"(?=(\\\\s|>|\\\\/>))\", \"name\": \"source.ts.embedded.html.vue\", \"patterns\": [{ \"include\": \"source.ts\" }] }] }] }, \"vue-directives-generic-attr\": { \"begin\": \"\\\\b(generic)\\\\s*(=)\", \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.html.vue\" }, \"2\": { \"name\": \"punctuation.separator.key-value.html.vue\" } }, \"end\": `(?<='|\")`, \"name\": \"meta.attribute.generic.vue\", \"patterns\": [{ \"begin\": `('|\")`, \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.html.vue\" } }, \"comment\": \"https://github.com/microsoft/vscode/blob/fd4346210f59135fad81a8b8c4cea7bf5a9ca6b4/extensions/typescript-basics/syntaxes/TypeScript.tmLanguage.json#L4002-L4020\", \"end\": \"(\\\\1)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.html.vue\" } }, \"name\": \"meta.type.parameters.vue\", \"patterns\": [{ \"include\": \"source.ts#comment\" }, { \"match\": \"(?<![_$[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(extends|in|out)(?![_$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"storage.modifier.ts\" }, { \"include\": \"source.ts#type\" }, { \"include\": \"source.ts#punctuation-comma\" }, { \"match\": \"(=)(?!>)\", \"name\": \"keyword.operator.assignment.ts\" }] }] }, \"vue-directives-original\": { \"begin\": \"(?:\\\\b(v-)|([:\\\\.])|(@)|(#))(\\\\[?)([\\\\w\\\\-]*)(\\\\]?)(?:\\\\.([\\\\w\\\\-]*))*\", \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.html.vue\" }, \"2\": { \"name\": \"punctuation.attribute-shorthand.bind.html.vue\" }, \"3\": { \"name\": \"punctuation.attribute-shorthand.event.html.vue\" }, \"4\": { \"name\": \"punctuation.attribute-shorthand.slot.html.vue\" }, \"5\": { \"name\": \"punctuation.separator.key-value.html.vue\" }, \"6\": { \"name\": \"entity.other.attribute-name.html.vue\" }, \"7\": { \"name\": \"punctuation.separator.key-value.html.vue\" }, \"8\": { \"name\": \"entity.other.attribute-name.html.vue\" }, \"9\": { \"name\": \"punctuation.separator.key-value.html.vue\" } }, \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.html.vue\" } }, \"name\": \"meta.attribute.directive.vue\", \"patterns\": [{ \"include\": \"#vue-directives-expression\" }] }, \"vue-directives-style-attr\": { \"begin\": \"\\\\b(style)\\\\s*(=)\", \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.html.vue\" }, \"2\": { \"name\": \"punctuation.separator.key-value.html.vue\" } }, \"end\": `(?<='|\")`, \"name\": \"meta.attribute.style.vue\", \"patterns\": [{ \"begin\": `('|\")`, \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.html.vue\" } }, \"comment\": \"Copy from source.css#rule-list-innards\", \"end\": \"(\\\\1)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.html.vue\" } }, \"name\": \"source.css.embedded.html.vue\", \"patterns\": [{ \"include\": \"source.css#comment-block\" }, { \"include\": \"source.css#escapes\" }, { \"include\": \"source.css#font-features\" }, { \"match\": \"(?x) (?<![\\\\w-])\\n--\\n(?:[-a-zA-Z_]    | [^\\\\x00-\\\\x7F])\\n(?:[-a-zA-Z0-9_] | [^\\\\x00-\\\\x7F]\\n|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)\\n)*\", \"name\": \"variable.css\" }, { \"begin\": \"(?<![-a-zA-Z])(?=[-a-zA-Z])\", \"end\": \"$|(?![-a-zA-Z])\", \"name\": \"meta.property-name.css\", \"patterns\": [{ \"include\": \"source.css#property-names\" }] }, { \"begin\": \"(:)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.key-value.css\" } }, \"comment\": \"Modify end to fix #199. TODO: handle ' character.\", \"contentName\": \"meta.property-value.css\", \"end\": `\\\\s*(;)|\\\\s*(?='|\")`, \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"patterns\": [{ \"include\": \"source.css#comment-block\" }, { \"include\": \"source.css#property-values\" }] }, { \"match\": \";\", \"name\": \"punctuation.terminator.rule.css\" }] }] }, \"vue-interpolations\": { \"patterns\": [{ \"begin\": \"(\\\\{\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.interpolation.begin.html.vue\" } }, \"end\": \"(\\\\}\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.interpolation.end.html.vue\" } }, \"name\": \"expression.embedded.vue\", \"patterns\": [{ \"begin\": \"\\\\G\", \"end\": \"(?=\\\\}\\\\})\", \"name\": \"source.ts.embedded.html.vue\", \"patterns\": [{ \"include\": \"source.ts\" }] }] }] } }, \"scopeName\": \"source.vue\", \"embeddedLangs\": [\"html\", \"markdown\", \"pug\", \"stylus\", \"sass\", \"css\", \"scss\", \"less\", \"javascript\", \"typescript\", \"jsx\", \"tsx\", \"json\", \"jsonc\", \"json5\", \"yaml\", \"toml\", \"graphql\", \"markdown-vue\", \"vue-directives\", \"vue-interpolations\", \"vue-sfc-style-variable-injection\"] });\nvar vue = [\n  ...html,\n  ...markdown,\n  ...pug,\n  ...stylus,\n  ...sass,\n  ...css,\n  ...scss,\n  ...less,\n  ...javascript,\n  ...typescript,\n  ...jsx,\n  ...tsx,\n  ...json,\n  ...jsonc,\n  ...json5,\n  ...yaml,\n  ...toml,\n  ...graphql,\n  ...markdown_vue,\n  ...vue_directives,\n  ...vue_interpolations,\n  ...vue_sfc_style_variable_injection,\n  lang\n];\n\nexport { vue as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,IAAM,SAAS,OAAO,OAAO,EAAE,aAAa,CAAC,GAAG,YAAY,CAAC,oBAAoB,GAAG,qBAAqB,wBAAwB,QAAQ,gBAAgB,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,GAAG,cAAc,EAAE,kBAAkB,EAAE,SAAS,wEAAwE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,mCAAmC,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,QAAQ,qCAAqC,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,EAAE,GAAG,aAAa,yBAAyB,CAAC;AACtvB,IAAI,eAAe;AAAA,EACjB;AACF;AAEA,IAAM,SAAS,OAAO,OAAO,EAAE,aAAa,CAAC,GAAG,YAAY,CAAC,cAAc,sBAAsB,wBAAwB,UAAU,GAAG,qBAAqB,+HAA+H,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,4BAA4B,CAAC,GAAG,aAAa,iBAAiB,CAAC;AAC7Y,IAAI,iBAAiB;AAAA,EACnB;AACF;AAEA,IAAM,SAAS,OAAO,OAAO,EAAE,aAAa,CAAC,GAAG,YAAY,CAAC,cAAc,sBAAsB,wBAAwB,UAAU,GAAG,qBAAqB,mHAAmH,QAAQ,sBAAsB,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,GAAG,aAAa,qBAAqB,CAAC;AAC7Y,IAAI,qBAAqB;AAAA,EACvB;AACF;AAEA,IAAM,SAAS,OAAO,OAAO,EAAE,aAAa,CAAC,GAAG,YAAY,CAAC,YAAY,GAAG,qBAAqB,sGAAsG,QAAQ,oCAAoC,YAAY,CAAC,EAAE,WAAW,oCAAoC,CAAC,GAAG,cAAc,EAAE,oCAAoC,EAAE,SAAS,sBAAsB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,QAAQ,2CAA2C,YAAY,CAAC,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,EAAE,GAAG,aAAa,oCAAoC,iBAAiB,CAAC,YAAY,EAAE,CAAC;AAC15B,IAAI,mCAAmC;AAAA,EACrC,GAAG;AAAA,EACH;AACF;AAEA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,OAAO,QAAQ,OAAO,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,+DAA+D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,sBAAsB,YAAY,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,iEAAiE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,gEAAgE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,YAAY,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,mEAAmE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,iBAAiB,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,oEAAoE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,iEAAiE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,eAAe,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,gEAAgE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,cAAc,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,iEAAiE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,mBAAmB,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,iEAAiE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,mBAAmB,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,+DAA+D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,aAAa,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,+DAA+D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,aAAa,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,gEAAgE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,iBAAiB,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,gEAAgE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,cAAc,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,iEAAiE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,eAAe,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,kEAAkE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,kEAAkE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,gBAAgB,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,iEAAiE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,eAAe,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,iEAAiE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,eAAe,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,0EAA0E,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,gEAAgE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,cAAc,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,uBAAuB,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,eAAe,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,qBAAqB,QAAQ,aAAa,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,oBAAoB,QAAQ,cAAc,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,UAAU,OAAO,YAAY,QAAQ,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,cAAc,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,oBAAoB,EAAE,SAAS,oCAAoC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,QAAQ,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,oBAAoB,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,OAAO,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,4BAA4B,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,uBAAuB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,OAAO,OAAO,8BAA8B,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,yBAAyB,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,OAAO,OAAO,8BAA8B,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,yBAAyB,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,+BAA+B,CAAC,EAAE,GAAG,0BAA0B,EAAE,SAAS,mCAAmC,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,oBAAoB,QAAQ,wCAAwC,YAAY,CAAC,EAAE,WAAW,6BAA6B,CAAC,EAAE,GAAG,6BAA6B,EAAE,YAAY,CAAC,EAAE,SAAS,mBAAmB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,iBAAiB,OAAO,WAAW,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,oBAAoB,YAAY,CAAC,EAAE,SAAS,eAAe,OAAO,oBAAoB,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,+BAA+B,EAAE,SAAS,uBAAuB,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,YAAY,QAAQ,8BAA8B,YAAY,CAAC,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,WAAW,kKAAkK,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,SAAS,2GAA2G,QAAQ,sBAAsB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,SAAS,YAAY,QAAQ,iCAAiC,CAAC,EAAE,CAAC,EAAE,GAAG,2BAA2B,EAAE,SAAS,0EAA0E,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,gDAAgD,GAAG,KAAK,EAAE,QAAQ,iDAAiD,GAAG,KAAK,EAAE,QAAQ,gDAAgD,GAAG,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,oBAAoB,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,6BAA6B,CAAC,EAAE,GAAG,6BAA6B,EAAE,SAAS,qBAAqB,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,YAAY,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,WAAW,0CAA0C,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,SAAS,gIAAgI,QAAQ,eAAe,GAAG,EAAE,SAAS,+BAA+B,OAAO,mBAAmB,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,4BAA4B,CAAC,EAAE,GAAG,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,WAAW,qDAAqD,eAAe,2BAA2B,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,6BAA6B,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,QAAQ,kCAAkC,CAAC,EAAE,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sDAAsD,EAAE,GAAG,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,oDAAoD,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,OAAO,OAAO,cAAc,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,aAAa,cAAc,iBAAiB,CAAC,QAAQ,YAAY,OAAO,UAAU,QAAQ,OAAO,QAAQ,QAAQ,cAAc,cAAc,OAAO,OAAO,QAAQ,SAAS,SAAS,QAAQ,QAAQ,WAAW,gBAAgB,kBAAkB,sBAAsB,kCAAkC,EAAE,CAAC;AACx2kB,IAAI,MAAM;AAAA,EACR,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}