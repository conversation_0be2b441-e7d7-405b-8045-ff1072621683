{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/ruby.mjs"], "sourcesContent": ["import html from './html.mjs';\nimport xml from './xml.mjs';\nimport sql from './sql.mjs';\nimport css from './css.mjs';\nimport c from './c.mjs';\nimport javascript from './javascript.mjs';\nimport shellscript from './shellscript.mjs';\nimport lua from './lua.mjs';\nimport './java.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Ruby\", \"name\": \"ruby\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.control.class.ruby\" }, \"2\": { \"name\": \"entity.name.type.class.ruby\" }, \"3\": { \"name\": \"keyword.operator.other.ruby\" }, \"4\": { \"name\": \"entity.other.inherited-class.ruby\" }, \"5\": { \"name\": \"keyword.operator.other.ruby\" }, \"6\": { \"name\": \"variable.other.object.ruby\" } }, \"match\": \"^\\\\s*(class)\\\\s+(?:([.a-zA-Z0-9_:]+)(?:\\\\s*(<)\\\\s*([.a-zA-Z0-9_:]+))?|(<<)\\\\s*([.a-zA-Z0-9_:]+))\", \"name\": \"meta.class.ruby\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.module.ruby\" }, \"2\": { \"name\": \"entity.name.type.module.ruby\" }, \"3\": { \"name\": \"entity.other.inherited-class.module.first.ruby\" }, \"4\": { \"name\": \"punctuation.separator.inheritance.ruby\" }, \"5\": { \"name\": \"entity.other.inherited-class.module.second.ruby\" }, \"6\": { \"name\": \"punctuation.separator.inheritance.ruby\" }, \"7\": { \"name\": \"entity.other.inherited-class.module.third.ruby\" }, \"8\": { \"name\": \"punctuation.separator.inheritance.ruby\" } }, \"match\": \"^\\\\s*(module)\\\\s+(([A-Z]\\\\w*(::))?([A-Z]\\\\w*(::))?([A-Z]\\\\w*(::))*[A-Z]\\\\w*)\", \"name\": \"meta.module.ruby\" }, { \"comment\": \"else if is a common mistake carried over from other languages. it works if you put in a second end, but it\\u2019s never what you want.\", \"match\": \"(?<!\\\\.)\\\\belse(\\\\s)+if\\\\b\", \"name\": \"invalid.deprecated.ruby\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.ruby\" } }, \"comment\": \"symbols as hash key (1.9 syntax)\", \"match\": \"(?>[a-zA-Z_]\\\\w*(?>[?!])?)(:)(?!:)\", \"name\": \"constant.other.symbol.hashkey.ruby\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.ruby\" } }, \"comment\": \"symbols as hash key (1.8 syntax)\", \"match\": \"(?<!:)(:)(?>[a-zA-Z_]\\\\w*(?>[?!])?)(?=\\\\s*=>)\", \"name\": \"constant.other.symbol.hashkey.ruby\" }, { \"comment\": \"everything being a reserved word, not a value and needing a 'end' is a..\", \"match\": \"(?<!\\\\.)\\\\b(BEGIN|begin|case|class|else|elsif|END|end|ensure|for|if|in|module|rescue|then|unless|until|when|while)\\\\b(?![?!])\", \"name\": \"keyword.control.ruby\" }, { \"comment\": \"contextual smart pair support for block parameters\", \"match\": \"(?<!\\\\.)\\\\bdo\\\\b\", \"name\": \"keyword.control.start-block.ruby\" }, { \"comment\": \"contextual smart pair support\", \"match\": \"(?<=\\\\{)(\\\\s+)\", \"name\": \"meta.syntax.ruby.start-block\" }, { \"match\": \"(?<!\\\\.)\\\\b(alias|alias_method|block_given[?]|break|defined[?]|iterator[?]|next|redo|retry|return|super|undef|yield)(\\\\b|(?<=[?]))(?![?!])\", \"name\": \"keyword.control.pseudo-method.ruby\" }, { \"match\": \"\\\\b(nil|true|false)\\\\b(?![?!])\", \"name\": \"constant.language.ruby\" }, { \"match\": \"\\\\b(__(dir|FILE|LINE)__)\\\\b(?![?!])\", \"name\": \"variable.language.ruby\" }, { \"begin\": \"^__END__\\\\n\", \"captures\": { \"0\": { \"name\": \"string.unquoted.program-block.ruby\" } }, \"comment\": \"__END__ marker\", \"contentName\": \"text.plain\", \"end\": \"(?=not)impossible\", \"patterns\": [{ \"begin\": \"(?=<?xml|<(?i:html\\\\b)|!DOCTYPE (?i:html\\\\b))\", \"end\": \"(?=not)impossible\", \"name\": \"text.html.embedded.ruby\", \"patterns\": [{ \"include\": \"text.html.basic\" }] }] }, { \"match\": \"\\\\b(self)\\\\b(?![?!])\", \"name\": \"variable.language.self.ruby\" }, { \"comment\": \" everything being a method but having a special function is a..\", \"match\": \"\\\\b(initialize|new|loop|include|extend|prepend|fail|raise|attr_reader|attr_writer|attr_accessor|attr|catch|throw|private|private_class_method|module_function|public|public_class_method|protected|refine|using)\\\\b(?![?!])\", \"name\": \"keyword.other.special-method.ruby\" }, { \"begin\": \"\\\\b(?<!\\\\.|::)(require|require_relative)\\\\b\", \"captures\": { \"1\": { \"name\": \"keyword.other.special-method.ruby\" } }, \"end\": \"$|(?=#|\\\\})\", \"name\": \"meta.require.ruby\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.ruby\" } }, \"match\": \"(@)[a-zA-Z_]\\\\w*\", \"name\": \"variable.other.readwrite.instance.ruby\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.ruby\" } }, \"match\": \"(@@)[a-zA-Z_]\\\\w*\", \"name\": \"variable.other.readwrite.class.ruby\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.ruby\" } }, \"match\": \"(\\\\$)[a-zA-Z_]\\\\w*\", \"name\": \"variable.other.readwrite.global.ruby\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.ruby\" } }, \"match\": \"(\\\\$)(!|@|&|`|'|\\\\+|\\\\d+|~|=|/|\\\\\\\\|,|;|\\\\.|<|>|_|\\\\*|\\\\$|\\\\?|:|\\\"|-[0adFiIlpvw])\", \"name\": \"variable.other.readwrite.global.pre-defined.ruby\" }, { \"begin\": \"\\\\b(ENV)\\\\[\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other.constant.ruby\" } }, \"end\": \"\\\\]\", \"name\": \"meta.environment-variable.ruby\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"match\": \"\\\\b[A-Z]\\\\w*(?=((\\\\.|::)[A-Za-z]|\\\\[))\", \"name\": \"support.class.ruby\" }, { \"match\": \"\\\\b(abort|at_exit|autoload[?]?|binding|callcc|caller|caller_locations|chomp|chop|eval|exec|exit|exit!|fork|format|gets|global_variables|gsub|lambda|load|local_variables|open|p|print|printf|proc|putc|puts|rand|readline|readlines|select|set_trace_func|sleep|spawn|sprintf|srand|sub|syscall|system|test|trace_var|trap|untrace_var|warn)(\\\\b|(?<=[?!]))(?![?!])\", \"name\": \"support.function.kernel.ruby\" }, { \"match\": \"\\\\b[A-Z]\\\\w*\\\\b\", \"name\": \"variable.other.constant.ruby\" }, { \"begin\": \"(?x)\\n(?=def\\\\b)\\n(?<=^|\\\\s)(def)\\\\s+\\n( (?>[a-zA-Z_]\\\\w*(?>\\\\.|::))?\\n(?>[a-zA-Z_]\\\\w*(?>[?!]|=(?!>))?\\n|===?|!=|!~|>[>=]?|<=>|<[<=]?|[%&`/\\\\|^]|\\\\*\\\\*?|=?~|[-+]@?|\\\\[\\\\]=?) )\\n\\\\s*(\\\\()\\n\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.def.ruby\" }, \"2\": { \"name\": \"entity.name.function.ruby\" }, \"3\": { \"name\": \"punctuation.definition.parameters.ruby\" } }, \"comment\": \"the method pattern comes from the symbol pattern, see there for a explaination\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.ruby\" } }, \"name\": \"meta.function.method.with-arguments.ruby\", \"patterns\": [{ \"begin\": \"(?=[&*_a-zA-Z])\", \"end\": \"(?=[,)])\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.type.variable.ruby\" }, \"2\": { \"name\": \"constant.other.symbol.hashkey.parameter.function.ruby\" }, \"3\": { \"name\": \"punctuation.definition.constant.ruby\" }, \"4\": { \"name\": \"variable.parameter.function.ruby\" } }, \"match\": \"\\\\G([&*]?)(?:([_a-zA-Z]\\\\w*(:))|([_a-zA-Z]\\\\w*))\" }, { \"include\": \"#parens\" }, { \"include\": \"#braces\" }, { \"include\": \"$self\" }] }], \"repository\": { \"braces\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.function.begin.ruby\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.ruby\" } }, \"patterns\": [{ \"include\": \"#parens\" }, { \"include\": \"#braces\" }, { \"include\": \"$self\" }] }, \"parens\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.function.begin.ruby\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.ruby\" } }, \"patterns\": [{ \"include\": \"#parens\" }, { \"include\": \"#braces\" }, { \"include\": \"$self\" }] } } }, { \"begin\": \"(?x)\\n(?=def\\\\b)\\n(?<=^|\\\\s)(def)\\\\s+\\n( (?>[a-zA-Z_]\\\\w*(?>\\\\.|::))?\\n(?>[a-zA-Z_]\\\\w*(?>[?!]|=(?!>))?\\n|===?|!=|!~|>[>=]?|<=>|<[<=]?|[%&`/\\\\|^]|\\\\*\\\\*?|=?~|[-+]@?|\\\\[\\\\]=?) )\\n[ \\\\t]\\n(?=[ \\\\t]*[^\\\\s#;])\\n\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.def.ruby\" }, \"2\": { \"name\": \"entity.name.function.ruby\" } }, \"comment\": \"same as the previous rule, but without parentheses around the arguments\", \"end\": \"$\", \"name\": \"meta.function.method.with-arguments.ruby\", \"patterns\": [{ \"begin\": \"(?![\\\\s,])\", \"end\": \"(?=,|$)\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.type.variable.ruby\" }, \"2\": { \"name\": \"constant.other.symbol.hashkey.parameter.function.ruby\" }, \"3\": { \"name\": \"punctuation.definition.constant.ruby\" }, \"4\": { \"name\": \"variable.parameter.function.ruby\" } }, \"match\": \"\\\\G([&*]?)(?:([_a-zA-Z]\\\\w*(:))|([_a-zA-Z]\\\\w*))\", \"name\": \"variable.parameter.function.ruby\" }, { \"include\": \"$self\" }] }] }, { \"captures\": { \"1\": { \"name\": \"keyword.control.def.ruby\" }, \"3\": { \"name\": \"entity.name.function.ruby\" } }, \"comment\": \" the optional name is just to catch the def also without a method-name\", \"match\": \"(?x)\\n(?=def\\\\b)\\n(?<=^|\\\\s)(def)\\\\b\\n( \\\\s+\\n( (?>[a-zA-Z_]\\\\w*(?>\\\\.|::))?\\n(?>[a-zA-Z_]\\\\w*(?>[?!]|=(?!>))?\\n|===?|!=|!~|>[>=]?|<=>|<[<=]?|[%&`/\\\\|^]|\\\\*\\\\*?|=?~|[-+]@?|\\\\[\\\\]=?) ) )?\\n\", \"name\": \"meta.function.method.without-arguments.ruby\" }, { \"match\": \"\\\\b\\\\d(?>_?\\\\d)*(?=\\\\.\\\\d|[eE])(\\\\.\\\\d(?>_?\\\\d)*)?([eE][-+]?\\\\d(?>_?\\\\d)*)?r?i?\\\\b\", \"name\": \"constant.numeric.float.ruby\" }, { \"match\": \"\\\\b(0|(0[dD]\\\\d|[1-9])(?>_?\\\\d)*)r?i?\\\\b\", \"name\": \"constant.numeric.integer.ruby\" }, { \"match\": \"\\\\b0[xX]\\\\h(?>_?\\\\h)*r?i?\\\\b\", \"name\": \"constant.numeric.hex.ruby\" }, { \"match\": \"\\\\b0[bB][01](?>_?[01])*r?i?\\\\b\", \"name\": \"constant.numeric.binary.ruby\" }, { \"match\": \"\\\\b0([oO]?[0-7](?>_?[0-7])*)?r?i?\\\\b\", \"name\": \"constant.numeric.octal.ruby\" }, { \"begin\": \":'\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.constant.ruby\" } }, \"end\": \"'\", \"name\": \"constant.other.symbol.single-quoted.ruby\", \"patterns\": [{ \"match\": \"\\\\\\\\['\\\\\\\\]\", \"name\": \"constant.character.escape.ruby\" }] }, { \"begin\": ':\"', \"captures\": { \"0\": { \"name\": \"punctuation.definition.constant.ruby\" } }, \"end\": '\"', \"name\": \"constant.other.symbol.double-quoted.ruby\", \"patterns\": [{ \"include\": \"#interpolated_ruby\" }, { \"include\": \"#escaped_char\" }] }, { \"comment\": \"Needs higher precedence than regular expressions.\", \"match\": \"(?<!\\\\()/=\", \"name\": \"keyword.operator.assignment.augmented.ruby\" }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"comment\": \"single quoted string (does not allow interpolation)\", \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.quoted.single.ruby\", \"patterns\": [{ \"match\": \"\\\\\\\\'|\\\\\\\\\\\\\\\\\", \"name\": \"constant.character.escape.ruby\" }] }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"comment\": \"double quoted string (allows for interpolation)\", \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.quoted.double.ruby\", \"patterns\": [{ \"include\": \"#interpolated_ruby\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": \"`\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"comment\": \"execute string (allows for interpolation)\", \"end\": \"`\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.interpolated.ruby\", \"patterns\": [{ \"include\": \"#interpolated_ruby\" }, { \"include\": \"#escaped_char\" }] }, { \"include\": \"#percent_literals\" }, { \"begin\": \"(?x)\\n(?:\\n^\\n| (?<=\\n[=>~(?:\\\\[,|&;]\\n| [\\\\s;]if\\\\s\\n| [\\\\s;]elsif\\\\s\\n| [\\\\s;]while\\\\s\\n| [\\\\s;]unless\\\\s\\n| [\\\\s;]when\\\\s\\n| [\\\\s;]assert_match\\\\s\\n| [\\\\s;]or\\\\s\\n| [\\\\s;]and\\\\s\\n| [\\\\s;]not\\\\s\\n| [\\\\s.]index\\\\s\\n| [\\\\s.]scan\\\\s\\n| [\\\\s.]sub\\\\s\\n| [\\\\s.]sub!\\\\s\\n| [\\\\s.]gsub\\\\s\\n| [\\\\s.]gsub!\\\\s\\n| [\\\\s.]match\\\\s\\n)\\n| (?<=\\n^when\\\\s\\n| ^if\\\\s\\n| ^elsif\\\\s\\n| ^while\\\\s\\n| ^unless\\\\s\\n)\\n)\\n\\\\s*((/))(?![*+{}?])\\n\", \"captures\": { \"1\": { \"name\": \"string.regexp.classic.ruby\" }, \"2\": { \"name\": \"punctuation.definition.string.ruby\" } }, \"comment\": \"regular expressions (normal)\\n\t\t\twe only start a regexp if the character before it (excluding whitespace)\\n\t\t\tis what we think is before a regexp\\n\t\t\t\", \"contentName\": \"string.regexp.classic.ruby\", \"end\": \"((/[eimnosux]*))\", \"patterns\": [{ \"include\": \"#regex_sub\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.ruby\" } }, \"comment\": \"symbols\", \"match\": \"(?<!:)(:)(?>[a-zA-Z_]\\\\w*(?>[?!]|=(?![>=]))?|===?|>[>=]?|<=>|<[<=]?|[%&`/\\\\|]|\\\\*\\\\*?|=?~|[-+]@?|\\\\[\\\\]=?|(@@?|\\\\$)[a-zA-Z_]\\\\w*)\", \"name\": \"constant.other.symbol.ruby\" }, { \"begin\": \"^=begin\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.ruby\" } }, \"comment\": \"multiline comments\", \"end\": \"^=end\", \"name\": \"comment.block.documentation.ruby\" }, { \"begin\": \"(^[ \\\\t]+)?(?=#)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.ruby\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.ruby\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.ruby\" }] }, { \"comment\": '\\n\t\t\tmatches questionmark-letters.\\n\\n\t\t\texamples (1st alternation = hex):\\n\t\t\t?\\\\x1     ?\\\\x61\\n\\n\t\t\texamples (2nd alternation = octal):\\n\t\t\t?\\\\0      ?\\\\07     ?\\\\017\\n\\n\t\t\texamples (3rd alternation = escaped):\\n\t\t\t?\\\\n      ?\\\\b\\n\\n\t\t\texamples (4th alternation = meta-ctrl):\\n\t\t\t?\\\\C-a    ?\\\\M-a    ?\\\\C-\\\\M-\\\\C-\\\\M-a\\n\\n\t\t\texamples (4th alternation = normal):\\n\t\t\t?a       ?A       ?0 \\n\t\t\t?*       ?\"       ?( \\n\t\t\t?.       ?#\\n\t\t\t\\n\t\t\t\\n\t\t\tthe negative lookbehind prevents against matching\\n\t\t\tp(42.tainted?)\\n\t\t\t', \"match\": \"(?<!\\\\w)\\\\?(\\\\\\\\(x\\\\h{1,2}(?!\\\\h)\\\\b|0[0-7]{0,2}(?![0-7])\\\\b|[^x0MC])|(\\\\\\\\[MC]-)+\\\\w|[^\\\\s\\\\\\\\])\", \"name\": \"constant.numeric.ruby\" }, { \"begin\": '(?=(?><<[-~](\"?)((?:[_\\\\w]+_|)HTML)\\\\b\\\\1))', \"comment\": \"Heredoc with embedded html\", \"end\": \"(?!\\\\G)\", \"name\": \"meta.embedded.block.html\", \"patterns\": [{ \"begin\": '(?><<[-~](\"?)((?:[_\\\\w]+_|)HTML)\\\\b\\\\1)', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"contentName\": \"text.html\", \"end\": \"\\\\s*\\\\2$\\\\n?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.unquoted.heredoc.ruby\", \"patterns\": [{ \"include\": \"#heredoc\" }, { \"include\": \"#interpolated_ruby\" }, { \"include\": \"text.html.basic\" }, { \"include\": \"#escaped_char\" }] }] }, { \"begin\": '(?=(?><<[-~](\"?)((?:[_\\\\w]+_|)XML)\\\\b\\\\1))', \"comment\": \"Heredoc with embedded xml\", \"end\": \"(?!\\\\G)\", \"name\": \"meta.embedded.block.xml\", \"patterns\": [{ \"begin\": '(?><<[-~](\"?)((?:[_\\\\w]+_|)XML)\\\\b\\\\1)', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"contentName\": \"text.xml\", \"end\": \"\\\\s*\\\\2$\\\\n?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.unquoted.heredoc.ruby\", \"patterns\": [{ \"include\": \"#heredoc\" }, { \"include\": \"#interpolated_ruby\" }, { \"include\": \"text.xml\" }, { \"include\": \"#escaped_char\" }] }] }, { \"begin\": '(?=(?><<[-~](\"?)((?:[_\\\\w]+_|)SQL)\\\\b\\\\1))', \"comment\": \"Heredoc with embedded sql\", \"end\": \"(?!\\\\G)\", \"name\": \"meta.embedded.block.sql\", \"patterns\": [{ \"begin\": '(?><<[-~](\"?)((?:[_\\\\w]+_|)SQL)\\\\b\\\\1)', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"contentName\": \"source.sql\", \"end\": \"\\\\s*\\\\2$\\\\n?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.unquoted.heredoc.ruby\", \"patterns\": [{ \"include\": \"#heredoc\" }, { \"include\": \"#interpolated_ruby\" }, { \"include\": \"source.sql\" }, { \"include\": \"#escaped_char\" }] }] }, { \"begin\": '(?=(?><<[-~](\"?)((?:[_\\\\w]+_|)CSS)\\\\b\\\\1))', \"comment\": \"Heredoc with embedded css\", \"end\": \"(?!\\\\G)\", \"name\": \"meta.embedded.block.css\", \"patterns\": [{ \"begin\": '(?><<[-~](\"?)((?:[_\\\\w]+_|)CSS)\\\\b\\\\1)', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"contentName\": \"source.css\", \"end\": \"\\\\s*\\\\2$\\\\n?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.unquoted.heredoc.ruby\", \"patterns\": [{ \"include\": \"#heredoc\" }, { \"include\": \"#interpolated_ruby\" }, { \"include\": \"source.css\" }, { \"include\": \"#escaped_char\" }] }] }, { \"begin\": '(?=(?><<[-~](\"?)((?:[_\\\\w]+_|)CPP)\\\\b\\\\1))', \"comment\": \"Heredoc with embedded c++\", \"end\": \"(?!\\\\G)\", \"name\": \"meta.embedded.block.c++\", \"patterns\": [{ \"begin\": '(?><<[-~](\"?)((?:[_\\\\w]+_|)CPP)\\\\b\\\\1)', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"contentName\": \"source.c++\", \"end\": \"\\\\s*\\\\2$\\\\n?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.unquoted.heredoc.ruby\", \"patterns\": [{ \"include\": \"#heredoc\" }, { \"include\": \"#interpolated_ruby\" }, { \"include\": \"source.c++\" }, { \"include\": \"#escaped_char\" }] }] }, { \"begin\": '(?=(?><<[-~](\"?)((?:[_\\\\w]+_|)C)\\\\b\\\\1))', \"comment\": \"Heredoc with embedded c\", \"end\": \"(?!\\\\G)\", \"name\": \"meta.embedded.block.c\", \"patterns\": [{ \"begin\": '(?><<[-~](\"?)((?:[_\\\\w]+_|)C)\\\\b\\\\1)', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"contentName\": \"source.c\", \"end\": \"\\\\s*\\\\2$\\\\n?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.unquoted.heredoc.ruby\", \"patterns\": [{ \"include\": \"#heredoc\" }, { \"include\": \"#interpolated_ruby\" }, { \"include\": \"source.c\" }, { \"include\": \"#escaped_char\" }] }] }, { \"begin\": '(?=(?><<[-~](\"?)((?:[_\\\\w]+_|)(?:JS|JAVASCRIPT))\\\\b\\\\1))', \"comment\": \"Heredoc with embedded javascript\", \"end\": \"(?!\\\\G)\", \"name\": \"meta.embedded.block.js\", \"patterns\": [{ \"begin\": '(?><<[-~](\"?)((?:[_\\\\w]+_|)(?:JS|JAVASCRIPT))\\\\b\\\\1)', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"contentName\": \"source.js\", \"end\": \"\\\\s*\\\\2$\\\\n?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.unquoted.heredoc.ruby\", \"patterns\": [{ \"include\": \"#heredoc\" }, { \"include\": \"#interpolated_ruby\" }, { \"include\": \"source.js\" }, { \"include\": \"#escaped_char\" }] }] }, { \"begin\": '(?=(?><<[-~](\"?)((?:[_\\\\w]+_|)JQUERY)\\\\b\\\\1))', \"comment\": \"Heredoc with embedded jQuery javascript\", \"end\": \"(?!\\\\G)\", \"name\": \"meta.embedded.block.js.jquery\", \"patterns\": [{ \"begin\": '(?><<[-~](\"?)((?:[_\\\\w]+_|)JQUERY)\\\\b\\\\1)', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"contentName\": \"source.js.jquery\", \"end\": \"\\\\s*\\\\2$\\\\n?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.unquoted.heredoc.ruby\", \"patterns\": [{ \"include\": \"#heredoc\" }, { \"include\": \"#interpolated_ruby\" }, { \"include\": \"source.js.jquery\" }, { \"include\": \"#escaped_char\" }] }] }, { \"begin\": '(?=(?><<[-~](\"?)((?:[_\\\\w]+_|)(?:SH|SHELL))\\\\b\\\\1))', \"comment\": \"Heredoc with embedded shell\", \"end\": \"(?!\\\\G)\", \"name\": \"meta.embedded.block.shell\", \"patterns\": [{ \"begin\": '(?><<[-~](\"?)((?:[_\\\\w]+_|)(?:SH|SHELL))\\\\b\\\\1)', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"contentName\": \"source.shell\", \"end\": \"\\\\s*\\\\2$\\\\n?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.unquoted.heredoc.ruby\", \"patterns\": [{ \"include\": \"#heredoc\" }, { \"include\": \"#interpolated_ruby\" }, { \"include\": \"source.shell\" }, { \"include\": \"#escaped_char\" }] }] }, { \"begin\": '(?=(?><<[-~](\"?)((?:[_\\\\w]+_|)LUA)\\\\b\\\\1))', \"comment\": \"Heredoc with embedded lua\", \"end\": \"(?!\\\\G)\", \"name\": \"meta.embedded.block.lua\", \"patterns\": [{ \"begin\": '(?><<[-~](\"?)((?:[_\\\\w]+_|)LUA)\\\\b\\\\1)', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"contentName\": \"source.lua\", \"end\": \"\\\\s*\\\\2$\\\\n?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.unquoted.heredoc.ruby\", \"patterns\": [{ \"include\": \"#heredoc\" }, { \"include\": \"#interpolated_ruby\" }, { \"include\": \"source.lua\" }, { \"include\": \"#escaped_char\" }] }] }, { \"begin\": '(?=(?><<[-~](\"?)((?:[_\\\\w]+_|)RUBY)\\\\b\\\\1))', \"comment\": \"Heredoc with embedded ruby\", \"end\": \"(?!\\\\G)\", \"name\": \"meta.embedded.block.ruby\", \"patterns\": [{ \"begin\": '(?><<[-~](\"?)((?:[_\\\\w]+_|)RUBY)\\\\b\\\\1)', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"contentName\": \"source.ruby\", \"end\": \"\\\\s*\\\\2$\\\\n?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.unquoted.heredoc.ruby\", \"patterns\": [{ \"include\": \"#heredoc\" }, { \"include\": \"#interpolated_ruby\" }, { \"include\": \"source.ruby\" }, { \"include\": \"#escaped_char\" }] }] }, { \"begin\": \"(?>=\\\\s*<<(\\\\w+))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"end\": \"^\\\\1$\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.unquoted.heredoc.ruby\", \"patterns\": [{ \"include\": \"#heredoc\" }, { \"include\": \"#interpolated_ruby\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": \"(?><<[-~](\\\\w+))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"comment\": \"heredoc with indented terminator\", \"end\": \"\\\\s*\\\\1$\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.unquoted.heredoc.ruby\", \"patterns\": [{ \"include\": \"#heredoc\" }, { \"include\": \"#interpolated_ruby\" }, { \"include\": \"#escaped_char\" }] }, { \"begin\": \"(?<=\\\\{|do|\\\\{\\\\s|do\\\\s)(\\\\|)\", \"captures\": { \"1\": { \"name\": \"punctuation.separator.arguments.ruby\" } }, \"end\": \"(?<!\\\\|)(\\\\|)(?!\\\\|)\", \"patterns\": [{ \"include\": \"$self\" }, { \"match\": \"[_a-zA-Z][_a-zA-Z0-9]*\", \"name\": \"variable.other.block.ruby\" }, { \"match\": \",\", \"name\": \"punctuation.separator.variable.ruby\" }] }, { \"match\": \"=>\", \"name\": \"punctuation.separator.key-value\" }, { \"match\": \"->\", \"name\": \"support.function.kernel.lambda.ruby\" }, { \"match\": \"<<=|%=|&{1,2}=|\\\\*=|\\\\*\\\\*=|\\\\+=|-=|\\\\^=|\\\\|{1,2}=|<<\", \"name\": \"keyword.operator.assignment.augmented.ruby\" }, { \"match\": \"<=>|<(?!<|=)|>(?!<|=|>)|<=|>=|===|==|=~|!=|!~|(?<=[ \\\\t])\\\\?\", \"name\": \"keyword.operator.comparison.ruby\" }, { \"match\": \"(?<!\\\\.)\\\\b(and|not|or)\\\\b(?![?!])\", \"name\": \"keyword.operator.logical.ruby\" }, { \"comment\": \"Make sure this goes after assignment and comparison\", \"match\": \"(?<=^|[ \\\\t])!|&&|\\\\|\\\\||\\\\^\", \"name\": \"keyword.operator.logical.ruby\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.method.ruby\" } }, \"comment\": \"Safe navigation operator - Added in 2.3\", \"match\": \"(&\\\\.)\\\\s*(?![A-Z])\" }, { \"match\": \"(%|&|\\\\*\\\\*|\\\\*|\\\\+|-|/)\", \"name\": \"keyword.operator.arithmetic.ruby\" }, { \"match\": \"=\", \"name\": \"keyword.operator.assignment.ruby\" }, { \"match\": \"\\\\||~|>>\", \"name\": \"keyword.operator.other.ruby\" }, { \"match\": \";\", \"name\": \"punctuation.separator.statement.ruby\" }, { \"match\": \",\", \"name\": \"punctuation.separator.object.ruby\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.namespace.ruby\" } }, \"comment\": \"Mark as namespace separator if double colons followed by capital letter\", \"match\": \"(::)\\\\s*(?=[A-Z])\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.method.ruby\" } }, \"comment\": \"Mark as method separator if double colons not followed by capital letter\", \"match\": \"(\\\\.|::)\\\\s*(?![A-Z])\" }, { \"comment\": \"Must come after method and constant separators to prefer double colons\", \"match\": \":\", \"name\": \"punctuation.separator.other.ruby\" }, { \"match\": \"\\\\{\", \"name\": \"punctuation.section.scope.begin.ruby\" }, { \"match\": \"\\\\}\", \"name\": \"punctuation.section.scope.end.ruby\" }, { \"match\": \"\\\\[\", \"name\": \"punctuation.section.array.begin.ruby\" }, { \"match\": \"\\\\]\", \"name\": \"punctuation.section.array.end.ruby\" }, { \"match\": \"\\\\(|\\\\)\", \"name\": \"punctuation.section.function.ruby\" }], \"repository\": { \"escaped_char\": { \"match\": \"\\\\\\\\(?:[0-7]{1,3}|x[\\\\da-fA-F]{1,2}|.)\", \"name\": \"constant.character.escape.ruby\" }, \"heredoc\": { \"begin\": \"^<<[-~]?\\\\w+\", \"end\": \"$\", \"patterns\": [{ \"include\": \"$self\" }] }, \"interpolated_ruby\": { \"patterns\": [{ \"begin\": \"#\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.ruby\" } }, \"contentName\": \"source.ruby\", \"end\": \"(\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.ruby\" }, \"1\": { \"name\": \"source.ruby\" } }, \"name\": \"meta.embedded.line.ruby\", \"patterns\": [{ \"include\": \"#nest_curly_and_self\" }, { \"include\": \"$self\" }], \"repository\": { \"nest_curly_and_self\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"captures\": { \"0\": { \"name\": \"punctuation.section.scope.ruby\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#nest_curly_and_self\" }] }, { \"include\": \"$self\" }] } } }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.ruby\" } }, \"match\": \"(#@)[a-zA-Z_]\\\\w*\", \"name\": \"variable.other.readwrite.instance.ruby\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.ruby\" } }, \"match\": \"(#@@)[a-zA-Z_]\\\\w*\", \"name\": \"variable.other.readwrite.class.ruby\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.ruby\" } }, \"match\": \"(#\\\\$)[a-zA-Z_]\\\\w*\", \"name\": \"variable.other.readwrite.global.ruby\" }] }, \"percent_literals\": { \"patterns\": [{ \"begin\": \"%i(?:([(\\\\[{<])|([^\\\\w\\\\s]|_))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.array.begin.ruby\" } }, \"end\": \"[)\\\\]}>]\\\\2|\\\\1\\\\2\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.array.end.ruby\" } }, \"name\": \"meta.array.symbol.ruby\", \"patterns\": [{ \"begin\": \"\\\\G(?<=\\\\()(?!\\\\))\", \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#parens\" }, { \"include\": \"#symbol\" }] }, { \"begin\": \"\\\\G(?<=\\\\[)(?!\\\\])\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"include\": \"#brackets\" }, { \"include\": \"#symbol\" }] }, { \"begin\": \"\\\\G(?<=\\\\{)(?!\\\\})\", \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#braces\" }, { \"include\": \"#symbol\" }] }, { \"begin\": \"\\\\G(?<=<)(?!>)\", \"end\": \"(?=>)\", \"patterns\": [{ \"include\": \"#angles\" }, { \"include\": \"#symbol\" }] }, { \"include\": \"#symbol\" }], \"repository\": { \"angles\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"constant.character.escape.ruby\" } }, \"match\": \"\\\\\\\\<|\\\\\\\\>\", \"name\": \"constant.other.symbol.ruby\" }, { \"begin\": \"<\", \"captures\": { \"0\": { \"name\": \"constant.other.symbol.ruby\" } }, \"end\": \">\", \"patterns\": [{ \"include\": \"#angles\" }, { \"include\": \"#symbol\" }] }] }, \"braces\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"constant.character.escape.ruby\" } }, \"match\": \"\\\\\\\\\\\\{|\\\\\\\\\\\\}\", \"name\": \"constant.other.symbol.ruby\" }, { \"begin\": \"\\\\{\", \"captures\": { \"0\": { \"name\": \"constant.other.symbol.ruby\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#braces\" }, { \"include\": \"#symbol\" }] }] }, \"brackets\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"constant.character.escape.ruby\" } }, \"match\": \"\\\\\\\\\\\\[|\\\\\\\\\\\\]\", \"name\": \"constant.other.symbol.ruby\" }, { \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"constant.other.symbol.ruby\" } }, \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#brackets\" }, { \"include\": \"#symbol\" }] }] }, \"parens\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"constant.character.escape.ruby\" } }, \"match\": \"\\\\\\\\\\\\(|\\\\\\\\\\\\)\", \"name\": \"constant.other.symbol.ruby\" }, { \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"constant.other.symbol.ruby\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#parens\" }, { \"include\": \"#symbol\" }] }] }, \"symbol\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"constant.character.escape.ruby\" } }, \"match\": \"\\\\\\\\\\\\\\\\|\\\\\\\\[ ]\", \"name\": \"constant.other.symbol.ruby\" }, { \"match\": \"\\\\S\\\\w*\", \"name\": \"constant.other.symbol.ruby\" }] } } }, { \"begin\": \"%I(?:([(\\\\[{<])|([^\\\\w\\\\s]|_))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.array.begin.ruby\" } }, \"end\": \"[)\\\\]}>]\\\\2|\\\\1\\\\2\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.array.end.ruby\" } }, \"name\": \"meta.array.symbol.interpolated.ruby\", \"patterns\": [{ \"begin\": \"\\\\G(?<=\\\\()(?!\\\\))\", \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#parens\" }, { \"include\": \"#symbol\" }] }, { \"begin\": \"\\\\G(?<=\\\\[)(?!\\\\])\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"include\": \"#brackets\" }, { \"include\": \"#symbol\" }] }, { \"begin\": \"\\\\G(?<=\\\\{)(?!\\\\})\", \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#braces\" }, { \"include\": \"#symbol\" }] }, { \"begin\": \"\\\\G(?<=<)(?!>)\", \"end\": \"(?=>)\", \"patterns\": [{ \"include\": \"#angles\" }, { \"include\": \"#symbol\" }] }, { \"include\": \"#symbol\" }], \"repository\": { \"angles\": { \"patterns\": [{ \"begin\": \"<\", \"captures\": { \"0\": { \"name\": \"constant.other.symbol.ruby\" } }, \"end\": \">\", \"patterns\": [{ \"include\": \"#angles\" }, { \"include\": \"#symbol\" }] }] }, \"braces\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"captures\": { \"0\": { \"name\": \"constant.other.symbol.ruby\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#braces\" }, { \"include\": \"#symbol\" }] }] }, \"brackets\": { \"patterns\": [{ \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"constant.other.symbol.ruby\" } }, \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#brackets\" }, { \"include\": \"#symbol\" }] }] }, \"parens\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"constant.other.symbol.ruby\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#parens\" }, { \"include\": \"#symbol\" }] }] }, \"symbol\": { \"patterns\": [{ \"begin\": \"(?=\\\\\\\\|#\\\\{)\", \"end\": \"(?!\\\\G)\", \"name\": \"constant.other.symbol.ruby\", \"patterns\": [{ \"include\": \"#escaped_char\" }, { \"include\": \"#interpolated_ruby\" }] }, { \"match\": \"\\\\S\\\\w*\", \"name\": \"constant.other.symbol.ruby\" }] } } }, { \"begin\": \"%q(?:([(\\\\[{<])|([^\\\\w\\\\s]|_))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"end\": \"[)\\\\]}>]\\\\2|\\\\1\\\\2\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.quoted.other.ruby\", \"patterns\": [{ \"begin\": \"\\\\G(?<=\\\\()(?!\\\\))\", \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#parens\" }] }, { \"begin\": \"\\\\G(?<=\\\\[)(?!\\\\])\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"include\": \"#brackets\" }] }, { \"begin\": \"\\\\G(?<=\\\\{)(?!\\\\})\", \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#braces\" }] }, { \"begin\": \"\\\\G(?<=<)(?!>)\", \"end\": \"(?=>)\", \"patterns\": [{ \"include\": \"#angles\" }] }], \"repository\": { \"angles\": { \"patterns\": [{ \"match\": \"\\\\\\\\<|\\\\\\\\>|\\\\\\\\\\\\\\\\\", \"name\": \"constant.character.escape.ruby\" }, { \"begin\": \"<\", \"end\": \">\", \"patterns\": [{ \"include\": \"#angles\" }] }] }, \"braces\": { \"patterns\": [{ \"match\": \"\\\\\\\\\\\\{|\\\\\\\\\\\\}|\\\\\\\\\\\\\\\\\", \"name\": \"constant.character.escape.ruby\" }, { \"begin\": \"\\\\{\", \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#braces\" }] }] }, \"brackets\": { \"patterns\": [{ \"match\": \"\\\\\\\\\\\\[|\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\\", \"name\": \"constant.character.escape.ruby\" }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#brackets\" }] }] }, \"parens\": { \"patterns\": [{ \"match\": \"\\\\\\\\\\\\(|\\\\\\\\\\\\)|\\\\\\\\\\\\\\\\\", \"name\": \"constant.character.escape.ruby\" }, { \"begin\": \"\\\\(\", \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#parens\" }] }] } } }, { \"begin\": \"%Q?(?:([(\\\\[{<])|([^\\\\w\\\\s=]|_))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"end\": \"[)\\\\]}>]\\\\2|\\\\1\\\\2\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.quoted.other.interpolated.ruby\", \"patterns\": [{ \"begin\": \"\\\\G(?<=\\\\()(?!\\\\))\", \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#parens\" }] }, { \"begin\": \"\\\\G(?<=\\\\[)(?!\\\\])\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"include\": \"#brackets\" }] }, { \"begin\": \"\\\\G(?<=\\\\{)(?!\\\\})\", \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#braces\" }] }, { \"begin\": \"\\\\G(?<=<)(?!>)\", \"end\": \"(?=>)\", \"patterns\": [{ \"include\": \"#angles\" }] }, { \"include\": \"#escaped_char\" }, { \"include\": \"#interpolated_ruby\" }], \"repository\": { \"angles\": { \"patterns\": [{ \"include\": \"#escaped_char\" }, { \"include\": \"#interpolated_ruby\" }, { \"begin\": \"<\", \"end\": \">\", \"patterns\": [{ \"include\": \"#angles\" }] }] }, \"braces\": { \"patterns\": [{ \"include\": \"#escaped_char\" }, { \"include\": \"#interpolated_ruby\" }, { \"begin\": \"\\\\{\", \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#braces\" }] }] }, \"brackets\": { \"patterns\": [{ \"include\": \"#escaped_char\" }, { \"include\": \"#interpolated_ruby\" }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#brackets\" }] }] }, \"parens\": { \"patterns\": [{ \"include\": \"#escaped_char\" }, { \"include\": \"#interpolated_ruby\" }, { \"begin\": \"\\\\(\", \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#parens\" }] }] } } }, { \"begin\": \"%r(?:([(\\\\[{<])|([^\\\\w\\\\s]|_))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"end\": \"([)\\\\]}>]\\\\2|\\\\1\\\\2)[eimnosux]*\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.regexp.percent.ruby\", \"patterns\": [{ \"begin\": \"\\\\G(?<=\\\\()(?!\\\\))\", \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#parens\" }] }, { \"begin\": \"\\\\G(?<=\\\\[)(?!\\\\])\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"include\": \"#brackets\" }] }, { \"begin\": \"\\\\G(?<=\\\\{)(?!\\\\})\", \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#braces\" }] }, { \"begin\": \"\\\\G(?<=<)(?!>)\", \"end\": \"(?=>)\", \"patterns\": [{ \"include\": \"#angles\" }] }, { \"include\": \"#regex_sub\" }], \"repository\": { \"angles\": { \"patterns\": [{ \"include\": \"#regex_sub\" }, { \"begin\": \"<\", \"end\": \">\", \"patterns\": [{ \"include\": \"#angles\" }] }] }, \"braces\": { \"patterns\": [{ \"include\": \"#regex_sub\" }, { \"begin\": \"\\\\{\", \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#braces\" }] }] }, \"brackets\": { \"patterns\": [{ \"include\": \"#regex_sub\" }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#brackets\" }] }] }, \"parens\": { \"patterns\": [{ \"include\": \"#regex_sub\" }, { \"begin\": \"\\\\(\", \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#parens\" }] }] } } }, { \"begin\": \"%s(?:([(\\\\[{<])|([^\\\\w\\\\s]|_))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.constant.begin.ruby\" } }, \"end\": \"[)\\\\]}>]\\\\2|\\\\1\\\\2\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.constant.end.ruby\" } }, \"name\": \"constant.other.symbol.percent.ruby\", \"patterns\": [{ \"begin\": \"\\\\G(?<=\\\\()(?!\\\\))\", \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#parens\" }] }, { \"begin\": \"\\\\G(?<=\\\\[)(?!\\\\])\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"include\": \"#brackets\" }] }, { \"begin\": \"\\\\G(?<=\\\\{)(?!\\\\})\", \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#braces\" }] }, { \"begin\": \"\\\\G(?<=<)(?!>)\", \"end\": \"(?=>)\", \"patterns\": [{ \"include\": \"#angles\" }] }], \"repository\": { \"angles\": { \"patterns\": [{ \"match\": \"\\\\\\\\<|\\\\\\\\>|\\\\\\\\\\\\\\\\\", \"name\": \"constant.character.escape.ruby\" }, { \"begin\": \"<\", \"end\": \">\", \"patterns\": [{ \"include\": \"#angles\" }] }] }, \"braces\": { \"patterns\": [{ \"match\": \"\\\\\\\\\\\\{|\\\\\\\\\\\\}|\\\\\\\\\\\\\\\\\", \"name\": \"constant.character.escape.ruby\" }, { \"begin\": \"\\\\{\", \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#braces\" }] }] }, \"brackets\": { \"patterns\": [{ \"match\": \"\\\\\\\\\\\\[|\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\\", \"name\": \"constant.character.escape.ruby\" }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#brackets\" }] }] }, \"parens\": { \"patterns\": [{ \"match\": \"\\\\\\\\\\\\(|\\\\\\\\\\\\)|\\\\\\\\\\\\\\\\\", \"name\": \"constant.character.escape.ruby\" }, { \"begin\": \"\\\\(\", \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#parens\" }] }] } } }, { \"begin\": \"%w(?:([(\\\\[{<])|([^\\\\w\\\\s]|_))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.array.begin.ruby\" } }, \"end\": \"[)\\\\]}>]\\\\2|\\\\1\\\\2\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.array.end.ruby\" } }, \"name\": \"meta.array.string.ruby\", \"patterns\": [{ \"begin\": \"\\\\G(?<=\\\\()(?!\\\\))\", \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#parens\" }, { \"include\": \"#string\" }] }, { \"begin\": \"\\\\G(?<=\\\\[)(?!\\\\])\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"include\": \"#brackets\" }, { \"include\": \"#string\" }] }, { \"begin\": \"\\\\G(?<=\\\\{)(?!\\\\})\", \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#braces\" }, { \"include\": \"#string\" }] }, { \"begin\": \"\\\\G(?<=<)(?!>)\", \"end\": \"(?=>)\", \"patterns\": [{ \"include\": \"#angles\" }, { \"include\": \"#string\" }] }, { \"include\": \"#string\" }], \"repository\": { \"angles\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"constant.character.escape.ruby\" } }, \"match\": \"\\\\\\\\<|\\\\\\\\>\", \"name\": \"string.other.ruby\" }, { \"begin\": \"<\", \"captures\": { \"0\": { \"name\": \"string.other.ruby\" } }, \"end\": \">\", \"patterns\": [{ \"include\": \"#angles\" }, { \"include\": \"#string\" }] }] }, \"braces\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"constant.character.escape.ruby\" } }, \"match\": \"\\\\\\\\\\\\{|\\\\\\\\\\\\}\", \"name\": \"string.other.ruby\" }, { \"begin\": \"\\\\{\", \"captures\": { \"0\": { \"name\": \"string.other.ruby\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#braces\" }, { \"include\": \"#string\" }] }] }, \"brackets\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"constant.character.escape.ruby\" } }, \"match\": \"\\\\\\\\\\\\[|\\\\\\\\\\\\]\", \"name\": \"string.other.ruby\" }, { \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"string.other.ruby\" } }, \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#brackets\" }, { \"include\": \"#string\" }] }] }, \"parens\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"constant.character.escape.ruby\" } }, \"match\": \"\\\\\\\\\\\\(|\\\\\\\\\\\\)\", \"name\": \"string.other.ruby\" }, { \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"string.other.ruby\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#parens\" }, { \"include\": \"#string\" }] }] }, \"string\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"constant.character.escape.ruby\" } }, \"match\": \"\\\\\\\\\\\\\\\\|\\\\\\\\[ ]\", \"name\": \"string.other.ruby\" }, { \"match\": \"\\\\S\\\\w*\", \"name\": \"string.other.ruby\" }] } } }, { \"begin\": \"%W(?:([(\\\\[{<])|([^\\\\w\\\\s]|_))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.array.begin.ruby\" } }, \"end\": \"[)\\\\]}>]\\\\2|\\\\1\\\\2\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.array.end.ruby\" } }, \"name\": \"meta.array.string.interpolated.ruby\", \"patterns\": [{ \"begin\": \"\\\\G(?<=\\\\()(?!\\\\))\", \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#parens\" }, { \"include\": \"#string\" }] }, { \"begin\": \"\\\\G(?<=\\\\[)(?!\\\\])\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"include\": \"#brackets\" }, { \"include\": \"#string\" }] }, { \"begin\": \"\\\\G(?<=\\\\{)(?!\\\\})\", \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#braces\" }, { \"include\": \"#string\" }] }, { \"begin\": \"\\\\G(?<=<)(?!>)\", \"end\": \"(?=>)\", \"patterns\": [{ \"include\": \"#angles\" }, { \"include\": \"#string\" }] }, { \"include\": \"#string\" }], \"repository\": { \"angles\": { \"patterns\": [{ \"begin\": \"<\", \"captures\": { \"0\": { \"name\": \"string.other.ruby\" } }, \"end\": \">\", \"patterns\": [{ \"include\": \"#angles\" }, { \"include\": \"#string\" }] }] }, \"braces\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"captures\": { \"0\": { \"name\": \"string.other.ruby\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#braces\" }, { \"include\": \"#string\" }] }] }, \"brackets\": { \"patterns\": [{ \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"string.other.ruby\" } }, \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#brackets\" }, { \"include\": \"#string\" }] }] }, \"parens\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"string.other.ruby\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#parens\" }, { \"include\": \"#string\" }] }] }, \"string\": { \"patterns\": [{ \"begin\": \"(?=\\\\\\\\|#\\\\{)\", \"end\": \"(?!\\\\G)\", \"name\": \"string.other.ruby\", \"patterns\": [{ \"include\": \"#escaped_char\" }, { \"include\": \"#interpolated_ruby\" }] }, { \"match\": \"\\\\S\\\\w*\", \"name\": \"string.other.ruby\" }] } } }, { \"begin\": \"%x(?:([(\\\\[{<])|([^\\\\w\\\\s]|_))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ruby\" } }, \"end\": \"[)\\\\]}>]\\\\2|\\\\1\\\\2\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ruby\" } }, \"name\": \"string.interpolated.percent.ruby\", \"patterns\": [{ \"begin\": \"\\\\G(?<=\\\\()(?!\\\\))\", \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#parens\" }] }, { \"begin\": \"\\\\G(?<=\\\\[)(?!\\\\])\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"include\": \"#brackets\" }] }, { \"begin\": \"\\\\G(?<=\\\\{)(?!\\\\})\", \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#braces\" }] }, { \"begin\": \"\\\\G(?<=<)(?!>)\", \"end\": \"(?=>)\", \"patterns\": [{ \"include\": \"#angles\" }] }, { \"include\": \"#escaped_char\" }, { \"include\": \"#interpolated_ruby\" }], \"repository\": { \"angles\": { \"patterns\": [{ \"include\": \"#escaped_char\" }, { \"include\": \"#interpolated_ruby\" }, { \"begin\": \"<\", \"end\": \">\", \"patterns\": [{ \"include\": \"#angles\" }] }] }, \"braces\": { \"patterns\": [{ \"include\": \"#escaped_char\" }, { \"include\": \"#interpolated_ruby\" }, { \"begin\": \"\\\\{\", \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#braces\" }] }] }, \"brackets\": { \"patterns\": [{ \"include\": \"#escaped_char\" }, { \"include\": \"#interpolated_ruby\" }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#brackets\" }] }] }, \"parens\": { \"patterns\": [{ \"include\": \"#escaped_char\" }, { \"include\": \"#interpolated_ruby\" }, { \"begin\": \"\\\\(\", \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#parens\" }] }] } } }] }, \"regex_sub\": { \"patterns\": [{ \"include\": \"#interpolated_ruby\" }, { \"include\": \"#escaped_char\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.quantifier.begin.ruby\" }, \"3\": { \"name\": \"punctuation.definition.quantifier.end.ruby\" } }, \"match\": \"(\\\\{)\\\\d+(,\\\\d+)?(\\\\})\", \"name\": \"keyword.operator.quantifier.ruby\" }, { \"begin\": \"\\\\[\\\\^?\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.character-class.begin.ruby\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.character-class.end.ruby\" } }, \"name\": \"constant.other.character-class.set.ruby\", \"patterns\": [{ \"include\": \"#escaped_char\" }] }, { \"begin\": \"\\\\(\\\\?#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.ruby\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.ruby\" } }, \"name\": \"comment.line.number-sign.ruby\", \"patterns\": [{ \"include\": \"#escaped_char\" }] }, { \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.group.ruby\" } }, \"end\": \"\\\\)\", \"name\": \"meta.group.regexp.ruby\", \"patterns\": [{ \"include\": \"#regex_sub\" }] }, { \"begin\": \"(?<=^|\\\\s)(#)\\\\s(?=[[a-zA-Z0-9,. \\\\t?!-][^\\\\x{00}-\\\\x{7F}]]*$)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.ruby\" } }, \"comment\": \"We are restrictive in what we allow to go after the comment character to avoid false positives, since the availability of comments depend on regexp flags.\", \"end\": \"$\\\\n?\", \"name\": \"comment.line.number-sign.ruby\" }] } }, \"scopeName\": \"source.ruby\", \"embeddedLangs\": [\"html\", \"xml\", \"sql\", \"css\", \"c\", \"javascript\", \"shellscript\", \"lua\"], \"aliases\": [\"rb\"] });\nvar ruby = [\n  ...html,\n  ...xml,\n  ...sql,\n  ...css,\n  ...c,\n  ...javascript,\n  ...shellscript,\n  ...lua,\n  lang\n];\n\nexport { ruby as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,QAAQ,QAAQ,QAAQ,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,oGAAoG,QAAQ,kBAAkB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,iDAAiD,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,kDAAkD,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,SAAS,gFAAgF,QAAQ,mBAAmB,GAAG,EAAE,WAAW,qIAA0I,SAAS,8BAA8B,QAAQ,0BAA0B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,WAAW,oCAAoC,SAAS,sCAAsC,QAAQ,qCAAqC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,WAAW,oCAAoC,SAAS,iDAAiD,QAAQ,qCAAqC,GAAG,EAAE,WAAW,4EAA4E,SAAS,iIAAiI,QAAQ,uBAAuB,GAAG,EAAE,WAAW,sDAAsD,SAAS,oBAAoB,QAAQ,mCAAmC,GAAG,EAAE,WAAW,iCAAiC,SAAS,kBAAkB,QAAQ,+BAA+B,GAAG,EAAE,SAAS,8IAA8I,QAAQ,qCAAqC,GAAG,EAAE,SAAS,kCAAkC,QAAQ,yBAAyB,GAAG,EAAE,SAAS,uCAAuC,QAAQ,yBAAyB,GAAG,EAAE,SAAS,eAAe,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,WAAW,kBAAkB,eAAe,cAAc,OAAO,qBAAqB,YAAY,CAAC,EAAE,SAAS,iDAAiD,OAAO,qBAAqB,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,wBAAwB,QAAQ,8BAA8B,GAAG,EAAE,WAAW,mEAAmE,SAAS,+NAA+N,QAAQ,oCAAoC,GAAG,EAAE,SAAS,+CAA+C,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,eAAe,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,oBAAoB,QAAQ,yCAAyC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,qBAAqB,QAAQ,sCAAsC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,sBAAsB,QAAQ,uCAAuC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,qFAAqF,QAAQ,mDAAmD,GAAG,EAAE,SAAS,eAAe,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,OAAO,QAAQ,kCAAkC,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,0CAA0C,QAAQ,qBAAqB,GAAG,EAAE,SAAS,uWAAuW,QAAQ,+BAA+B,GAAG,EAAE,SAAS,mBAAmB,QAAQ,+BAA+B,GAAG,EAAE,SAAS,iMAAiM,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,WAAW,kFAAkF,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,4CAA4C,YAAY,CAAC,EAAE,SAAS,mBAAmB,OAAO,YAAY,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,wDAAwD,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,mDAAmD,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,UAAU,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS,mNAAmN,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,WAAW,2EAA2E,OAAO,KAAK,QAAQ,4CAA4C,YAAY,CAAC,EAAE,SAAS,cAAc,OAAO,WAAW,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,wDAAwD,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,oDAAoD,QAAQ,mCAAmC,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,WAAW,0EAA0E,SAAS,gMAAgM,QAAQ,8CAA8C,GAAG,EAAE,SAAS,sFAAsF,QAAQ,8BAA8B,GAAG,EAAE,SAAS,4CAA4C,QAAQ,gCAAgC,GAAG,EAAE,SAAS,gCAAgC,QAAQ,4BAA4B,GAAG,EAAE,SAAS,kCAAkC,QAAQ,+BAA+B,GAAG,EAAE,SAAS,wCAAwC,QAAQ,8BAA8B,GAAG,EAAE,SAAS,MAAM,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,KAAK,QAAQ,4CAA4C,YAAY,CAAC,EAAE,SAAS,eAAe,QAAQ,iCAAiC,CAAC,EAAE,GAAG,EAAE,SAAS,MAAM,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,KAAK,QAAQ,4CAA4C,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,WAAW,qDAAqD,SAAS,cAAc,QAAQ,6CAA6C,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,WAAW,uDAAuD,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,SAAS,kBAAkB,QAAQ,iCAAiC,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,WAAW,mDAAmD,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,WAAW,6CAA6C,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,SAAS,saAAsa,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,WAAW,0JAA0J,eAAe,8BAA8B,OAAO,oBAAoB,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,WAAW,WAAW,SAAS,qIAAqI,QAAQ,6BAA6B,GAAG,EAAE,SAAS,WAAW,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,WAAW,sBAAsB,OAAO,SAAS,QAAQ,mCAAmC,GAAG,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,OAAO,QAAQ,gCAAgC,CAAC,EAAE,GAAG,EAAE,WAAW,2gBAA2gB,SAAS,qGAAqG,QAAQ,wBAAwB,GAAG,EAAE,SAAS,+CAA+C,WAAW,8BAA8B,OAAO,WAAW,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,2CAA2C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,aAAa,OAAO,gBAAgB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,8CAA8C,WAAW,6BAA6B,OAAO,WAAW,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,0CAA0C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,YAAY,OAAO,gBAAgB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,8CAA8C,WAAW,6BAA6B,OAAO,WAAW,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,0CAA0C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,cAAc,OAAO,gBAAgB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,8CAA8C,WAAW,6BAA6B,OAAO,WAAW,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,0CAA0C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,cAAc,OAAO,gBAAgB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,8CAA8C,WAAW,6BAA6B,OAAO,WAAW,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,0CAA0C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,cAAc,OAAO,gBAAgB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,4CAA4C,WAAW,2BAA2B,OAAO,WAAW,QAAQ,yBAAyB,YAAY,CAAC,EAAE,SAAS,wCAAwC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,YAAY,OAAO,gBAAgB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,4DAA4D,WAAW,oCAAoC,OAAO,WAAW,QAAQ,0BAA0B,YAAY,CAAC,EAAE,SAAS,wDAAwD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,aAAa,OAAO,gBAAgB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,iDAAiD,WAAW,2CAA2C,OAAO,WAAW,QAAQ,iCAAiC,YAAY,CAAC,EAAE,SAAS,6CAA6C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,oBAAoB,OAAO,gBAAgB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,uDAAuD,WAAW,+BAA+B,OAAO,WAAW,QAAQ,6BAA6B,YAAY,CAAC,EAAE,SAAS,mDAAmD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,gBAAgB,OAAO,gBAAgB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,8CAA8C,WAAW,6BAA6B,OAAO,WAAW,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,0CAA0C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,cAAc,OAAO,gBAAgB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,+CAA+C,WAAW,8BAA8B,OAAO,WAAW,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,2CAA2C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,eAAe,eAAe,OAAO,gBAAgB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,WAAW,oCAAoC,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,iCAAiC,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,wBAAwB,YAAY,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,SAAS,0BAA0B,QAAQ,4BAA4B,GAAG,EAAE,SAAS,KAAK,QAAQ,sCAAsC,CAAC,EAAE,GAAG,EAAE,SAAS,MAAM,QAAQ,kCAAkC,GAAG,EAAE,SAAS,MAAM,QAAQ,sCAAsC,GAAG,EAAE,SAAS,yDAAyD,QAAQ,6CAA6C,GAAG,EAAE,SAAS,gEAAgE,QAAQ,mCAAmC,GAAG,EAAE,SAAS,sCAAsC,QAAQ,gCAAgC,GAAG,EAAE,WAAW,uDAAuD,SAAS,gCAAgC,QAAQ,gCAAgC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,WAAW,2CAA2C,SAAS,sBAAsB,GAAG,EAAE,SAAS,4BAA4B,QAAQ,mCAAmC,GAAG,EAAE,SAAS,KAAK,QAAQ,mCAAmC,GAAG,EAAE,SAAS,YAAY,QAAQ,8BAA8B,GAAG,EAAE,SAAS,KAAK,QAAQ,uCAAuC,GAAG,EAAE,SAAS,KAAK,QAAQ,oCAAoC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,WAAW,2EAA2E,SAAS,oBAAoB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,WAAW,4EAA4E,SAAS,wBAAwB,GAAG,EAAE,WAAW,0EAA0E,SAAS,KAAK,QAAQ,mCAAmC,GAAG,EAAE,SAAS,OAAO,QAAQ,uCAAuC,GAAG,EAAE,SAAS,OAAO,QAAQ,qCAAqC,GAAG,EAAE,SAAS,OAAO,QAAQ,uCAAuC,GAAG,EAAE,SAAS,OAAO,QAAQ,qCAAqC,GAAG,EAAE,SAAS,WAAW,QAAQ,oCAAoC,CAAC,GAAG,cAAc,EAAE,gBAAgB,EAAE,SAAS,0CAA0C,QAAQ,iCAAiC,GAAG,WAAW,EAAE,SAAS,gBAAgB,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,SAAS,QAAQ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,eAAe,eAAe,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,cAAc,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,QAAQ,CAAC,GAAG,cAAc,EAAE,uBAAuB,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,qBAAqB,QAAQ,yCAAyC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,sBAAsB,QAAQ,sCAAsC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,uBAAuB,QAAQ,uCAAuC,CAAC,EAAE,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,SAAS,kCAAkC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,kBAAkB,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,CAAC,GAAG,cAAc,EAAE,UAAU,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,eAAe,QAAQ,6BAA6B,GAAG,EAAE,SAAS,KAAK,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,mBAAmB,QAAQ,6BAA6B,GAAG,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,mBAAmB,QAAQ,6BAA6B,GAAG,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,mBAAmB,QAAQ,6BAA6B,GAAG,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,oBAAoB,QAAQ,6BAA6B,GAAG,EAAE,SAAS,WAAW,QAAQ,6BAA6B,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS,kCAAkC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,QAAQ,uCAAuC,YAAY,CAAC,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,kBAAkB,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,CAAC,GAAG,cAAc,EAAE,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,iBAAiB,OAAO,WAAW,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,EAAE,SAAS,WAAW,QAAQ,6BAA6B,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS,kCAAkC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,kBAAkB,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,wBAAwB,QAAQ,iCAAiC,GAAG,EAAE,SAAS,KAAK,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,4BAA4B,QAAQ,iCAAiC,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,4BAA4B,QAAQ,iCAAiC,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,4BAA4B,QAAQ,iCAAiC,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS,oCAAoC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,yCAAyC,YAAY,CAAC,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,kBAAkB,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,CAAC,GAAG,cAAc,EAAE,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,KAAK,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS,kCAAkC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,mCAAmC,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,kBAAkB,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,aAAa,CAAC,GAAG,cAAc,EAAE,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,KAAK,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS,kCAAkC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,sCAAsC,YAAY,CAAC,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,kBAAkB,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,wBAAwB,QAAQ,iCAAiC,GAAG,EAAE,SAAS,KAAK,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,4BAA4B,QAAQ,iCAAiC,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,4BAA4B,QAAQ,iCAAiC,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,4BAA4B,QAAQ,iCAAiC,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS,kCAAkC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,kBAAkB,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,CAAC,GAAG,cAAc,EAAE,UAAU,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,eAAe,QAAQ,oBAAoB,GAAG,EAAE,SAAS,KAAK,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,mBAAmB,QAAQ,oBAAoB,GAAG,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,mBAAmB,QAAQ,oBAAoB,GAAG,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,mBAAmB,QAAQ,oBAAoB,GAAG,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,oBAAoB,QAAQ,oBAAoB,GAAG,EAAE,SAAS,WAAW,QAAQ,oBAAoB,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS,kCAAkC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,QAAQ,uCAAuC,YAAY,CAAC,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,kBAAkB,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,CAAC,GAAG,cAAc,EAAE,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,iBAAiB,OAAO,WAAW,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,EAAE,SAAS,WAAW,QAAQ,oBAAoB,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS,kCAAkC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,oCAAoC,YAAY,CAAC,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,kBAAkB,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,CAAC,GAAG,cAAc,EAAE,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,KAAK,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,SAAS,0BAA0B,QAAQ,mCAAmC,GAAG,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oDAAoD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,QAAQ,2CAA2C,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,OAAO,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,kEAAkE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,WAAW,8JAA8J,OAAO,SAAS,QAAQ,gCAAgC,CAAC,EAAE,EAAE,GAAG,aAAa,eAAe,iBAAiB,CAAC,QAAQ,OAAO,OAAO,OAAO,KAAK,cAAc,eAAe,KAAK,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;AACt4vC,IAAI,OAAO;AAAA,EACT,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}