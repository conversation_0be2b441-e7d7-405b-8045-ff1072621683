{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/angular-html.mjs"], "sourcesContent": ["import html from './html.mjs';\n\nconst lang$3 = Object.freeze({ \"injectionSelector\": \"L:text.html -comment\", \"name\": \"angular-expression\", \"patterns\": [{ \"include\": \"#ngExpression\" }], \"repository\": { \"arrayLiteral\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"meta.brace.square.ts\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.square.ts\" } }, \"name\": \"meta.array.literal.ts\", \"patterns\": [{ \"include\": \"#ngExpression\" }, { \"include\": \"#punctuationComma\" }] }, \"booleanLiteral\": { \"patterns\": [{ \"match\": \"(?<!\\\\.|\\\\$)\\\\btrue\\\\b(?!\\\\$)\", \"name\": \"constant.language.boolean.true.ts\" }, { \"match\": \"(?<!\\\\.|\\\\$)\\\\bfalse\\\\b(?!\\\\$)\", \"name\": \"constant.language.boolean.false.ts\" }] }, \"expressionOperator\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.operator.logical.ts\" }, \"2\": { \"name\": \"entity.name.function.pipe.ng\" } }, \"match\": \"((?<!\\\\|)\\\\|(?!\\\\|))\\\\s?([a-zA-Z0-9\\\\-\\\\_\\\\$]*)\" }, { \"match\": \"(?<!\\\\.|\\\\$)\\\\b(let)\\\\b(?!\\\\$)\", \"name\": \"storage.type.ts\" }, { \"match\": \"(?<!\\\\.|\\\\$)\\\\b(await)\\\\b(?!\\\\$)\", \"name\": \"keyword.control.flow.ts\" }, { \"match\": \"(?<!\\\\.|\\\\$)\\\\bdelete\\\\b(?!\\\\$)\", \"name\": \"keyword.operator.expression.delete.ts\" }, { \"match\": \"(?<!\\\\.|\\\\$)\\\\bin\\\\b(?!\\\\$)\", \"name\": \"keyword.operator.expression.in.ts\" }, { \"match\": \"(?<!\\\\.|\\\\$)\\\\bof\\\\b(?!\\\\$)\", \"name\": \"keyword.operator.expression.of.ts\" }, { \"match\": \"(?<!\\\\.|\\\\$)\\\\bif\\\\b(?!\\\\$)\", \"name\": \"keyword.control.if.ts\" }, { \"match\": \"(?<!\\\\.|\\\\$)\\\\belse\\\\b(?!\\\\$)\", \"name\": \"keyword.control.else.ts\" }, { \"match\": \"(?<!\\\\.|\\\\$)\\\\bthen\\\\b(?!\\\\$)\", \"name\": \"keyword.control.then.ts\" }, { \"match\": \"(?<!\\\\.|\\\\$)\\\\binstanceof\\\\b(?!\\\\$)\", \"name\": \"keyword.operator.expression.instanceof.ts\" }, { \"match\": \"(?<!\\\\.|\\\\$)\\\\bnew\\\\b(?!\\\\$)\", \"name\": \"keyword.operator.new.ts\" }, { \"match\": \"(?<!\\\\.|\\\\$)\\\\bvoid\\\\b(?!\\\\$)\", \"name\": \"keyword.operator.expression.void.ts\" }, { \"begin\": \"(?<!\\\\.|\\\\$)\\\\bas\\\\b(?!\\\\$)\", \"beginCaptures\": { \"0\": { \"name\": \"storage.type.as.ts\" } }, \"end\": `(?=$|\"|'|[;,:})\\\\]])`, \"patterns\": [{ \"include\": \"#type\" }] }, { \"match\": \"\\\\*=|(?<!\\\\()\\\\/=|%=|\\\\+=|\\\\-=\", \"name\": \"keyword.operator.assignment.compound.ts\" }, { \"match\": \"\\\\&=|\\\\^=|<<=|>>=|>>>=|\\\\|=\", \"name\": \"keyword.operator.assignment.compound.bitwise.ts\" }, { \"match\": \"<<|>>>|>>\", \"name\": \"keyword.operator.bitwise.shift.ts\" }, { \"match\": \"===|!==|==|!=\", \"name\": \"keyword.operator.comparison.ts\" }, { \"match\": \"<=|>=|<>|<|>\", \"name\": \"keyword.operator.relational.ts\" }, { \"match\": \"\\\\!|&&|\\\\?\\\\?|\\\\|\\\\|\", \"name\": \"keyword.operator.logical.ts\" }, { \"match\": \"\\\\&|~|\\\\^|\\\\|\", \"name\": \"keyword.operator.bitwise.ts\" }, { \"match\": \"\\\\=\", \"name\": \"keyword.operator.assignment.ts\" }, { \"match\": \"--\", \"name\": \"keyword.operator.decrement.ts\" }, { \"match\": \"\\\\+\\\\+\", \"name\": \"keyword.operator.increment.ts\" }, { \"match\": \"\\\\%|\\\\*|\\\\/|-|\\\\+\", \"name\": \"keyword.operator.arithmetic.ts\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.arithmetic.ts\" } }, \"match\": \"(?<=[_$[:alnum:]])\\\\s*(\\\\/)(?![\\\\/*])\" }, { \"include\": \"#typeofOperator\" }] }, \"functionCall\": { \"begin\": \"(?=(\\\\??\\\\.\\\\s*)?([_$[:alpha:]][_$[:alnum:]]*)\\\\s*(<([^<>]|\\\\<[^<>]+\\\\>)+>\\\\s*)?\\\\()\", \"end\": \"(?<=\\\\))(?!(\\\\??\\\\.\\\\s*)?([_$[:alpha:]][_$[:alnum:]]*)\\\\s*(<([^<>]|\\\\<[^<>]+\\\\>)+>\\\\s*)?\\\\()\", \"patterns\": [{ \"match\": \"\\\\?\", \"name\": \"punctuation.accessor.ts\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.accessor.ts\" }, { \"match\": \"([_$[:alpha:]][_$[:alnum:]]*)\", \"name\": \"entity.name.function.ts\" }, { \"begin\": \"\\\\<\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.typeparameters.begin.ts\" } }, \"end\": \"\\\\>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.typeparameters.end.ts\" } }, \"name\": \"meta.type.parameters.ts\", \"patterns\": [{ \"include\": \"#type\" }, { \"include\": \"#punctuationComma\" }] }, { \"include\": \"#parenExpression\" }] }, \"functionParameters\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.begin.ts\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.ts\" } }, \"name\": \"meta.parameters.ts\", \"patterns\": [{ \"include\": \"#decorator\" }, { \"include\": \"#parameterName\" }, { \"include\": \"#variableInitializer\" }, { \"match\": \",\", \"name\": \"punctuation.separator.parameter.ts\" }] }, \"identifiers\": { \"patterns\": [{ \"match\": \"([_$[:alpha:]][_$[:alnum:]]*)(?=\\\\s*\\\\.\\\\s*prototype\\\\b(?!\\\\$))\", \"name\": \"support.class.ts\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.ts\" }, \"2\": { \"name\": \"constant.other.object.property.ts\" }, \"3\": { \"name\": \"variable.other.object.property.ts\" } }, \"match\": \"(?x)([?!]?\\\\.)\\\\s*(?:\\n([[:upper:]][_$[:digit:][:upper:]]*)|\\n([_$[:alpha:]][_$[:alnum:]]*)\\n)(?=\\\\s*\\\\.\\\\s*[_$[:alpha:]][_$[:alnum:]]*)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.ts\" }, \"2\": { \"name\": \"entity.name.function.ts\" } }, \"match\": \"(?x)(?:([?!]?\\\\.)\\\\s*)?([_$[:alpha:]][_$[:alnum:]]*)(?=\\\\s*=\\\\s*((async\\\\s+)|(function\\\\s*[(<])|(function\\\\s+)|([_$[:alpha:]][_$[:alnum:]]*\\\\s*=>)|((<([^<>]|\\\\<[^<>]+\\\\>)+>\\\\s*)?\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)(\\\\s*:\\\\s*(.)*)?\\\\s*=>)))\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.ts\" }, \"2\": { \"name\": \"constant.other.property.ts\" } }, \"match\": \"([?!]?\\\\.)\\\\s*([[:upper:]][_$[:digit:][:upper:]]*)(?![_$[:alnum:]])\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.ts\" }, \"2\": { \"name\": \"variable.other.property.ts\" } }, \"match\": \"([?!]?\\\\.)\\\\s*([_$[:alpha:]][_$[:alnum:]]*)\" }, { \"captures\": { \"1\": { \"name\": \"constant.other.object.ts\" }, \"2\": { \"name\": \"variable.other.object.ts\" } }, \"match\": \"(?x)(?:\\n([[:upper:]][_$[:digit:][:upper:]]*)|\\n([_$[:alpha:]][_$[:alnum:]]*)\\n)(?=\\\\s*\\\\.\\\\s*[_$[:alpha:]][_$[:alnum:]]*)\" }, { \"match\": \"([[:upper:]][_$[:digit:][:upper:]]*)(?![_$[:alnum:]])\", \"name\": \"constant.character.other\" }, { \"match\": \"[_$[:alpha:]][_$[:alnum:]]*\", \"name\": \"variable.other.readwrite.ts\" }] }, \"literal\": { \"name\": \"literal.ts\", \"patterns\": [{ \"include\": \"#numericLiteral\" }, { \"include\": \"#booleanLiteral\" }, { \"include\": \"#nullLiteral\" }, { \"include\": \"#undefinedLiteral\" }, { \"include\": \"#numericConstantLiteral\" }, { \"include\": \"#arrayLiteral\" }, { \"include\": \"#thisLiteral\" }] }, \"ngExpression\": { \"name\": \"meta.expression.ng\", \"patterns\": [{ \"include\": \"#string\" }, { \"include\": \"#literal\" }, { \"include\": \"#ternaryExpression\" }, { \"include\": \"#expressionOperator\" }, { \"include\": \"#functionCall\" }, { \"include\": \"#identifiers\" }, { \"include\": \"#parenExpression\" }, { \"include\": \"#punctuationComma\" }, { \"include\": \"#punctuationAccessor\" }] }, \"nullLiteral\": { \"match\": \"(?<!\\\\.|\\\\$)\\\\bnull\\\\b(?!\\\\$)\", \"name\": \"constant.language.null.ts\" }, \"numericConstantLiteral\": { \"patterns\": [{ \"match\": \"(?<!\\\\.|\\\\$)\\\\bNaN\\\\b(?!\\\\$)\", \"name\": \"constant.language.nan.ts\" }, { \"match\": \"(?<!\\\\.|\\\\$)\\\\bInfinity\\\\b(?!\\\\$)\", \"name\": \"constant.language.infinity.ts\" }] }, \"numericLiteral\": { \"patterns\": [{ \"match\": \"\\\\b(?<!\\\\$)0(x|X)[0-9a-fA-F]+\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.hex.ts\" }, { \"match\": \"\\\\b(?<!\\\\$)0(b|B)[01]+\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.binary.ts\" }, { \"match\": \"\\\\\\\\b(?<!\\\\$)0(o|O)?[0-7]+\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.octal.ts\" }, { \"captures\": { \"0\": { \"name\": \"constant.numeric.decimal.ts\" }, \"1\": { \"name\": \"meta.delimiter.decimal.period.ts\" }, \"2\": { \"name\": \"meta.delimiter.decimal.period.ts\" }, \"3\": { \"name\": \"meta.delimiter.decimal.period.ts\" }, \"4\": { \"name\": \"meta.delimiter.decimal.period.ts\" }, \"5\": { \"name\": \"meta.delimiter.decimal.period.ts\" }, \"6\": { \"name\": \"meta.delimiter.decimal.period.ts\" } }, \"match\": \"(?x)\\n(?<!\\\\$)(?:\\n(?:\\\\b[0-9]+(\\\\.)[0-9]+[eE][+-]?[0-9]+\\\\b)|#1.1E+3\\n(?:\\\\b[0-9]+(\\\\.)[eE][+-]?[0-9]+\\\\b)|#1.E+3\\n(?:\\\\B(\\\\.)[0-9]+[eE][+-]?[0-9]+\\\\b)|#.1E+3\\n(?:\\\\b[0-9]+[eE][+-]?[0-9]+\\\\b)|#1E+3(?:\\\\b[0-9]+(\\\\.)[0-9]+\\\\b)|#1.1\\n(?:\\\\b[0-9]+(\\\\.)\\\\B)|#1.\\n(?:\\\\B(\\\\.)[0-9]+\\\\b)|#.1\\n(?:\\\\b[0-9]+\\\\b(?!\\\\.))#1\\n)(?!\\\\$)\" }] }, \"parameterName\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.modifier.ts\" }, \"2\": { \"name\": \"storage.modifier.ts\" }, \"3\": { \"name\": \"keyword.operator.rest.ts\" }, \"4\": { \"name\": \"entity.name.function.ts\" }, \"5\": { \"name\": \"keyword.operator.optional.ts\" } }, \"match\": \"(?x)(?:\\\\s*\\\\b(readonly)\\\\s+)?(?:\\\\s*\\\\b(public|private|protected)\\\\s+)?(\\\\.\\\\.\\\\.)?\\\\s*(?<!=|:)([_$[:alpha:]][_$[:alnum:]]*)\\\\s*(\\\\??)(?=\\\\s* (=\\\\s*( (async\\\\s+) | (function\\\\s*[(<]) | (function\\\\s+) | ([_$[:alpha:]][_$[:alnum:]]*\\\\s*=>) | ((<([^<>]|\\\\<[^<>]+\\\\>)+>\\\\s*)?\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)(\\\\s*:\\\\s*(.)*)?\\\\s*=>)) ) | (:\\\\s*( (<) | ([(]\\\\s*( ([)]) | (\\\\.\\\\.\\\\.) | ([_$[:alnum:]]+\\\\s*( ([:,?=])| ([)]\\\\s*=>) )) ))) ))\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.ts\" }, \"2\": { \"name\": \"storage.modifier.ts\" }, \"3\": { \"name\": \"keyword.operator.rest.ts\" }, \"4\": { \"name\": \"variable.parameter.ts\" }, \"5\": { \"name\": \"keyword.operator.optional.ts\" } }, \"match\": \"(?:\\\\s*\\\\b(readonly)\\\\s+)?(?:\\\\s*\\\\b(public|private|protected)\\\\s+)?(\\\\.\\\\.\\\\.)?\\\\s*(?<!=|:)([_$[:alpha:]][_$[:alnum:]]*)\\\\s*(\\\\??)\" }] }, \"parenExpression\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"meta.brace.round.ts\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.ts\" } }, \"patterns\": [{ \"include\": \"#ngExpression\" }, { \"include\": \"#punctuationComma\" }] }, \"punctuationAccessor\": { \"match\": \"\\\\?\\\\.|\\\\!\\\\.|\\\\.\", \"name\": \"punctuation.accessor.ts\" }, \"punctuationComma\": { \"match\": \",\", \"name\": \"punctuation.separator.comma.ts\" }, \"punctuationSemicolon\": { \"match\": \";\", \"name\": \"punctuation.terminator.statement.ts\" }, \"qstringDouble\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ts\" } }, \"end\": '(\")|((?:[^\\\\\\\\\\\\n])$)', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.ts\" }, \"2\": { \"name\": \"invalid.illegal.newline.ts\" } }, \"name\": \"string.quoted.double.ts\", \"patterns\": [{ \"include\": \"#stringCharacterEscape\" }] }, \"qstringSingle\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ts\" } }, \"end\": \"(\\\\')|((?:[^\\\\\\\\\\\\n])$)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.ts\" }, \"2\": { \"name\": \"invalid.illegal.newline.ts\" } }, \"name\": \"string.quoted.single.ts\", \"patterns\": [{ \"include\": \"#stringCharacterEscape\" }] }, \"string\": { \"patterns\": [{ \"include\": \"#qstringSingle\" }, { \"include\": \"#qstringDouble\" }] }, \"stringCharacterEscape\": { \"match\": \"\\\\\\\\(x\\\\h{2}|[0-2][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.|$)\", \"name\": \"constant.character.escape.ts\" }, \"ternaryExpression\": { \"begin\": \"(?!\\\\?\\\\.\\\\s*[^[:digit:]])(\\\\?)(?!\\\\?)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.ternary.ts\" } }, \"end\": \"\\\\s*(:)\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.ternary.ts\" } }, \"patterns\": [{ \"include\": \"#ngExpression\" }] }, \"thisLiteral\": { \"match\": \"(?<!\\\\.|\\\\$)\\\\bthis\\\\b(?!\\\\$)\", \"name\": \"variable.language.this.ts\" }, \"type\": { \"name\": \"meta.type.ts\", \"patterns\": [{ \"include\": \"#string\" }, { \"include\": \"#numericLiteral\" }, { \"include\": \"#typeBuiltinLiterals\" }, { \"include\": \"#typeTuple\" }, { \"include\": \"#typeObject\" }, { \"include\": \"#typeOperators\" }, { \"include\": \"#typeFnTypeParameters\" }, { \"include\": \"#typeParenOrFunctionParameters\" }, { \"include\": \"#typeName\" }] }, \"typeAnnotation\": { \"begin\": \":\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.type.annotation.ts\" } }, \"end\": '(?=$|[,);\\\\}\\\\]]|\\\\/\\\\/|\")|(?==[^>])|(?<=[\\\\}>\\\\]\\\\)]|[_$[:alpha:]])\\\\s*(?=\\\\{)', \"name\": \"meta.type.annotation.ts\", \"patterns\": [{ \"include\": \"#type\" }] }, \"typeBuiltinLiterals\": { \"match\": \"(?<!\\\\.|\\\\$)\\\\b(this|true|false|undefined|null)\\\\b(?!\\\\$)\", \"name\": \"support.type.builtin.ts\" }, \"typeFnTypeParameters\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.control.new.ts\" } }, \"match\": \"(?<!\\\\.|\\\\$)\\\\b(new)\\\\b(?=\\\\s*\\\\<)\", \"name\": \"meta.type.constructor.ts\" }, { \"begin\": \"(?<!\\\\.|\\\\$)\\\\b(new)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.new.ts\" } }, \"end\": \"(?<=\\\\))\", \"name\": \"meta.type.constructor.ts\", \"patterns\": [{ \"include\": \"#functionParameters\" }] }, { \"begin\": \"(?<=\\\\>)\\\\s*(?=\\\\()\", \"end\": \"(?<=\\\\))\", \"include\": \"#typeofOperator\", \"name\": \"meta.type.function.ts\", \"patterns\": [{ \"include\": \"#functionParameters\" }] }, { \"begin\": \"(?x)((?=[(]\\\\s*(([)])|(\\\\.\\\\.\\\\.)|([_$[:alnum:]]+\\\\s*(([:,?=])|([)]\\\\s*=>))))))\", \"end\": \"(?<=\\\\))\", \"name\": \"meta.type.function.ts\", \"patterns\": [{ \"include\": \"#functionParameters\" }] }] }, \"typeName\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.name.type.module.ts\" }, \"2\": { \"name\": \"punctuation.accessor.ts\" } }, \"match\": \"([_$[:alpha:]][_$[:alnum:]]*)\\\\s*([?!]?\\\\.)\" }, { \"match\": \"[_$[:alpha:]][_$[:alnum:]]*\", \"name\": \"entity.name.type.ts\" }] }, \"typeObject\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ts\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ts\" } }, \"name\": \"meta.object.type.ts\", \"patterns\": [{ \"include\": \"#typeObjectMembers\" }] }, \"typeObjectMembers\": { \"patterns\": [{ \"include\": \"#typeAnnotation\" }, { \"include\": \"#punctuationComma\" }, { \"include\": \"#punctuationSemicolon\" }] }, \"typeOperators\": { \"patterns\": [{ \"include\": \"#typeofOperator\" }, { \"match\": \"[&|]\", \"name\": \"keyword.operator.type.ts\" }, { \"match\": \"(?<!\\\\.|\\\\$)\\\\bkeyof\\\\b(?!\\\\$)\", \"name\": \"keyword.operator.expression.keyof.ts\" }] }, \"typeParenOrFunctionParameters\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"meta.brace.round.ts\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.ts\" } }, \"name\": \"meta.type.paren.cover.ts\", \"patterns\": [{ \"include\": \"#type\" }, { \"include\": \"#functionParameters\" }] }, \"typeTuple\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"meta.brace.square.ts\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.square.ts\" } }, \"name\": \"meta.type.tuple.ts\", \"patterns\": [{ \"include\": \"#type\" }, { \"include\": \"#punctuationComma\" }] }, \"typeofOperator\": { \"match\": \"(?<!\\\\.|\\\\$)\\\\btypeof\\\\b(?!\\\\$)\", \"name\": \"keyword.operator.expression.typeof.ts\" }, \"undefinedLiteral\": { \"match\": \"(?<!\\\\.|\\\\$)\\\\bundefined\\\\b(?!\\\\$)\", \"name\": \"constant.language.undefined.ts\" }, \"variableInitializer\": { \"begin\": \"(?<!=|!)(=)(?!=)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.assignment.ts\" } }, \"end\": \"(?=$|[,);}\\\\]])\", \"patterns\": [{ \"include\": \"#ngExpression\" }] } }, \"scopeName\": \"expression.ng\" });\nvar angular_expression = [\n  lang$3\n];\n\nconst lang$2 = Object.freeze({ \"injectTo\": [\"text.html.derivative\", \"text.html.derivative.ng\", \"source.ts.ng\"], \"injectionSelector\": \"L:text.html -comment\", \"name\": \"angular-template\", \"patterns\": [{ \"include\": \"#interpolation\" }, { \"include\": \"#propertyBinding\" }, { \"include\": \"#eventBinding\" }, { \"include\": \"#twoWayBinding\" }, { \"include\": \"#templateBinding\" }], \"repository\": { \"bindingKey\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.ng-binding-name.begin.html\" }, \"2\": { \"name\": \"entity.other.ng-binding-name.$2.html\", \"patterns\": [{ \"match\": \"\\\\.\", \"name\": \"punctuation.accessor.html\" }] }, \"3\": { \"name\": \"punctuation.definition.ng-binding-name.end.html\" } }, \"match\": \"([\\\\[\\\\(]{1,2}|\\\\*)(?:\\\\s*)(@?[-_a-zA-Z0-9.$]*%?)(?:\\\\s*)([\\\\]\\\\)]{1,2})?\" }] }, \"eventBinding\": { \"begin\": `(\\\\(\\\\s*@?[-_a-zA-Z0-9.$]*\\\\s*\\\\))(=)([\"'])`, \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.html entity.other.ng-binding-name.event.html\", \"patterns\": [{ \"include\": \"#bindingKey\" }] }, \"2\": { \"name\": \"punctuation.separator.key-value.html\" }, \"3\": { \"name\": \"string.quoted.html punctuation.definition.string.begin.html\" } }, \"contentName\": \"expression.ng\", \"end\": \"\\\\3\", \"endCaptures\": { \"0\": { \"name\": \"string.quoted.html punctuation.definition.string.end.html\" } }, \"name\": \"meta.ng-binding.event.html\", \"patterns\": [{ \"include\": \"expression.ng\" }] }, \"interpolation\": { \"begin\": \"{{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ts\" } }, \"contentName\": \"expression.ng\", \"end\": \"}}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ts\" } }, \"patterns\": [{ \"include\": \"expression.ng\" }] }, \"propertyBinding\": { \"begin\": `(\\\\[\\\\s*@?[-_a-zA-Z0-9.$]*%?\\\\s*])(=)([\"'])`, \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.html entity.other.ng-binding-name.property.html\", \"patterns\": [{ \"include\": \"#bindingKey\" }] }, \"2\": { \"name\": \"punctuation.separator.key-value.html\" }, \"3\": { \"name\": \"string.quoted.html punctuation.definition.string.begin.html\" } }, \"contentName\": \"expression.ng\", \"end\": \"\\\\3\", \"endCaptures\": { \"0\": { \"name\": \"string.quoted.html punctuation.definition.string.end.html\" } }, \"name\": \"meta.ng-binding.property.html\", \"patterns\": [{ \"include\": \"expression.ng\" }] }, \"templateBinding\": { \"begin\": `(\\\\*[-_a-zA-Z0-9.$]*)(=)([\"'])`, \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.html entity.other.ng-binding-name.template.html\", \"patterns\": [{ \"include\": \"#bindingKey\" }] }, \"2\": { \"name\": \"punctuation.separator.key-value.html\" }, \"3\": { \"name\": \"string.quoted.html punctuation.definition.string.begin.html\" } }, \"contentName\": \"expression.ng\", \"end\": \"\\\\3\", \"endCaptures\": { \"0\": { \"name\": \"string.quoted.html punctuation.definition.string.end.html\" } }, \"name\": \"meta.ng-binding.template.html\", \"patterns\": [{ \"include\": \"expression.ng\" }] }, \"twoWayBinding\": { \"begin\": `(\\\\[\\\\s*\\\\(\\\\s*@?[-_a-zA-Z0-9.$]*\\\\s*\\\\)\\\\s*\\\\])(=)([\"'])`, \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.html entity.other.ng-binding-name.two-way.html\", \"patterns\": [{ \"include\": \"#bindingKey\" }] }, \"2\": { \"name\": \"punctuation.separator.key-value.html\" }, \"3\": { \"name\": \"string.quoted.html punctuation.definition.string.begin.html\" } }, \"contentName\": \"expression.ng\", \"end\": \"\\\\3\", \"endCaptures\": { \"0\": { \"name\": \"string.quoted.html punctuation.definition.string.end.html\" } }, \"name\": \"meta.ng-binding.two-way.html\", \"patterns\": [{ \"include\": \"expression.ng\" }] } }, \"scopeName\": \"template.ng\", \"embeddedLangs\": [\"angular-expression\"] });\nvar angular_template = [\n  ...angular_expression,\n  lang$2\n];\n\nconst lang$1 = Object.freeze({ \"injectTo\": [\"text.html.derivative\", \"text.html.derivative.ng\", \"source.ts.ng\"], \"injectionSelector\": \"L:text.html -comment -expression.ng -meta.tag -source.css -source.js\", \"name\": \"angular-template-blocks\", \"patterns\": [{ \"include\": \"#block\" }], \"repository\": { \"block\": { \"begin\": \"(@)((?:\\\\w+\\\\s*)+)(?=\\\\(|\\\\{)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.block.kind.ng\" } }, \"contentName\": \"control.block.ng\", \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"include\": \"#blockExpression\" }, { \"include\": \"#blockBody\" }] }, \"blockBody\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ts\" } }, \"contentName\": \"control.block.body.ng\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ts\" } }, \"patterns\": [{ \"include\": \"text.html.derivative.ng\" }, { \"include\": \"template.ng\" }] }, \"blockExpression\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"meta.brace.round.ts\" } }, \"contentName\": \"control.block.expression.ng\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.ts\" } }, \"patterns\": [{ \"include\": \"expression.ng\" }] }, \"transition\": { \"match\": \"@\", \"name\": \"keyword.control.block.transition.ng\" } }, \"scopeName\": \"template.blocks.ng\", \"embeddedLangs\": [\"angular-expression\", \"angular-template\"] });\nvar angular_template_blocks = [\n  ...angular_expression,\n  ...angular_template,\n  lang$1\n];\n\nconst lang = Object.freeze({ \"displayName\": \"Angular HTML\", \"injections\": { \"R:text.html - (comment.block, text.html meta.embedded, meta.tag.*.*.html, meta.tag.*.*.*.html, meta.tag.*.*.*.*.html)\": { \"comment\": \"Uses R: to ensure this matches after any other injections.\", \"patterns\": [{ \"match\": \"<\", \"name\": \"invalid.illegal.bad-angle-bracket.html\" }] } }, \"name\": \"angular-html\", \"patterns\": [{ \"include\": \"text.html.basic#core-minus-invalid\" }, { \"begin\": \"(</?)(\\\\w[^\\\\s>]*)(?<!/)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \"((?: ?/)?>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.other.unrecognized.html.derivative\", \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }] }], \"scopeName\": \"text.html.derivative.ng\", \"embeddedLangs\": [\"html\", \"angular-expression\", \"angular-template\", \"angular-template-blocks\"] });\nvar angular_html = [\n  ...html,\n  ...angular_expression,\n  ...angular_template,\n  ...angular_template_blocks,\n  lang\n];\n\nvar angularHtml = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  default: angular_html\n});\n\nexport { angular_html as a, angular_template as b, angular_expression as c, angular_template_blocks as d, angularHtml as e };\n"], "mappings": ";;;;;AAEA,IAAM,SAAS,OAAO,OAAO,EAAE,qBAAqB,wBAAwB,QAAQ,sBAAsB,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,GAAG,cAAc,EAAE,gBAAgB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,QAAQ,yBAAyB,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,SAAS,iCAAiC,QAAQ,oCAAoC,GAAG,EAAE,SAAS,kCAAkC,QAAQ,qCAAqC,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS,kDAAkD,GAAG,EAAE,SAAS,kCAAkC,QAAQ,kBAAkB,GAAG,EAAE,SAAS,oCAAoC,QAAQ,0BAA0B,GAAG,EAAE,SAAS,mCAAmC,QAAQ,wCAAwC,GAAG,EAAE,SAAS,+BAA+B,QAAQ,oCAAoC,GAAG,EAAE,SAAS,+BAA+B,QAAQ,oCAAoC,GAAG,EAAE,SAAS,+BAA+B,QAAQ,wBAAwB,GAAG,EAAE,SAAS,iCAAiC,QAAQ,0BAA0B,GAAG,EAAE,SAAS,iCAAiC,QAAQ,0BAA0B,GAAG,EAAE,SAAS,uCAAuC,QAAQ,4CAA4C,GAAG,EAAE,SAAS,gCAAgC,QAAQ,0BAA0B,GAAG,EAAE,SAAS,iCAAiC,QAAQ,sCAAsC,GAAG,EAAE,SAAS,+BAA+B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qBAAqB,EAAE,GAAG,OAAO,wBAAwB,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,kCAAkC,QAAQ,0CAA0C,GAAG,EAAE,SAAS,+BAA+B,QAAQ,kDAAkD,GAAG,EAAE,SAAS,aAAa,QAAQ,oCAAoC,GAAG,EAAE,SAAS,iBAAiB,QAAQ,iCAAiC,GAAG,EAAE,SAAS,gBAAgB,QAAQ,iCAAiC,GAAG,EAAE,SAAS,wBAAwB,QAAQ,8BAA8B,GAAG,EAAE,SAAS,iBAAiB,QAAQ,8BAA8B,GAAG,EAAE,SAAS,OAAO,QAAQ,iCAAiC,GAAG,EAAE,SAAS,MAAM,QAAQ,gCAAgC,GAAG,EAAE,SAAS,UAAU,QAAQ,gCAAgC,GAAG,EAAE,SAAS,qBAAqB,QAAQ,iCAAiC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,wCAAwC,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,wFAAwF,OAAO,gGAAgG,YAAY,CAAC,EAAE,SAAS,OAAO,QAAQ,0BAA0B,GAAG,EAAE,SAAS,OAAO,QAAQ,0BAA0B,GAAG,EAAE,SAAS,iCAAiC,QAAQ,0BAA0B,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,sBAAsB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,sBAAsB,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,SAAS,KAAK,QAAQ,qCAAqC,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,mEAAmE,QAAQ,mBAAmB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,SAAS,2IAA2I,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,SAAS,yOAAyO,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,sEAAsE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,8CAA8C,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,SAAS,6HAA6H,GAAG,EAAE,SAAS,yDAAyD,QAAQ,2BAA2B,GAAG,EAAE,SAAS,+BAA+B,QAAQ,8BAA8B,CAAC,EAAE,GAAG,WAAW,EAAE,QAAQ,cAAc,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,gBAAgB,EAAE,QAAQ,sBAAsB,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,eAAe,EAAE,SAAS,iCAAiC,QAAQ,4BAA4B,GAAG,0BAA0B,EAAE,YAAY,CAAC,EAAE,SAAS,gCAAgC,QAAQ,2BAA2B,GAAG,EAAE,SAAS,qCAAqC,QAAQ,gCAAgC,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,SAAS,2CAA2C,QAAQ,0BAA0B,GAAG,EAAE,SAAS,oCAAoC,QAAQ,6BAA6B,GAAG,EAAE,SAAS,wCAAwC,QAAQ,4BAA4B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,oUAAoU,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS,6aAA6a,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS,sIAAsI,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,qBAAqB,QAAQ,0BAA0B,GAAG,oBAAoB,EAAE,SAAS,KAAK,QAAQ,iCAAiC,GAAG,wBAAwB,EAAE,SAAS,KAAK,QAAQ,sCAAsC,GAAG,iBAAiB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,yBAAyB,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,2BAA2B,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,uEAAuE,QAAQ,+BAA+B,GAAG,qBAAqB,EAAE,SAAS,0CAA0C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,eAAe,EAAE,SAAS,iCAAiC,QAAQ,4BAA4B,GAAG,QAAQ,EAAE,QAAQ,gBAAgB,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,mFAAmF,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,6DAA6D,QAAQ,0BAA0B,GAAG,wBAAwB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,SAAS,sCAAsC,QAAQ,2BAA2B,GAAG,EAAE,SAAS,sCAAsC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,YAAY,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,EAAE,SAAS,uBAAuB,OAAO,YAAY,WAAW,mBAAmB,QAAQ,yBAAyB,YAAY,CAAC,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,EAAE,SAAS,mFAAmF,OAAO,YAAY,QAAQ,yBAAyB,YAAY,CAAC,EAAE,WAAW,sBAAsB,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,SAAS,8CAA8C,GAAG,EAAE,SAAS,+BAA+B,QAAQ,sBAAsB,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,SAAS,QAAQ,QAAQ,2BAA2B,GAAG,EAAE,SAAS,kCAAkC,QAAQ,uCAAuC,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,QAAQ,sBAAsB,YAAY,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,mCAAmC,QAAQ,wCAAwC,GAAG,oBAAoB,EAAE,SAAS,sCAAsC,QAAQ,iCAAiC,GAAG,uBAAuB,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,OAAO,mBAAmB,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,EAAE,GAAG,aAAa,gBAAgB,CAAC;AAC55b,IAAI,qBAAqB;AAAA,EACvB;AACF;AAEA,IAAM,SAAS,OAAO,OAAO,EAAE,YAAY,CAAC,wBAAwB,2BAA2B,cAAc,GAAG,qBAAqB,wBAAwB,QAAQ,oBAAoB,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,mBAAmB,CAAC,GAAG,cAAc,EAAE,cAAc,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oDAAoD,GAAG,KAAK,EAAE,QAAQ,wCAAwC,YAAY,CAAC,EAAE,SAAS,OAAO,QAAQ,4BAA4B,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,SAAS,4EAA4E,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,+CAA+C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4EAA4E,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,8DAA8D,EAAE,GAAG,eAAe,iBAAiB,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,4DAA4D,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,MAAM,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,eAAe,iBAAiB,OAAO,MAAM,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,+CAA+C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+EAA+E,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,8DAA8D,EAAE,GAAG,eAAe,iBAAiB,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,4DAA4D,EAAE,GAAG,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,kCAAkC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+EAA+E,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,8DAA8D,EAAE,GAAG,eAAe,iBAAiB,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,4DAA4D,EAAE,GAAG,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,6DAA6D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8EAA8E,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,8DAA8D,EAAE,GAAG,eAAe,iBAAiB,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,4DAA4D,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,EAAE,GAAG,aAAa,eAAe,iBAAiB,CAAC,oBAAoB,EAAE,CAAC;AACz9G,IAAI,mBAAmB;AAAA,EACrB,GAAG;AAAA,EACH;AACF;AAEA,IAAM,SAAS,OAAO,OAAO,EAAE,YAAY,CAAC,wBAAwB,2BAA2B,cAAc,GAAG,qBAAqB,wEAAwE,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,SAAS,CAAC,GAAG,cAAc,EAAE,SAAS,EAAE,SAAS,iCAAiC,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,eAAe,oBAAoB,OAAO,YAAY,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,eAAe,yBAAyB,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,eAAe,+BAA+B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,KAAK,QAAQ,sCAAsC,EAAE,GAAG,aAAa,sBAAsB,iBAAiB,CAAC,sBAAsB,kBAAkB,EAAE,CAAC;AACr1C,IAAI,0BAA0B;AAAA,EAC5B,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;AAEA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,gBAAgB,cAAc,EAAE,yHAAyH,EAAE,WAAW,8DAA8D,YAAY,CAAC,EAAE,SAAS,KAAK,QAAQ,yCAAyC,CAAC,EAAE,EAAE,GAAG,QAAQ,gBAAgB,YAAY,CAAC,EAAE,WAAW,qCAAqC,GAAG,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,eAAe,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,+CAA+C,YAAY,CAAC,EAAE,WAAW,4BAA4B,CAAC,EAAE,CAAC,GAAG,aAAa,2BAA2B,iBAAiB,CAAC,QAAQ,sBAAsB,oBAAoB,yBAAyB,EAAE,CAAC;AAC57B,IAAI,eAAe;AAAA,EACjB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;AAEA,IAAI,cAA2B,OAAO,OAAO;AAAA,EAC3C,WAAW;AAAA,EACX,SAAS;AACX,CAAC;", "names": []}