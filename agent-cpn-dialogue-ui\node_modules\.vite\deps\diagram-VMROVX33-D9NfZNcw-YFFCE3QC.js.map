{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/hierarchy/count.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/hierarchy/each.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/hierarchy/eachBefore.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/hierarchy/eachAfter.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/hierarchy/find.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/hierarchy/sum.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/hierarchy/sort.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/hierarchy/path.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/hierarchy/ancestors.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/hierarchy/descendants.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/hierarchy/leaves.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/hierarchy/links.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/hierarchy/iterator.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/hierarchy/index.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/accessors.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/constant.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/treemap/round.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/treemap/dice.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/treemap/slice.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/treemap/squarify.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/d3-hierarchy/src/treemap/index.js", "../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/mermaid/dist/chunks/mermaid.core/diagram-VMROVX33.mjs"], "sourcesContent": ["function count(node) {\n  var sum = 0,\n      children = node.children,\n      i = children && children.length;\n  if (!i) sum = 1;\n  else while (--i >= 0) sum += children[i].value;\n  node.value = sum;\n}\n\nexport default function() {\n  return this.eachAfter(count);\n}\n", "export default function(callback, that) {\n  let index = -1;\n  for (const node of this) {\n    callback.call(that, node, ++index, this);\n  }\n  return this;\n}\n", "export default function(callback, that) {\n  var node = this, nodes = [node], children, i, index = -1;\n  while (node = nodes.pop()) {\n    callback.call(that, node, ++index, this);\n    if (children = node.children) {\n      for (i = children.length - 1; i >= 0; --i) {\n        nodes.push(children[i]);\n      }\n    }\n  }\n  return this;\n}\n", "export default function(callback, that) {\n  var node = this, nodes = [node], next = [], children, i, n, index = -1;\n  while (node = nodes.pop()) {\n    next.push(node);\n    if (children = node.children) {\n      for (i = 0, n = children.length; i < n; ++i) {\n        nodes.push(children[i]);\n      }\n    }\n  }\n  while (node = next.pop()) {\n    callback.call(that, node, ++index, this);\n  }\n  return this;\n}\n", "export default function(callback, that) {\n  let index = -1;\n  for (const node of this) {\n    if (callback.call(that, node, ++index, this)) {\n      return node;\n    }\n  }\n}\n", "export default function(value) {\n  return this.eachAfter(function(node) {\n    var sum = +value(node.data) || 0,\n        children = node.children,\n        i = children && children.length;\n    while (--i >= 0) sum += children[i].value;\n    node.value = sum;\n  });\n}\n", "export default function(compare) {\n  return this.eachBefore(function(node) {\n    if (node.children) {\n      node.children.sort(compare);\n    }\n  });\n}\n", "export default function(end) {\n  var start = this,\n      ancestor = leastCommonAncestor(start, end),\n      nodes = [start];\n  while (start !== ancestor) {\n    start = start.parent;\n    nodes.push(start);\n  }\n  var k = nodes.length;\n  while (end !== ancestor) {\n    nodes.splice(k, 0, end);\n    end = end.parent;\n  }\n  return nodes;\n}\n\nfunction leastCommonAncestor(a, b) {\n  if (a === b) return a;\n  var aNodes = a.ancestors(),\n      bNodes = b.ancestors(),\n      c = null;\n  a = aNodes.pop();\n  b = bNodes.pop();\n  while (a === b) {\n    c = a;\n    a = aNodes.pop();\n    b = bNodes.pop();\n  }\n  return c;\n}\n", "export default function() {\n  var node = this, nodes = [node];\n  while (node = node.parent) {\n    nodes.push(node);\n  }\n  return nodes;\n}\n", "export default function() {\n  return Array.from(this);\n}\n", "export default function() {\n  var leaves = [];\n  this.eachBefore(function(node) {\n    if (!node.children) {\n      leaves.push(node);\n    }\n  });\n  return leaves;\n}\n", "export default function() {\n  var root = this, links = [];\n  root.each(function(node) {\n    if (node !== root) { // Don’t include the root’s parent, if any.\n      links.push({source: node.parent, target: node});\n    }\n  });\n  return links;\n}\n", "export default function*() {\n  var node = this, current, next = [node], children, i, n;\n  do {\n    current = next.reverse(), next = [];\n    while (node = current.pop()) {\n      yield node;\n      if (children = node.children) {\n        for (i = 0, n = children.length; i < n; ++i) {\n          next.push(children[i]);\n        }\n      }\n    }\n  } while (next.length);\n}\n", "import node_count from \"./count.js\";\nimport node_each from \"./each.js\";\nimport node_eachBefore from \"./eachBefore.js\";\nimport node_eachAfter from \"./eachAfter.js\";\nimport node_find from \"./find.js\";\nimport node_sum from \"./sum.js\";\nimport node_sort from \"./sort.js\";\nimport node_path from \"./path.js\";\nimport node_ancestors from \"./ancestors.js\";\nimport node_descendants from \"./descendants.js\";\nimport node_leaves from \"./leaves.js\";\nimport node_links from \"./links.js\";\nimport node_iterator from \"./iterator.js\";\n\nexport default function hierarchy(data, children) {\n  if (data instanceof Map) {\n    data = [undefined, data];\n    if (children === undefined) children = mapChildren;\n  } else if (children === undefined) {\n    children = objectChildren;\n  }\n\n  var root = new Node(data),\n      node,\n      nodes = [root],\n      child,\n      childs,\n      i,\n      n;\n\n  while (node = nodes.pop()) {\n    if ((childs = children(node.data)) && (n = (childs = Array.from(childs)).length)) {\n      node.children = childs;\n      for (i = n - 1; i >= 0; --i) {\n        nodes.push(child = childs[i] = new Node(childs[i]));\n        child.parent = node;\n        child.depth = node.depth + 1;\n      }\n    }\n  }\n\n  return root.eachBefore(computeHeight);\n}\n\nfunction node_copy() {\n  return hierarchy(this).eachBefore(copyData);\n}\n\nfunction objectChildren(d) {\n  return d.children;\n}\n\nfunction mapChildren(d) {\n  return Array.isArray(d) ? d[1] : null;\n}\n\nfunction copyData(node) {\n  if (node.data.value !== undefined) node.value = node.data.value;\n  node.data = node.data.data;\n}\n\nexport function computeHeight(node) {\n  var height = 0;\n  do node.height = height;\n  while ((node = node.parent) && (node.height < ++height));\n}\n\nexport function Node(data) {\n  this.data = data;\n  this.depth =\n  this.height = 0;\n  this.parent = null;\n}\n\nNode.prototype = hierarchy.prototype = {\n  constructor: Node,\n  count: node_count,\n  each: node_each,\n  eachAfter: node_eachAfter,\n  eachBefore: node_eachBefore,\n  find: node_find,\n  sum: node_sum,\n  sort: node_sort,\n  path: node_path,\n  ancestors: node_ancestors,\n  descendants: node_descendants,\n  leaves: node_leaves,\n  links: node_links,\n  copy: node_copy,\n  [Symbol.iterator]: node_iterator\n};\n", "export function optional(f) {\n  return f == null ? null : required(f);\n}\n\nexport function required(f) {\n  if (typeof f !== \"function\") throw new Error;\n  return f;\n}\n", "export function constantZero() {\n  return 0;\n}\n\nexport default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "export default function(node) {\n  node.x0 = Math.round(node.x0);\n  node.y0 = Math.round(node.y0);\n  node.x1 = Math.round(node.x1);\n  node.y1 = Math.round(node.y1);\n}\n", "export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      node,\n      i = -1,\n      n = nodes.length,\n      k = parent.value && (x1 - x0) / parent.value;\n\n  while (++i < n) {\n    node = nodes[i], node.y0 = y0, node.y1 = y1;\n    node.x0 = x0, node.x1 = x0 += node.value * k;\n  }\n}\n", "export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      node,\n      i = -1,\n      n = nodes.length,\n      k = parent.value && (y1 - y0) / parent.value;\n\n  while (++i < n) {\n    node = nodes[i], node.x0 = x0, node.x1 = x1;\n    node.y0 = y0, node.y1 = y0 += node.value * k;\n  }\n}\n", "import treemapDice from \"./dice.js\";\nimport treemapSlice from \"./slice.js\";\n\nexport var phi = (1 + Math.sqrt(5)) / 2;\n\nexport function squarifyRatio(ratio, parent, x0, y0, x1, y1) {\n  var rows = [],\n      nodes = parent.children,\n      row,\n      nodeValue,\n      i0 = 0,\n      i1 = 0,\n      n = nodes.length,\n      dx, dy,\n      value = parent.value,\n      sumValue,\n      minValue,\n      maxValue,\n      newRatio,\n      minRatio,\n      alpha,\n      beta;\n\n  while (i0 < n) {\n    dx = x1 - x0, dy = y1 - y0;\n\n    // Find the next non-empty node.\n    do sumValue = nodes[i1++].value; while (!sumValue && i1 < n);\n    minValue = maxValue = sumValue;\n    alpha = Math.max(dy / dx, dx / dy) / (value * ratio);\n    beta = sumValue * sumValue * alpha;\n    minRatio = Math.max(maxValue / beta, beta / minValue);\n\n    // Keep adding nodes while the aspect ratio maintains or improves.\n    for (; i1 < n; ++i1) {\n      sumValue += nodeValue = nodes[i1].value;\n      if (nodeValue < minValue) minValue = nodeValue;\n      if (nodeValue > maxValue) maxValue = nodeValue;\n      beta = sumValue * sumValue * alpha;\n      newRatio = Math.max(maxValue / beta, beta / minValue);\n      if (newRatio > minRatio) { sumValue -= nodeValue; break; }\n      minRatio = newRatio;\n    }\n\n    // Position and record the row orientation.\n    rows.push(row = {value: sumValue, dice: dx < dy, children: nodes.slice(i0, i1)});\n    if (row.dice) treemapDice(row, x0, y0, x1, value ? y0 += dy * sumValue / value : y1);\n    else treemapSlice(row, x0, y0, value ? x0 += dx * sumValue / value : x1, y1);\n    value -= sumValue, i0 = i1;\n  }\n\n  return rows;\n}\n\nexport default (function custom(ratio) {\n\n  function squarify(parent, x0, y0, x1, y1) {\n    squarifyRatio(ratio, parent, x0, y0, x1, y1);\n  }\n\n  squarify.ratio = function(x) {\n    return custom((x = +x) > 1 ? x : 1);\n  };\n\n  return squarify;\n})(phi);\n", "import roundNode from \"./round.js\";\nimport squarify from \"./squarify.js\";\nimport {required} from \"../accessors.js\";\nimport constant, {constantZero} from \"../constant.js\";\n\nexport default function() {\n  var tile = squarify,\n      round = false,\n      dx = 1,\n      dy = 1,\n      paddingStack = [0],\n      paddingInner = constantZero,\n      paddingTop = constantZero,\n      paddingRight = constantZero,\n      paddingBottom = constantZero,\n      paddingLeft = constantZero;\n\n  function treemap(root) {\n    root.x0 =\n    root.y0 = 0;\n    root.x1 = dx;\n    root.y1 = dy;\n    root.eachBefore(positionNode);\n    paddingStack = [0];\n    if (round) root.eachBefore(roundNode);\n    return root;\n  }\n\n  function positionNode(node) {\n    var p = paddingStack[node.depth],\n        x0 = node.x0 + p,\n        y0 = node.y0 + p,\n        x1 = node.x1 - p,\n        y1 = node.y1 - p;\n    if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n    if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n    node.x0 = x0;\n    node.y0 = y0;\n    node.x1 = x1;\n    node.y1 = y1;\n    if (node.children) {\n      p = paddingStack[node.depth + 1] = paddingInner(node) / 2;\n      x0 += paddingLeft(node) - p;\n      y0 += paddingTop(node) - p;\n      x1 -= paddingRight(node) - p;\n      y1 -= paddingBottom(node) - p;\n      if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n      if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n      tile(node, x0, y0, x1, y1);\n    }\n  }\n\n  treemap.round = function(x) {\n    return arguments.length ? (round = !!x, treemap) : round;\n  };\n\n  treemap.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], treemap) : [dx, dy];\n  };\n\n  treemap.tile = function(x) {\n    return arguments.length ? (tile = required(x), treemap) : tile;\n  };\n\n  treemap.padding = function(x) {\n    return arguments.length ? treemap.paddingInner(x).paddingOuter(x) : treemap.paddingInner();\n  };\n\n  treemap.paddingInner = function(x) {\n    return arguments.length ? (paddingInner = typeof x === \"function\" ? x : constant(+x), treemap) : paddingInner;\n  };\n\n  treemap.paddingOuter = function(x) {\n    return arguments.length ? treemap.paddingTop(x).paddingRight(x).paddingBottom(x).paddingLeft(x) : treemap.paddingTop();\n  };\n\n  treemap.paddingTop = function(x) {\n    return arguments.length ? (paddingTop = typeof x === \"function\" ? x : constant(+x), treemap) : paddingTop;\n  };\n\n  treemap.paddingRight = function(x) {\n    return arguments.length ? (paddingRight = typeof x === \"function\" ? x : constant(+x), treemap) : paddingRight;\n  };\n\n  treemap.paddingBottom = function(x) {\n    return arguments.length ? (paddingBottom = typeof x === \"function\" ? x : constant(+x), treemap) : paddingBottom;\n  };\n\n  treemap.paddingLeft = function(x) {\n    return arguments.length ? (paddingLeft = typeof x === \"function\" ? x : constant(+x), treemap) : paddingLeft;\n  };\n\n  return treemap;\n}\n", "import {\n  setupViewPortForSVG\n} from \"./chunk-SKB7J2MH.mjs\";\nimport {\n  isLabelStyle,\n  styles2String\n} from \"./chunk-UWXLY5YG.mjs\";\nimport {\n  populateCommonDb\n} from \"./chunk-353BL4L5.mjs\";\nimport {\n  cleanAndMerge\n} from \"./chunk-55PJQP7W.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-P3VETL53.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-3XYRH5AP.mjs\";\n\n// src/diagrams/treemap/db.ts\nvar TreeMapDB = class {\n  constructor() {\n    this.nodes = [];\n    this.levels = /* @__PURE__ */ new Map();\n    this.outerNodes = [];\n    this.classes = /* @__PURE__ */ new Map();\n    this.setAccTitle = setAccTitle;\n    this.getAccTitle = getAccTitle;\n    this.setDiagramTitle = setDiagramTitle;\n    this.getDiagramTitle = getDiagramTitle;\n    this.getAccDescription = getAccDescription;\n    this.setAccDescription = setAccDescription;\n  }\n  static {\n    __name(this, \"TreeMapDB\");\n  }\n  getNodes() {\n    return this.nodes;\n  }\n  getConfig() {\n    const defaultConfig = defaultConfig_default;\n    const userConfig = getConfig();\n    return cleanAndMerge({\n      ...defaultConfig.treemap,\n      ...userConfig.treemap ?? {}\n    });\n  }\n  addNode(node, level) {\n    this.nodes.push(node);\n    this.levels.set(node, level);\n    if (level === 0) {\n      this.outerNodes.push(node);\n      this.root ??= node;\n    }\n  }\n  getRoot() {\n    return { name: \"\", children: this.outerNodes };\n  }\n  addClass(id, _style) {\n    const styleClass = this.classes.get(id) ?? { id, styles: [], textStyles: [] };\n    const styles = _style.replace(/\\\\,/g, \"\\xA7\\xA7\\xA7\").replace(/,/g, \";\").replace(/§§§/g, \",\").split(\";\");\n    if (styles) {\n      styles.forEach((s) => {\n        if (isLabelStyle(s)) {\n          if (styleClass?.textStyles) {\n            styleClass.textStyles.push(s);\n          } else {\n            styleClass.textStyles = [s];\n          }\n        }\n        if (styleClass?.styles) {\n          styleClass.styles.push(s);\n        } else {\n          styleClass.styles = [s];\n        }\n      });\n    }\n    this.classes.set(id, styleClass);\n  }\n  getClasses() {\n    return this.classes;\n  }\n  getStylesForClass(classSelector) {\n    return this.classes.get(classSelector)?.styles ?? [];\n  }\n  clear() {\n    clear();\n    this.nodes = [];\n    this.levels = /* @__PURE__ */ new Map();\n    this.outerNodes = [];\n    this.classes = /* @__PURE__ */ new Map();\n    this.root = void 0;\n  }\n};\n\n// src/diagrams/treemap/parser.ts\nimport { parse } from \"@mermaid-js/parser\";\n\n// src/diagrams/treemap/utils.ts\nfunction buildHierarchy(items) {\n  if (!items.length) {\n    return [];\n  }\n  const root = [];\n  const stack = [];\n  items.forEach((item) => {\n    const node = {\n      name: item.name,\n      children: item.type === \"Leaf\" ? void 0 : []\n    };\n    node.classSelector = item?.classSelector;\n    if (item?.cssCompiledStyles) {\n      node.cssCompiledStyles = [item.cssCompiledStyles];\n    }\n    if (item.type === \"Leaf\" && item.value !== void 0) {\n      node.value = item.value;\n    }\n    while (stack.length > 0 && stack[stack.length - 1].level >= item.level) {\n      stack.pop();\n    }\n    if (stack.length === 0) {\n      root.push(node);\n    } else {\n      const parent = stack[stack.length - 1].node;\n      if (parent.children) {\n        parent.children.push(node);\n      } else {\n        parent.children = [node];\n      }\n    }\n    if (item.type !== \"Leaf\") {\n      stack.push({ node, level: item.level });\n    }\n  });\n  return root;\n}\n__name(buildHierarchy, \"buildHierarchy\");\n\n// src/diagrams/treemap/parser.ts\nvar populate = /* @__PURE__ */ __name((ast, db) => {\n  populateCommonDb(ast, db);\n  const items = [];\n  for (const row of ast.TreemapRows ?? []) {\n    if (row.$type === \"ClassDefStatement\") {\n      db.addClass(row.className ?? \"\", row.styleText ?? \"\");\n    }\n  }\n  for (const row of ast.TreemapRows ?? []) {\n    const item = row.item;\n    if (!item) {\n      continue;\n    }\n    const level = row.indent ? parseInt(row.indent) : 0;\n    const name = getItemName(item);\n    const styles = item.classSelector ? db.getStylesForClass(item.classSelector) : [];\n    const cssCompiledStyles = styles.length > 0 ? styles.join(\";\") : void 0;\n    const itemData = {\n      level,\n      name,\n      type: item.$type,\n      value: item.value,\n      classSelector: item.classSelector,\n      cssCompiledStyles\n    };\n    items.push(itemData);\n  }\n  const hierarchyNodes = buildHierarchy(items);\n  const addNodesRecursively = /* @__PURE__ */ __name((nodes, level) => {\n    for (const node of nodes) {\n      db.addNode(node, level);\n      if (node.children && node.children.length > 0) {\n        addNodesRecursively(node.children, level + 1);\n      }\n    }\n  }, \"addNodesRecursively\");\n  addNodesRecursively(hierarchyNodes, 0);\n}, \"populate\");\nvar getItemName = /* @__PURE__ */ __name((item) => {\n  return item.name ? String(item.name) : \"\";\n}, \"getItemName\");\nvar parser = {\n  // @ts-expect-error - TreeMapDB is not assignable to DiagramDB\n  parser: { yy: void 0 },\n  parse: /* @__PURE__ */ __name(async (text) => {\n    try {\n      const parseFunc = parse;\n      const ast = await parseFunc(\"treemap\", text);\n      log.debug(\"Treemap AST:\", ast);\n      const db = parser.parser?.yy;\n      if (!(db instanceof TreeMapDB)) {\n        throw new Error(\n          \"parser.parser?.yy was not a TreemapDB. This is due to a bug within Mermaid, please report this issue at https://github.com/mermaid-js/mermaid/issues.\"\n        );\n      }\n      populate(ast, db);\n    } catch (error) {\n      log.error(\"Error parsing treemap:\", error);\n      throw error;\n    }\n  }, \"parse\")\n};\n\n// src/diagrams/treemap/renderer.ts\nimport { scaleOrdinal, treemap, hierarchy, format, select } from \"d3\";\nvar DEFAULT_INNER_PADDING = 10;\nvar SECTION_INNER_PADDING = 10;\nvar SECTION_HEADER_HEIGHT = 25;\nvar draw = /* @__PURE__ */ __name((_text, id, _version, diagram2) => {\n  const treemapDb = diagram2.db;\n  const config = treemapDb.getConfig();\n  const treemapInnerPadding = config.padding ?? DEFAULT_INNER_PADDING;\n  const title = treemapDb.getDiagramTitle();\n  const root = treemapDb.getRoot();\n  const { themeVariables } = getConfig();\n  if (!root) {\n    return;\n  }\n  const titleHeight = title ? 30 : 0;\n  const svg = selectSvgElement(id);\n  const width = config.nodeWidth ? config.nodeWidth * SECTION_INNER_PADDING : 960;\n  const height = config.nodeHeight ? config.nodeHeight * SECTION_INNER_PADDING : 500;\n  const svgWidth = width;\n  const svgHeight = height + titleHeight;\n  svg.attr(\"viewBox\", `0 0 ${svgWidth} ${svgHeight}`);\n  configureSvgSize(svg, svgHeight, svgWidth, config.useMaxWidth);\n  let valueFormat;\n  try {\n    const formatStr = config.valueFormat || \",\";\n    if (formatStr === \"$0,0\") {\n      valueFormat = /* @__PURE__ */ __name((value) => \"$\" + format(\",\")(value), \"valueFormat\");\n    } else if (formatStr.startsWith(\"$\") && formatStr.includes(\",\")) {\n      const precision = /\\.\\d+/.exec(formatStr);\n      const precisionStr = precision ? precision[0] : \"\";\n      valueFormat = /* @__PURE__ */ __name((value) => \"$\" + format(\",\" + precisionStr)(value), \"valueFormat\");\n    } else if (formatStr.startsWith(\"$\")) {\n      const restOfFormat = formatStr.substring(1);\n      valueFormat = /* @__PURE__ */ __name((value) => \"$\" + format(restOfFormat || \"\")(value), \"valueFormat\");\n    } else {\n      valueFormat = format(formatStr);\n    }\n  } catch (error) {\n    log.error(\"Error creating format function:\", error);\n    valueFormat = format(\",\");\n  }\n  const colorScale = scaleOrdinal().range([\n    \"transparent\",\n    themeVariables.cScale0,\n    themeVariables.cScale1,\n    themeVariables.cScale2,\n    themeVariables.cScale3,\n    themeVariables.cScale4,\n    themeVariables.cScale5,\n    themeVariables.cScale6,\n    themeVariables.cScale7,\n    themeVariables.cScale8,\n    themeVariables.cScale9,\n    themeVariables.cScale10,\n    themeVariables.cScale11\n  ]);\n  const colorScalePeer = scaleOrdinal().range([\n    \"transparent\",\n    themeVariables.cScalePeer0,\n    themeVariables.cScalePeer1,\n    themeVariables.cScalePeer2,\n    themeVariables.cScalePeer3,\n    themeVariables.cScalePeer4,\n    themeVariables.cScalePeer5,\n    themeVariables.cScalePeer6,\n    themeVariables.cScalePeer7,\n    themeVariables.cScalePeer8,\n    themeVariables.cScalePeer9,\n    themeVariables.cScalePeer10,\n    themeVariables.cScalePeer11\n  ]);\n  const colorScaleLabel = scaleOrdinal().range([\n    themeVariables.cScaleLabel0,\n    themeVariables.cScaleLabel1,\n    themeVariables.cScaleLabel2,\n    themeVariables.cScaleLabel3,\n    themeVariables.cScaleLabel4,\n    themeVariables.cScaleLabel5,\n    themeVariables.cScaleLabel6,\n    themeVariables.cScaleLabel7,\n    themeVariables.cScaleLabel8,\n    themeVariables.cScaleLabel9,\n    themeVariables.cScaleLabel10,\n    themeVariables.cScaleLabel11\n  ]);\n  if (title) {\n    svg.append(\"text\").attr(\"x\", svgWidth / 2).attr(\"y\", titleHeight / 2).attr(\"class\", \"treemapTitle\").attr(\"text-anchor\", \"middle\").attr(\"dominant-baseline\", \"middle\").text(title);\n  }\n  const g = svg.append(\"g\").attr(\"transform\", `translate(0, ${titleHeight})`).attr(\"class\", \"treemapContainer\");\n  const hierarchyRoot = hierarchy(root).sum((d) => d.value ?? 0).sort((a, b) => (b.value ?? 0) - (a.value ?? 0));\n  const treemapLayout = treemap().size([width, height]).paddingTop(\n    (d) => d.children && d.children.length > 0 ? SECTION_HEADER_HEIGHT + SECTION_INNER_PADDING : 0\n  ).paddingInner(treemapInnerPadding).paddingLeft((d) => d.children && d.children.length > 0 ? SECTION_INNER_PADDING : 0).paddingRight((d) => d.children && d.children.length > 0 ? SECTION_INNER_PADDING : 0).paddingBottom((d) => d.children && d.children.length > 0 ? SECTION_INNER_PADDING : 0).round(true);\n  const treemapData = treemapLayout(hierarchyRoot);\n  const branchNodes = treemapData.descendants().filter((d) => d.children && d.children.length > 0);\n  const sections = g.selectAll(\".treemapSection\").data(branchNodes).enter().append(\"g\").attr(\"class\", \"treemapSection\").attr(\"transform\", (d) => `translate(${d.x0},${d.y0})`);\n  sections.append(\"rect\").attr(\"width\", (d) => d.x1 - d.x0).attr(\"height\", SECTION_HEADER_HEIGHT).attr(\"class\", \"treemapSectionHeader\").attr(\"fill\", \"none\").attr(\"fill-opacity\", 0.6).attr(\"stroke-width\", 0.6).attr(\"style\", (d) => {\n    if (d.depth === 0) {\n      return \"display: none;\";\n    }\n    return \"\";\n  });\n  sections.append(\"clipPath\").attr(\"id\", (_d, i) => `clip-section-${id}-${i}`).append(\"rect\").attr(\"width\", (d) => Math.max(0, d.x1 - d.x0 - 12)).attr(\"height\", SECTION_HEADER_HEIGHT);\n  sections.append(\"rect\").attr(\"width\", (d) => d.x1 - d.x0).attr(\"height\", (d) => d.y1 - d.y0).attr(\"class\", (_d, i) => {\n    return `treemapSection section${i}`;\n  }).attr(\"fill\", (d) => colorScale(d.data.name)).attr(\"fill-opacity\", 0.6).attr(\"stroke\", (d) => colorScalePeer(d.data.name)).attr(\"stroke-width\", 2).attr(\"stroke-opacity\", 0.4).attr(\"style\", (d) => {\n    if (d.depth === 0) {\n      return \"display: none;\";\n    }\n    const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles });\n    return styles.nodeStyles + \";\" + styles.borderStyles.join(\";\");\n  });\n  sections.append(\"text\").attr(\"class\", \"treemapSectionLabel\").attr(\"x\", 6).attr(\"y\", SECTION_HEADER_HEIGHT / 2).attr(\"dominant-baseline\", \"middle\").text((d) => d.depth === 0 ? \"\" : d.data.name).attr(\"font-weight\", \"bold\").attr(\"style\", (d) => {\n    if (d.depth === 0) {\n      return \"display: none;\";\n    }\n    const labelStyles = \"dominant-baseline: middle; font-size: 12px; fill:\" + colorScaleLabel(d.data.name) + \"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\";\n    const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles });\n    return labelStyles + styles.labelStyles.replace(\"color:\", \"fill:\");\n  }).each(function(d) {\n    if (d.depth === 0) {\n      return;\n    }\n    const self = select(this);\n    const originalText = d.data.name;\n    self.text(originalText);\n    const totalHeaderWidth = d.x1 - d.x0;\n    const labelXPosition = 6;\n    let spaceForTextContent;\n    if (config.showValues !== false && d.value) {\n      const valueEndsAtXRelative = totalHeaderWidth - 10;\n      const estimatedValueTextActualWidth = 30;\n      const gapBetweenLabelAndValue = 10;\n      const labelMustEndBeforeX = valueEndsAtXRelative - estimatedValueTextActualWidth - gapBetweenLabelAndValue;\n      spaceForTextContent = labelMustEndBeforeX - labelXPosition;\n    } else {\n      const labelOwnRightPadding = 6;\n      spaceForTextContent = totalHeaderWidth - labelXPosition - labelOwnRightPadding;\n    }\n    const minimumWidthToDisplay = 15;\n    const actualAvailableWidth = Math.max(minimumWidthToDisplay, spaceForTextContent);\n    const textNode = self.node();\n    const currentTextContentLength = textNode.getComputedTextLength();\n    if (currentTextContentLength > actualAvailableWidth) {\n      const ellipsis = \"...\";\n      let currentTruncatedText = originalText;\n      while (currentTruncatedText.length > 0) {\n        currentTruncatedText = originalText.substring(0, currentTruncatedText.length - 1);\n        if (currentTruncatedText.length === 0) {\n          self.text(ellipsis);\n          if (textNode.getComputedTextLength() > actualAvailableWidth) {\n            self.text(\"\");\n          }\n          break;\n        }\n        self.text(currentTruncatedText + ellipsis);\n        if (textNode.getComputedTextLength() <= actualAvailableWidth) {\n          break;\n        }\n      }\n    }\n  });\n  if (config.showValues !== false) {\n    sections.append(\"text\").attr(\"class\", \"treemapSectionValue\").attr(\"x\", (d) => d.x1 - d.x0 - 10).attr(\"y\", SECTION_HEADER_HEIGHT / 2).attr(\"text-anchor\", \"end\").attr(\"dominant-baseline\", \"middle\").text((d) => d.value ? valueFormat(d.value) : \"\").attr(\"font-style\", \"italic\").attr(\"style\", (d) => {\n      if (d.depth === 0) {\n        return \"display: none;\";\n      }\n      const labelStyles = \"text-anchor: end; dominant-baseline: middle; font-size: 10px; fill:\" + colorScaleLabel(d.data.name) + \"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\";\n      const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles });\n      return labelStyles + styles.labelStyles.replace(\"color:\", \"fill:\");\n    });\n  }\n  const leafNodes = treemapData.leaves();\n  const cell = g.selectAll(\".treemapLeafGroup\").data(leafNodes).enter().append(\"g\").attr(\"class\", (d, i) => {\n    return `treemapNode treemapLeafGroup leaf${i}${d.data.classSelector ? ` ${d.data.classSelector}` : \"\"}x`;\n  }).attr(\"transform\", (d) => `translate(${d.x0},${d.y0})`);\n  cell.append(\"rect\").attr(\"width\", (d) => d.x1 - d.x0).attr(\"height\", (d) => d.y1 - d.y0).attr(\"class\", \"treemapLeaf\").attr(\"fill\", (d) => {\n    return d.parent ? colorScale(d.parent.data.name) : colorScale(d.data.name);\n  }).attr(\"style\", (d) => {\n    const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles });\n    return styles.nodeStyles;\n  }).attr(\"fill-opacity\", 0.3).attr(\"stroke\", (d) => {\n    return d.parent ? colorScale(d.parent.data.name) : colorScale(d.data.name);\n  }).attr(\"stroke-width\", 3);\n  cell.append(\"clipPath\").attr(\"id\", (_d, i) => `clip-${id}-${i}`).append(\"rect\").attr(\"width\", (d) => Math.max(0, d.x1 - d.x0 - 4)).attr(\"height\", (d) => Math.max(0, d.y1 - d.y0 - 4));\n  const leafLabels = cell.append(\"text\").attr(\"class\", \"treemapLabel\").attr(\"x\", (d) => (d.x1 - d.x0) / 2).attr(\"y\", (d) => (d.y1 - d.y0) / 2).attr(\"style\", (d) => {\n    const labelStyles = \"text-anchor: middle; dominant-baseline: middle; font-size: 38px;fill:\" + colorScaleLabel(d.data.name) + \";\";\n    const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles });\n    return labelStyles + styles.labelStyles.replace(\"color:\", \"fill:\");\n  }).attr(\"clip-path\", (_d, i) => `url(#clip-${id}-${i})`).text((d) => d.data.name);\n  leafLabels.each(function(d) {\n    const self = select(this);\n    const nodeWidth = d.x1 - d.x0;\n    const nodeHeight = d.y1 - d.y0;\n    const textNode = self.node();\n    const padding = 4;\n    const availableWidth = nodeWidth - 2 * padding;\n    const availableHeight = nodeHeight - 2 * padding;\n    if (availableWidth < 10 || availableHeight < 10) {\n      self.style(\"display\", \"none\");\n      return;\n    }\n    let currentLabelFontSize = parseInt(self.style(\"font-size\"), 10);\n    const minLabelFontSize = 8;\n    const originalValueRelFontSize = 28;\n    const valueScaleFactor = 0.6;\n    const minValueFontSize = 6;\n    const spacingBetweenLabelAndValue = 2;\n    while (textNode.getComputedTextLength() > availableWidth && currentLabelFontSize > minLabelFontSize) {\n      currentLabelFontSize--;\n      self.style(\"font-size\", `${currentLabelFontSize}px`);\n    }\n    let prospectiveValueFontSize = Math.max(\n      minValueFontSize,\n      Math.min(originalValueRelFontSize, Math.round(currentLabelFontSize * valueScaleFactor))\n    );\n    let combinedHeight = currentLabelFontSize + spacingBetweenLabelAndValue + prospectiveValueFontSize;\n    while (combinedHeight > availableHeight && currentLabelFontSize > minLabelFontSize) {\n      currentLabelFontSize--;\n      prospectiveValueFontSize = Math.max(\n        minValueFontSize,\n        Math.min(originalValueRelFontSize, Math.round(currentLabelFontSize * valueScaleFactor))\n      );\n      if (prospectiveValueFontSize < minValueFontSize && currentLabelFontSize === minLabelFontSize) {\n        break;\n      }\n      self.style(\"font-size\", `${currentLabelFontSize}px`);\n      combinedHeight = currentLabelFontSize + spacingBetweenLabelAndValue + prospectiveValueFontSize;\n      if (prospectiveValueFontSize <= minValueFontSize && combinedHeight > availableHeight) {\n      }\n    }\n    self.style(\"font-size\", `${currentLabelFontSize}px`);\n    if (textNode.getComputedTextLength() > availableWidth || currentLabelFontSize < minLabelFontSize || availableHeight < currentLabelFontSize) {\n      self.style(\"display\", \"none\");\n    }\n  });\n  if (config.showValues !== false) {\n    const leafValues = cell.append(\"text\").attr(\"class\", \"treemapValue\").attr(\"x\", (d) => (d.x1 - d.x0) / 2).attr(\"y\", function(d) {\n      return (d.y1 - d.y0) / 2;\n    }).attr(\"style\", (d) => {\n      const labelStyles = \"text-anchor: middle; dominant-baseline: hanging; font-size: 28px;fill:\" + colorScaleLabel(d.data.name) + \";\";\n      const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles });\n      return labelStyles + styles.labelStyles.replace(\"color:\", \"fill:\");\n    }).attr(\"clip-path\", (_d, i) => `url(#clip-${id}-${i})`).text((d) => d.value ? valueFormat(d.value) : \"\");\n    leafValues.each(function(d) {\n      const valueTextElement = select(this);\n      const parentCellNode = this.parentNode;\n      if (!parentCellNode) {\n        valueTextElement.style(\"display\", \"none\");\n        return;\n      }\n      const labelElement = select(parentCellNode).select(\".treemapLabel\");\n      if (labelElement.empty() || labelElement.style(\"display\") === \"none\") {\n        valueTextElement.style(\"display\", \"none\");\n        return;\n      }\n      const finalLabelFontSize = parseFloat(labelElement.style(\"font-size\"));\n      const originalValueFontSize = 28;\n      const valueScaleFactor = 0.6;\n      const minValueFontSize = 6;\n      const spacingBetweenLabelAndValue = 2;\n      const actualValueFontSize = Math.max(\n        minValueFontSize,\n        Math.min(originalValueFontSize, Math.round(finalLabelFontSize * valueScaleFactor))\n      );\n      valueTextElement.style(\"font-size\", `${actualValueFontSize}px`);\n      const labelCenterY = (d.y1 - d.y0) / 2;\n      const valueTopActualY = labelCenterY + finalLabelFontSize / 2 + spacingBetweenLabelAndValue;\n      valueTextElement.attr(\"y\", valueTopActualY);\n      const nodeWidth = d.x1 - d.x0;\n      const nodeTotalHeight = d.y1 - d.y0;\n      const cellBottomPadding = 4;\n      const maxValueBottomY = nodeTotalHeight - cellBottomPadding;\n      const availableWidthForValue = nodeWidth - 2 * 4;\n      if (valueTextElement.node().getComputedTextLength() > availableWidthForValue || valueTopActualY + actualValueFontSize > maxValueBottomY || actualValueFontSize < minValueFontSize) {\n        valueTextElement.style(\"display\", \"none\");\n      } else {\n        valueTextElement.style(\"display\", null);\n      }\n    });\n  }\n  const diagramPadding = config.diagramPadding ?? 8;\n  setupViewPortForSVG(svg, diagramPadding, \"flowchart\", config?.useMaxWidth || false);\n}, \"draw\");\nvar getClasses = /* @__PURE__ */ __name(function(_text, diagramObj) {\n  return diagramObj.db.getClasses();\n}, \"getClasses\");\nvar renderer = { draw, getClasses };\n\n// src/diagrams/treemap/styles.ts\nvar defaultTreemapStyleOptions = {\n  sectionStrokeColor: \"black\",\n  sectionStrokeWidth: \"1\",\n  sectionFillColor: \"#efefef\",\n  leafStrokeColor: \"black\",\n  leafStrokeWidth: \"1\",\n  leafFillColor: \"#efefef\",\n  labelColor: \"black\",\n  labelFontSize: \"12px\",\n  valueFontSize: \"10px\",\n  valueColor: \"black\",\n  titleColor: \"black\",\n  titleFontSize: \"14px\"\n};\nvar getStyles = /* @__PURE__ */ __name(({\n  treemap: treemap2\n} = {}) => {\n  const options = cleanAndMerge(defaultTreemapStyleOptions, treemap2);\n  return `\n  .treemapNode.section {\n    stroke: ${options.sectionStrokeColor};\n    stroke-width: ${options.sectionStrokeWidth};\n    fill: ${options.sectionFillColor};\n  }\n  .treemapNode.leaf {\n    stroke: ${options.leafStrokeColor};\n    stroke-width: ${options.leafStrokeWidth};\n    fill: ${options.leafFillColor};\n  }\n  .treemapLabel {\n    fill: ${options.labelColor};\n    font-size: ${options.labelFontSize};\n  }\n  .treemapValue {\n    fill: ${options.valueColor};\n    font-size: ${options.valueFontSize};\n  }\n  .treemapTitle {\n    fill: ${options.titleColor};\n    font-size: ${options.titleFontSize};\n  }\n  `;\n}, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/treemap/diagram.ts\nvar diagram = {\n  parser,\n  get db() {\n    return new TreeMapDB();\n  },\n  renderer,\n  styles: styles_default\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,MAAM,MAAM;AACnB,MAAI,MAAM,GACN,WAAW,KAAK,UAChB,IAAI,YAAY,SAAS;AAC7B,MAAI,CAAC;AAAG,UAAM;;AACT,WAAO,EAAE,KAAK;AAAG,aAAO,SAAS,CAAC,EAAE;AACzC,OAAK,QAAQ;AACf;AAEe,SAAA,aAAW;AACxB,SAAO,KAAK,UAAU,KAAK;AAC7B;ACXe,SAAA,UAAS,UAAU,MAAM;AACtC,MAAI,QAAQ;AACZ,aAAW,QAAQ,MAAM;AACvB,aAAS,KAAK,MAAM,MAAM,EAAE,OAAO,IAAI;EACzC;AACA,SAAO;AACT;ACNe,SAAA,gBAAS,UAAU,MAAM;AACtC,MAAI,OAAO,MAAM,QAAQ,CAAC,IAAI,GAAG,UAAU,GAAG,QAAQ;AACtD,SAAO,OAAO,MAAM,IAAA,GAAO;AACzB,aAAS,KAAK,MAAM,MAAM,EAAE,OAAO,IAAI;AACvC,QAAI,WAAW,KAAK,UAAU;AAC5B,WAAK,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACzC,cAAM,KAAK,SAAS,CAAC,CAAC;MACxB;IACF;EACF;AACA,SAAO;AACT;ACXe,SAAA,eAAS,UAAU,MAAM;AACtC,MAAI,OAAO,MAAM,QAAQ,CAAC,IAAI,GAAG,OAAO,CAAA,GAAI,UAAU,GAAG,GAAG,QAAQ;AACpE,SAAO,OAAO,MAAM,IAAA,GAAO;AACzB,SAAK,KAAK,IAAI;AACd,QAAI,WAAW,KAAK,UAAU;AAC5B,WAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,cAAM,KAAK,SAAS,CAAC,CAAC;MACxB;IACF;EACF;AACA,SAAO,OAAO,KAAK,IAAA,GAAO;AACxB,aAAS,KAAK,MAAM,MAAM,EAAE,OAAO,IAAI;EACzC;AACA,SAAO;AACT;ACde,SAAA,UAAS,UAAU,MAAM;AACtC,MAAI,QAAQ;AACZ,aAAW,QAAQ,MAAM;AACvB,QAAI,SAAS,KAAK,MAAM,MAAM,EAAE,OAAO,IAAI,GAAG;AAC5C,aAAO;IACT;EACF;AACF;ACPe,SAAA,SAAS,OAAO;AAC7B,SAAO,KAAK,UAAU,SAAS,MAAM;AACnC,QAAI,MAAM,CAAC,MAAM,KAAK,IAAI,KAAK,GAC3B,WAAW,KAAK,UAChB,IAAI,YAAY,SAAS;AAC7B,WAAO,EAAE,KAAK;AAAG,aAAO,SAAS,CAAC,EAAE;AACpC,SAAK,QAAQ;EACf,CAAC;AACH;ACRe,SAAA,UAAS,SAAS;AAC/B,SAAO,KAAK,WAAW,SAAS,MAAM;AACpC,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,KAAK,OAAO;IAC5B;EACF,CAAC;AACH;ACNe,SAAA,UAAS,KAAK;AAC3B,MAAI,QAAQ,MACR,WAAW,oBAAoB,OAAO,GAAG,GACzC,QAAQ,CAAC,KAAK;AAClB,SAAO,UAAU,UAAU;AACzB,YAAQ,MAAM;AACd,UAAM,KAAK,KAAK;EAClB;AACA,MAAI,IAAI,MAAM;AACd,SAAO,QAAQ,UAAU;AACvB,UAAM,OAAO,GAAG,GAAG,GAAG;AACtB,UAAM,IAAI;EACZ;AACA,SAAO;AACT;AAEA,SAAS,oBAAoB,GAAG,GAAG;AACjC,MAAI,MAAM;AAAG,WAAO;AACpB,MAAI,SAAS,EAAE,UAAS,GACpB,SAAS,EAAE,UAAS,GACpB,IAAI;AACR,MAAI,OAAO,IAAG;AACd,MAAI,OAAO,IAAG;AACd,SAAO,MAAM,GAAG;AACd,QAAI;AACJ,QAAI,OAAO,IAAG;AACd,QAAI,OAAO,IAAG;EAChB;AACA,SAAO;AACT;AC7Be,SAAA,iBAAW;AACxB,MAAI,OAAO,MAAM,QAAQ,CAAC,IAAI;AAC9B,SAAO,OAAO,KAAK,QAAQ;AACzB,UAAM,KAAK,IAAI;EACjB;AACA,SAAO;AACT;ACNe,SAAA,mBAAW;AACxB,SAAO,MAAM,KAAK,IAAI;AACxB;ACFe,SAAA,cAAW;AACxB,MAAI,SAAS,CAAA;AACb,OAAK,WAAW,SAAS,MAAM;AAC7B,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO,KAAK,IAAI;IAClB;EACF,CAAC;AACD,SAAO;AACT;ACRe,SAAA,aAAW;AACxB,MAAI,OAAO,MAAM,QAAQ,CAAA;AACzB,OAAK,KAAK,SAAS,MAAM;AACvB,QAAI,SAAS,MAAM;AACjB,YAAM,KAAK,EAAC,QAAQ,KAAK,QAAQ,QAAQ,KAAI,CAAC;IAChD;EACF,CAAC;AACD,SAAO;AACT;ACRe,UAAA,gBAAY;AACzB,MAAI,OAAO,MAAM,SAAS,OAAO,CAAC,IAAI,GAAG,UAAU,GAAG;AACtD,KAAG;AACD,cAAU,KAAK,QAAO,GAAI,OAAO,CAAA;AACjC,WAAO,OAAO,QAAQ,IAAA,GAAO;AAC3B,YAAM;AACN,UAAI,WAAW,KAAK,UAAU;AAC5B,aAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,eAAK,KAAK,SAAS,CAAC,CAAC;QACvB;MACF;IACF;EACF,SAAS,KAAK;AAChB;ACCe,SAAS,UAAU,MAAM,UAAU;AAChD,MAAI,gBAAgB,KAAK;AACvB,WAAO,CAAC,QAAW,IAAI;AACvB,QAAI,aAAa;AAAW,iBAAW;EACzC,WAAW,aAAa,QAAW;AACjC,eAAW;EACb;AAEA,MAAI,OAAO,IAAI,KAAK,IAAI,GACpB,MACA,QAAQ,CAAC,IAAI,GACb,OACA,QACA,GACA;AAEJ,SAAO,OAAO,MAAM,IAAA,GAAO;AACzB,SAAK,SAAS,SAAS,KAAK,IAAI,OAAO,KAAK,SAAS,MAAM,KAAK,MAAM,GAAG,SAAS;AAChF,WAAK,WAAW;AAChB,WAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,cAAM,KAAK,QAAQ,OAAO,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC;AAClD,cAAM,SAAS;AACf,cAAM,QAAQ,KAAK,QAAQ;MAC7B;IACF;EACF;AAEA,SAAO,KAAK,WAAW,aAAa;AACtC;AAEA,SAAS,YAAY;AACnB,SAAO,UAAU,IAAI,EAAE,WAAW,QAAQ;AAC5C;AAEA,SAAS,eAAe,GAAG;AACzB,SAAO,EAAE;AACX;AAEA,SAAS,YAAY,GAAG;AACtB,SAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI;AACnC;AAEA,SAAS,SAAS,MAAM;AACtB,MAAI,KAAK,KAAK,UAAU;AAAW,SAAK,QAAQ,KAAK,KAAK;AAC1D,OAAK,OAAO,KAAK,KAAK;AACxB;AAEO,SAAS,cAAc,MAAM;AAClC,MAAI,SAAS;AACb;AAAG,SAAK,SAAS;UACT,OAAO,KAAK,WAAY,KAAK,SAAS,EAAE;AAClD;AAEO,SAAS,KAAK,MAAM;AACzB,OAAK,OAAO;AACZ,OAAK,QACL,KAAK,SAAS;AACd,OAAK,SAAS;AAChB;AAEA,KAAK,YAAY,UAAU,YAAY;EACrC,aAAa;EACb,OAAO;EACP,MAAM;EACN,WAAW;EACX,YAAY;EACZ,MAAM;EACN,KAAK;EACL,MAAM;EACN,MAAM;EACN,WAAW;EACX,aAAa;EACb,QAAQ;EACR,OAAO;EACP,MAAM;EACN,CAAC,OAAO,QAAQ,GAAG;AACrB;ACtFO,SAAS,SAAS,GAAG;AAC1B,MAAI,OAAO,MAAM;AAAY,UAAM,IAAI,MAAA;AACvC,SAAO;AACT;ACPO,SAAS,eAAe;AAC7B,SAAO;AACT;AAEe,SAAA,SAAS,GAAG;AACzB,SAAO,WAAW;AAChB,WAAO;EACT;AACF;ACRe,SAAA,UAAS,MAAM;AAC5B,OAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC5B,OAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC5B,OAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC5B,OAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC9B;ACLe,SAAA,YAAS,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC9C,MAAI,QAAQ,OAAO,UACf,MACA,IAAI,IACJ,IAAI,MAAM,QACV,IAAI,OAAO,UAAU,KAAK,MAAM,OAAO;AAE3C,SAAO,EAAE,IAAI,GAAG;AACd,WAAO,MAAM,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AACzC,SAAK,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,QAAQ;EAC7C;AACF;ACXe,SAAA,aAAS,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC9C,MAAI,QAAQ,OAAO,UACf,MACA,IAAI,IACJ,IAAI,MAAM,QACV,IAAI,OAAO,UAAU,KAAK,MAAM,OAAO;AAE3C,SAAO,EAAE,IAAI,GAAG;AACd,WAAO,MAAM,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AACzC,SAAK,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,QAAQ;EAC7C;AACF;ACRO,IAAI,OAAO,IAAI,KAAK,KAAK,CAAC,KAAK;AAE/B,SAAS,cAAc,OAAO,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC3D,MAAI,OAAO,CAAA,GACP,QAAQ,OAAO,UACf,KACA,WACA,KAAK,GACL,KAAK,GACL,IAAI,MAAM,QACV,IAAI,IACJ,QAAQ,OAAO,OACf,UACA,UACA,UACA,UACA,UACA,OACA;AAEJ,SAAO,KAAK,GAAG;AACb,SAAK,KAAK,IAAI,KAAK,KAAK;AAGxB;AAAG,iBAAW,MAAM,IAAI,EAAE;WAAc,CAAC,YAAY,KAAK;AAC1D,eAAW,WAAW;AACtB,YAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE,KAAK,QAAQ;AAC9C,WAAO,WAAW,WAAW;AAC7B,eAAW,KAAK,IAAI,WAAW,MAAM,OAAO,QAAQ;AAGpD,WAAO,KAAK,GAAG,EAAE,IAAI;AACnB,kBAAY,YAAY,MAAM,EAAE,EAAE;AAClC,UAAI,YAAY;AAAU,mBAAW;AACrC,UAAI,YAAY;AAAU,mBAAW;AACrC,aAAO,WAAW,WAAW;AAC7B,iBAAW,KAAK,IAAI,WAAW,MAAM,OAAO,QAAQ;AACpD,UAAI,WAAW,UAAU;AAAE,oBAAY;AAAW;MAAO;AACzD,iBAAW;IACb;AAGA,SAAK,KAAK,MAAM,EAAC,OAAO,UAAU,MAAM,KAAK,IAAI,UAAU,MAAM,MAAM,IAAI,EAAE,EAAC,CAAC;AAC/E,QAAI,IAAI;AAAM,kBAAY,KAAK,IAAI,IAAI,IAAI,QAAQ,MAAM,KAAK,WAAW,QAAQ,EAAE;;AAC9E,mBAAa,KAAK,IAAI,IAAI,QAAQ,MAAM,KAAK,WAAW,QAAQ,IAAI,EAAE;AAC3E,aAAS,UAAU,KAAK;EAC1B;AAEA,SAAO;AACT;AAEA,IAAA,WAAgB,SAAS,OAAO,OAAO;AAErC,WAASA,UAAS,QAAQ,IAAI,IAAI,IAAI,IAAI;AACxC,kBAAc,OAAO,QAAQ,IAAI,IAAI,IAAI,EAAE;EAC7C;AAEAA,YAAS,QAAQ,SAAS,GAAG;AAC3B,WAAO,QAAQ,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;EACpC;AAEA,SAAOA;AACT,EAAG,GAAG;AC5DS,SAAA,UAAW;AACxB,MAAI,OAAO,UACP,QAAQ,OACR,KAAK,GACL,KAAK,GACL,eAAe,CAAC,CAAC,GACjB,eAAe,cACf,aAAa,cACb,eAAe,cACf,gBAAgB,cAChB,cAAc;AAElB,WAASC,SAAQ,MAAM;AACrB,SAAK,KACL,KAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,WAAW,YAAY;AAC5B,mBAAe,CAAC,CAAC;AACjB,QAAI;AAAO,WAAK,WAAW,SAAS;AACpC,WAAO;EACT;AAEA,WAAS,aAAa,MAAM;AAC1B,QAAI,IAAI,aAAa,KAAK,KAAK,GAC3B,KAAK,KAAK,KAAK,GACf,KAAK,KAAK,KAAK,GACf,KAAK,KAAK,KAAK,GACf,KAAK,KAAK,KAAK;AACnB,QAAI,KAAK;AAAI,WAAK,MAAM,KAAK,MAAM;AACnC,QAAI,KAAK;AAAI,WAAK,MAAM,KAAK,MAAM;AACnC,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,KAAK;AACV,QAAI,KAAK,UAAU;AACjB,UAAI,aAAa,KAAK,QAAQ,CAAC,IAAI,aAAa,IAAI,IAAI;AACxD,YAAM,YAAY,IAAI,IAAI;AAC1B,YAAM,WAAW,IAAI,IAAI;AACzB,YAAM,aAAa,IAAI,IAAI;AAC3B,YAAM,cAAc,IAAI,IAAI;AAC5B,UAAI,KAAK;AAAI,aAAK,MAAM,KAAK,MAAM;AACnC,UAAI,KAAK;AAAI,aAAK,MAAM,KAAK,MAAM;AACnC,WAAK,MAAM,IAAI,IAAI,IAAI,EAAE;IAC3B;EACF;AAEAA,WAAQ,QAAQ,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,QAAQ,CAAC,CAAC,GAAGA,YAAW;EACrD;AAEAA,WAAQ,OAAO,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAGA,YAAW,CAAC,IAAI,EAAE;EACvE;AAEAA,WAAQ,OAAO,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,OAAO,SAAS,CAAC,GAAGA,YAAW;EAC5D;AAEAA,WAAQ,UAAU,SAAS,GAAG;AAC5B,WAAO,UAAU,SAASA,SAAQ,aAAa,CAAC,EAAE,aAAa,CAAC,IAAIA,SAAQ,aAAY;EAC1F;AAEAA,WAAQ,eAAe,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,eAAe,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,YAAW;EACnG;AAEAA,WAAQ,eAAe,SAAS,GAAG;AACjC,WAAO,UAAU,SAASA,SAAQ,WAAW,CAAC,EAAE,aAAa,CAAC,EAAE,cAAc,CAAC,EAAE,YAAY,CAAC,IAAIA,SAAQ,WAAU;EACtH;AAEAA,WAAQ,aAAa,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,aAAa,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,YAAW;EACjG;AAEAA,WAAQ,eAAe,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,eAAe,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,YAAW;EACnG;AAEAA,WAAQ,gBAAgB,SAAS,GAAG;AAClC,WAAO,UAAU,UAAU,gBAAgB,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,YAAW;EACpG;AAEAA,WAAQ,cAAc,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,cAAc,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,YAAW;EAClG;AAEA,SAAOA;AACT;AC7DA,IAAI,aAAY,KAAA,MAAM;EACpB,cAAc;AACZ,SAAK,QAAQ,CAAA;AACb,SAAK,SAAyB,oBAAI,IAAG;AACrC,SAAK,aAAa,CAAA;AAClB,SAAK,UAA0B,oBAAI,IAAG;AACtC,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;EAC3B;EAIA,WAAW;AACT,WAAO,KAAK;EACd;EACA,YAAY;AACV,UAAM,gBAAgB;AACtB,UAAM,aAAa,UAAS;AAC5B,WAAO,cAAc;MACnB,GAAG,cAAc;MACjB,GAAG,WAAW,WAAW,CAAA;IAC/B,CAAK;EACH;EACA,QAAQ,MAAM,OAAO;AACnB,SAAK,MAAM,KAAK,IAAI;AACpB,SAAK,OAAO,IAAI,MAAM,KAAK;AAC3B,QAAI,UAAU,GAAG;AACf,WAAK,WAAW,KAAK,IAAI;AACzB,WAAK,SAAL,KAAK,OAAS;IAChB;EACF;EACA,UAAU;AACR,WAAO,EAAE,MAAM,IAAI,UAAU,KAAK,WAAU;EAC9C;EACA,SAAS,IAAI,QAAQ;AACnB,UAAM,aAAa,KAAK,QAAQ,IAAI,EAAE,KAAK,EAAE,IAAI,QAAQ,CAAA,GAAI,YAAY,CAAA,EAAE;AAC3E,UAAM,SAAS,OAAO,QAAQ,QAAQ,KAAc,EAAE,QAAQ,MAAM,GAAG,EAAE,QAAQ,QAAQ,GAAG,EAAE,MAAM,GAAG;AACvG,QAAI,QAAQ;AACV,aAAO,QAAQ,CAAC,MAAM;AACpB,YAAI,aAAa,CAAC,GAAG;AACnB,cAAI,cAAA,OAAA,SAAA,WAAY,YAAY;AAC1B,uBAAW,WAAW,KAAK,CAAC;UAC9B,OAAO;AACL,uBAAW,aAAa,CAAC,CAAC;UAC5B;QACF;AACA,YAAI,cAAA,OAAA,SAAA,WAAY,QAAQ;AACtB,qBAAW,OAAO,KAAK,CAAC;QAC1B,OAAO;AACL,qBAAW,SAAS,CAAC,CAAC;QACxB;MACF,CAAC;IACH;AACA,SAAK,QAAQ,IAAI,IAAI,UAAU;EACjC;EACA,aAAa;AACX,WAAO,KAAK;EACd;EACA,kBAAkB,eAAe;;AAC/B,aAAOC,MAAA,KAAK,QAAQ,IAAI,aAAa,MAA9B,OAAA,SAAAA,IAAiC,WAAU,CAAA;EACpD;EACA,QAAQ;AACN,YAAK;AACL,SAAK,QAAQ,CAAA;AACb,SAAK,SAAyB,oBAAI,IAAG;AACrC,SAAK,aAAa,CAAA;AAClB,SAAK,UAA0B,oBAAI,IAAG;AACtC,SAAK,OAAO;EACd;AACF,GA3DI,OAAO,IAAM,WAAW,GAdZ;AA+EhB,SAAS,eAAe,OAAO;AAC7B,MAAI,CAAC,MAAM,QAAQ;AACjB,WAAO,CAAA;EACT;AACA,QAAM,OAAO,CAAA;AACb,QAAM,QAAQ,CAAA;AACd,QAAM,QAAQ,CAAC,SAAS;AACtB,UAAM,OAAO;MACX,MAAM,KAAK;MACX,UAAU,KAAK,SAAS,SAAS,SAAS,CAAA;IAChD;AACI,SAAK,gBAAgB,QAAA,OAAA,SAAA,KAAM;AAC3B,QAAI,QAAA,OAAA,SAAA,KAAM,mBAAmB;AAC3B,WAAK,oBAAoB,CAAC,KAAK,iBAAiB;IAClD;AACA,QAAI,KAAK,SAAS,UAAU,KAAK,UAAU,QAAQ;AACjD,WAAK,QAAQ,KAAK;IACpB;AACA,WAAO,MAAM,SAAS,KAAK,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,KAAK,OAAO;AACtE,YAAM,IAAG;IACX;AACA,QAAI,MAAM,WAAW,GAAG;AACtB,WAAK,KAAK,IAAI;IAChB,OAAO;AACL,YAAM,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE;AACvC,UAAI,OAAO,UAAU;AACnB,eAAO,SAAS,KAAK,IAAI;MAC3B,OAAO;AACL,eAAO,WAAW,CAAC,IAAI;MACzB;IACF;AACA,QAAI,KAAK,SAAS,QAAQ;AACxB,YAAM,KAAK,EAAE,MAAM,OAAO,KAAK,MAAA,CAAO;IACxC;EACF,CAAC;AACD,SAAO;AACT;AACA,OAAO,gBAAgB,gBAAgB;AAGvC,IAAI,WAA2B,OAAO,CAAC,KAAK,OAAO;AACjD,mBAAiB,KAAK,EAAE;AACxB,QAAM,QAAQ,CAAA;AACd,aAAW,OAAO,IAAI,eAAe,CAAA,GAAI;AACvC,QAAI,IAAI,UAAU,qBAAqB;AACrC,SAAG,SAAS,IAAI,aAAa,IAAI,IAAI,aAAa,EAAE;IACtD;EACF;AACA,aAAW,OAAO,IAAI,eAAe,CAAA,GAAI;AACvC,UAAM,OAAO,IAAI;AACjB,QAAI,CAAC,MAAM;AACT;IACF;AACA,UAAM,QAAQ,IAAI,SAAS,SAAS,IAAI,MAAM,IAAI;AAClD,UAAM,OAAO,YAAY,IAAI;AAC7B,UAAM,SAAS,KAAK,gBAAgB,GAAG,kBAAkB,KAAK,aAAa,IAAI,CAAA;AAC/E,UAAM,oBAAoB,OAAO,SAAS,IAAI,OAAO,KAAK,GAAG,IAAI;AACjE,UAAM,WAAW;MACf;MACA;MACA,MAAM,KAAK;MACX,OAAO,KAAK;MACZ,eAAe,KAAK;MACpB;IACN;AACI,UAAM,KAAK,QAAQ;EACrB;AACA,QAAM,iBAAiB,eAAe,KAAK;AAC3C,QAAM,sBAAsC,OAAO,CAAC,OAAO,UAAU;AACnE,eAAW,QAAQ,OAAO;AACxB,SAAG,QAAQ,MAAM,KAAK;AACtB,UAAI,KAAK,YAAY,KAAK,SAAS,SAAS,GAAG;AAC7C,4BAAoB,KAAK,UAAU,QAAQ,CAAC;MAC9C;IACF;EACF,GAAG,qBAAqB;AACxB,sBAAoB,gBAAgB,CAAC;AACvC,GAAG,UAAU;AACb,IAAI,cAA8B,OAAO,CAAC,SAAS;AACjD,SAAO,KAAK,OAAO,OAAO,KAAK,IAAI,IAAI;AACzC,GAAG,aAAa;AAChB,IAAI,SAAS;;EAEX,QAAQ,EAAE,IAAI,OAAM;EACpB,OAAuB,OAAO,OAAO,SAAS;;AAC5C,QAAI;AACF,YAAM,YAAY;AAClB,YAAM,MAAM,MAAM,UAAU,WAAW,IAAI;AAC3C,UAAI,MAAM,gBAAgB,GAAG;AAC7B,YAAM,MAAKA,MAAA,OAAO,WAAP,OAAA,SAAAA,IAAe;AAC1B,UAAI,EAAE,cAAc,YAAY;AAC9B,cAAM,IAAI;UACR;QACV;MACM;AACA,eAAS,KAAK,EAAE;IAClB,SAAS,OAAO;AACd,UAAI,MAAM,0BAA0B,KAAK;AACzC,YAAM;IACR;EACF,GAAG,OAAO;AACZ;AAIA,IAAI,wBAAwB;AAC5B,IAAI,wBAAwB;AAC5B,IAAI,wBAAwB;AAC5B,IAAI,OAAuB,OAAO,CAAC,OAAO,IAAI,UAAU,aAAa;AACnE,QAAM,YAAY,SAAS;AAC3B,QAAM,SAAS,UAAU,UAAS;AAClC,QAAM,sBAAsB,OAAO,WAAW;AAC9C,QAAM,QAAQ,UAAU,gBAAe;AACvC,QAAM,OAAO,UAAU,QAAO;AAC9B,QAAM,EAAE,eAAc,IAAK,UAAS;AACpC,MAAI,CAAC,MAAM;AACT;EACF;AACA,QAAM,cAAc,QAAQ,KAAK;AACjC,QAAM,MAAM,iBAAiB,EAAE;AAC/B,QAAM,QAAQ,OAAO,YAAY,OAAO,YAAY,wBAAwB;AAC5E,QAAM,SAAS,OAAO,aAAa,OAAO,aAAa,wBAAwB;AAC/E,QAAM,WAAW;AACjB,QAAM,YAAY,SAAS;AAC3B,MAAI,KAAK,WAAW,OAAO,QAAQ,IAAI,SAAS,EAAE;AAClD,mBAAiB,KAAK,WAAW,UAAU,OAAO,WAAW;AAC7D,MAAI;AACJ,MAAI;AACF,UAAM,YAAY,OAAO,eAAe;AACxC,QAAI,cAAc,QAAQ;AACxB,oBAA8B,OAAO,CAAC,UAAU,MAAM,OAAO,GAAG,EAAE,KAAK,GAAG,aAAa;IACzF,WAAW,UAAU,WAAW,GAAG,KAAK,UAAU,SAAS,GAAG,GAAG;AAC/D,YAAM,YAAY,QAAQ,KAAK,SAAS;AACxC,YAAM,eAAe,YAAY,UAAU,CAAC,IAAI;AAChD,oBAA8B,OAAO,CAAC,UAAU,MAAM,OAAO,MAAM,YAAY,EAAE,KAAK,GAAG,aAAa;IACxG,WAAW,UAAU,WAAW,GAAG,GAAG;AACpC,YAAM,eAAe,UAAU,UAAU,CAAC;AAC1C,oBAA8B,OAAO,CAAC,UAAU,MAAM,OAAO,gBAAgB,EAAE,EAAE,KAAK,GAAG,aAAa;IACxG,OAAO;AACL,oBAAc,OAAO,SAAS;IAChC;EACF,SAAS,OAAO;AACd,QAAI,MAAM,mCAAmC,KAAK;AAClD,kBAAc,OAAO,GAAG;EAC1B;AACA,QAAM,aAAaC,QAAY,EAAG,MAAM;IACtC;IACA,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;EACnB,CAAG;AACD,QAAM,iBAAiBA,QAAY,EAAG,MAAM;IAC1C;IACA,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;EACnB,CAAG;AACD,QAAM,kBAAkBA,QAAY,EAAG,MAAM;IAC3C,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;EACnB,CAAG;AACD,MAAI,OAAO;AACT,QAAI,OAAO,MAAM,EAAE,KAAK,KAAK,WAAW,CAAC,EAAE,KAAK,KAAK,cAAc,CAAC,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,eAAe,QAAQ,EAAE,KAAK,qBAAqB,QAAQ,EAAE,KAAK,KAAK;EAClL;AACA,QAAM,IAAI,IAAI,OAAO,GAAG,EAAE,KAAK,aAAa,gBAAgB,WAAW,GAAG,EAAE,KAAK,SAAS,kBAAkB;AAC5G,QAAM,gBAAgB,UAAU,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE;AAC7G,QAAM,gBAAgB,QAAA,EAAU,KAAK,CAAC,OAAO,MAAM,CAAC,EAAE;IACpD,CAAC,MAAM,EAAE,YAAY,EAAE,SAAS,SAAS,IAAI,wBAAwB,wBAAwB;EACjG,EAAI,aAAa,mBAAmB,EAAE,YAAY,CAAC,MAAM,EAAE,YAAY,EAAE,SAAS,SAAS,IAAI,wBAAwB,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,YAAY,EAAE,SAAS,SAAS,IAAI,wBAAwB,CAAC,EAAE,cAAc,CAAC,MAAM,EAAE,YAAY,EAAE,SAAS,SAAS,IAAI,wBAAwB,CAAC,EAAE,MAAM,IAAI;AAC7S,QAAM,cAAc,cAAc,aAAa;AAC/C,QAAM,cAAc,YAAY,YAAW,EAAG,OAAO,CAAC,MAAM,EAAE,YAAY,EAAE,SAAS,SAAS,CAAC;AAC/F,QAAM,WAAW,EAAE,UAAU,iBAAiB,EAAE,KAAK,WAAW,EAAE,MAAK,EAAG,OAAO,GAAG,EAAE,KAAK,SAAS,gBAAgB,EAAE,KAAK,aAAa,CAAC,MAAM,aAAa,EAAE,EAAE,IAAI,EAAE,EAAE,GAAG;AAC3K,WAAS,OAAO,MAAM,EAAE,KAAK,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,UAAU,qBAAqB,EAAE,KAAK,SAAS,sBAAsB,EAAE,KAAK,QAAQ,MAAM,EAAE,KAAK,gBAAgB,GAAG,EAAE,KAAK,gBAAgB,GAAG,EAAE,KAAK,SAAS,CAAC,MAAM;AAClO,QAAI,EAAE,UAAU,GAAG;AACjB,aAAO;IACT;AACA,WAAO;EACT,CAAC;AACD,WAAS,OAAO,UAAU,EAAE,KAAK,MAAM,CAAC,IAAI,MAAM,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,OAAO,MAAM,EAAE,KAAK,SAAS,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,UAAU,qBAAqB;AACpL,WAAS,OAAO,MAAM,EAAE,KAAK,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,SAAS,CAAC,IAAI,MAAM;AACpH,WAAO,yBAAyB,CAAC;EACnC,CAAC,EAAE,KAAK,QAAQ,CAAC,MAAM,WAAW,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,gBAAgB,GAAG,EAAE,KAAK,UAAU,CAAC,MAAM,eAAe,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,gBAAgB,CAAC,EAAE,KAAK,kBAAkB,GAAG,EAAE,KAAK,SAAS,CAAC,MAAM;AACpM,QAAI,EAAE,UAAU,GAAG;AACjB,aAAO;IACT;AACA,UAAM,SAAS,cAAc,EAAE,mBAAmB,EAAE,KAAK,kBAAA,CAAmB;AAC5E,WAAO,OAAO,aAAa,MAAM,OAAO,aAAa,KAAK,GAAG;EAC/D,CAAC;AACD,WAAS,OAAO,MAAM,EAAE,KAAK,SAAS,qBAAqB,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,wBAAwB,CAAC,EAAE,KAAK,qBAAqB,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,UAAU,IAAI,KAAK,EAAE,KAAK,IAAI,EAAE,KAAK,eAAe,MAAM,EAAE,KAAK,SAAS,CAAC,MAAM;AAChP,QAAI,EAAE,UAAU,GAAG;AACjB,aAAO;IACT;AACA,UAAM,cAAc,sDAAsD,gBAAgB,EAAE,KAAK,IAAI,IAAI;AACzG,UAAM,SAAS,cAAc,EAAE,mBAAmB,EAAE,KAAK,kBAAA,CAAmB;AAC5E,WAAO,cAAc,OAAO,YAAY,QAAQ,UAAU,OAAO;EACnE,CAAC,EAAE,KAAK,SAAS,GAAG;AAClB,QAAI,EAAE,UAAU,GAAG;AACjB;IACF;AACA,UAAM,OAAO,OAAO,IAAI;AACxB,UAAM,eAAe,EAAE,KAAK;AAC5B,SAAK,KAAK,YAAY;AACtB,UAAM,mBAAmB,EAAE,KAAK,EAAE;AAClC,UAAM,iBAAiB;AACvB,QAAI;AACJ,QAAI,OAAO,eAAe,SAAS,EAAE,OAAO;AAC1C,YAAM,uBAAuB,mBAAmB;AAChD,YAAM,gCAAgC;AACtC,YAAM,0BAA0B;AAChC,YAAM,sBAAsB,uBAAuB,gCAAgC;AACnF,4BAAsB,sBAAsB;IAC9C,OAAO;AACL,YAAM,uBAAuB;AAC7B,4BAAsB,mBAAmB,iBAAiB;IAC5D;AACA,UAAM,wBAAwB;AAC9B,UAAM,uBAAuB,KAAK,IAAI,uBAAuB,mBAAmB;AAChF,UAAM,WAAW,KAAK,KAAI;AAC1B,UAAM,2BAA2B,SAAS,sBAAqB;AAC/D,QAAI,2BAA2B,sBAAsB;AACnD,YAAM,WAAW;AACjB,UAAI,uBAAuB;AAC3B,aAAO,qBAAqB,SAAS,GAAG;AACtC,+BAAuB,aAAa,UAAU,GAAG,qBAAqB,SAAS,CAAC;AAChF,YAAI,qBAAqB,WAAW,GAAG;AACrC,eAAK,KAAK,QAAQ;AAClB,cAAI,SAAS,sBAAqB,IAAK,sBAAsB;AAC3D,iBAAK,KAAK,EAAE;UACd;AACA;QACF;AACA,aAAK,KAAK,uBAAuB,QAAQ;AACzC,YAAI,SAAS,sBAAqB,KAAM,sBAAsB;AAC5D;QACF;MACF;IACF;EACF,CAAC;AACD,MAAI,OAAO,eAAe,OAAO;AAC/B,aAAS,OAAO,MAAM,EAAE,KAAK,SAAS,qBAAqB,EAAE,KAAK,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,KAAK,wBAAwB,CAAC,EAAE,KAAK,eAAe,KAAK,EAAE,KAAK,qBAAqB,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,QAAQ,YAAY,EAAE,KAAK,IAAI,EAAE,EAAE,KAAK,cAAc,QAAQ,EAAE,KAAK,SAAS,CAAC,MAAM;AACrS,UAAI,EAAE,UAAU,GAAG;AACjB,eAAO;MACT;AACA,YAAM,cAAc,wEAAwE,gBAAgB,EAAE,KAAK,IAAI,IAAI;AAC3H,YAAM,SAAS,cAAc,EAAE,mBAAmB,EAAE,KAAK,kBAAA,CAAmB;AAC5E,aAAO,cAAc,OAAO,YAAY,QAAQ,UAAU,OAAO;IACnE,CAAC;EACH;AACA,QAAM,YAAY,YAAY,OAAM;AACpC,QAAM,OAAO,EAAE,UAAU,mBAAmB,EAAE,KAAK,SAAS,EAAE,MAAK,EAAG,OAAO,GAAG,EAAE,KAAK,SAAS,CAAC,GAAG,MAAM;AACxG,WAAO,oCAAoC,CAAC,GAAG,EAAE,KAAK,gBAAgB,IAAI,EAAE,KAAK,aAAa,KAAK,EAAE;EACvG,CAAC,EAAE,KAAK,aAAa,CAAC,MAAM,aAAa,EAAE,EAAE,IAAI,EAAE,EAAE,GAAG;AACxD,OAAK,OAAO,MAAM,EAAE,KAAK,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,SAAS,aAAa,EAAE,KAAK,QAAQ,CAAC,MAAM;AACxI,WAAO,EAAE,SAAS,WAAW,EAAE,OAAO,KAAK,IAAI,IAAI,WAAW,EAAE,KAAK,IAAI;EAC3E,CAAC,EAAE,KAAK,SAAS,CAAC,MAAM;AACtB,UAAM,SAAS,cAAc,EAAE,mBAAmB,EAAE,KAAK,kBAAA,CAAmB;AAC5E,WAAO,OAAO;EAChB,CAAC,EAAE,KAAK,gBAAgB,GAAG,EAAE,KAAK,UAAU,CAAC,MAAM;AACjD,WAAO,EAAE,SAAS,WAAW,EAAE,OAAO,KAAK,IAAI,IAAI,WAAW,EAAE,KAAK,IAAI;EAC3E,CAAC,EAAE,KAAK,gBAAgB,CAAC;AACzB,OAAK,OAAO,UAAU,EAAE,KAAK,MAAM,CAAC,IAAI,MAAM,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,OAAO,MAAM,EAAE,KAAK,SAAS,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,UAAU,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACrL,QAAM,aAAa,KAAK,OAAO,MAAM,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,KAAK,SAAS,CAAC,MAAM;AAChK,UAAM,cAAc,0EAA0E,gBAAgB,EAAE,KAAK,IAAI,IAAI;AAC7H,UAAM,SAAS,cAAc,EAAE,mBAAmB,EAAE,KAAK,kBAAA,CAAmB;AAC5E,WAAO,cAAc,OAAO,YAAY,QAAQ,UAAU,OAAO;EACnE,CAAC,EAAE,KAAK,aAAa,CAAC,IAAI,MAAM,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI;AAChF,aAAW,KAAK,SAAS,GAAG;AAC1B,UAAM,OAAO,OAAO,IAAI;AACxB,UAAM,YAAY,EAAE,KAAK,EAAE;AAC3B,UAAM,aAAa,EAAE,KAAK,EAAE;AAC5B,UAAM,WAAW,KAAK,KAAI;AAC1B,UAAM,UAAU;AAChB,UAAM,iBAAiB,YAAY,IAAI;AACvC,UAAM,kBAAkB,aAAa,IAAI;AACzC,QAAI,iBAAiB,MAAM,kBAAkB,IAAI;AAC/C,WAAK,MAAM,WAAW,MAAM;AAC5B;IACF;AACA,QAAI,uBAAuB,SAAS,KAAK,MAAM,WAAW,GAAG,EAAE;AAC/D,UAAM,mBAAmB;AACzB,UAAM,2BAA2B;AACjC,UAAM,mBAAmB;AACzB,UAAM,mBAAmB;AACzB,UAAM,8BAA8B;AACpC,WAAO,SAAS,sBAAqB,IAAK,kBAAkB,uBAAuB,kBAAkB;AACnG;AACA,WAAK,MAAM,aAAa,GAAG,oBAAoB,IAAI;IACrD;AACA,QAAI,2BAA2B,KAAK;MAClC;MACA,KAAK,IAAI,0BAA0B,KAAK,MAAM,uBAAuB,gBAAgB,CAAC;IAC5F;AACI,QAAI,iBAAiB,uBAAuB,8BAA8B;AAC1E,WAAO,iBAAiB,mBAAmB,uBAAuB,kBAAkB;AAClF;AACA,iCAA2B,KAAK;QAC9B;QACA,KAAK,IAAI,0BAA0B,KAAK,MAAM,uBAAuB,gBAAgB,CAAC;MAC9F;AACM,UAAI,2BAA2B,oBAAoB,yBAAyB,kBAAkB;AAC5F;MACF;AACA,WAAK,MAAM,aAAa,GAAG,oBAAoB,IAAI;AACnD,uBAAiB,uBAAuB,8BAA8B;IAGxE;AACA,SAAK,MAAM,aAAa,GAAG,oBAAoB,IAAI;AACnD,QAAI,SAAS,sBAAA,IAA0B,kBAAkB,uBAAuB,oBAAoB,kBAAkB,sBAAsB;AAC1I,WAAK,MAAM,WAAW,MAAM;IAC9B;EACF,CAAC;AACD,MAAI,OAAO,eAAe,OAAO;AAC/B,UAAM,aAAa,KAAK,OAAO,MAAM,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,KAAK,KAAK,SAAS,GAAG;AAC7H,cAAQ,EAAE,KAAK,EAAE,MAAM;IACzB,CAAC,EAAE,KAAK,SAAS,CAAC,MAAM;AACtB,YAAM,cAAc,2EAA2E,gBAAgB,EAAE,KAAK,IAAI,IAAI;AAC9H,YAAM,SAAS,cAAc,EAAE,mBAAmB,EAAE,KAAK,kBAAA,CAAmB;AAC5E,aAAO,cAAc,OAAO,YAAY,QAAQ,UAAU,OAAO;IACnE,CAAC,EAAE,KAAK,aAAa,CAAC,IAAI,MAAM,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,QAAQ,YAAY,EAAE,KAAK,IAAI,EAAE;AACxG,eAAW,KAAK,SAAS,GAAG;AAC1B,YAAM,mBAAmB,OAAO,IAAI;AACpC,YAAM,iBAAiB,KAAK;AAC5B,UAAI,CAAC,gBAAgB;AACnB,yBAAiB,MAAM,WAAW,MAAM;AACxC;MACF;AACA,YAAM,eAAe,OAAO,cAAc,EAAE,OAAO,eAAe;AAClE,UAAI,aAAa,MAAA,KAAW,aAAa,MAAM,SAAS,MAAM,QAAQ;AACpE,yBAAiB,MAAM,WAAW,MAAM;AACxC;MACF;AACA,YAAM,qBAAqB,WAAW,aAAa,MAAM,WAAW,CAAC;AACrE,YAAM,wBAAwB;AAC9B,YAAM,mBAAmB;AACzB,YAAM,mBAAmB;AACzB,YAAM,8BAA8B;AACpC,YAAM,sBAAsB,KAAK;QAC/B;QACA,KAAK,IAAI,uBAAuB,KAAK,MAAM,qBAAqB,gBAAgB,CAAC;MACzF;AACM,uBAAiB,MAAM,aAAa,GAAG,mBAAmB,IAAI;AAC9D,YAAM,gBAAgB,EAAE,KAAK,EAAE,MAAM;AACrC,YAAM,kBAAkB,eAAe,qBAAqB,IAAI;AAChE,uBAAiB,KAAK,KAAK,eAAe;AAC1C,YAAM,YAAY,EAAE,KAAK,EAAE;AAC3B,YAAM,kBAAkB,EAAE,KAAK,EAAE;AACjC,YAAM,oBAAoB;AAC1B,YAAM,kBAAkB,kBAAkB;AAC1C,YAAM,yBAAyB,YAAY,IAAI;AAC/C,UAAI,iBAAiB,KAAA,EAAO,sBAAA,IAA0B,0BAA0B,kBAAkB,sBAAsB,mBAAmB,sBAAsB,kBAAkB;AACjL,yBAAiB,MAAM,WAAW,MAAM;MAC1C,OAAO;AACL,yBAAiB,MAAM,WAAW,IAAI;MACxC;IACF,CAAC;EACH;AACA,QAAM,iBAAiB,OAAO,kBAAkB;AAChD,sBAAoB,KAAK,gBAAgB,cAAa,UAAA,OAAA,SAAA,OAAQ,gBAAe,KAAK;AACpF,GAAG,MAAM;AACT,IAAI,aAA6B,OAAO,SAAS,OAAO,YAAY;AAClE,SAAO,WAAW,GAAG,WAAU;AACjC,GAAG,YAAY;AACf,IAAI,WAAW,EAAE,MAAM,WAAU;AAGjC,IAAI,6BAA6B;EAC/B,oBAAoB;EACpB,oBAAoB;EACpB,kBAAkB;EAClB,iBAAiB;EACjB,iBAAiB;EACjB,eAAe;EACf,YAAY;EACZ,eAAe;EACf,eAAe;EACf,YAAY;EACZ,YAAY;EACZ,eAAe;AACjB;AACA,IAAI,YAA4B,OAAO,CAAC;EACtC,SAAS;AACX,IAAI,CAAA,MAAO;AACT,QAAM,UAAU,cAAc,4BAA4B,QAAQ;AAClE,SAAO;;cAEK,QAAQ,kBAAkB;oBACpB,QAAQ,kBAAkB;YAClC,QAAQ,gBAAgB;;;cAGtB,QAAQ,eAAe;oBACjB,QAAQ,eAAe;YAC/B,QAAQ,aAAa;;;YAGrB,QAAQ,UAAU;iBACb,QAAQ,aAAa;;;YAG1B,QAAQ,UAAU;iBACb,QAAQ,aAAa;;;YAG1B,QAAQ,UAAU;iBACb,QAAQ,aAAa;;;AAGtC,GAAG,WAAW;AACd,IAAI,iBAAiB;AAGlB,IAAC,UAAU;EACZ;EACA,IAAI,KAAK;AACP,WAAO,IAAI,UAAS;EACtB;EACA;EACA,QAAQ;AACV;", "names": ["squarify", "treemap", "_a", "scaleOrdinal"]}