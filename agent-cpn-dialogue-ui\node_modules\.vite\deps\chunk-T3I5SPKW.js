import {
  __name
} from "./chunk-E47MS5QI.js";

// node_modules/.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/vue-element-plus-x/dist/chunk-AACKK3MU-DcmAWiOl.js
var _a;
var ImperativeState = (_a = class {
  /**
   * @param init - Function that creates the default state.
   */
  constructor(init) {
    this.init = init;
    this.records = this.init();
  }
  reset() {
    this.records = this.init();
  }
}, __name(_a, "ImperativeState"), _a);

export {
  ImperativeState
};
//# sourceMappingURL=chunk-T3I5SPKW.js.map
