{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/nim.mjs"], "sourcesContent": ["import c from './c.mjs';\nimport html from './html.mjs';\nimport xml from './xml.mjs';\nimport javascript from './javascript.mjs';\nimport css from './css.mjs';\nimport glsl from './glsl.mjs';\nimport markdown from './markdown.mjs';\nimport './java.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Nim\", \"fileTypes\": [\"nim\"], \"name\": \"nim\", \"patterns\": [{ \"begin\": \"[ \\\\t]*##\\\\[\", \"contentName\": \"comment.block.doc-comment.content.nim\", \"end\": \"\\\\]##\", \"name\": \"comment.block.doc-comment.nim\", \"patterns\": [{ \"include\": \"#multilinedoccomment\", \"name\": \"comment.block.doc-comment.nested.nim\" }] }, { \"begin\": \"[ \\\\t]*#\\\\[\", \"contentName\": \"comment.block.content.nim\", \"end\": \"\\\\]#\", \"name\": \"comment.block.nim\", \"patterns\": [{ \"include\": \"#multilinecomment\", \"name\": \"comment.block.nested.nim\" }] }, { \"begin\": \"(^[ \\\\t]+)?(?=##)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.nim\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"##\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.nim\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.doc-comment.nim\" }] }, { \"begin\": \"(^[ \\\\t]+)?(?=#[^\\\\[])\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.nim\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.nim\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.nim\" }] }, { \"comment\": \"A nim procedure or method\", \"name\": \"meta.proc.nim\", \"patterns\": [{ \"begin\": \"\\\\b(proc|method|template|macro|iterator|converter|func)\\\\s+\\\\`?([^\\\\:\\\\{\\\\s\\\\`\\\\*\\\\(]*)\\\\`?(\\\\s*\\\\*)?\\\\s*(?=\\\\(|\\\\=|:|\\\\[|\\\\n|\\\\{)\", \"captures\": { \"1\": { \"name\": \"keyword.other\" }, \"2\": { \"name\": \"entity.name.function.nim\" }, \"3\": { \"name\": \"keyword.control.export\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"source.nim\" }] }] }, { \"begin\": 'discard \"\"\"', \"comment\": \"A discarded triple string literal comment\", \"end\": '\"\"\"(?!\")', \"name\": \"comment.line.discarded.nim\" }, { \"include\": \"#float_literal\" }, { \"include\": \"#integer_literal\" }, { \"comment\": \"Operator as function name\", \"match\": \"(?<=\\\\`)[^\\\\` ]+(?=\\\\`)\", \"name\": \"entity.name.function.nim\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.export\" } }, \"comment\": \"Export qualifier.\", \"match\": \"\\\\b\\\\s*(\\\\*)(?:\\\\s*(?=[,:])|\\\\s+(?=[=]))\" }, { \"captures\": { \"1\": { \"name\": \"support.type.nim\" }, \"2\": { \"name\": \"keyword.control.export\" } }, \"comment\": \"Export qualifier following a type def.\", \"match\": \"\\\\b([A-Z]\\\\w+)(\\\\*)\" }, { \"include\": \"#string_literal\" }, { \"comment\": \"Language Constants.\", \"match\": \"\\\\b(true|false|Inf|NegInf|NaN|nil)\\\\b\", \"name\": \"constant.language.nim\" }, { \"comment\": \"Keywords that affect program control flow or scope.\", \"match\": \"\\\\b(block|break|case|continue|do|elif|else|end|except|finally|for|if|raise|return|try|when|while|yield)\\\\b\", \"name\": \"keyword.control.nim\" }, { \"comment\": \"Keyword boolean operators for expressions.\", \"match\": \"(\\\\b(and|in|is|isnot|not|notin|or|xor)\\\\b)\", \"name\": \"keyword.boolean.nim\" }, { \"comment\": \"Generic operators for expressions.\", \"match\": \"(=|\\\\+|-|\\\\*|/|<|>|@|\\\\$|~|&|%|!|\\\\?|\\\\^|\\\\.|:|\\\\\\\\)+\", \"name\": \"keyword.operator.nim\" }, { \"comment\": \"Other keywords.\", \"match\": \"(\\\\b(addr|as|asm|atomic|bind|cast|const|converter|concept|defer|discard|distinct|div|enum|export|from|import|include|let|mod|mixin|object|of|ptr|ref|shl|shr|static|type|using|var|tuple|iterator|macro|func|method|proc|template)\\\\b)\", \"name\": \"keyword.other.nim\" }, { \"comment\": \"Invalid and unused keywords.\", \"match\": \"(\\\\b(generic|interface|lambda|out|shared)\\\\b)\", \"name\": \"invalid.illegal.invalid-keyword.nim\" }, { \"comment\": \"Common functions\", \"match\": \"\\\\b(new|await|assert|echo|defined|declared|newException|countup|countdown|high|low)\\\\b\", \"name\": \"keyword.other.common.function.nim\" }, { \"comment\": \"Built-in, concrete types.\", \"match\": \"\\\\b(((uint|int)(8|16|32|64)?)|float(32|64)?|bool|string|auto|cstring|char|byte|tobject|typedesc|stmt|expr|any|untyped|typed)\\\\b\", \"name\": \"storage.type.concrete.nim\" }, { \"comment\": \"Built-in, generic types.\", \"match\": \"\\\\b(range|array|seq|set|pointer)\\\\b\", \"name\": \"storage.type.generic.nim\" }, { \"comment\": \"Special types.\", \"match\": \"\\\\b(openarray|varargs|void)\\\\b\", \"name\": \"storage.type.generic.nim\" }, { \"comment\": \"Other constants.\", \"match\": \"\\\\b[A-Z][A-Z0-9_]+\\\\b\", \"name\": \"support.constant.nim\" }, { \"comment\": \"Other types.\", \"match\": \"\\\\b[A-Z]\\\\w+\\\\b\", \"name\": \"support.type.nim\" }, { \"comment\": \"Function call.\", \"match\": \"\\\\b\\\\w+\\\\b(?=(\\\\[([a-zA-Z0-9_,]|\\\\s)+\\\\])?\\\\()\", \"name\": \"support.function.any-method.nim\" }, { \"comment\": \"Function call (no parenthesis).\", \"match\": \"(?!(openarray|varargs|void|range|array|seq|set|pointer|new|await|assert|echo|defined|declared|newException|countup|countdown|high|low|((uint|int)(8|16|32|64)?)|float(32|64)?|bool|string|auto|cstring|char|byte|tobject|typedesc|stmt|expr|any|untyped|typed|addr|as|asm|atomic|bind|cast|const|converter|concept|defer|discard|distinct|div|enum|export|from|import|include|let|mod|mixin|object|of|ptr|ref|shl|shr|static|type|using|var|tuple|iterator|macro|func|method|proc|template|and|in|is|isnot|not|notin|or|xor|proc|method|template|macro|iterator|converter|func|true|false|Inf|NegInf|NaN|nil|block|break|case|continue|do|elif|else|end|except|finally|for|if|raise|return|try|when|while|yield)\\\\b)\\\\w+\\\\s+(?!(and|in|is|isnot|not|notin|or|xor|[^a-zA-Z0-9_\\\"'`(-+]+)\\\\b)(?=[a-zA-Z0-9_\\\"'`(-+])\", \"name\": \"support.function.any-method.nim\" }, { \"begin\": '(^\\\\s*)?(?=\\\\{\\\\.emit: ?\"\"\")', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.leading.nim\" } }, \"end\": \"(?!\\\\G)(\\\\s*$\\\\n?)?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.trailing.nim\" } }, \"patterns\": [{ \"begin\": '\\\\{\\\\.(emit:) ?(\"\"\")', \"captures\": { \"1\": { \"name\": \"keyword.other.nim\" }, \"2\": { \"name\": \"punctuation.section.embedded.begin.nim\" } }, \"contentName\": \"source.c\", \"end\": '(\")\"\"(?!\")(\\\\.{0,1}\\\\})?', \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.nim\" }, \"1\": { \"name\": \"source.c\" } }, \"name\": \"meta.embedded.block.c\", \"patterns\": [{ \"begin\": \"\\\\`\", \"end\": \"\\\\`\", \"name\": \"keyword.operator.nim\" }, { \"include\": \"source.c\" }] }] }, { \"begin\": \"\\\\{\\\\.\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.pragma.start.nim\" } }, \"end\": \"\\\\.?\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.pragma.end.nim\" } }, \"patterns\": [{ \"begin\": \"\\\\b([[:alpha:]]\\\\w*)(?:\\\\s|\\\\s*:)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.preprocessor.pragma.nim\" } }, \"end\": \"(?=\\\\.?\\\\}|,)\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"begin\": \"\\\\b([[:alpha:]]\\\\w*)\\\\(\", \"beginCaptures\": { \"1\": { \"name\": \"meta.preprocessor.pragma.nim\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"captures\": { \"1\": { \"name\": \"meta.preprocessor.pragma.nim\" } }, \"match\": \"\\\\b([[:alpha:]]\\\\w*)(?=\\\\.?\\\\}|,)\" }, { \"begin\": '\\\\b([[:alpha:]]\\\\w*)(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"meta.preprocessor.pragma.nim\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.nim\" } }, \"end\": '\"\"\"(?!\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.nim\" } }, \"name\": \"string.quoted.triple.raw.nim\" }, { \"begin\": '\\\\b([[:alpha:]]\\\\w*)(\")', \"beginCaptures\": { \"1\": { \"name\": \"meta.preprocessor.pragma.nim\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.nim\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.nim\" } }, \"name\": \"string.quoted.double.raw.nim\" }, { \"begin\": \"\\\\b(hint\\\\[\\\\w+\\\\]):\", \"beginCaptures\": { \"1\": { \"name\": \"meta.preprocessor.pragma.nim\" } }, \"end\": \"(?=\\\\.?\\\\}|,)\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"match\": \",\", \"name\": \"punctuation.separator.comma.nim\" }] }, { \"begin\": '(^\\\\s*)?(?=asm \"\"\")', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.leading.nim\" } }, \"end\": \"(?!\\\\G)(\\\\s*$\\\\n?)?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.trailing.nim\" } }, \"patterns\": [{ \"begin\": '(asm) (\"\"\")', \"captures\": { \"1\": { \"name\": \"keyword.other.nim\" }, \"2\": { \"name\": \"punctuation.section.embedded.begin.nim\" } }, \"contentName\": \"source.asm\", \"end\": '(\")\"\"(?!\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.nim\" }, \"1\": { \"name\": \"source.asm\" } }, \"name\": \"meta.embedded.block.asm\", \"patterns\": [{ \"begin\": \"\\\\`\", \"end\": \"\\\\`\", \"name\": \"keyword.operator.nim\" }, { \"include\": \"source.asm\" }] }] }, { \"captures\": { \"1\": { \"name\": \"storage.type.function.nim\" }, \"2\": { \"name\": \"keyword.operator.nim\" } }, \"comment\": \"tmpl specifier\", \"match\": '(tmpl(i)?)(?=( (html|xml|js|css|glsl|md))?\"\"\")' }, { \"begin\": '(^\\\\s*)?(?=html\"\"\")', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.leading.nim\" } }, \"end\": \"(?!\\\\G)(\\\\s*$\\\\n?)?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.trailing.nim\" } }, \"patterns\": [{ \"begin\": '(html)(\"\"\")', \"captures\": { \"1\": { \"name\": \"keyword.other.nim\" }, \"2\": { \"name\": \"punctuation.section.embedded.begin.nim\" } }, \"contentName\": \"text.html\", \"end\": '(\")\"\"(?!\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.nim\" }, \"1\": { \"name\": \"text.html\" } }, \"name\": \"meta.embedded.block.html\", \"patterns\": [{ \"begin\": \"(?<!\\\\$)(\\\\$)\\\\(\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"begin\": \"(?<!\\\\$)(\\\\$)\\\\{\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" }, \"2\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"begin\": \"(?<!\\\\$)(\\\\$)(for|while|case|of|when|if|else|elif)( )\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" }, \"2\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"(\\\\{|\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"plain\" } }, \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"match\": \"(?<!\\\\$)(\\\\$\\\\w+)\", \"name\": \"keyword.operator.nim\" }, { \"include\": \"text.html.basic\" }] }] }, { \"begin\": '(^\\\\s*)?(?=xml\"\"\")', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.leading.nim\" } }, \"end\": \"(?!\\\\G)(\\\\s*$\\\\n?)?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.trailing.nim\" } }, \"patterns\": [{ \"begin\": '(xml)(\"\"\")', \"captures\": { \"1\": { \"name\": \"keyword.other.nim\" }, \"2\": { \"name\": \"punctuation.section.embedded.begin.nim\" } }, \"contentName\": \"text.xml\", \"end\": '(\")\"\"(?!\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.nim\" }, \"1\": { \"name\": \"text.xml\" } }, \"name\": \"meta.embedded.block.xml\", \"patterns\": [{ \"begin\": \"(?<!\\\\$)(\\\\$)\\\\(\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"begin\": \"(?<!\\\\$)(\\\\$)\\\\{\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" }, \"2\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"begin\": \"(?<!\\\\$)(\\\\$)(for|while|case|of|when|if|else|elif)( )\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" }, \"2\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"(\\\\{|\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"plain\" } }, \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"match\": \"(?<!\\\\$)(\\\\$\\\\w+)\", \"name\": \"keyword.operator.nim\" }, { \"include\": \"text.xml\" }] }] }, { \"begin\": '(^\\\\s*)?(?=js\"\"\")', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.leading.nim\" } }, \"end\": \"(?!\\\\G)(\\\\s*$\\\\n?)?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.trailing.nim\" } }, \"patterns\": [{ \"begin\": '(js)(\"\"\")', \"captures\": { \"1\": { \"name\": \"keyword.other.nim\" }, \"2\": { \"name\": \"punctuation.section.embedded.begin.nim\" } }, \"contentName\": \"source.js\", \"end\": '(\")\"\"(?!\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.nim\" }, \"1\": { \"name\": \"source.js\" } }, \"name\": \"meta.embedded.block.js\", \"patterns\": [{ \"begin\": \"(?<!\\\\$)(\\\\$)\\\\(\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"begin\": \"(?<!\\\\$)(\\\\$)\\\\{\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" }, \"2\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"begin\": \"(?<!\\\\$)(\\\\$)(for|while|case|of|when|if|else|elif)( )\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" }, \"2\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"(\\\\{|\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"plain\" } }, \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"match\": \"(?<!\\\\$)(\\\\$\\\\w+)\", \"name\": \"keyword.operator.nim\" }, { \"include\": \"source.js\" }] }] }, { \"begin\": '(^\\\\s*)?(?=css\"\"\")', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.leading.nim\" } }, \"end\": \"(?!\\\\G)(\\\\s*$\\\\n?)?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.trailing.nim\" } }, \"patterns\": [{ \"begin\": '(css)(\"\"\")', \"captures\": { \"1\": { \"name\": \"keyword.other.nim\" }, \"2\": { \"name\": \"punctuation.section.embedded.begin.nim\" } }, \"contentName\": \"source.css\", \"end\": '(\")\"\"(?!\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.nim\" }, \"1\": { \"name\": \"source.css\" } }, \"name\": \"meta.embedded.block.css\", \"patterns\": [{ \"begin\": \"(?<!\\\\$)(\\\\$)\\\\(\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"begin\": \"(?<!\\\\$)(\\\\$)\\\\{\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" }, \"2\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"begin\": \"(?<!\\\\$)(\\\\$)(for|while|case|of|when|if|else|elif)( )\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" }, \"2\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"(\\\\{|\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"plain\" } }, \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"match\": \"(?<!\\\\$)(\\\\$\\\\w+)\", \"name\": \"keyword.operator.nim\" }, { \"include\": \"source.css\" }] }] }, { \"begin\": '(^\\\\s*)?(?=glsl\"\"\")', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.leading.nim\" } }, \"end\": \"(?!\\\\G)(\\\\s*$\\\\n?)?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.trailing.nim\" } }, \"patterns\": [{ \"begin\": '(glsl)(\"\"\")', \"captures\": { \"1\": { \"name\": \"keyword.other.nim\" }, \"2\": { \"name\": \"punctuation.section.embedded.begin.nim\" } }, \"contentName\": \"source.glsl\", \"end\": '(\")\"\"(?!\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.nim\" }, \"1\": { \"name\": \"source.glsl\" } }, \"name\": \"meta.embedded.block.glsl\", \"patterns\": [{ \"begin\": \"(?<!\\\\$)(\\\\$)\\\\(\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"begin\": \"(?<!\\\\$)(\\\\$)\\\\{\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" }, \"2\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"begin\": \"(?<!\\\\$)(\\\\$)(for|while|case|of|when|if|else|elif)( )\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" }, \"2\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"(\\\\{|\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"plain\" } }, \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"match\": \"(?<!\\\\$)(\\\\$\\\\w+)\", \"name\": \"keyword.operator.nim\" }, { \"include\": \"source.glsl\" }] }] }, { \"begin\": '(^\\\\s*)?(?=md\"\"\")', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.leading.nim\" } }, \"end\": \"(?!\\\\G)(\\\\s*$\\\\n?)?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.trailing.nim\" } }, \"patterns\": [{ \"begin\": '(md)(\"\"\")', \"captures\": { \"1\": { \"name\": \"keyword.other.nim\" }, \"2\": { \"name\": \"punctuation.section.embedded.begin.nim\" } }, \"contentName\": \"text.html.markdown\", \"end\": '(\")\"\"(?!\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.nim\" }, \"1\": { \"name\": \"text.html.markdown\" } }, \"name\": \"meta.embedded.block.html.markdown\", \"patterns\": [{ \"begin\": \"(?<!\\\\$)(\\\\$)\\\\(\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"begin\": \"(?<!\\\\$)(\\\\$)\\\\{\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" }, \"2\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"begin\": \"(?<!\\\\$)(\\\\$)(for|while|case|of|when|if|else|elif)( )\", \"captures\": { \"1\": { \"name\": \"keyword.operator.nim\" }, \"2\": { \"name\": \"keyword.operator.nim\" } }, \"end\": \"(\\\\{|\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"plain\" } }, \"patterns\": [{ \"include\": \"source.nim\" }] }, { \"match\": \"(?<!\\\\$)(\\\\$\\\\w+)\", \"name\": \"keyword.operator.nim\" }, { \"include\": \"text.html.markdown\" }] }] }], \"repository\": { \"char_escapes\": { \"patterns\": [{ \"match\": \"\\\\\\\\[cC]|\\\\\\\\[rR]\", \"name\": \"constant.character.escape.carriagereturn.nim\" }, { \"match\": \"\\\\\\\\[lL]|\\\\\\\\[nN]\", \"name\": \"constant.character.escape.linefeed.nim\" }, { \"match\": \"\\\\\\\\[fF]\", \"name\": \"constant.character.escape.formfeed.nim\" }, { \"match\": \"\\\\\\\\[tT]\", \"name\": \"constant.character.escape.tabulator.nim\" }, { \"match\": \"\\\\\\\\[vV]\", \"name\": \"constant.character.escape.verticaltabulator.nim\" }, { \"match\": '\\\\\\\\\\\\\"', \"name\": \"constant.character.escape.double-quote.nim\" }, { \"match\": \"\\\\\\\\'\", \"name\": \"constant.character.escape.single-quote.nim\" }, { \"match\": \"\\\\\\\\[0-9]+\", \"name\": \"constant.character.escape.chardecimalvalue.nim\" }, { \"match\": \"\\\\\\\\[aA]\", \"name\": \"constant.character.escape.alert.nim\" }, { \"match\": \"\\\\\\\\[bB]\", \"name\": \"constant.character.escape.backspace.nim\" }, { \"match\": \"\\\\\\\\[eE]\", \"name\": \"constant.character.escape.escape.nim\" }, { \"match\": \"\\\\\\\\[xX]\\\\h\\\\h\", \"name\": \"constant.character.escape.hex.nim\" }, { \"match\": \"\\\\\\\\\\\\\\\\\", \"name\": \"constant.character.escape.backslash.nim\" }] }, \"extended_string_quoted_double_raw\": { \"begin\": '\\\\b(\\\\w+)(\")', \"beginCaptures\": { \"1\": { \"name\": \"support.function.any-method.nim\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.nim\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.nim\" } }, \"name\": \"string.quoted.double.raw.nim\", \"patterns\": [{ \"include\": \"#raw_string_escapes\" }] }, \"extended_string_quoted_triple_raw\": { \"begin\": '\\\\b(\\\\w+)(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"support.function.any-method.nim\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.nim\" } }, \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.nim\" } }, \"name\": \"string.quoted.triple.raw.nim\" }, \"float_literal\": { \"patterns\": [{ \"match\": \"\\\\b\\\\d[_\\\\d]*((\\\\.\\\\d[_\\\\d]*([eE][\\\\+\\\\-]?\\\\d[_\\\\d]*)?)|([eE][\\\\+\\\\-]?\\\\d[_\\\\d]*))('([fF](32|64|128)|[fFdD]))?\", \"name\": \"constant.numeric.float.decimal.nim\" }, { \"match\": \"\\\\b0[xX]\\\\h[_\\\\h]*'([fF](32|64|128)|[fFdD])\", \"name\": \"constant.numeric.float.hexadecimal.nim\" }, { \"match\": \"\\\\b0o[0-7][_0-7]*'([fF](32|64|128)|[fFdD])\", \"name\": \"constant.numeric.float.octal.nim\" }, { \"match\": \"\\\\b0(b|B)[01][_01]*'([fF](32|64|128)|[fFdD])\", \"name\": \"constant.numeric.float.binary.nim\" }, { \"match\": \"\\\\b(\\\\d[_\\\\d]*)'([fF](32|64|128)|[fFdD])\", \"name\": \"constant.numeric.float.decimal.nim\" }] }, \"fmt_interpolation\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.template-expression.begin.nim\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.template-expression.end.nim\" } }, \"name\": \"meta.template.expression.nim\", \"patterns\": [{ \"begin\": \":\", \"end\": \"(?=\\\\})\", \"name\": \"meta.template.format-specifier.nim\" }, { \"include\": \"source.nim\" }] }, \"fmt_string\": { \"begin\": '\\\\b(fmt)(\")', \"beginCaptures\": { \"1\": { \"name\": \"support.function.any-method.nim\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.nim\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.nim\" } }, \"name\": \"string.quoted.double.raw.nim\", \"patterns\": [{ \"match\": '(?<!\")\"(?!\")', \"name\": \"invalid.illegal.nim\" }, { \"include\": \"#raw_string_escapes\" }, { \"include\": \"#fmt_interpolation\" }] }, \"fmt_string_call\": { \"begin\": '(fmt)\\\\((?=\")', \"beginCaptures\": { \"1\": { \"name\": \"support.function.any-method.nim\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.nim\" } }, \"end\": '\"(?=\\\\))', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.nim\" } }, \"name\": \"string.quoted.double.nim\", \"patterns\": [{ \"match\": '\"', \"name\": \"invalid.illegal.nim\" }, { \"include\": \"#string_escapes\" }, { \"include\": \"#fmt_interpolation\" }] }] }, \"fmt_string_operator\": { \"begin\": '(&)(\")', \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.nim\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.nim\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.nim\" } }, \"name\": \"string.quoted.double.nim\", \"patterns\": [{ \"match\": '\"', \"name\": \"invalid.illegal.nim\" }, { \"include\": \"#string_escapes\" }, { \"include\": \"#fmt_interpolation\" }] }, \"fmt_string_triple\": { \"begin\": '\\\\b(fmt)(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"support.function.any-method.nim\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.nim\" } }, \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.nim\" } }, \"name\": \"string.quoted.triple.raw.nim\", \"patterns\": [{ \"include\": \"#fmt_interpolation\" }] }, \"fmt_string_triple_operator\": { \"begin\": '(&)(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.nim\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.nim\" } }, \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.nim\" } }, \"name\": \"string.quoted.triple.raw.nim\", \"patterns\": [{ \"include\": \"#fmt_interpolation\" }] }, \"integer_literal\": { \"patterns\": [{ \"match\": \"\\\\b(0[xX]\\\\h[_\\\\h]*)('(([iIuU](8|16|32|64))|[uU]))?\", \"name\": \"constant.numeric.integer.hexadecimal.nim\" }, { \"match\": \"\\\\b(0o[0-7][_0-7]*)('(([iIuU](8|16|32|64))|[uU]))?\", \"name\": \"constant.numeric.integer.octal.nim\" }, { \"match\": \"\\\\b(0(b|B)[01][_01]*)('(([iIuU](8|16|32|64))|[uU]))?\", \"name\": \"constant.numeric.integer.binary.nim\" }, { \"match\": \"\\\\b(\\\\d[_\\\\d]*)('(([iIuU](8|16|32|64))|[uU]))?\", \"name\": \"constant.numeric.integer.decimal.nim\" }] }, \"multilinecomment\": { \"begin\": \"#\\\\[\", \"end\": \"\\\\]#\", \"patterns\": [{ \"include\": \"#multilinecomment\" }] }, \"multilinedoccomment\": { \"begin\": \"##\\\\[\", \"end\": \"\\\\]##\", \"patterns\": [{ \"include\": \"#multilinedoccomment\" }] }, \"raw_string_escapes\": { \"captures\": { \"1\": { \"name\": \"constant.character.escape.double-quote.nim\" } }, \"match\": '[^\"](\"\")' }, \"string_escapes\": { \"patterns\": [{ \"match\": \"\\\\\\\\[pP]\", \"name\": \"constant.character.escape.newline.nim\" }, { \"match\": \"\\\\\\\\[uU]\\\\h\\\\h\\\\h\\\\h\", \"name\": \"constant.character.escape.hex.nim\" }, { \"match\": \"\\\\\\\\[uU]\\\\{\\\\h+\\\\}\", \"name\": \"constant.character.escape.hex.nim\" }, { \"include\": \"#char_escapes\" }] }, \"string_literal\": { \"patterns\": [{ \"include\": \"#fmt_string_triple\" }, { \"include\": \"#fmt_string_triple_operator\" }, { \"include\": \"#extended_string_quoted_triple_raw\" }, { \"include\": \"#string_quoted_triple_raw\" }, { \"include\": \"#fmt_string_operator\" }, { \"include\": \"#fmt_string\" }, { \"include\": \"#fmt_string_call\" }, { \"include\": \"#string_quoted_double_raw\" }, { \"include\": \"#extended_string_quoted_double_raw\" }, { \"include\": \"#string_quoted_single\" }, { \"include\": \"#string_quoted_triple\" }, { \"include\": \"#string_quoted_double\" }] }, \"string_quoted_double\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.nim\" } }, \"comment\": \"Double Quoted String\", \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.nim\" } }, \"name\": \"string.quoted.double.nim\", \"patterns\": [{ \"include\": \"#string_escapes\" }] }, \"string_quoted_double_raw\": { \"begin\": '\\\\br\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.nim\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.nim\" } }, \"name\": \"string.quoted.double.raw.nim\", \"patterns\": [{ \"include\": \"#raw_string_escapes\" }] }, \"string_quoted_single\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.nim\" } }, \"comment\": \"Single quoted character literal\", \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.nim\" } }, \"name\": \"string.quoted.single.nim\", \"patterns\": [{ \"include\": \"#char_escapes\" }, { \"match\": \"([^']{2,}?)\", \"name\": \"invalid.illegal.character.nim\" }] }, \"string_quoted_triple\": { \"begin\": '\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.nim\" } }, \"comment\": \"Triple Quoted String\", \"end\": '\"\"\"(?!\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.nim\" } }, \"name\": \"string.quoted.triple.nim\" }, \"string_quoted_triple_raw\": { \"begin\": 'r\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.nim\" } }, \"comment\": \"Raw Triple Quoted String\", \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.nim\" } }, \"name\": \"string.quoted.triple.raw.nim\" } }, \"scopeName\": \"source.nim\", \"embeddedLangs\": [\"c\", \"html\", \"xml\", \"javascript\", \"css\", \"glsl\", \"markdown\"] });\nvar nim = [\n  ...c,\n  ...html,\n  ...xml,\n  ...javascript,\n  ...css,\n  ...glsl,\n  ...markdown,\n  lang\n];\n\nexport { nim as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,OAAO,aAAa,CAAC,KAAK,GAAG,QAAQ,OAAO,YAAY,CAAC,EAAE,SAAS,gBAAgB,eAAe,yCAAyC,OAAO,SAAS,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,wBAAwB,QAAQ,uCAAuC,CAAC,EAAE,GAAG,EAAE,SAAS,eAAe,eAAe,6BAA6B,OAAO,QAAQ,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,qBAAqB,QAAQ,2BAA2B,CAAC,EAAE,GAAG,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,MAAM,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,OAAO,QAAQ,2CAA2C,CAAC,EAAE,GAAG,EAAE,SAAS,0BAA0B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,OAAO,QAAQ,+BAA+B,CAAC,EAAE,GAAG,EAAE,WAAW,6BAA6B,QAAQ,iBAAiB,YAAY,CAAC,EAAE,SAAS,sIAAsI,YAAY,EAAE,KAAK,EAAE,QAAQ,gBAAgB,GAAG,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,eAAe,WAAW,6CAA6C,OAAO,YAAY,QAAQ,6BAA6B,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,6BAA6B,SAAS,2BAA2B,QAAQ,2BAA2B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,WAAW,qBAAqB,SAAS,2CAA2C,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mBAAmB,GAAG,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,WAAW,0CAA0C,SAAS,sBAAsB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,uBAAuB,SAAS,yCAAyC,QAAQ,wBAAwB,GAAG,EAAE,WAAW,uDAAuD,SAAS,8GAA8G,QAAQ,sBAAsB,GAAG,EAAE,WAAW,8CAA8C,SAAS,8CAA8C,QAAQ,sBAAsB,GAAG,EAAE,WAAW,sCAAsC,SAAS,yDAAyD,QAAQ,uBAAuB,GAAG,EAAE,WAAW,mBAAmB,SAAS,0OAA0O,QAAQ,oBAAoB,GAAG,EAAE,WAAW,gCAAgC,SAAS,iDAAiD,QAAQ,sCAAsC,GAAG,EAAE,WAAW,oBAAoB,SAAS,0FAA0F,QAAQ,oCAAoC,GAAG,EAAE,WAAW,6BAA6B,SAAS,mIAAmI,QAAQ,4BAA4B,GAAG,EAAE,WAAW,4BAA4B,SAAS,uCAAuC,QAAQ,2BAA2B,GAAG,EAAE,WAAW,kBAAkB,SAAS,kCAAkC,QAAQ,2BAA2B,GAAG,EAAE,WAAW,oBAAoB,SAAS,yBAAyB,QAAQ,uBAAuB,GAAG,EAAE,WAAW,gBAAgB,SAAS,mBAAmB,QAAQ,mBAAmB,GAAG,EAAE,WAAW,kBAAkB,SAAS,kDAAkD,QAAQ,kCAAkC,GAAG,EAAE,WAAW,mCAAmC,SAAS,sxBAAsxB,QAAQ,kCAAkC,GAAG,EAAE,SAAS,gCAAgC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,wBAAwB,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,eAAe,YAAY,OAAO,4BAA4B,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,WAAW,EAAE,GAAG,QAAQ,yBAAyB,YAAY,CAAC,EAAE,SAAS,OAAO,OAAO,OAAO,QAAQ,uBAAuB,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,qCAAqC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,iBAAiB,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,2BAA2B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS,oCAAoC,GAAG,EAAE,SAAS,6BAA6B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,+BAA+B,GAAG,EAAE,SAAS,2BAA2B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,+BAA+B,GAAG,EAAE,SAAS,wBAAwB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,iBAAiB,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,QAAQ,kCAAkC,CAAC,EAAE,GAAG,EAAE,SAAS,uBAAuB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,eAAe,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,eAAe,cAAc,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,aAAa,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,OAAO,OAAO,OAAO,QAAQ,uBAAuB,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,WAAW,kBAAkB,SAAS,iDAAiD,GAAG,EAAE,SAAS,uBAAuB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,eAAe,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,eAAe,aAAa,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,YAAY,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,oBAAoB,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,oBAAoB,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,yDAAyD,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,QAAQ,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,qBAAqB,QAAQ,uBAAuB,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,cAAc,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,eAAe,YAAY,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,WAAW,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,oBAAoB,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,oBAAoB,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,yDAAyD,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,QAAQ,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,qBAAqB,QAAQ,uBAAuB,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,aAAa,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,eAAe,aAAa,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,YAAY,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,SAAS,oBAAoB,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,oBAAoB,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,yDAAyD,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,QAAQ,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,qBAAqB,QAAQ,uBAAuB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,sBAAsB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,cAAc,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,eAAe,cAAc,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,aAAa,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,oBAAoB,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,oBAAoB,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,yDAAyD,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,QAAQ,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,qBAAqB,QAAQ,uBAAuB,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,uBAAuB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,eAAe,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,eAAe,eAAe,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,cAAc,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,oBAAoB,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,oBAAoB,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,yDAAyD,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,QAAQ,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,qBAAqB,QAAQ,uBAAuB,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,aAAa,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,eAAe,sBAAsB,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,qBAAqB,EAAE,GAAG,QAAQ,qCAAqC,YAAY,CAAC,EAAE,SAAS,oBAAoB,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,oBAAoB,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,yDAAyD,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,QAAQ,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,qBAAqB,QAAQ,uBAAuB,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,gBAAgB,EAAE,YAAY,CAAC,EAAE,SAAS,qBAAqB,QAAQ,+CAA+C,GAAG,EAAE,SAAS,qBAAqB,QAAQ,yCAAyC,GAAG,EAAE,SAAS,YAAY,QAAQ,yCAAyC,GAAG,EAAE,SAAS,YAAY,QAAQ,0CAA0C,GAAG,EAAE,SAAS,YAAY,QAAQ,kDAAkD,GAAG,EAAE,SAAS,WAAW,QAAQ,6CAA6C,GAAG,EAAE,SAAS,SAAS,QAAQ,6CAA6C,GAAG,EAAE,SAAS,cAAc,QAAQ,iDAAiD,GAAG,EAAE,SAAS,YAAY,QAAQ,sCAAsC,GAAG,EAAE,SAAS,YAAY,QAAQ,0CAA0C,GAAG,EAAE,SAAS,YAAY,QAAQ,uCAAuC,GAAG,EAAE,SAAS,kBAAkB,QAAQ,oCAAoC,GAAG,EAAE,SAAS,YAAY,QAAQ,0CAA0C,CAAC,EAAE,GAAG,qCAAqC,EAAE,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,qCAAqC,EAAE,SAAS,kBAAkB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,+BAA+B,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,kHAAkH,QAAQ,qCAAqC,GAAG,EAAE,SAAS,+CAA+C,QAAQ,yCAAyC,GAAG,EAAE,SAAS,8CAA8C,QAAQ,mCAAmC,GAAG,EAAE,SAAS,gDAAgD,QAAQ,oCAAoC,GAAG,EAAE,SAAS,4CAA4C,QAAQ,qCAAqC,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uDAAuD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,SAAS,KAAK,OAAO,WAAW,QAAQ,qCAAqC,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,eAAe,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,SAAS,gBAAgB,QAAQ,sBAAsB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,KAAK,QAAQ,sBAAsB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,KAAK,QAAQ,sBAAsB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,8BAA8B,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,SAAS,uDAAuD,QAAQ,2CAA2C,GAAG,EAAE,SAAS,sDAAsD,QAAQ,qCAAqC,GAAG,EAAE,SAAS,wDAAwD,QAAQ,sCAAsC,GAAG,EAAE,SAAS,kDAAkD,QAAQ,uCAAuC,CAAC,EAAE,GAAG,oBAAoB,EAAE,SAAS,QAAQ,OAAO,QAAQ,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,SAAS,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,SAAS,WAAW,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,SAAS,YAAY,QAAQ,wCAAwC,GAAG,EAAE,SAAS,wBAAwB,QAAQ,oCAAoC,GAAG,EAAE,SAAS,sBAAsB,QAAQ,oCAAoC,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,qCAAqC,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,qCAAqC,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,WAAW,wBAAwB,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,4BAA4B,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,WAAW,mCAAmC,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,SAAS,eAAe,QAAQ,gCAAgC,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,WAAW,wBAAwB,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,2BAA2B,GAAG,4BAA4B,EAAE,SAAS,QAAQ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,WAAW,4BAA4B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,+BAA+B,EAAE,GAAG,aAAa,cAAc,iBAAiB,CAAC,KAAK,QAAQ,OAAO,cAAc,OAAO,QAAQ,UAAU,EAAE,CAAC;AAC19vB,IAAI,MAAM;AAAA,EACR,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}