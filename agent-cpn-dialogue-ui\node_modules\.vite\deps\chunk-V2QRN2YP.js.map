{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/mermaid/dist/chunks/mermaid.core/chunk-BFAMUDN2.mjs"], "sourcesContent": ["import {\n  __name\n} from \"./chunk-3XYRH5AP.mjs\";\n\n// src/rendering-util/insertElementsForSize.js\nimport { select } from \"d3\";\nvar getDiagramElement = /* @__PURE__ */ __name((id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  return svg;\n}, \"getDiagramElement\");\n\nexport {\n  getDiagramElement\n};\n"], "mappings": ";;;;;;AAMG,IAAC,oBAAoC,OAAO,CAAC,IAAI,kBAAkB;AACpE,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,OAAO,OAAO,EAAE;EACnC;AACA,QAAM,OAAO,kBAAkB,YAAY,OAAO,eAAe,MAAA,EAAQ,CAAC,EAAE,gBAAgB,IAAI,IAAI,OAAO,MAAM;AACjH,QAAM,MAAM,KAAK,OAAO,QAAQ,EAAE,IAAI;AACtC,SAAO;AACT,GAAG,mBAAmB;", "names": []}