{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/khroma/dist/methods/channel.js"], "sourcesContent": ["/* IMPORT */\nimport _ from '../utils/index.js';\nimport Color from '../color/index.js';\n/* MAIN */\nconst channel = (color, channel) => {\n    return _.lang.round(Color.parse(color)[channel]);\n};\n/* EXPORT */\nexport default channel;\n"], "mappings": ";;;;;;AAIK,IAAC,UAAU,CAAC,OAAOA,aAAY;AAChC,SAAOC,MAAE,KAAK,MAAM,QAAM,MAAM,KAAK,EAAED,QAAO,CAAC;AACnD;", "names": ["channel", "_"]}