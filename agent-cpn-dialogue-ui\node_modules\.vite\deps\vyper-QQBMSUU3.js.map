{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/vyper.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Vyper\", \"name\": \"vyper\", \"patterns\": [{ \"include\": \"#statement\" }, { \"include\": \"#expression\" }, { \"include\": \"#reserved-names-vyper\" }], \"repository\": { \"annotated-parameter\": { \"begin\": \"(?x)\\n\\\\b\\n([[:alpha:]_]\\\\w*) \\\\s* (:)\\n\", \"beginCaptures\": { \"1\": { \"name\": \"variable.parameter.function.language.python\" }, \"2\": { \"name\": \"punctuation.separator.annotation.python\" } }, \"end\": \"(,)|(?=\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.separator.parameters.python\" } }, \"patterns\": [{ \"include\": \"#expression\" }, { \"match\": \"=(?!=)\", \"name\": \"keyword.operator.assignment.python\" }] }, \"assignment-operator\": { \"match\": \"(?x)\\n<<= | >>= | //= | \\\\*\\\\*=\\n| \\\\+= | -= | /= | @=\\n| \\\\*= | %= | ~= | \\\\^= | &= | \\\\|=\\n| =(?!=)\\n\", \"name\": \"keyword.operator.assignment.python\" }, \"backticks\": { \"begin\": \"\\\\`\", \"end\": \"(?:\\\\`|(?<!\\\\\\\\)(\\\\n))\", \"name\": \"invalid.deprecated.backtick.python\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"builtin-callables\": { \"patterns\": [{ \"include\": \"#illegal-names\" }, { \"include\": \"#illegal-object-name\" }, { \"include\": \"#builtin-exceptions\" }, { \"include\": \"#builtin-functions\" }, { \"include\": \"#builtin-types\" }] }, \"builtin-exceptions\": { \"match\": \"(?x) (?<!\\\\.) \\\\b(\\n(\\nArithmetic | Assertion | Attribute | Buffer | BlockingIO\\n| BrokenPipe | ChildProcess\\n| (Connection (Aborted | Refused | Reset)?)\\n| EOF | Environment | FileExists | FileNotFound\\n| FloatingPoint | IO | Import | Indentation | Index | Interrupted\\n| IsADirectory | NotADirectory | Permission | ProcessLookup\\n| Timeout\\n| Key | Lookup | Memory | Name | NotImplemented | OS | Overflow\\n| Reference | Runtime | Recursion | Syntax | System\\n| Tab | Type | UnboundLocal | Unicode(Encode|Decode|Translate)?\\n| Value | Windows | ZeroDivision | ModuleNotFound\\n) Error\\n|\\n((Pending)?Deprecation | Runtime | Syntax | User | Future | Import\\n| Unicode | Bytes | Resource\\n)? Warning\\n|\\nSystemExit | Stop(Async)?Iteration\\n| KeyboardInterrupt\\n| GeneratorExit | (Base)?Exception\\n)\\\\b\\n\", \"name\": \"support.type.exception.python\" }, \"builtin-functions\": { \"patterns\": [{ \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\n__import__ | abs | all | any | ascii | bin | breakpoint | callable\\n| chr | compile | copyright | credits | delattr | dir | divmod\\n| enumerate | eval | exec | exit | filter | format | getattr\\n| globals | hasattr | hash | help | hex | id | input\\n| isinstance | issubclass | iter | len | license | locals | map\\n| max | memoryview | min | next | oct | open | ord | pow | print\\n| quit | range | reload | repr | reversed | round\\n| setattr | sorted | sum | vars | zip\\n)\\\\b\\n\", \"name\": \"support.function.builtin.python\" }, { \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\nfile | reduce | intern | raw_input | unicode | cmp | basestring\\n| execfile | long | xrange\\n)\\\\b\\n\", \"name\": \"variable.legacy.builtin.python\" }, { \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\n_abi_encode | floor | ceil | convert | slice | len | concat | sha256 | method_id | keccak256 | ecrecover | ecadd | ecmul | extract32 | as_wei_value | raw_call | blockhash | bitwise_and | bitwise_or | bitwise_xor | bitwise_not | uint256_addmod | uint256_mulmod | pow_mod256 | sqrt | shift | create_forwarder_to | min | max | empty | abs   )\\\\b\\n\", \"name\": \"support.function.builtin.vyper\" }, { \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\nsend | selfdestruct | raw_call | raw_log | create_forwarder_to   )\\\\b\\n\", \"name\": \"support.function.builtin.lowlevel.vyper\" }, { \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\nevent   )\\\\b\\n\", \"name\": \"support.type.event.vyper\" }, { \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\nmap | struct | HashMap   )\\\\b\\n\", \"name\": \"support.type.reference.vyper\" }, { \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\nprivate | nonreentrant | constant | event | internal | view | pure   )\\\\b\\n\", \"name\": \"support.function.builtin.modifiers.safe.vyper\" }, { \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\npublic | payable | modifying | external   )\\\\b\\n\", \"name\": \"support.function.builtin.modifiers.unsafe.vyper\" }] }, \"builtin-possible-callables\": { \"patterns\": [{ \"include\": \"#builtin-callables\" }, { \"include\": \"#magic-names\" }] }, \"builtin-types\": { \"patterns\": [{ \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\nbool | bytearray | bytes | classmethod | complex | dict\\n| float | frozenset | int | list | object | property\\n| set | slice | staticmethod | str | tuple | type\\n\\n(?# Although 'super' is not a type, it's related to types,\\nand is special enough to be highlighted differently from\\nother built-ins)\\n| super\\n)\\\\b\\n\", \"name\": \"support.type.python\" }, { \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\nbytes32 | bytearray | wei_value | timestamp | int256 | uint8 | uint256 | decimal | timedelta | string | int128 | address | bool | bytes   )\\\\b\\n\", \"name\": \"support.type.basetype.vyper\" }, { \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\nfinney | gwei | range | def | ____init____ | timedelta | babbage | zero_address | raise | external | empty_bytes32 | assert | continue | wei | ada | min_int256 | min_int128 | chainid | max_decimal | default | indexed | selfdestruct | lovelace | immutable | throw | kwei | max_int128 | while | constant | ___default___ | balance | twei | codesize | false | max_int256 | _default_ | init | mwei | if | ____default____ | true | payable | internal | until | this | nonpayable | pass | public | nonreentrant | blockhash | max_uint256 | shannon | none | units | _init_ | ___init___ | is_contract | for | zero_wei | min_decimal | szabo | timestamp | ether | pwei | send   )\\\\b\\n\", \"name\": \"support.type.keywords.vyper\" }, { \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\nshannon | finney | gwei | twei | kwei | wei | lovelace | ether | szabo | pwei | babbage | mwei | ada\\n\\n(?# Although 'super' is not a type, it's related to types,\\nand is special enough to be highlighted differently from\\nother built-ins)\\n| super\\n)\\\\b\\n\", \"name\": \"support.type.unit.vyper\" }, { \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\nZERO_ADDRESS | EMPTY_BYTES32 | MAX_INT128 | MIN_INT128 | MAX_DECIMAL | MIN_DECIMAL | MAX_UINT256\\n\\n(?# Although 'super' is not a type, it's related to types,\\nand is special enough to be highlighted differently from\\nother built-ins)\\n| super\\n)\\\\b\\n\", \"name\": \"support.type.constant.vyper\" }, { \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\nimplements   )\\\\b\\n\", \"name\": \"entity.other.inherited-class.interface.vyper\" }] }, \"call-wrapper-inheritance\": { \"begin\": \"(?x)\\n\\\\b(?=\\n([[:alpha:]_]\\\\w*) \\\\s* (\\\\()\\n)\\n\", \"comment\": \"same as a function call, but in inheritance context\", \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.python\" } }, \"name\": \"meta.function-call.python\", \"patterns\": [{ \"include\": \"#inheritance-name\" }, { \"include\": \"#function-arguments\" }] }, \"class-declaration\": { \"patterns\": [{ \"begin\": \"(?x)\\n\\\\s*(class)\\\\s+\\n(?=\\n[[:alpha:]_]\\\\w* \\\\s* (:|\\\\()\\n)\\n\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.class.python\" } }, \"end\": \"(:)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.class.begin.python\" } }, \"name\": \"meta.class.python\", \"patterns\": [{ \"include\": \"#class-name\" }, { \"include\": \"#class-inheritance\" }] }] }, \"class-inheritance\": { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.inheritance.begin.python\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.inheritance.end.python\" } }, \"name\": \"meta.class.inheritance.python\", \"patterns\": [{ \"match\": \"(\\\\*\\\\*|\\\\*)\", \"name\": \"keyword.operator.unpacking.arguments.python\" }, { \"match\": \",\", \"name\": \"punctuation.separator.inheritance.python\" }, { \"match\": \"=(?!=)\", \"name\": \"keyword.operator.assignment.python\" }, { \"match\": \"\\\\bmetaclass\\\\b\", \"name\": \"support.type.metaclass.python\" }, { \"include\": \"#illegal-names\" }, { \"include\": \"#class-kwarg\" }, { \"include\": \"#call-wrapper-inheritance\" }, { \"include\": \"#expression-base\" }, { \"include\": \"#member-access-class\" }, { \"include\": \"#inheritance-identifier\" }] }, \"class-kwarg\": { \"captures\": { \"1\": { \"name\": \"entity.other.inherited-class.python variable.parameter.class.python\" }, \"2\": { \"name\": \"keyword.operator.assignment.python\" } }, \"match\": \"(?x)\\n\\\\b ([[:alpha:]_]\\\\w*) \\\\s*(=)(?!=)\\n\" }, \"class-name\": { \"patterns\": [{ \"include\": \"#illegal-object-name\" }, { \"include\": \"#builtin-possible-callables\" }, { \"match\": \"(?x)\\n\\\\b ([[:alpha:]_]\\\\w*) \\\\b\\n\", \"name\": \"entity.name.type.class.python\" }] }, \"codetags\": { \"captures\": { \"1\": { \"name\": \"keyword.codetag.notation.python\" } }, \"match\": \"(?:\\\\b(NOTE|XXX|HACK|FIXME|BUG|TODO)\\\\b)\" }, \"comments\": { \"patterns\": [{ \"begin\": \"(?x)\\n(?:\\n\\\\# \\\\s* (type:)\\n\\\\s*+ (?# we want `\\\\s*+` which is possessive quantifier since\\nwe do not actually want to backtrack when matching\\nwhitespace here)\\n(?! $ | \\\\#)\\n)\\n\", \"beginCaptures\": { \"0\": { \"name\": \"meta.typehint.comment.python\" }, \"1\": { \"name\": \"comment.typehint.directive.notation.python\" } }, \"contentName\": \"meta.typehint.comment.python\", \"end\": \"(?:$|(?=\\\\#))\", \"name\": \"comment.line.number-sign.python\", \"patterns\": [{ \"match\": \"(?x)\\n\\\\G ignore\\n(?= \\\\s* (?: $ | \\\\#))\\n\", \"name\": \"comment.typehint.ignore.notation.python\" }, { \"match\": \"(?x)\\n(?<!\\\\.)\\\\b(\\nbool | bytes | float | int | object | str\\n| List | Dict | Iterable | Sequence | Set\\n| FrozenSet | Callable | Union | Tuple\\n| Any | None\\n)\\\\b\\n\", \"name\": \"comment.typehint.type.notation.python\" }, { \"match\": \"([\\\\[\\\\]\\\\(\\\\),\\\\.\\\\=\\\\*]|(->))\", \"name\": \"comment.typehint.punctuation.notation.python\" }, { \"match\": \"([[:alpha:]_]\\\\w*)\", \"name\": \"comment.typehint.variable.notation.python\" }] }, { \"include\": \"#comments-base\" }] }, \"comments-base\": { \"begin\": \"(\\\\#)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.python\" } }, \"end\": \"($)\", \"name\": \"comment.line.number-sign.python\", \"patterns\": [{ \"include\": \"#codetags\" }] }, \"comments-string-double-three\": { \"begin\": \"(\\\\#)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.python\" } }, \"end\": '($|(?=\"\"\"))', \"name\": \"comment.line.number-sign.python\", \"patterns\": [{ \"include\": \"#codetags\" }] }, \"comments-string-single-three\": { \"begin\": \"(\\\\#)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.python\" } }, \"end\": \"($|(?='''))\", \"name\": \"comment.line.number-sign.python\", \"patterns\": [{ \"include\": \"#codetags\" }] }, \"curly-braces\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.dict.begin.python\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.dict.end.python\" } }, \"patterns\": [{ \"match\": \":\", \"name\": \"punctuation.separator.dict.python\" }, { \"include\": \"#expression\" }] }, \"decorator\": { \"begin\": \"(?x)\\n^\\\\s*\\n((@)) \\\\s* (?=[[:alpha:]_]\\\\w*)\\n\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.decorator.python\" }, \"2\": { \"name\": \"punctuation.definition.decorator.python\" } }, \"end\": \"(?x)\\n( \\\\) )\\n\\n(?: (.*?) (?=\\\\s*(?:\\\\#|$)) )\\n| (?=\\\\n|\\\\#)\\n\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.python\" }, \"2\": { \"name\": \"invalid.illegal.decorator.python\" } }, \"name\": \"meta.function.decorator.python\", \"patterns\": [{ \"include\": \"#decorator-name\" }, { \"include\": \"#function-arguments\" }] }, \"decorator-name\": { \"patterns\": [{ \"include\": \"#builtin-callables\" }, { \"include\": \"#illegal-object-name\" }, { \"captures\": { \"2\": { \"name\": \"punctuation.separator.period.python\" } }, \"match\": \"(?x)\\n([[:alpha:]_]\\\\w*) | (\\\\.)\\n\", \"name\": \"entity.name.function.decorator.python\" }, { \"include\": \"#line-continuation\" }, { \"captures\": { \"1\": { \"name\": \"invalid.illegal.decorator.python\" } }, \"match\": \"(?x)\\n\\\\s* ([^([:alpha:]\\\\s_\\\\.#\\\\\\\\] .*?) (?=\\\\#|$)\\n\", \"name\": \"invalid.illegal.decorator.python\" }] }, \"docstring\": { \"patterns\": [{ \"begin\": `(\\\\'\\\\'\\\\'|\\\\\"\\\\\"\\\\\")`, \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\1)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" } }, \"name\": \"string.quoted.docstring.multi.python\", \"patterns\": [{ \"include\": \"#docstring-prompt\" }, { \"include\": \"#codetags\" }, { \"include\": \"#docstring-guts-unicode\" }] }, { \"begin\": `([rR])(\\\\'\\\\'\\\\'|\\\\\"\\\\\"\\\\\")`, \"beginCaptures\": { \"1\": { \"name\": \"storage.type.string.python\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\2)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" } }, \"name\": \"string.quoted.docstring.raw.multi.python\", \"patterns\": [{ \"include\": \"#string-consume-escape\" }, { \"include\": \"#docstring-prompt\" }, { \"include\": \"#codetags\" }] }, { \"begin\": `(\\\\'|\\\\\")`, \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\1)|(\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.quoted.docstring.single.python\", \"patterns\": [{ \"include\": \"#codetags\" }, { \"include\": \"#docstring-guts-unicode\" }] }, { \"begin\": `([rR])(\\\\'|\\\\\")`, \"beginCaptures\": { \"1\": { \"name\": \"storage.type.string.python\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\2)|(\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.quoted.docstring.raw.single.python\", \"patterns\": [{ \"include\": \"#string-consume-escape\" }, { \"include\": \"#codetags\" }] }] }, \"docstring-guts-unicode\": { \"patterns\": [{ \"include\": \"#escape-sequence-unicode\" }, { \"include\": \"#escape-sequence\" }, { \"include\": \"#string-line-continuation\" }] }, \"docstring-prompt\": { \"captures\": { \"1\": { \"name\": \"keyword.control.flow.python\" } }, \"match\": \"(?x)\\n(?:\\n(?:^|\\\\G) \\\\s* (?# '\\\\G' is necessary for ST)\\n((?:>>>|\\\\.\\\\.\\\\.) \\\\s) (?=\\\\s*\\\\S)\\n)\\n\" }, \"docstring-statement\": { \"begin\": `^(?=\\\\s*[rR]?(\\\\'\\\\'\\\\'|\\\\\"\\\\\"\\\\\"|\\\\'|\\\\\"))`, \"comment\": \"the string either terminates correctly or by the beginning of a new line (this is for single line docstrings that aren't terminated) AND it's not followed by another docstring\", \"end\": `((?<=\\\\1)|^)(?!\\\\s*[rR]?(\\\\'\\\\'\\\\'|\\\\\"\\\\\"\\\\\"|\\\\'|\\\\\"))`, \"patterns\": [{ \"include\": \"#docstring\" }] }, \"double-one-fregexp-conditional\": { \"begin\": \"(\\\\()\\\\?\\\\((\\\\w+(?:\\\\s+[[:alnum:]]+)?|\\\\d+)\\\\)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.conditional.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.conditional.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-one-fregexp-expression\" }] }, \"double-one-fregexp-expression\": { \"patterns\": [{ \"include\": \"#fregexp-base-expression\" }, { \"include\": \"#double-one-regexp-character-set\" }, { \"include\": \"#double-one-regexp-comments\" }, { \"include\": \"#regexp-flags\" }, { \"include\": \"#double-one-regexp-named-group\" }, { \"include\": \"#regexp-backreference\" }, { \"include\": \"#double-one-fregexp-lookahead\" }, { \"include\": \"#double-one-fregexp-lookahead-negative\" }, { \"include\": \"#double-one-fregexp-lookbehind\" }, { \"include\": \"#double-one-fregexp-lookbehind-negative\" }, { \"include\": \"#double-one-fregexp-conditional\" }, { \"include\": \"#double-one-fregexp-parentheses-non-capturing\" }, { \"include\": \"#double-one-fregexp-parentheses\" }] }, \"double-one-fregexp-lookahead\": { \"begin\": \"(\\\\()\\\\?=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-one-fregexp-expression\" }] }, \"double-one-fregexp-lookahead-negative\": { \"begin\": \"(\\\\()\\\\?!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-one-fregexp-expression\" }] }, \"double-one-fregexp-lookbehind\": { \"begin\": \"(\\\\()\\\\?<=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-one-fregexp-expression\" }] }, \"double-one-fregexp-lookbehind-negative\": { \"begin\": \"(\\\\()\\\\?<!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-one-fregexp-expression\" }] }, \"double-one-fregexp-named-group\": { \"begin\": \"(?x)\\n(\\\\()  (\\\\?P <\\\\w+(?:\\\\s+[[:alnum:]]+)?>)\\n\", \"beginCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\" }, \"2\": { \"name\": \"entity.name.tag.named.group.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.named.regexp\", \"patterns\": [{ \"include\": \"#double-one-fregexp-expression\" }] }, \"double-one-fregexp-parentheses\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-one-fregexp-expression\" }] }, \"double-one-fregexp-parentheses-non-capturing\": { \"begin\": \"\\\\(\\\\?:\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-one-fregexp-expression\" }] }, \"double-one-regexp-character-set\": { \"patterns\": [{ \"match\": \"(?x)\\n\\\\[ \\\\^? \\\\] (?! .*?\\\\])\\n\" }, { \"begin\": \"(\\\\[)(\\\\^)?(\\\\])?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.character.set.begin.regexp constant.other.set.regexp\" }, \"2\": { \"name\": \"keyword.operator.negation.regexp\" }, \"3\": { \"name\": \"constant.character.set.regexp\" } }, \"end\": '(\\\\]|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"punctuation.character.set.end.regexp constant.other.set.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.character.set.regexp\", \"patterns\": [{ \"include\": \"#regexp-charecter-set-escapes\" }, { \"match\": \"[^\\\\n]\", \"name\": \"constant.character.set.regexp\" }] }] }, \"double-one-regexp-comments\": { \"begin\": \"\\\\(\\\\?#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.comment.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"punctuation.comment.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"comment.regexp\", \"patterns\": [{ \"include\": \"#codetags\" }] }, \"double-one-regexp-conditional\": { \"begin\": \"(\\\\()\\\\?\\\\((\\\\w+(?:\\\\s+[[:alnum:]]+)?|\\\\d+)\\\\)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.conditional.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.conditional.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-one-regexp-expression\" }] }, \"double-one-regexp-expression\": { \"patterns\": [{ \"include\": \"#regexp-base-expression\" }, { \"include\": \"#double-one-regexp-character-set\" }, { \"include\": \"#double-one-regexp-comments\" }, { \"include\": \"#regexp-flags\" }, { \"include\": \"#double-one-regexp-named-group\" }, { \"include\": \"#regexp-backreference\" }, { \"include\": \"#double-one-regexp-lookahead\" }, { \"include\": \"#double-one-regexp-lookahead-negative\" }, { \"include\": \"#double-one-regexp-lookbehind\" }, { \"include\": \"#double-one-regexp-lookbehind-negative\" }, { \"include\": \"#double-one-regexp-conditional\" }, { \"include\": \"#double-one-regexp-parentheses-non-capturing\" }, { \"include\": \"#double-one-regexp-parentheses\" }] }, \"double-one-regexp-lookahead\": { \"begin\": \"(\\\\()\\\\?=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-one-regexp-expression\" }] }, \"double-one-regexp-lookahead-negative\": { \"begin\": \"(\\\\()\\\\?!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-one-regexp-expression\" }] }, \"double-one-regexp-lookbehind\": { \"begin\": \"(\\\\()\\\\?<=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-one-regexp-expression\" }] }, \"double-one-regexp-lookbehind-negative\": { \"begin\": \"(\\\\()\\\\?<!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-one-regexp-expression\" }] }, \"double-one-regexp-named-group\": { \"begin\": \"(?x)\\n(\\\\()  (\\\\?P <\\\\w+(?:\\\\s+[[:alnum:]]+)?>)\\n\", \"beginCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\" }, \"2\": { \"name\": \"entity.name.tag.named.group.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.named.regexp\", \"patterns\": [{ \"include\": \"#double-one-regexp-expression\" }] }, \"double-one-regexp-parentheses\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-one-regexp-expression\" }] }, \"double-one-regexp-parentheses-non-capturing\": { \"begin\": \"\\\\(\\\\?:\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"))|((?=(?<!\\\\\\\\)\\\\n))', \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-one-regexp-expression\" }] }, \"double-three-fregexp-conditional\": { \"begin\": \"(\\\\()\\\\?\\\\((\\\\w+(?:\\\\s+[[:alnum:]]+)?|\\\\d+)\\\\)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.conditional.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.conditional.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-three-fregexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-fregexp-expression\": { \"patterns\": [{ \"include\": \"#fregexp-base-expression\" }, { \"include\": \"#double-three-regexp-character-set\" }, { \"include\": \"#double-three-regexp-comments\" }, { \"include\": \"#regexp-flags\" }, { \"include\": \"#double-three-regexp-named-group\" }, { \"include\": \"#regexp-backreference\" }, { \"include\": \"#double-three-fregexp-lookahead\" }, { \"include\": \"#double-three-fregexp-lookahead-negative\" }, { \"include\": \"#double-three-fregexp-lookbehind\" }, { \"include\": \"#double-three-fregexp-lookbehind-negative\" }, { \"include\": \"#double-three-fregexp-conditional\" }, { \"include\": \"#double-three-fregexp-parentheses-non-capturing\" }, { \"include\": \"#double-three-fregexp-parentheses\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-fregexp-lookahead\": { \"begin\": \"(\\\\()\\\\?=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-three-fregexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-fregexp-lookahead-negative\": { \"begin\": \"(\\\\()\\\\?!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-three-fregexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-fregexp-lookbehind\": { \"begin\": \"(\\\\()\\\\?<=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-three-fregexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-fregexp-lookbehind-negative\": { \"begin\": \"(\\\\()\\\\?<!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-three-fregexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-fregexp-named-group\": { \"begin\": \"(?x)\\n(\\\\()  (\\\\?P <\\\\w+(?:\\\\s+[[:alnum:]]+)?>)\\n\", \"beginCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\" }, \"2\": { \"name\": \"entity.name.tag.named.group.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.named.regexp\", \"patterns\": [{ \"include\": \"#double-three-fregexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-fregexp-parentheses\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-three-fregexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-fregexp-parentheses-non-capturing\": { \"begin\": \"\\\\(\\\\?:\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-three-fregexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-regexp-character-set\": { \"patterns\": [{ \"match\": \"(?x)\\n\\\\[ \\\\^? \\\\] (?! .*?\\\\])\\n\" }, { \"begin\": \"(\\\\[)(\\\\^)?(\\\\])?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.character.set.begin.regexp constant.other.set.regexp\" }, \"2\": { \"name\": \"keyword.operator.negation.regexp\" }, \"3\": { \"name\": \"constant.character.set.regexp\" } }, \"end\": '(\\\\]|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"punctuation.character.set.end.regexp constant.other.set.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.character.set.regexp\", \"patterns\": [{ \"include\": \"#regexp-charecter-set-escapes\" }, { \"match\": \"[^\\\\n]\", \"name\": \"constant.character.set.regexp\" }] }] }, \"double-three-regexp-comments\": { \"begin\": \"\\\\(\\\\?#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.comment.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"punctuation.comment.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"comment.regexp\", \"patterns\": [{ \"include\": \"#codetags\" }] }, \"double-three-regexp-conditional\": { \"begin\": \"(\\\\()\\\\?\\\\((\\\\w+(?:\\\\s+[[:alnum:]]+)?|\\\\d+)\\\\)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.conditional.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.conditional.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-three-regexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-regexp-expression\": { \"patterns\": [{ \"include\": \"#regexp-base-expression\" }, { \"include\": \"#double-three-regexp-character-set\" }, { \"include\": \"#double-three-regexp-comments\" }, { \"include\": \"#regexp-flags\" }, { \"include\": \"#double-three-regexp-named-group\" }, { \"include\": \"#regexp-backreference\" }, { \"include\": \"#double-three-regexp-lookahead\" }, { \"include\": \"#double-three-regexp-lookahead-negative\" }, { \"include\": \"#double-three-regexp-lookbehind\" }, { \"include\": \"#double-three-regexp-lookbehind-negative\" }, { \"include\": \"#double-three-regexp-conditional\" }, { \"include\": \"#double-three-regexp-parentheses-non-capturing\" }, { \"include\": \"#double-three-regexp-parentheses\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-regexp-lookahead\": { \"begin\": \"(\\\\()\\\\?=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-three-regexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-regexp-lookahead-negative\": { \"begin\": \"(\\\\()\\\\?!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-three-regexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-regexp-lookbehind\": { \"begin\": \"(\\\\()\\\\?<=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-three-regexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-regexp-lookbehind-negative\": { \"begin\": \"(\\\\()\\\\?<!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-three-regexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-regexp-named-group\": { \"begin\": \"(?x)\\n(\\\\()  (\\\\?P <\\\\w+(?:\\\\s+[[:alnum:]]+)?>)\\n\", \"beginCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\" }, \"2\": { \"name\": \"entity.name.tag.named.group.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.named.regexp\", \"patterns\": [{ \"include\": \"#double-three-regexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-regexp-parentheses\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-three-regexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"double-three-regexp-parentheses-non-capturing\": { \"begin\": \"\\\\(\\\\?:\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\" } }, \"end\": '(\\\\)|(?=\"\"\"))', \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#double-three-regexp-expression\" }, { \"include\": \"#comments-string-double-three\" }] }, \"ellipsis\": { \"match\": \"\\\\.\\\\.\\\\.\", \"name\": \"constant.other.ellipsis.python\" }, \"escape-sequence\": { \"match\": `(?x)\n\\\\\\\\ (\nx[0-9A-Fa-f]{2}\n| [0-7]{1,3}\n| [\\\\\\\\\"'abfnrtv]\n)\n`, \"name\": \"constant.character.escape.python\" }, \"escape-sequence-unicode\": { \"patterns\": [{ \"match\": \"(?x)\\n\\\\\\\\ (\\nu[0-9A-Fa-f]{4}\\n| U[0-9A-Fa-f]{8}\\n| N\\\\{[\\\\w\\\\s]+?\\\\}\\n)\\n\", \"name\": \"constant.character.escape.python\" }] }, \"expression\": { \"comment\": \"All valid Python expressions\", \"patterns\": [{ \"include\": \"#expression-base\" }, { \"include\": \"#member-access\" }, { \"comment\": \"Tokenize identifiers to help linters\", \"match\": \"(?x) \\\\b ([[:alpha:]_]\\\\w*) \\\\b\" }] }, \"expression-bare\": { \"comment\": \"valid Python expressions w/o comments and line continuation\", \"patterns\": [{ \"include\": \"#backticks\" }, { \"include\": \"#illegal-anno\" }, { \"include\": \"#literal\" }, { \"include\": \"#regexp\" }, { \"include\": \"#string\" }, { \"include\": \"#lambda\" }, { \"include\": \"#generator\" }, { \"include\": \"#illegal-operator\" }, { \"include\": \"#operator\" }, { \"include\": \"#curly-braces\" }, { \"include\": \"#item-access\" }, { \"include\": \"#list\" }, { \"include\": \"#odd-function-call\" }, { \"include\": \"#round-braces\" }, { \"include\": \"#function-call\" }, { \"include\": \"#builtin-functions\" }, { \"include\": \"#builtin-types\" }, { \"include\": \"#builtin-exceptions\" }, { \"include\": \"#magic-names\" }, { \"include\": \"#special-names\" }, { \"include\": \"#illegal-names\" }, { \"include\": \"#special-variables\" }, { \"include\": \"#ellipsis\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#line-continuation\" }, { \"include\": \"#special-variables-types\" }] }, \"expression-base\": { \"comment\": \"valid Python expressions with comments and line continuation\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#expression-bare\" }, { \"include\": \"#line-continuation\" }] }, \"f-expression\": { \"comment\": \"All valid Python expressions, except comments and line continuation\", \"patterns\": [{ \"include\": \"#expression-bare\" }, { \"include\": \"#member-access\" }, { \"comment\": \"Tokenize identifiers to help linters\", \"match\": \"(?x) \\\\b ([[:alpha:]_]\\\\w*) \\\\b\" }] }, \"fregexp-base-expression\": { \"patterns\": [{ \"include\": \"#fregexp-quantifier\" }, { \"include\": \"#fstring-formatting-braces\" }, { \"match\": \"\\\\{.*?\\\\}\" }, { \"include\": \"#regexp-base-common\" }] }, \"fregexp-double-one-line\": { \"begin\": '\\\\b(([uU]r)|([fF]r)|(r[fF]?))(\")', \"beginCaptures\": { \"2\": { \"name\": \"invalid.deprecated.prefix.python\" }, \"3\": { \"name\": \"storage.type.string.python\" }, \"4\": { \"name\": \"storage.type.string.python\" }, \"5\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": '(\")|(?<!\\\\\\\\)(\\\\n)', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.interpolated.python string.regexp.quoted.single.python\", \"patterns\": [{ \"include\": \"#double-one-fregexp-expression\" }] }, \"fregexp-double-three-line\": { \"begin\": '\\\\b(([uU]r)|([fF]r)|(r[fF]?))(\"\"\")', \"beginCaptures\": { \"2\": { \"name\": \"invalid.deprecated.prefix.python\" }, \"3\": { \"name\": \"storage.type.string.python\" }, \"4\": { \"name\": \"storage.type.string.python\" }, \"5\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": '(\"\"\")', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.interpolated.python string.regexp.quoted.multi.python\", \"patterns\": [{ \"include\": \"#double-three-fregexp-expression\" }] }, \"fregexp-quantifier\": { \"match\": \"(?x)\\n\\\\{\\\\{(\\n\\\\d+ | \\\\d+,(\\\\d+)? | ,\\\\d+\\n)\\\\}\\\\}\\n\", \"name\": \"keyword.operator.quantifier.regexp\" }, \"fregexp-single-one-line\": { \"begin\": \"\\\\b(([uU]r)|([fF]r)|(r[fF]?))(\\\\')\", \"beginCaptures\": { \"2\": { \"name\": \"invalid.deprecated.prefix.python\" }, \"3\": { \"name\": \"storage.type.string.python\" }, \"4\": { \"name\": \"storage.type.string.python\" }, \"5\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\')|(?<!\\\\\\\\)(\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.interpolated.python string.regexp.quoted.single.python\", \"patterns\": [{ \"include\": \"#single-one-fregexp-expression\" }] }, \"fregexp-single-three-line\": { \"begin\": \"\\\\b(([uU]r)|([fF]r)|(r[fF]?))(\\\\'\\\\'\\\\')\", \"beginCaptures\": { \"2\": { \"name\": \"invalid.deprecated.prefix.python\" }, \"3\": { \"name\": \"storage.type.string.python\" }, \"4\": { \"name\": \"storage.type.string.python\" }, \"5\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\'\\\\'\\\\')\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.interpolated.python string.regexp.quoted.multi.python\", \"patterns\": [{ \"include\": \"#single-three-fregexp-expression\" }] }, \"fstring-fnorm-quoted-multi-line\": { \"begin\": `(\\\\b[fF])([bBuU])?('''|\"\"\")`, \"beginCaptures\": { \"1\": { \"name\": \"string.interpolated.python string.quoted.multi.python storage.type.string.python\" }, \"2\": { \"name\": \"invalid.illegal.prefix.python\" }, \"3\": { \"name\": \"punctuation.definition.string.begin.python string.interpolated.python string.quoted.multi.python\" } }, \"end\": \"(\\\\3)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python string.interpolated.python string.quoted.multi.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.fstring.python\", \"patterns\": [{ \"include\": \"#fstring-guts\" }, { \"include\": \"#fstring-illegal-multi-brace\" }, { \"include\": \"#fstring-multi-brace\" }, { \"include\": \"#fstring-multi-core\" }] }, \"fstring-fnorm-quoted-single-line\": { \"begin\": `(\\\\b[fF])([bBuU])?((['\"]))`, \"beginCaptures\": { \"1\": { \"name\": \"string.interpolated.python string.quoted.single.python storage.type.string.python\" }, \"2\": { \"name\": \"invalid.illegal.prefix.python\" }, \"3\": { \"name\": \"punctuation.definition.string.begin.python string.interpolated.python string.quoted.single.python\" } }, \"end\": \"(\\\\3)|((?<!\\\\\\\\)\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python string.interpolated.python string.quoted.single.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.fstring.python\", \"patterns\": [{ \"include\": \"#fstring-guts\" }, { \"include\": \"#fstring-illegal-single-brace\" }, { \"include\": \"#fstring-single-brace\" }, { \"include\": \"#fstring-single-core\" }] }, \"fstring-formatting\": { \"patterns\": [{ \"include\": \"#fstring-formatting-braces\" }, { \"include\": \"#fstring-formatting-singe-brace\" }] }, \"fstring-formatting-braces\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.character.format.placeholder.other.python\" }, \"2\": { \"name\": \"invalid.illegal.brace.python\" }, \"3\": { \"name\": \"constant.character.format.placeholder.other.python\" } }, \"comment\": \"empty braces are illegal\", \"match\": \"({)(\\\\s*?)(})\" }, { \"match\": \"({{|}})\", \"name\": \"constant.character.escape.python\" }] }, \"fstring-formatting-singe-brace\": { \"match\": \"(}(?!}))\", \"name\": \"invalid.illegal.brace.python\" }, \"fstring-guts\": { \"patterns\": [{ \"include\": \"#escape-sequence-unicode\" }, { \"include\": \"#escape-sequence\" }, { \"include\": \"#string-line-continuation\" }, { \"include\": \"#fstring-formatting\" }] }, \"fstring-illegal-multi-brace\": { \"patterns\": [{ \"include\": \"#impossible\" }] }, \"fstring-illegal-single-brace\": { \"begin\": \"(\\\\{)(?=[^\\\\n}]*$\\\\n?)\", \"beginCaptures\": { \"1\": { \"name\": \"constant.character.format.placeholder.other.python\" } }, \"comment\": \"it is illegal to have a multiline brace inside a single-line string\", \"end\": \"(\\\\})|(?=\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"constant.character.format.placeholder.other.python\" } }, \"patterns\": [{ \"include\": \"#fstring-terminator-single\" }, { \"include\": \"#f-expression\" }] }, \"fstring-multi-brace\": { \"begin\": \"(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"constant.character.format.placeholder.other.python\" } }, \"comment\": \"value interpolation using { ... }\", \"end\": \"(?x)\\n(\\\\})\\n\", \"endCaptures\": { \"1\": { \"name\": \"constant.character.format.placeholder.other.python\" } }, \"patterns\": [{ \"include\": \"#fstring-terminator-multi\" }, { \"include\": \"#f-expression\" }] }, \"fstring-multi-core\": { \"match\": `(?x)\n(.+?)\n(\n(?# .* and .*? in multi-line match need special handling of\nnewlines otherwise SublimeText and Atom will match slightly\ndifferently.\n\nThe guard for newlines has to be separate from the\nlookahead because of special $ matching rule.)\n($\\\\n?)\n|\n(?=[\\\\\\\\\\\\}\\\\{]|'''|\"\"\")\n)\n(?# due to how multiline regexps are matched we need a special case\nfor matching a newline character)\n| \\\\n\n`, \"name\": \"string.interpolated.python string.quoted.multi.python\" }, \"fstring-normf-quoted-multi-line\": { \"begin\": `(\\\\b[bBuU])([fF])('''|\"\"\")`, \"beginCaptures\": { \"1\": { \"name\": \"invalid.illegal.prefix.python\" }, \"2\": { \"name\": \"string.interpolated.python string.quoted.multi.python storage.type.string.python\" }, \"3\": { \"name\": \"punctuation.definition.string.begin.python string.quoted.multi.python\" } }, \"end\": \"(\\\\3)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python string.interpolated.python string.quoted.multi.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.fstring.python\", \"patterns\": [{ \"include\": \"#fstring-guts\" }, { \"include\": \"#fstring-illegal-multi-brace\" }, { \"include\": \"#fstring-multi-brace\" }, { \"include\": \"#fstring-multi-core\" }] }, \"fstring-normf-quoted-single-line\": { \"begin\": `(\\\\b[bBuU])([fF])((['\"]))`, \"beginCaptures\": { \"1\": { \"name\": \"invalid.illegal.prefix.python\" }, \"2\": { \"name\": \"string.interpolated.python string.quoted.single.python storage.type.string.python\" }, \"3\": { \"name\": \"punctuation.definition.string.begin.python string.quoted.single.python\" } }, \"end\": \"(\\\\3)|((?<!\\\\\\\\)\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python string.interpolated.python string.quoted.single.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.fstring.python\", \"patterns\": [{ \"include\": \"#fstring-guts\" }, { \"include\": \"#fstring-illegal-single-brace\" }, { \"include\": \"#fstring-single-brace\" }, { \"include\": \"#fstring-single-core\" }] }, \"fstring-raw-guts\": { \"patterns\": [{ \"include\": \"#string-consume-escape\" }, { \"include\": \"#fstring-formatting\" }] }, \"fstring-raw-multi-core\": { \"match\": `(?x)\n(.+?)\n(\n(?# .* and .*? in multi-line match need special handling of\nnewlines otherwise SublimeText and Atom will match slightly\ndifferently.\n\nThe guard for newlines has to be separate from the\nlookahead because of special $ matching rule.)\n($\\\\n?)\n|\n(?=[\\\\\\\\\\\\}\\\\{]|'''|\"\"\")\n)\n(?# due to how multiline regexps are matched we need a special case\nfor matching a newline character)\n| \\\\n\n`, \"name\": \"string.interpolated.python string.quoted.raw.multi.python\" }, \"fstring-raw-quoted-multi-line\": { \"begin\": `(\\\\b(?:[R][fF]|[fF][R]))('''|\"\"\")`, \"beginCaptures\": { \"1\": { \"name\": \"string.interpolated.python string.quoted.raw.multi.python storage.type.string.python\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.python string.quoted.raw.multi.python\" } }, \"end\": \"(\\\\2)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python string.interpolated.python string.quoted.raw.multi.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.fstring.python\", \"patterns\": [{ \"include\": \"#fstring-raw-guts\" }, { \"include\": \"#fstring-illegal-multi-brace\" }, { \"include\": \"#fstring-multi-brace\" }, { \"include\": \"#fstring-raw-multi-core\" }] }, \"fstring-raw-quoted-single-line\": { \"begin\": `(\\\\b(?:[R][fF]|[fF][R]))((['\"]))`, \"beginCaptures\": { \"1\": { \"name\": \"string.interpolated.python string.quoted.raw.single.python storage.type.string.python\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.python string.quoted.raw.single.python\" } }, \"end\": \"(\\\\2)|((?<!\\\\\\\\)\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python string.interpolated.python string.quoted.raw.single.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.fstring.python\", \"patterns\": [{ \"include\": \"#fstring-raw-guts\" }, { \"include\": \"#fstring-illegal-single-brace\" }, { \"include\": \"#fstring-single-brace\" }, { \"include\": \"#fstring-raw-single-core\" }] }, \"fstring-raw-single-core\": { \"match\": `(?x)\n(.+?)\n(\n(?# .* and .*? in multi-line match need special handling of\nnewlines otherwise SublimeText and Atom will match slightly\ndifferently.\n\nThe guard for newlines has to be separate from the\nlookahead because of special $ matching rule.)\n($\\\\n?)\n|\n(?=[\\\\\\\\\\\\}\\\\{]|(['\"])|((?<!\\\\\\\\)\\\\n))\n)\n(?# due to how multiline regexps are matched we need a special case\nfor matching a newline character)\n| \\\\n\n`, \"name\": \"string.interpolated.python string.quoted.raw.single.python\" }, \"fstring-single-brace\": { \"begin\": \"(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"constant.character.format.placeholder.other.python\" } }, \"comment\": \"value interpolation using { ... }\", \"end\": \"(?x)\\n(\\\\})|(?=\\\\n)\\n\", \"endCaptures\": { \"1\": { \"name\": \"constant.character.format.placeholder.other.python\" } }, \"patterns\": [{ \"include\": \"#fstring-terminator-single\" }, { \"include\": \"#f-expression\" }] }, \"fstring-single-core\": { \"match\": `(?x)\n(.+?)\n(\n(?# .* and .*? in multi-line match need special handling of\nnewlines otherwise SublimeText and Atom will match slightly\ndifferently.\n\nThe guard for newlines has to be separate from the\nlookahead because of special $ matching rule.)\n($\\\\n?)\n|\n(?=[\\\\\\\\\\\\}\\\\{]|(['\"])|((?<!\\\\\\\\)\\\\n))\n)\n(?# due to how multiline regexps are matched we need a special case\nfor matching a newline character)\n| \\\\n\n`, \"name\": \"string.interpolated.python string.quoted.single.python\" }, \"fstring-terminator-multi\": { \"patterns\": [{ \"match\": \"(![rsa])(?=})\", \"name\": \"storage.type.format.python\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.format.python\" }, \"2\": { \"name\": \"storage.type.format.python\" } }, \"match\": \"(?x)\\n(![rsa])?\\n( : \\\\w? [<>=^]? [-+ ]? \\\\#?\\n\\\\d* ,? (\\\\.\\\\d+)? [bcdeEfFgGnosxX%]? )(?=})\\n\" }, { \"include\": \"#fstring-terminator-multi-tail\" }] }, \"fstring-terminator-multi-tail\": { \"begin\": \"(![rsa])?(:)(?=.*?{)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.format.python\" }, \"2\": { \"name\": \"storage.type.format.python\" } }, \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#fstring-illegal-multi-brace\" }, { \"include\": \"#fstring-multi-brace\" }, { \"match\": \"([bcdeEfFgGnosxX%])(?=})\", \"name\": \"storage.type.format.python\" }, { \"match\": \"(\\\\.\\\\d+)\", \"name\": \"storage.type.format.python\" }, { \"match\": \"(,)\", \"name\": \"storage.type.format.python\" }, { \"match\": \"(\\\\d+)\", \"name\": \"storage.type.format.python\" }, { \"match\": \"(\\\\#)\", \"name\": \"storage.type.format.python\" }, { \"match\": \"([-+ ])\", \"name\": \"storage.type.format.python\" }, { \"match\": \"([<>=^])\", \"name\": \"storage.type.format.python\" }, { \"match\": \"(\\\\w)\", \"name\": \"storage.type.format.python\" }] }, \"fstring-terminator-single\": { \"patterns\": [{ \"match\": \"(![rsa])(?=})\", \"name\": \"storage.type.format.python\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.format.python\" }, \"2\": { \"name\": \"storage.type.format.python\" } }, \"match\": \"(?x)\\n(![rsa])?\\n( : \\\\w? [<>=^]? [-+ ]? \\\\#?\\n\\\\d* ,? (\\\\.\\\\d+)? [bcdeEfFgGnosxX%]? )(?=})\\n\" }, { \"include\": \"#fstring-terminator-single-tail\" }] }, \"fstring-terminator-single-tail\": { \"begin\": \"(![rsa])?(:)(?=.*?{)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.format.python\" }, \"2\": { \"name\": \"storage.type.format.python\" } }, \"end\": \"(?=})|(?=\\\\n)\", \"patterns\": [{ \"include\": \"#fstring-illegal-single-brace\" }, { \"include\": \"#fstring-single-brace\" }, { \"match\": \"([bcdeEfFgGnosxX%])(?=})\", \"name\": \"storage.type.format.python\" }, { \"match\": \"(\\\\.\\\\d+)\", \"name\": \"storage.type.format.python\" }, { \"match\": \"(,)\", \"name\": \"storage.type.format.python\" }, { \"match\": \"(\\\\d+)\", \"name\": \"storage.type.format.python\" }, { \"match\": \"(\\\\#)\", \"name\": \"storage.type.format.python\" }, { \"match\": \"([-+ ])\", \"name\": \"storage.type.format.python\" }, { \"match\": \"([<>=^])\", \"name\": \"storage.type.format.python\" }, { \"match\": \"(\\\\w)\", \"name\": \"storage.type.format.python\" }] }, \"function-arguments\": { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.python\" } }, \"contentName\": \"meta.function-call.arguments.python\", \"end\": \"(?=\\\\))(?!\\\\)\\\\s*\\\\()\", \"patterns\": [{ \"match\": \"(,)\", \"name\": \"punctuation.separator.arguments.python\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.unpacking.arguments.python\" } }, \"match\": \"(?x)\\n(?:(?<=[,(])|^) \\\\s* (\\\\*{1,2})\\n\" }, { \"include\": \"#lambda-incomplete\" }, { \"include\": \"#illegal-names\" }, { \"captures\": { \"1\": { \"name\": \"variable.parameter.function-call.python\" }, \"2\": { \"name\": \"keyword.operator.assignment.python\" } }, \"match\": \"\\\\b([[:alpha:]_]\\\\w*)\\\\s*(=)(?!=)\" }, { \"match\": \"=(?!=)\", \"name\": \"keyword.operator.assignment.python\" }, { \"include\": \"#expression\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.python\" }, \"2\": { \"name\": \"punctuation.definition.arguments.begin.python\" } }, \"match\": \"\\\\s*(\\\\))\\\\s*(\\\\()\" }] }, \"function-call\": { \"begin\": \"(?x)\\n\\\\b(?=\\n([[:alpha:]_]\\\\w*) \\\\s* (\\\\()\\n)\\n\", \"comment\": 'Regular function call of the type \"name(args)\"', \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.python\" } }, \"name\": \"meta.function-call.python\", \"patterns\": [{ \"include\": \"#special-variables\" }, { \"include\": \"#function-name\" }, { \"include\": \"#function-arguments\" }] }, \"function-declaration\": { \"begin\": \"(?x)\\n\\\\s*\\n(?:\\\\b(async) \\\\s+)? \\\\b(def)\\\\s+\\n(?=\\n[[:alpha:]_][[:word:]]* \\\\s* \\\\(\\n)\\n\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function.async.python\" }, \"2\": { \"name\": \"storage.type.function.python\" } }, \"end\": `(:|(?=[#'\"\\\\n]))`, \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.function.begin.python\" } }, \"name\": \"meta.function.python\", \"patterns\": [{ \"include\": \"#function-def-name\" }, { \"include\": \"#parameters\" }, { \"include\": \"#line-continuation\" }, { \"include\": \"#return-annotation\" }] }, \"function-def-name\": { \"patterns\": [{ \"match\": \"(?x)\\n\\\\b (__default__) \\\\b\\n\", \"name\": \"entity.name.function.fallback.vyper\" }, { \"match\": \"(?x)\\n\\\\b (__init__) \\\\b\\n\", \"name\": \"entity.name.function.constructor.vyper\" }, { \"include\": \"#illegal-object-name\" }, { \"include\": \"#builtin-possible-callables\" }, { \"match\": \"(?x)\\n\\\\b ([[:alpha:]_]\\\\w*) \\\\b\\n\", \"name\": \"entity.name.function.python\" }] }, \"function-name\": { \"patterns\": [{ \"include\": \"#builtin-possible-callables\" }, { \"comment\": \"Some color schemas support meta.function-call.generic scope\", \"match\": \"(?x)\\n\\\\b ([[:alpha:]_]\\\\w*) \\\\b\\n\", \"name\": \"meta.function-call.generic.python\" }] }, \"generator\": { \"begin\": \"\\\\bfor\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.flow.python\" } }, \"comment\": 'Match \"for ... in\" construct used in generators and for loops to\\ncorrectly identify the \"in\" as a control flow keyword.\\n', \"end\": \"\\\\bin\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.flow.python\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"illegal-anno\": { \"match\": \"->\", \"name\": \"invalid.illegal.annotation.python\" }, \"illegal-names\": { \"captures\": { \"1\": { \"name\": \"keyword.control.flow.python\" }, \"2\": { \"name\": \"keyword.control.import.python\" } }, \"match\": \"(?x)\\n\\\\b(?:\\n(\\nand | assert | async | await | break | class | continue | def\\n| del | elif | else | except | finally | for | from | global\\n| if | in | is | (?<=\\\\.)lambda | lambda(?=\\\\s*[\\\\.=])\\n| nonlocal | not | or | pass | raise | return | try | while | with\\n| yield\\n) | (\\nas | import\\n)\\n)\\\\b\\n\" }, \"illegal-object-name\": { \"comment\": `It's illegal to name class or function \"True\"`, \"match\": \"\\\\b(True|False|None)\\\\b\", \"name\": \"keyword.illegal.name.python\" }, \"illegal-operator\": { \"patterns\": [{ \"match\": \"&&|\\\\|\\\\||--|\\\\+\\\\+\", \"name\": \"invalid.illegal.operator.python\" }, { \"match\": \"[?$]\", \"name\": \"invalid.illegal.operator.python\" }, { \"comment\": \"We don't want `!` to flash when we're typing `!=`\", \"match\": \"!\\\\b\", \"name\": \"invalid.illegal.operator.python\" }] }, \"import\": { \"comment\": \"Import statements used to correctly mark `from`, `import`, and `as`\\n\", \"patterns\": [{ \"begin\": \"\\\\b(?<!\\\\.)(from)\\\\b(?=.+import)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.import.python\" } }, \"end\": \"$|(?=import)\", \"patterns\": [{ \"match\": \"\\\\.+\", \"name\": \"punctuation.separator.period.python\" }, { \"include\": \"#expression\" }] }, { \"begin\": \"\\\\b(?<!\\\\.)(import)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.import.python\" } }, \"end\": \"$\", \"patterns\": [{ \"match\": \"\\\\b(?<!\\\\.)as\\\\b\", \"name\": \"keyword.control.import.python\" }, { \"include\": \"#expression\" }] }] }, \"impossible\": { \"comment\": \"This is a special rule that should be used where no match is desired. It is not a good idea to match something like '1{0}' because in some cases that can result in infinite loops in token generation. So the rule instead matches and impossible expression to allow a match to fail and move to the next token.\", \"match\": \"$.^\" }, \"inheritance-identifier\": { \"captures\": { \"1\": { \"name\": \"entity.other.inherited-class.python\" } }, \"match\": \"(?x)\\n\\\\b ([[:alpha:]_]\\\\w*) \\\\b\\n\" }, \"inheritance-name\": { \"patterns\": [{ \"include\": \"#lambda-incomplete\" }, { \"include\": \"#builtin-possible-callables\" }, { \"include\": \"#inheritance-identifier\" }] }, \"item-access\": { \"patterns\": [{ \"begin\": \"(?x)\\n\\\\b(?=\\n[[:alpha:]_]\\\\w* \\\\s* \\\\[\\n)\\n\", \"end\": \"(\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.python\" } }, \"name\": \"meta.item-access.python\", \"patterns\": [{ \"include\": \"#item-name\" }, { \"include\": \"#item-index\" }, { \"include\": \"#expression\" }] }] }, \"item-index\": { \"begin\": \"(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.python\" } }, \"contentName\": \"meta.item-access.arguments.python\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"match\": \":\", \"name\": \"punctuation.separator.slice.python\" }, { \"include\": \"#expression\" }] }, \"item-name\": { \"patterns\": [{ \"include\": \"#special-variables\" }, { \"include\": \"#builtin-functions\" }, { \"include\": \"#special-names\" }, { \"match\": \"(?x)\\n\\\\b ([[:alpha:]_]\\\\w*) \\\\b\\n\", \"name\": \"meta.indexed-name.python\" }, { \"include\": \"#special-variables-types\" }] }, \"lambda\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.control.flow.python\" } }, \"match\": \"((?<=\\\\.)lambda|lambda(?=\\\\s*[\\\\.=]))\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.function.lambda.python\" } }, \"match\": \"\\\\b(lambda)\\\\s*?(?=[,\\\\n]|$)\" }, { \"begin\": \"(?x)\\n\\\\b (lambda) \\\\b\\n\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function.lambda.python\" } }, \"contentName\": \"meta.function.lambda.parameters.python\", \"end\": \"(:)|(\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.function.lambda.begin.python\" } }, \"name\": \"meta.lambda-function.python\", \"patterns\": [{ \"match\": \"(\\\\*\\\\*|\\\\*)\", \"name\": \"keyword.operator.unpacking.parameter.python\" }, { \"include\": \"#lambda-nested-incomplete\" }, { \"include\": \"#illegal-names\" }, { \"captures\": { \"1\": { \"name\": \"variable.parameter.function.language.python\" }, \"2\": { \"name\": \"punctuation.separator.parameters.python\" } }, \"match\": \"([[:alpha:]_]\\\\w*)\\\\s*(?:(,)|(?=:|$))\" }, { \"include\": \"#comments\" }, { \"include\": \"#backticks\" }, { \"include\": \"#illegal-anno\" }, { \"include\": \"#lambda-parameter-with-default\" }, { \"include\": \"#line-continuation\" }, { \"include\": \"#illegal-operator\" }] }] }, \"lambda-incomplete\": { \"match\": \"\\\\blambda(?=\\\\s*[,)])\", \"name\": \"storage.type.function.lambda.python\" }, \"lambda-nested-incomplete\": { \"match\": \"\\\\blambda(?=\\\\s*[:,)])\", \"name\": \"storage.type.function.lambda.python\" }, \"lambda-parameter-with-default\": { \"begin\": \"(?x)\\n\\\\b\\n([[:alpha:]_]\\\\w*) \\\\s* (=)\\n\", \"beginCaptures\": { \"1\": { \"name\": \"variable.parameter.function.language.python\" }, \"2\": { \"name\": \"keyword.operator.python\" } }, \"end\": \"(,)|(?=:|$)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.separator.parameters.python\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"line-continuation\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.separator.continuation.line.python\" }, \"2\": { \"name\": \"invalid.illegal.line.continuation.python\" } }, \"match\": \"(\\\\\\\\)\\\\s*(\\\\S.*$\\\\n?)\" }, { \"begin\": \"(\\\\\\\\)\\\\s*$\\\\n?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.continuation.line.python\" } }, \"end\": `(?x)\n(?=^\\\\s*$)\n|\n(?! (\\\\s* [rR]? (\\\\'\\\\'\\\\'|\\\\\"\\\\\"\\\\\"|\\\\'|\\\\\"))\n|\n(\\\\G $)  (?# '\\\\G' is necessary for ST)\n)\n`, \"patterns\": [{ \"include\": \"#regexp\" }, { \"include\": \"#string\" }] }] }, \"list\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.list.begin.python\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.list.end.python\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"literal\": { \"patterns\": [{ \"match\": \"\\\\b(True|False|None|NotImplemented|Ellipsis)\\\\b\", \"name\": \"constant.language.python\" }, { \"include\": \"#number\" }] }, \"loose-default\": { \"begin\": \"(=)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.python\" } }, \"end\": \"(,)|(?=\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.separator.parameters.python\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"magic-function-names\": { \"captures\": { \"1\": { \"name\": \"support.function.magic.python\" } }, \"comment\": \"these methods have magic interpretation by python and are generally called\\nindirectly through syntactic constructs\\n\", \"match\": \"(?x)\\n\\\\b(\\n__(?:\\nabs | add | aenter | aexit | aiter | and | anext | await\\n| bool | call | ceil | cmp | coerce | complex | contains\\n| copy | deepcopy | del | delattr | delete | delitem\\n| delslice | dir | div | divmod | enter | eq | exit | float\\n| floor | floordiv | format | ge | get | getattr\\n| getattribute | getinitargs | getitem | getnewargs\\n| getslice | getstate | gt | hash | hex | iadd | iand | idiv\\n| ifloordiv | ilshift | imod | imul | index | init\\n| instancecheck | int | invert | ior | ipow | irshift | isub\\n| iter | itruediv | ixor | le | len | long | lshift | lt\\n| missing | mod | mul | ne | neg | new | next | nonzero | oct | or\\n| pos | pow | radd | rand | rdiv | rdivmod | reduce\\n| reduce_ex | repr | reversed | rfloordiv | rlshift | rmod\\n| rmul | ror | round | rpow | rrshift | rshift | rsub\\n| rtruediv | rxor | set | setattr | setitem | setslice\\n| setstate | sizeof | str | sub | subclasscheck | truediv\\n| trunc | unicode | xor | matmul | rmatmul | imatmul\\n| init_subclass | set_name | fspath | bytes | prepare\\n)__\\n)\\\\b\\n\" }, \"magic-names\": { \"patterns\": [{ \"include\": \"#magic-function-names\" }, { \"include\": \"#magic-variable-names\" }] }, \"magic-variable-names\": { \"captures\": { \"1\": { \"name\": \"support.variable.magic.python\" } }, \"comment\": \"magic variables which a class/module may have.\", \"match\": \"(?x)\\n\\\\b(\\n__(?:\\nall | bases | builtins | class | class_getitem | code | debug\\n| defaults | dict | doc | file | func | kwdefaults | members\\n| metaclass | methods | module | mro | mro_entries | name\\n| qualname | post_init | self | signature | slots | subclasses\\n| version | weakref | wrapped | annotations | classcell\\n| spec | path | package | future | traceback\\n)__\\n)\\\\b\\n\" }, \"member-access\": { \"begin\": \"(\\\\.)\\\\s*(?!\\\\.)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.period.python\" } }, \"end\": \"(?x)\\n\\n\\n(?<=\\\\S)(?=\\\\W) |\\n\\n\\n(^|(?<=\\\\s))(?=[^\\\\\\\\\\\\w\\\\s]) |\\n$\\n\", \"name\": \"meta.member.access.python\", \"patterns\": [{ \"include\": \"#function-call\" }, { \"include\": \"#member-access-base\" }, { \"include\": \"#member-access-attribute\" }] }, \"member-access-attribute\": { \"comment\": \"Highlight attribute access in otherwise non-specialized cases.\", \"match\": \"(?x)\\n\\\\b ([[:alpha:]_]\\\\w*) \\\\b\\n\", \"name\": \"meta.attribute.python\" }, \"member-access-base\": { \"patterns\": [{ \"include\": \"#magic-names\" }, { \"include\": \"#illegal-names\" }, { \"include\": \"#illegal-object-name\" }, { \"include\": \"#special-names\" }, { \"include\": \"#line-continuation\" }, { \"include\": \"#item-access\" }, { \"include\": \"#special-variables-types\" }] }, \"member-access-class\": { \"begin\": \"(\\\\.)\\\\s*(?!\\\\.)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.period.python\" } }, \"end\": \"(?<=\\\\S)(?=\\\\W)|$\", \"name\": \"meta.member.access.python\", \"patterns\": [{ \"include\": \"#call-wrapper-inheritance\" }, { \"include\": \"#member-access-base\" }, { \"include\": \"#inheritance-identifier\" }] }, \"number\": { \"name\": \"constant.numeric.python\", \"patterns\": [{ \"include\": \"#number-float\" }, { \"include\": \"#number-dec\" }, { \"include\": \"#number-hex\" }, { \"include\": \"#number-oct\" }, { \"include\": \"#number-bin\" }, { \"include\": \"#number-long\" }, { \"match\": \"\\\\b[0-9]+\\\\w+\", \"name\": \"invalid.illegal.name.python\" }] }, \"number-bin\": { \"captures\": { \"1\": { \"name\": \"storage.type.number.python\" } }, \"match\": \"(?x)\\n(?<![\\\\w\\\\.])\\n(0[bB]) (_?[01])+\\n\\\\b\\n\", \"name\": \"constant.numeric.bin.python\" }, \"number-dec\": { \"captures\": { \"1\": { \"name\": \"storage.type.imaginary.number.python\" }, \"2\": { \"name\": \"invalid.illegal.dec.python\" } }, \"match\": \"(?x)\\n(?<![\\\\w\\\\.])(?:\\n[1-9](?: _?[0-9] )*\\n|\\n0+\\n|\\n[0-9](?: _?[0-9] )* ([jJ])\\n|\\n0 ([0-9]+)(?![eE\\\\.])\\n)\\\\b\\n\", \"name\": \"constant.numeric.dec.python\" }, \"number-float\": { \"captures\": { \"1\": { \"name\": \"storage.type.imaginary.number.python\" } }, \"match\": \"(?x)\\n(?<! \\\\w)(?:\\n(?:\\n\\\\.[0-9](?: _?[0-9] )*\\n|\\n[0-9](?: _?[0-9] )* \\\\. [0-9](?: _?[0-9] )*\\n|\\n[0-9](?: _?[0-9] )* \\\\.\\n) (?: [eE][+-]?[0-9](?: _?[0-9] )* )?\\n|\\n[0-9](?: _?[0-9] )* (?: [eE][+-]?[0-9](?: _?[0-9] )* )\\n)([jJ])?\\\\b\\n\", \"name\": \"constant.numeric.float.python\" }, \"number-hex\": { \"captures\": { \"1\": { \"name\": \"storage.type.number.python\" } }, \"match\": \"(?x)\\n(?<![\\\\w\\\\.])\\n(0[xX]) (_?[0-9a-fA-F])+\\n\\\\b\\n\", \"name\": \"constant.numeric.hex.python\" }, \"number-long\": { \"captures\": { \"2\": { \"name\": \"storage.type.number.python\" } }, \"comment\": \"this is to support python2 syntax for long ints\", \"match\": \"(?x)\\n(?<![\\\\w\\\\.])\\n([1-9][0-9]* | 0) ([lL])\\n\\\\b\\n\", \"name\": \"constant.numeric.bin.python\" }, \"number-oct\": { \"captures\": { \"1\": { \"name\": \"storage.type.number.python\" } }, \"match\": \"(?x)\\n(?<![\\\\w\\\\.])\\n(0[oO]) (_?[0-7])+\\n\\\\b\\n\", \"name\": \"constant.numeric.oct.python\" }, \"odd-function-call\": { \"begin\": \"(?x)\\n(?<= \\\\] | \\\\) ) \\\\s*\\n(?=\\\\()\\n\", \"comment\": 'A bit obscured function call where there may have been an\\narbitrary number of other operations to get the function.\\nE.g. \"arr[idx](args)\"\\n', \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.python\" } }, \"patterns\": [{ \"include\": \"#function-arguments\" }] }, \"operator\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.logical.python\" }, \"2\": { \"name\": \"keyword.control.flow.python\" }, \"3\": { \"name\": \"keyword.operator.bitwise.python\" }, \"4\": { \"name\": \"keyword.operator.arithmetic.python\" }, \"5\": { \"name\": \"keyword.operator.comparison.python\" } }, \"match\": \"(?x)\\n\\\\b(?<!\\\\.)\\n(?:\\n(and | or | not | in | is)                         (?# 1)\\n|\\n(for | if | else | await | (?:yield(?:\\\\s+from)?))  (?# 2)\\n)\\n(?!\\\\s*:)\\\\b\\n\\n| (<< | >> | & | \\\\| | \\\\^ | ~)                          (?# 3)\\n\\n| (\\\\*\\\\* | \\\\* | \\\\+ | - | % | // | / | @)                (?# 4)\\n\\n| (!= | == | >= | <= | < | >)                          (?# 5)\\n\" }, \"parameter-special\": { \"captures\": { \"1\": { \"name\": \"variable.parameter.function.language.python\" }, \"2\": { \"name\": \"variable.parameter.function.language.special.self.python\" }, \"3\": { \"name\": \"variable.parameter.function.language.special.cls.python\" }, \"4\": { \"name\": \"punctuation.separator.parameters.python\" } }, \"match\": \"(?x)\\n\\\\b ((self)|(cls)) \\\\b \\\\s*(?:(,)|(?=\\\\)))\\n\" }, \"parameters\": { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.parameters.begin.python\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.parameters.end.python\" } }, \"name\": \"meta.function.parameters.python\", \"patterns\": [{ \"match\": \"(\\\\*\\\\*|\\\\*)\", \"name\": \"keyword.operator.unpacking.parameter.python\" }, { \"include\": \"#lambda-incomplete\" }, { \"include\": \"#illegal-names\" }, { \"include\": \"#illegal-object-name\" }, { \"include\": \"#parameter-special\" }, { \"captures\": { \"1\": { \"name\": \"variable.parameter.function.language.python\" }, \"2\": { \"name\": \"punctuation.separator.parameters.python\" } }, \"match\": \"(?x)\\n([[:alpha:]_]\\\\w*)\\n\\\\s* (?: (,) | (?=[)#\\\\n=]))\\n\" }, { \"include\": \"#comments\" }, { \"include\": \"#loose-default\" }, { \"include\": \"#annotated-parameter\" }] }, \"punctuation\": { \"patterns\": [{ \"match\": \":\", \"name\": \"punctuation.separator.colon.python\" }, { \"match\": \",\", \"name\": \"punctuation.separator.element.python\" }] }, \"regexp\": { \"patterns\": [{ \"include\": \"#regexp-single-three-line\" }, { \"include\": \"#regexp-double-three-line\" }, { \"include\": \"#regexp-single-one-line\" }, { \"include\": \"#regexp-double-one-line\" }, { \"include\": \"#fregexp-single-three-line\" }, { \"include\": \"#fregexp-double-three-line\" }, { \"include\": \"#fregexp-single-one-line\" }, { \"include\": \"#fregexp-double-one-line\" }] }, \"regexp-backreference\": { \"captures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.backreference.named.begin.regexp\" }, \"2\": { \"name\": \"entity.name.tag.named.backreference.regexp\" }, \"3\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.backreference.named.end.regexp\" } }, \"match\": \"(?x)\\n(\\\\()  (\\\\?P= \\\\w+(?:\\\\s+[[:alnum:]]+)?)  (\\\\))\\n\", \"name\": \"meta.backreference.named.regexp\" }, \"regexp-backreference-number\": { \"captures\": { \"1\": { \"name\": \"entity.name.tag.backreference.regexp\" } }, \"match\": \"(\\\\\\\\[1-9]\\\\d?)\", \"name\": \"meta.backreference.regexp\" }, \"regexp-base-common\": { \"patterns\": [{ \"match\": \"\\\\.\", \"name\": \"support.other.match.any.regexp\" }, { \"match\": \"\\\\^\", \"name\": \"support.other.match.begin.regexp\" }, { \"match\": \"\\\\$\", \"name\": \"support.other.match.end.regexp\" }, { \"match\": \"[+*?]\\\\??\", \"name\": \"keyword.operator.quantifier.regexp\" }, { \"match\": \"\\\\|\", \"name\": \"keyword.operator.disjunction.regexp\" }, { \"include\": \"#regexp-escape-sequence\" }] }, \"regexp-base-expression\": { \"patterns\": [{ \"include\": \"#regexp-quantifier\" }, { \"include\": \"#regexp-base-common\" }] }, \"regexp-charecter-set-escapes\": { \"patterns\": [{ \"match\": \"\\\\\\\\[abfnrtv\\\\\\\\]\", \"name\": \"constant.character.escape.regexp\" }, { \"include\": \"#regexp-escape-special\" }, { \"match\": \"\\\\\\\\([0-7]{1,3})\", \"name\": \"constant.character.escape.regexp\" }, { \"include\": \"#regexp-escape-character\" }, { \"include\": \"#regexp-escape-unicode\" }, { \"include\": \"#regexp-escape-catchall\" }] }, \"regexp-double-one-line\": { \"begin\": '\\\\b(([uU]r)|([bB]r)|(r[bB]?))(\")', \"beginCaptures\": { \"2\": { \"name\": \"invalid.deprecated.prefix.python\" }, \"3\": { \"name\": \"storage.type.string.python\" }, \"4\": { \"name\": \"storage.type.string.python\" }, \"5\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": '(\")|(?<!\\\\\\\\)(\\\\n)', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.regexp.quoted.single.python\", \"patterns\": [{ \"include\": \"#double-one-regexp-expression\" }] }, \"regexp-double-three-line\": { \"begin\": '\\\\b(([uU]r)|([bB]r)|(r[bB]?))(\"\"\")', \"beginCaptures\": { \"2\": { \"name\": \"invalid.deprecated.prefix.python\" }, \"3\": { \"name\": \"storage.type.string.python\" }, \"4\": { \"name\": \"storage.type.string.python\" }, \"5\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": '(\"\"\")', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.regexp.quoted.multi.python\", \"patterns\": [{ \"include\": \"#double-three-regexp-expression\" }] }, \"regexp-escape-catchall\": { \"match\": \"\\\\\\\\(.|\\\\n)\", \"name\": \"constant.character.escape.regexp\" }, \"regexp-escape-character\": { \"match\": \"(?x)\\n\\\\\\\\ (\\nx[0-9A-Fa-f]{2}\\n| 0[0-7]{1,2}\\n| [0-7]{3}\\n)\\n\", \"name\": \"constant.character.escape.regexp\" }, \"regexp-escape-sequence\": { \"patterns\": [{ \"include\": \"#regexp-escape-special\" }, { \"include\": \"#regexp-escape-character\" }, { \"include\": \"#regexp-escape-unicode\" }, { \"include\": \"#regexp-backreference-number\" }, { \"include\": \"#regexp-escape-catchall\" }] }, \"regexp-escape-special\": { \"match\": \"\\\\\\\\([AbBdDsSwWZ])\", \"name\": \"support.other.escape.special.regexp\" }, \"regexp-escape-unicode\": { \"match\": \"(?x)\\n\\\\\\\\ (\\nu[0-9A-Fa-f]{4}\\n| U[0-9A-Fa-f]{8}\\n)\\n\", \"name\": \"constant.character.unicode.regexp\" }, \"regexp-flags\": { \"match\": \"\\\\(\\\\?[aiLmsux]+\\\\)\", \"name\": \"storage.modifier.flag.regexp\" }, \"regexp-quantifier\": { \"match\": \"(?x)\\n\\\\{(\\n\\\\d+ | \\\\d+,(\\\\d+)? | ,\\\\d+\\n)\\\\}\\n\", \"name\": \"keyword.operator.quantifier.regexp\" }, \"regexp-single-one-line\": { \"begin\": \"\\\\b(([uU]r)|([bB]r)|(r[bB]?))(\\\\')\", \"beginCaptures\": { \"2\": { \"name\": \"invalid.deprecated.prefix.python\" }, \"3\": { \"name\": \"storage.type.string.python\" }, \"4\": { \"name\": \"storage.type.string.python\" }, \"5\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\')|(?<!\\\\\\\\)(\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.regexp.quoted.single.python\", \"patterns\": [{ \"include\": \"#single-one-regexp-expression\" }] }, \"regexp-single-three-line\": { \"begin\": \"\\\\b(([uU]r)|([bB]r)|(r[bB]?))(\\\\'\\\\'\\\\')\", \"beginCaptures\": { \"2\": { \"name\": \"invalid.deprecated.prefix.python\" }, \"3\": { \"name\": \"storage.type.string.python\" }, \"4\": { \"name\": \"storage.type.string.python\" }, \"5\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\'\\\\'\\\\')\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.regexp.quoted.multi.python\", \"patterns\": [{ \"include\": \"#single-three-regexp-expression\" }] }, \"reserved-names-vyper\": { \"match\": \"(?x)\\n\\\\b (finney | gwei | range | def | ____init____ | timedelta | babbage | zero_address | raise | external | empty_bytes32 | assert | continue | wei | ada | min_int256 | min_int128 | chainid | max_decimal | default | indexed | selfdestruct | lovelace | immutable | throw | kwei | max_int128 | while | constant | ___default___ | balance | twei | codesize | false | max_int256 | _default_ | init | mwei | if | ____default____ | true | payable | internal | until | this | nonpayable | pass | public | nonreentrant | blockhash | max_uint256 | shannon | none | units | _init_ | ___init___ | is_contract | for | zero_wei | min_decimal | szabo | timestamp | ether | pwei | send) \\\\b\\n\", \"name\": \"name.reserved.vyper\" }, \"return-annotation\": { \"begin\": \"(->)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.annotation.result.python\" } }, \"end\": \"(?=:)\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"round-braces\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.begin.python\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.end.python\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"semicolon\": { \"patterns\": [{ \"match\": \"\\\\;$\", \"name\": \"invalid.deprecated.semicolon.python\" }] }, \"single-one-fregexp-conditional\": { \"begin\": \"(\\\\()\\\\?\\\\((\\\\w+(?:\\\\s+[[:alnum:]]+)?|\\\\d+)\\\\)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.conditional.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.conditional.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-one-fregexp-expression\" }] }, \"single-one-fregexp-expression\": { \"patterns\": [{ \"include\": \"#fregexp-base-expression\" }, { \"include\": \"#single-one-regexp-character-set\" }, { \"include\": \"#single-one-regexp-comments\" }, { \"include\": \"#regexp-flags\" }, { \"include\": \"#single-one-regexp-named-group\" }, { \"include\": \"#regexp-backreference\" }, { \"include\": \"#single-one-fregexp-lookahead\" }, { \"include\": \"#single-one-fregexp-lookahead-negative\" }, { \"include\": \"#single-one-fregexp-lookbehind\" }, { \"include\": \"#single-one-fregexp-lookbehind-negative\" }, { \"include\": \"#single-one-fregexp-conditional\" }, { \"include\": \"#single-one-fregexp-parentheses-non-capturing\" }, { \"include\": \"#single-one-fregexp-parentheses\" }] }, \"single-one-fregexp-lookahead\": { \"begin\": \"(\\\\()\\\\?=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-one-fregexp-expression\" }] }, \"single-one-fregexp-lookahead-negative\": { \"begin\": \"(\\\\()\\\\?!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-one-fregexp-expression\" }] }, \"single-one-fregexp-lookbehind\": { \"begin\": \"(\\\\()\\\\?<=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-one-fregexp-expression\" }] }, \"single-one-fregexp-lookbehind-negative\": { \"begin\": \"(\\\\()\\\\?<!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-one-fregexp-expression\" }] }, \"single-one-fregexp-named-group\": { \"begin\": \"(?x)\\n(\\\\()  (\\\\?P <\\\\w+(?:\\\\s+[[:alnum:]]+)?>)\\n\", \"beginCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\" }, \"2\": { \"name\": \"entity.name.tag.named.group.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.named.regexp\", \"patterns\": [{ \"include\": \"#single-one-fregexp-expression\" }] }, \"single-one-fregexp-parentheses\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-one-fregexp-expression\" }] }, \"single-one-fregexp-parentheses-non-capturing\": { \"begin\": \"\\\\(\\\\?:\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-one-fregexp-expression\" }] }, \"single-one-regexp-character-set\": { \"patterns\": [{ \"match\": \"(?x)\\n\\\\[ \\\\^? \\\\] (?! .*?\\\\])\\n\" }, { \"begin\": \"(\\\\[)(\\\\^)?(\\\\])?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.character.set.begin.regexp constant.other.set.regexp\" }, \"2\": { \"name\": \"keyword.operator.negation.regexp\" }, \"3\": { \"name\": \"constant.character.set.regexp\" } }, \"end\": \"(\\\\]|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.character.set.end.regexp constant.other.set.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.character.set.regexp\", \"patterns\": [{ \"include\": \"#regexp-charecter-set-escapes\" }, { \"match\": \"[^\\\\n]\", \"name\": \"constant.character.set.regexp\" }] }] }, \"single-one-regexp-comments\": { \"begin\": \"\\\\(\\\\?#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.comment.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.comment.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"comment.regexp\", \"patterns\": [{ \"include\": \"#codetags\" }] }, \"single-one-regexp-conditional\": { \"begin\": \"(\\\\()\\\\?\\\\((\\\\w+(?:\\\\s+[[:alnum:]]+)?|\\\\d+)\\\\)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.conditional.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.conditional.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-one-regexp-expression\" }] }, \"single-one-regexp-expression\": { \"patterns\": [{ \"include\": \"#regexp-base-expression\" }, { \"include\": \"#single-one-regexp-character-set\" }, { \"include\": \"#single-one-regexp-comments\" }, { \"include\": \"#regexp-flags\" }, { \"include\": \"#single-one-regexp-named-group\" }, { \"include\": \"#regexp-backreference\" }, { \"include\": \"#single-one-regexp-lookahead\" }, { \"include\": \"#single-one-regexp-lookahead-negative\" }, { \"include\": \"#single-one-regexp-lookbehind\" }, { \"include\": \"#single-one-regexp-lookbehind-negative\" }, { \"include\": \"#single-one-regexp-conditional\" }, { \"include\": \"#single-one-regexp-parentheses-non-capturing\" }, { \"include\": \"#single-one-regexp-parentheses\" }] }, \"single-one-regexp-lookahead\": { \"begin\": \"(\\\\()\\\\?=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-one-regexp-expression\" }] }, \"single-one-regexp-lookahead-negative\": { \"begin\": \"(\\\\()\\\\?!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-one-regexp-expression\" }] }, \"single-one-regexp-lookbehind\": { \"begin\": \"(\\\\()\\\\?<=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-one-regexp-expression\" }] }, \"single-one-regexp-lookbehind-negative\": { \"begin\": \"(\\\\()\\\\?<!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-one-regexp-expression\" }] }, \"single-one-regexp-named-group\": { \"begin\": \"(?x)\\n(\\\\()  (\\\\?P <\\\\w+(?:\\\\s+[[:alnum:]]+)?>)\\n\", \"beginCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\" }, \"2\": { \"name\": \"entity.name.tag.named.group.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.named.regexp\", \"patterns\": [{ \"include\": \"#single-one-regexp-expression\" }] }, \"single-one-regexp-parentheses\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-one-regexp-expression\" }] }, \"single-one-regexp-parentheses-non-capturing\": { \"begin\": \"\\\\(\\\\?:\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'))|((?=(?<!\\\\\\\\)\\\\n))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-one-regexp-expression\" }] }, \"single-three-fregexp-conditional\": { \"begin\": \"(\\\\()\\\\?\\\\((\\\\w+(?:\\\\s+[[:alnum:]]+)?|\\\\d+)\\\\)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.conditional.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.conditional.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-three-fregexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-fregexp-expression\": { \"patterns\": [{ \"include\": \"#fregexp-base-expression\" }, { \"include\": \"#single-three-regexp-character-set\" }, { \"include\": \"#single-three-regexp-comments\" }, { \"include\": \"#regexp-flags\" }, { \"include\": \"#single-three-regexp-named-group\" }, { \"include\": \"#regexp-backreference\" }, { \"include\": \"#single-three-fregexp-lookahead\" }, { \"include\": \"#single-three-fregexp-lookahead-negative\" }, { \"include\": \"#single-three-fregexp-lookbehind\" }, { \"include\": \"#single-three-fregexp-lookbehind-negative\" }, { \"include\": \"#single-three-fregexp-conditional\" }, { \"include\": \"#single-three-fregexp-parentheses-non-capturing\" }, { \"include\": \"#single-three-fregexp-parentheses\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-fregexp-lookahead\": { \"begin\": \"(\\\\()\\\\?=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-three-fregexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-fregexp-lookahead-negative\": { \"begin\": \"(\\\\()\\\\?!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-three-fregexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-fregexp-lookbehind\": { \"begin\": \"(\\\\()\\\\?<=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-three-fregexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-fregexp-lookbehind-negative\": { \"begin\": \"(\\\\()\\\\?<!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-three-fregexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-fregexp-named-group\": { \"begin\": \"(?x)\\n(\\\\()  (\\\\?P <\\\\w+(?:\\\\s+[[:alnum:]]+)?>)\\n\", \"beginCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\" }, \"2\": { \"name\": \"entity.name.tag.named.group.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.named.regexp\", \"patterns\": [{ \"include\": \"#single-three-fregexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-fregexp-parentheses\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-three-fregexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-fregexp-parentheses-non-capturing\": { \"begin\": \"\\\\(\\\\?:\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-three-fregexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-regexp-character-set\": { \"patterns\": [{ \"match\": \"(?x)\\n\\\\[ \\\\^? \\\\] (?! .*?\\\\])\\n\" }, { \"begin\": \"(\\\\[)(\\\\^)?(\\\\])?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.character.set.begin.regexp constant.other.set.regexp\" }, \"2\": { \"name\": \"keyword.operator.negation.regexp\" }, \"3\": { \"name\": \"constant.character.set.regexp\" } }, \"end\": \"(\\\\]|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.character.set.end.regexp constant.other.set.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.character.set.regexp\", \"patterns\": [{ \"include\": \"#regexp-charecter-set-escapes\" }, { \"match\": \"[^\\\\n]\", \"name\": \"constant.character.set.regexp\" }] }] }, \"single-three-regexp-comments\": { \"begin\": \"\\\\(\\\\?#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.comment.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.comment.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"comment.regexp\", \"patterns\": [{ \"include\": \"#codetags\" }] }, \"single-three-regexp-conditional\": { \"begin\": \"(\\\\()\\\\?\\\\((\\\\w+(?:\\\\s+[[:alnum:]]+)?|\\\\d+)\\\\)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.conditional.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.conditional.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-three-regexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-regexp-expression\": { \"patterns\": [{ \"include\": \"#regexp-base-expression\" }, { \"include\": \"#single-three-regexp-character-set\" }, { \"include\": \"#single-three-regexp-comments\" }, { \"include\": \"#regexp-flags\" }, { \"include\": \"#single-three-regexp-named-group\" }, { \"include\": \"#regexp-backreference\" }, { \"include\": \"#single-three-regexp-lookahead\" }, { \"include\": \"#single-three-regexp-lookahead-negative\" }, { \"include\": \"#single-three-regexp-lookbehind\" }, { \"include\": \"#single-three-regexp-lookbehind-negative\" }, { \"include\": \"#single-three-regexp-conditional\" }, { \"include\": \"#single-three-regexp-parentheses-non-capturing\" }, { \"include\": \"#single-three-regexp-parentheses\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-regexp-lookahead\": { \"begin\": \"(\\\\()\\\\?=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-three-regexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-regexp-lookahead-negative\": { \"begin\": \"(\\\\()\\\\?!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookahead.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookahead.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-three-regexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-regexp-lookbehind\": { \"begin\": \"(\\\\()\\\\?<=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-three-regexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-regexp-lookbehind-negative\": { \"begin\": \"(\\\\()\\\\?<!\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.lookbehind.negative.regexp\" }, \"1\": { \"name\": \"punctuation.parenthesis.lookbehind.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-three-regexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-regexp-named-group\": { \"begin\": \"(?x)\\n(\\\\()  (\\\\?P <\\\\w+(?:\\\\s+[[:alnum:]]+)?>)\\n\", \"beginCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\" }, \"2\": { \"name\": \"entity.name.tag.named.group.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"meta.named.regexp\", \"patterns\": [{ \"include\": \"#single-three-regexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-regexp-parentheses\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-three-regexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"single-three-regexp-parentheses-non-capturing\": { \"begin\": \"\\\\(\\\\?:\", \"beginCaptures\": { \"0\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\" } }, \"end\": \"(\\\\)|(?=\\\\'\\\\'\\\\'))\", \"endCaptures\": { \"1\": { \"name\": \"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"patterns\": [{ \"include\": \"#single-three-regexp-expression\" }, { \"include\": \"#comments-string-single-three\" }] }, \"special-names\": { \"match\": \"(?x)\\n\\\\b\\n\\n\\n\\n\\n\\n(\\n_* [[:upper:]] [_\\\\d]* [[:upper:]]\\n)\\n[[:upper:]\\\\d]* (_\\\\w*)?\\n\\\\b\\n\", \"name\": \"constant.other.caps.python\" }, \"special-variables\": { \"captures\": { \"1\": { \"name\": \"variable.language.special.self.python\" }, \"2\": { \"name\": \"variable.language.special.cls.python\" } }, \"match\": \"(?x)\\n\\\\b (?<!\\\\.) (?:\\n(self) | (cls)\\n)\\\\b\\n\" }, \"special-variables-types\": { \"patterns\": [{ \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\nlog   )\\\\b\\n\", \"name\": \"variable.language.special.log.vyper\" }, { \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\nmsg   )\\\\b\\n\", \"name\": \"variable.language.special.msg.vyper\" }, { \"match\": \"(?x)\\n(?<!\\\\.) \\\\b(\\nblock   )\\\\b\\n\", \"name\": \"variable.language.special.block.vyper\" }] }, \"statement\": { \"patterns\": [{ \"include\": \"#import\" }, { \"include\": \"#class-declaration\" }, { \"include\": \"#function-declaration\" }, { \"include\": \"#generator\" }, { \"include\": \"#statement-keyword\" }, { \"include\": \"#assignment-operator\" }, { \"include\": \"#decorator\" }, { \"include\": \"#docstring-statement\" }, { \"include\": \"#semicolon\" }] }, \"statement-keyword\": { \"patterns\": [{ \"match\": \"\\\\b((async\\\\s+)?\\\\s*def)\\\\b\", \"name\": \"storage.type.function.python\" }, { \"comment\": \"if `as` is eventually followed by `:` or line continuation\\nit's probably control flow like:\\n    with foo as bar, \\\\\\n         Foo as Bar:\\n      try:\\n        do_stuff()\\n      except Exception as e:\\n        pass\\n\", \"match\": \"\\\\b(?<!\\\\.)as\\\\b(?=.*[:\\\\\\\\])\", \"name\": \"keyword.control.flow.python\" }, { \"comment\": \"other legal use of `as` is in an import\", \"match\": \"\\\\b(?<!\\\\.)as\\\\b\", \"name\": \"keyword.control.import.python\" }, { \"match\": \"(?x)\\n\\\\b(?<!\\\\.)(\\nasync | continue | del | assert | break | finally | for\\n| from | elif | else | if | except | pass | raise\\n| return | try | while | with\\n)\\\\b\\n\", \"name\": \"keyword.control.flow.python\" }, { \"match\": \"(?x)\\n\\\\b(?<!\\\\.)(\\nglobal | nonlocal\\n)\\\\b\\n\", \"name\": \"storage.modifier.declaration.python\" }, { \"match\": \"\\\\b(?<!\\\\.)(class)\\\\b\", \"name\": \"storage.type.class.python\" }] }, \"string\": { \"patterns\": [{ \"include\": \"#string-quoted-multi-line\" }, { \"include\": \"#string-quoted-single-line\" }, { \"include\": \"#string-bin-quoted-multi-line\" }, { \"include\": \"#string-bin-quoted-single-line\" }, { \"include\": \"#string-raw-quoted-multi-line\" }, { \"include\": \"#string-raw-quoted-single-line\" }, { \"include\": \"#string-raw-bin-quoted-multi-line\" }, { \"include\": \"#string-raw-bin-quoted-single-line\" }, { \"include\": \"#fstring-fnorm-quoted-multi-line\" }, { \"include\": \"#fstring-fnorm-quoted-single-line\" }, { \"include\": \"#fstring-normf-quoted-multi-line\" }, { \"include\": \"#fstring-normf-quoted-single-line\" }, { \"include\": \"#fstring-raw-quoted-multi-line\" }, { \"include\": \"#fstring-raw-quoted-single-line\" }] }, \"string-bin-quoted-multi-line\": { \"begin\": `(\\\\b[bB])('''|\"\"\")`, \"beginCaptures\": { \"1\": { \"name\": \"storage.type.string.python\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\2)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.quoted.binary.multi.python\", \"patterns\": [{ \"include\": \"#string-entity\" }] }, \"string-bin-quoted-single-line\": { \"begin\": `(\\\\b[bB])((['\"]))`, \"beginCaptures\": { \"1\": { \"name\": \"storage.type.string.python\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\2)|((?<!\\\\\\\\)\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.quoted.binary.single.python\", \"patterns\": [{ \"include\": \"#string-entity\" }] }, \"string-brace-formatting\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.character.format.placeholder.other.python\" }, \"3\": { \"name\": \"storage.type.format.python\" }, \"4\": { \"name\": \"storage.type.format.python\" } }, \"match\": `(?x)\n(\n{{ | }}\n| (?:\n{\n\\\\w* (\\\\.[[:alpha:]_]\\\\w* | \\\\[[^\\\\]'\"]+\\\\])*\n(![rsa])?\n( : \\\\w? [<>=^]? [-+ ]? \\\\#?\n\\\\d* ,? (\\\\.\\\\d+)? [bcdeEfFgGnosxX%]? )?\n})\n)\n`, \"name\": \"meta.format.brace.python\" }, { \"captures\": { \"1\": { \"name\": \"constant.character.format.placeholder.other.python\" }, \"3\": { \"name\": \"storage.type.format.python\" }, \"4\": { \"name\": \"storage.type.format.python\" } }, \"match\": `(?x)\n(\n{\n\\\\w* (\\\\.[[:alpha:]_]\\\\w* | \\\\[[^\\\\]'\"]+\\\\])*\n(![rsa])?\n(:)\n[^'\"{}\\\\n]* (?:\n\\\\{ [^'\"}\\\\n]*? \\\\} [^'\"{}\\\\n]*\n)*\n}\n)\n`, \"name\": \"meta.format.brace.python\" }] }, \"string-consume-escape\": { \"match\": `\\\\\\\\['\"\\\\n\\\\\\\\]` }, \"string-entity\": { \"patterns\": [{ \"include\": \"#escape-sequence\" }, { \"include\": \"#string-line-continuation\" }, { \"include\": \"#string-formatting\" }] }, \"string-formatting\": { \"captures\": { \"1\": { \"name\": \"constant.character.format.placeholder.other.python\" } }, \"match\": \"(?x)\\n(\\n% (\\\\([\\\\w\\\\s]*\\\\))?\\n[-+#0 ]*\\n(\\\\d+|\\\\*)? (\\\\.(\\\\d+|\\\\*))?\\n([hlL])?\\n[diouxXeEfFgGcrsab%]\\n)\\n\", \"name\": \"meta.format.percent.python\" }, \"string-line-continuation\": { \"match\": \"\\\\\\\\$\", \"name\": \"constant.language.python\" }, \"string-multi-bad-brace1-formatting-raw\": { \"begin\": `(?x)\n(?= \\\\{%\n( .*? (?!'''|\"\"\") )\n%\\\\}\n)\n`, \"comment\": \"template using {% ... %}\", \"end\": `(?='''|\"\"\")`, \"patterns\": [{ \"include\": \"#string-consume-escape\" }] }, \"string-multi-bad-brace1-formatting-unicode\": { \"begin\": `(?x)\n(?= \\\\{%\n( .*? (?!'''|\"\"\") )\n%\\\\}\n)\n`, \"comment\": \"template using {% ... %}\", \"end\": `(?='''|\"\"\")`, \"patterns\": [{ \"include\": \"#escape-sequence-unicode\" }, { \"include\": \"#escape-sequence\" }, { \"include\": \"#string-line-continuation\" }] }, \"string-multi-bad-brace2-formatting-raw\": { \"begin\": `(?x)\n(?!\\\\{\\\\{)\n(?= \\\\{ (\n\\\\w*? (?!'''|\"\"\") [^!:\\\\.\\\\[}\\\\w]\n)\n.*?(?!'''|\"\"\")\n\\\\}\n)\n`, \"comment\": \"odd format or format-like syntax\", \"end\": `(?='''|\"\"\")`, \"patterns\": [{ \"include\": \"#string-consume-escape\" }, { \"include\": \"#string-formatting\" }] }, \"string-multi-bad-brace2-formatting-unicode\": { \"begin\": `(?x)\n(?!\\\\{\\\\{)\n(?= \\\\{ (\n\\\\w*? (?!'''|\"\"\") [^!:\\\\.\\\\[}\\\\w]\n)\n.*?(?!'''|\"\"\")\n\\\\}\n)\n`, \"comment\": \"odd format or format-like syntax\", \"end\": `(?='''|\"\"\")`, \"patterns\": [{ \"include\": \"#escape-sequence-unicode\" }, { \"include\": \"#string-entity\" }] }, \"string-quoted-multi-line\": { \"begin\": `(?:\\\\b([rR])(?=[uU]))?([uU])?('''|\"\"\")`, \"beginCaptures\": { \"1\": { \"name\": \"invalid.illegal.prefix.python\" }, \"2\": { \"name\": \"storage.type.string.python\" }, \"3\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\3)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.quoted.multi.python\", \"patterns\": [{ \"include\": \"#string-multi-bad-brace1-formatting-unicode\" }, { \"include\": \"#string-multi-bad-brace2-formatting-unicode\" }, { \"include\": \"#string-unicode-guts\" }] }, \"string-quoted-single-line\": { \"begin\": `(?:\\\\b([rR])(?=[uU]))?([uU])?((['\"]))`, \"beginCaptures\": { \"1\": { \"name\": \"invalid.illegal.prefix.python\" }, \"2\": { \"name\": \"storage.type.string.python\" }, \"3\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\3)|((?<!\\\\\\\\)\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.quoted.single.python\", \"patterns\": [{ \"include\": \"#string-single-bad-brace1-formatting-unicode\" }, { \"include\": \"#string-single-bad-brace2-formatting-unicode\" }, { \"include\": \"#string-unicode-guts\" }] }, \"string-raw-bin-guts\": { \"patterns\": [{ \"include\": \"#string-consume-escape\" }, { \"include\": \"#string-formatting\" }] }, \"string-raw-bin-quoted-multi-line\": { \"begin\": `(\\\\b(?:R[bB]|[bB]R))('''|\"\"\")`, \"beginCaptures\": { \"1\": { \"name\": \"storage.type.string.python\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\2)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.quoted.raw.binary.multi.python\", \"patterns\": [{ \"include\": \"#string-raw-bin-guts\" }] }, \"string-raw-bin-quoted-single-line\": { \"begin\": `(\\\\b(?:R[bB]|[bB]R))((['\"]))`, \"beginCaptures\": { \"1\": { \"name\": \"storage.type.string.python\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\2)|((?<!\\\\\\\\)\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.quoted.raw.binary.single.python\", \"patterns\": [{ \"include\": \"#string-raw-bin-guts\" }] }, \"string-raw-guts\": { \"patterns\": [{ \"include\": \"#string-consume-escape\" }, { \"include\": \"#string-formatting\" }, { \"include\": \"#string-brace-formatting\" }] }, \"string-raw-quoted-multi-line\": { \"begin\": `\\\\b(([uU]R)|(R))('''|\"\"\")`, \"beginCaptures\": { \"2\": { \"name\": \"invalid.deprecated.prefix.python\" }, \"3\": { \"name\": \"storage.type.string.python\" }, \"4\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\4)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.quoted.raw.multi.python\", \"patterns\": [{ \"include\": \"#string-multi-bad-brace1-formatting-raw\" }, { \"include\": \"#string-multi-bad-brace2-formatting-raw\" }, { \"include\": \"#string-raw-guts\" }] }, \"string-raw-quoted-single-line\": { \"begin\": `\\\\b(([uU]R)|(R))((['\"]))`, \"beginCaptures\": { \"2\": { \"name\": \"invalid.deprecated.prefix.python\" }, \"3\": { \"name\": \"storage.type.string.python\" }, \"4\": { \"name\": \"punctuation.definition.string.begin.python\" } }, \"end\": \"(\\\\4)|((?<!\\\\\\\\)\\\\n)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.python\" }, \"2\": { \"name\": \"invalid.illegal.newline.python\" } }, \"name\": \"string.quoted.raw.single.python\", \"patterns\": [{ \"include\": \"#string-single-bad-brace1-formatting-raw\" }, { \"include\": \"#string-single-bad-brace2-formatting-raw\" }, { \"include\": \"#string-raw-guts\" }] }, \"string-single-bad-brace1-formatting-raw\": { \"begin\": `(?x)\n(?= \\\\{%\n( .*? (?!(['\"])|((?<!\\\\\\\\)\\\\n)) )\n%\\\\}\n)\n`, \"comment\": \"template using {% ... %}\", \"end\": `(?=(['\"])|((?<!\\\\\\\\)\\\\n))`, \"patterns\": [{ \"include\": \"#string-consume-escape\" }] }, \"string-single-bad-brace1-formatting-unicode\": { \"begin\": `(?x)\n(?= \\\\{%\n( .*? (?!(['\"])|((?<!\\\\\\\\)\\\\n)) )\n%\\\\}\n)\n`, \"comment\": \"template using {% ... %}\", \"end\": `(?=(['\"])|((?<!\\\\\\\\)\\\\n))`, \"patterns\": [{ \"include\": \"#escape-sequence-unicode\" }, { \"include\": \"#escape-sequence\" }, { \"include\": \"#string-line-continuation\" }] }, \"string-single-bad-brace2-formatting-raw\": { \"begin\": `(?x)\n(?!\\\\{\\\\{)\n(?= \\\\{ (\n\\\\w*? (?!(['\"])|((?<!\\\\\\\\)\\\\n)) [^!:\\\\.\\\\[}\\\\w]\n)\n.*?(?!(['\"])|((?<!\\\\\\\\)\\\\n))\n\\\\}\n)\n`, \"comment\": \"odd format or format-like syntax\", \"end\": `(?=(['\"])|((?<!\\\\\\\\)\\\\n))`, \"patterns\": [{ \"include\": \"#string-consume-escape\" }, { \"include\": \"#string-formatting\" }] }, \"string-single-bad-brace2-formatting-unicode\": { \"begin\": `(?x)\n(?!\\\\{\\\\{)\n(?= \\\\{ (\n\\\\w*? (?!(['\"])|((?<!\\\\\\\\)\\\\n)) [^!:\\\\.\\\\[}\\\\w]\n)\n.*?(?!(['\"])|((?<!\\\\\\\\)\\\\n))\n\\\\}\n)\n`, \"comment\": \"odd format or format-like syntax\", \"end\": `(?=(['\"])|((?<!\\\\\\\\)\\\\n))`, \"patterns\": [{ \"include\": \"#escape-sequence-unicode\" }, { \"include\": \"#string-entity\" }] }, \"string-unicode-guts\": { \"patterns\": [{ \"include\": \"#escape-sequence-unicode\" }, { \"include\": \"#string-entity\" }, { \"include\": \"#string-brace-formatting\" }] } }, \"scopeName\": \"source.vyper\", \"aliases\": [\"vy\"] });\nvar vyper = [\n  lang\n];\n\nexport { vyper as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,SAAS,QAAQ,SAAS,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,wBAAwB,CAAC,GAAG,cAAc,EAAE,uBAAuB,EAAE,SAAS,4CAA4C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,eAAe,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,SAAS,UAAU,QAAQ,qCAAqC,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,2GAA2G,QAAQ,qCAAqC,GAAG,aAAa,EAAE,SAAS,OAAO,OAAO,0BAA0B,QAAQ,sCAAsC,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,sBAAsB,EAAE,SAAS,qyBAAqyB,QAAQ,gCAAgC,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,SAAS,ofAAof,QAAQ,kCAAkC,GAAG,EAAE,SAAS,4HAA4H,QAAQ,iCAAiC,GAAG,EAAE,SAAS,iXAAiX,QAAQ,iCAAiC,GAAG,EAAE,SAAS,gGAAgG,QAAQ,0CAA0C,GAAG,EAAE,SAAS,uCAAuC,QAAQ,2BAA2B,GAAG,EAAE,SAAS,wDAAwD,QAAQ,+BAA+B,GAAG,EAAE,SAAS,oGAAoG,QAAQ,gDAAgD,GAAG,EAAE,SAAS,yEAAyE,QAAQ,kDAAkD,CAAC,EAAE,GAAG,8BAA8B,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,oVAAoV,QAAQ,sBAAsB,GAAG,EAAE,SAAS,yKAAyK,QAAQ,8BAA8B,GAAG,EAAE,SAAS,wrBAAwrB,QAAQ,8BAA8B,GAAG,EAAE,SAAS,wRAAwR,QAAQ,0BAA0B,GAAG,EAAE,SAAS,oRAAoR,QAAQ,8BAA8B,GAAG,EAAE,SAAS,4CAA4C,QAAQ,+CAA+C,CAAC,EAAE,GAAG,4BAA4B,EAAE,SAAS,oDAAoD,WAAW,uDAAuD,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,SAAS,kEAAkE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,QAAQ,iCAAiC,YAAY,CAAC,EAAE,SAAS,gBAAgB,QAAQ,8CAA8C,GAAG,EAAE,SAAS,KAAK,QAAQ,2CAA2C,GAAG,EAAE,SAAS,UAAU,QAAQ,qCAAqC,GAAG,EAAE,SAAS,mBAAmB,QAAQ,gCAAgC,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sEAAsE,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,8CAA8C,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,SAAS,sCAAsC,QAAQ,gCAAgC,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,SAAS,2CAA2C,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,wLAAwL,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,gCAAgC,OAAO,iBAAiB,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,8CAA8C,QAAQ,0CAA0C,GAAG,EAAE,SAAS,0KAA0K,QAAQ,wCAAwC,GAAG,EAAE,SAAS,mCAAmC,QAAQ,+CAA+C,GAAG,EAAE,SAAS,sBAAsB,QAAQ,4CAA4C,CAAC,EAAE,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,OAAO,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,gCAAgC,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,eAAe,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,gCAAgC,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,eAAe,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,KAAK,QAAQ,oCAAoC,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,kDAAkD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,mEAAmE,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,QAAQ,kCAAkC,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,sCAAsC,QAAQ,wCAAwC,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,0DAA0D,QAAQ,mCAAmC,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,wCAAwC,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,EAAE,SAAS,+BAA+B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,4CAA4C,YAAY,CAAC,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,eAAe,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,yCAAyC,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,EAAE,SAAS,mBAAmB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,eAAe,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,6CAA6C,YAAY,CAAC,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,0BAA0B,EAAE,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,4BAA4B,CAAC,EAAE,GAAG,oBAAoB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,qGAAqG,GAAG,uBAAuB,EAAE,SAAS,+CAA+C,WAAW,mLAAmL,OAAO,0DAA0D,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,kCAAkC,EAAE,SAAS,kDAAkD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,mDAAmD,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,iCAAiC,EAAE,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,yCAAyC,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,0CAA0C,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gDAAgD,GAAG,EAAE,WAAW,kCAAkC,CAAC,EAAE,GAAG,gCAAgC,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,iFAAiF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,yCAAyC,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,0FAA0F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,mFAAmF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,0CAA0C,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,4FAA4F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,kCAAkC,EAAE,SAAS,qDAAqD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8EAA8E,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,4EAA4E,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,kCAAkC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wEAAwE,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,sEAAsE,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,gDAAgD,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sFAAsF,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,mCAAmC,EAAE,YAAY,CAAC,EAAE,SAAS,mCAAmC,GAAG,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mEAAmE,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,iEAAiE,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,gCAAgC,GAAG,EAAE,SAAS,UAAU,QAAQ,gCAAgC,CAAC,EAAE,CAAC,EAAE,GAAG,8BAA8B,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,kDAAkD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,mDAAmD,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,gCAAgC,EAAE,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,wCAAwC,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,yCAAyC,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,+CAA+C,GAAG,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,+BAA+B,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,iFAAiF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,wCAAwC,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,0FAA0F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,gCAAgC,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,mFAAmF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,yCAAyC,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,4FAA4F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,qDAAqD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8EAA8E,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,4EAA4E,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wEAAwE,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,sEAAsE,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,+CAA+C,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sFAAsF,EAAE,GAAG,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,oCAAoC,EAAE,SAAS,kDAAkD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,mDAAmD,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,mCAAmC,EAAE,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,qCAAqC,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,2CAA2C,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,4CAA4C,GAAG,EAAE,WAAW,oCAAoC,GAAG,EAAE,WAAW,kDAAkD,GAAG,EAAE,WAAW,oCAAoC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,kCAAkC,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,iFAAiF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,2CAA2C,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,0FAA0F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,mCAAmC,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,mFAAmF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,4CAA4C,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,4FAA4F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,oCAAoC,EAAE,SAAS,qDAAqD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8EAA8E,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,4EAA4E,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,oCAAoC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wEAAwE,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,sEAAsE,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,kDAAkD,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sFAAsF,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,qCAAqC,EAAE,YAAY,CAAC,EAAE,SAAS,mCAAmC,GAAG,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mEAAmE,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,iEAAiE,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,gCAAgC,GAAG,EAAE,SAAS,UAAU,QAAQ,gCAAgC,CAAC,EAAE,CAAC,EAAE,GAAG,gCAAgC,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,mCAAmC,EAAE,SAAS,kDAAkD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,mDAAmD,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,kCAAkC,EAAE,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,qCAAqC,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,0CAA0C,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,2CAA2C,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,iDAAiD,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,iFAAiF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,0CAA0C,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,0FAA0F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,kCAAkC,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,mFAAmF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,2CAA2C,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,4FAA4F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,mCAAmC,EAAE,SAAS,qDAAqD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8EAA8E,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,4EAA4E,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,mCAAmC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wEAAwE,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,sEAAsE,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,iDAAiD,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sFAAsF,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,aAAa,QAAQ,iCAAiC,GAAG,mBAAmB,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAM3jlC,QAAQ,mCAAmC,GAAG,2BAA2B,EAAE,YAAY,CAAC,EAAE,SAAS,8EAA8E,QAAQ,mCAAmC,CAAC,EAAE,GAAG,cAAc,EAAE,WAAW,gCAAgC,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,wCAAwC,SAAS,kCAAkC,CAAC,EAAE,GAAG,mBAAmB,EAAE,WAAW,+DAA+D,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,2BAA2B,CAAC,EAAE,GAAG,mBAAmB,EAAE,WAAW,gEAAgE,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,gBAAgB,EAAE,WAAW,uEAAuE,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,wCAAwC,SAAS,kCAAkC,CAAC,EAAE,GAAG,2BAA2B,EAAE,YAAY,CAAC,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,SAAS,YAAY,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,2BAA2B,EAAE,SAAS,oCAAoC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,iEAAiE,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,6BAA6B,EAAE,SAAS,sCAAsC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,gEAAgE,YAAY,CAAC,EAAE,WAAW,mCAAmC,CAAC,EAAE,GAAG,sBAAsB,EAAE,SAAS,yDAAyD,QAAQ,qCAAqC,GAAG,2BAA2B,EAAE,SAAS,sCAAsC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,wBAAwB,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,iEAAiE,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,6BAA6B,EAAE,SAAS,4CAA4C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,eAAe,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,gEAAgE,YAAY,CAAC,EAAE,WAAW,mCAAmC,CAAC,EAAE,GAAG,mCAAmC,EAAE,SAAS,+BAA+B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mFAAmF,GAAG,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,mGAAmG,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,iGAAiG,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,oCAAoC,EAAE,SAAS,8BAA8B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,oGAAoG,EAAE,GAAG,OAAO,wBAAwB,eAAe,EAAE,KAAK,EAAE,QAAQ,kGAAkG,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,kCAAkC,CAAC,EAAE,GAAG,6BAA6B,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qDAAqD,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,WAAW,4BAA4B,SAAS,gBAAgB,GAAG,EAAE,SAAS,WAAW,QAAQ,mCAAmC,CAAC,EAAE,GAAG,kCAAkC,EAAE,SAAS,YAAY,QAAQ,+BAA+B,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,+BAA+B,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,gCAAgC,EAAE,SAAS,0BAA0B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,WAAW,uEAAuE,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,WAAW,qCAAqC,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,sBAAsB,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAgBxyP,QAAQ,wDAAwD,GAAG,mCAAmC,EAAE,SAAS,8BAA8B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,mFAAmF,GAAG,KAAK,EAAE,QAAQ,wEAAwE,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,iGAAiG,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,oCAAoC,EAAE,SAAS,6BAA6B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,yEAAyE,EAAE,GAAG,OAAO,wBAAwB,eAAe,EAAE,KAAK,EAAE,QAAQ,kGAAkG,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,0BAA0B,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAgB9rD,QAAQ,4DAA4D,GAAG,iCAAiC,EAAE,SAAS,qCAAqC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uFAAuF,GAAG,KAAK,EAAE,QAAQ,4EAA4E,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,qGAAqG,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,kCAAkC,EAAE,SAAS,oCAAoC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wFAAwF,GAAG,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,wBAAwB,eAAe,EAAE,KAAK,EAAE,QAAQ,sGAAsG,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,2BAA2B,CAAC,EAAE,GAAG,2BAA2B,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAgB5hD,QAAQ,6DAA6D,GAAG,wBAAwB,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,WAAW,qCAAqC,OAAO,yBAAyB,eAAe,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAgBzf,QAAQ,yDAAyD,GAAG,4BAA4B,EAAE,YAAY,CAAC,EAAE,SAAS,iBAAiB,QAAQ,6BAA6B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,gGAAgG,GAAG,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,wBAAwB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,SAAS,4BAA4B,QAAQ,6BAA6B,GAAG,EAAE,SAAS,aAAa,QAAQ,6BAA6B,GAAG,EAAE,SAAS,OAAO,QAAQ,6BAA6B,GAAG,EAAE,SAAS,UAAU,QAAQ,6BAA6B,GAAG,EAAE,SAAS,SAAS,QAAQ,6BAA6B,GAAG,EAAE,SAAS,WAAW,QAAQ,6BAA6B,GAAG,EAAE,SAAS,YAAY,QAAQ,6BAA6B,GAAG,EAAE,SAAS,SAAS,QAAQ,6BAA6B,CAAC,EAAE,GAAG,6BAA6B,EAAE,YAAY,CAAC,EAAE,SAAS,iBAAiB,QAAQ,6BAA6B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,gGAAgG,GAAG,EAAE,WAAW,kCAAkC,CAAC,EAAE,GAAG,kCAAkC,EAAE,SAAS,wBAAwB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,iBAAiB,YAAY,CAAC,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,SAAS,4BAA4B,QAAQ,6BAA6B,GAAG,EAAE,SAAS,aAAa,QAAQ,6BAA6B,GAAG,EAAE,SAAS,OAAO,QAAQ,6BAA6B,GAAG,EAAE,SAAS,UAAU,QAAQ,6BAA6B,GAAG,EAAE,SAAS,SAAS,QAAQ,6BAA6B,GAAG,EAAE,SAAS,WAAW,QAAQ,6BAA6B,GAAG,EAAE,SAAS,YAAY,QAAQ,6BAA6B,GAAG,EAAE,SAAS,SAAS,QAAQ,6BAA6B,CAAC,EAAE,GAAG,sBAAsB,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,eAAe,uCAAuC,OAAO,yBAAyB,YAAY,CAAC,EAAE,SAAS,OAAO,QAAQ,yCAAyC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,SAAS,0CAA0C,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,oCAAoC,GAAG,EAAE,SAAS,UAAU,QAAQ,qCAAqC,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,SAAS,qBAAqB,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,oDAAoD,WAAW,kDAAkD,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,6FAA6F,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,oBAAoB,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,SAAS,iCAAiC,QAAQ,sCAAsC,GAAG,EAAE,SAAS,8BAA8B,QAAQ,yCAAyC,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,SAAS,sCAAsC,QAAQ,8BAA8B,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,+DAA+D,SAAS,sCAAsC,QAAQ,oCAAoC,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,WAAW,8HAA8H,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,MAAM,QAAQ,oCAAoC,GAAG,iBAAiB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,SAAS,mTAAmT,GAAG,uBAAuB,EAAE,WAAW,iDAAiD,SAAS,2BAA2B,QAAQ,8BAA8B,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,SAAS,uBAAuB,QAAQ,kCAAkC,GAAG,EAAE,SAAS,QAAQ,QAAQ,kCAAkC,GAAG,EAAE,WAAW,qDAAqD,SAAS,QAAQ,QAAQ,kCAAkC,CAAC,EAAE,GAAG,UAAU,EAAE,WAAW,yEAAyE,YAAY,CAAC,EAAE,SAAS,oCAAoC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,gBAAgB,YAAY,CAAC,EAAE,SAAS,QAAQ,QAAQ,sCAAsC,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,0BAA0B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,SAAS,oBAAoB,QAAQ,gCAAgC,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,cAAc,EAAE,WAAW,sTAAsT,SAAS,MAAM,GAAG,0BAA0B,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,qCAAqC,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,gDAAgD,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,eAAe,qCAAqC,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,KAAK,QAAQ,qCAAqC,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,SAAS,sCAAsC,QAAQ,2BAA2B,GAAG,EAAE,WAAW,2BAA2B,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,wCAAwC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,+BAA+B,GAAG,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,eAAe,0CAA0C,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,mDAAmD,EAAE,GAAG,QAAQ,+BAA+B,YAAY,CAAC,EAAE,SAAS,gBAAgB,QAAQ,8CAA8C,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,SAAS,wCAAwC,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,yBAAyB,QAAQ,sCAAsC,GAAG,4BAA4B,EAAE,SAAS,0BAA0B,QAAQ,sCAAsC,GAAG,iCAAiC,EAAE,SAAS,4CAA4C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,OAAO,eAAe,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iDAAiD,GAAG,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,SAAS,yBAAyB,GAAG,EAAE,SAAS,mBAAmB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAOt7U,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,QAAQ,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,mDAAmD,QAAQ,2BAA2B,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,OAAO,eAAe,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,wBAAwB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,WAAW,yHAAyH,SAAS,qiCAAqiC,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,wBAAwB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,WAAW,kDAAkD,SAAS,gYAAgY,GAAG,iBAAiB,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,yEAAyE,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,2BAA2B,CAAC,EAAE,GAAG,2BAA2B,EAAE,WAAW,kEAAkE,SAAS,sCAAsC,QAAQ,wBAAwB,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,2BAA2B,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,qBAAqB,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,UAAU,EAAE,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,SAAS,iBAAiB,QAAQ,8BAA8B,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,iDAAiD,QAAQ,8BAA8B,GAAG,cAAc,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,uHAAuH,QAAQ,8BAA8B,GAAG,gBAAgB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,gPAAgP,QAAQ,gCAAgC,GAAG,cAAc,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,wDAAwD,QAAQ,8BAA8B,GAAG,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,WAAW,mDAAmD,SAAS,wDAAwD,QAAQ,8BAA8B,GAAG,cAAc,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,kDAAkD,QAAQ,8BAA8B,GAAG,qBAAqB,EAAE,SAAS,0CAA0C,WAAW,iJAAiJ,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,+WAA+W,GAAG,qBAAqB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,2DAA2D,GAAG,KAAK,EAAE,QAAQ,0DAA0D,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,SAAS,qDAAqD,GAAG,cAAc,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,gBAAgB,QAAQ,8CAA8C,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,SAAS,2DAA2D,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,QAAQ,qCAAqC,GAAG,EAAE,SAAS,KAAK,QAAQ,uCAAuC,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,2BAA2B,CAAC,EAAE,GAAG,wBAAwB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4FAA4F,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,0FAA0F,EAAE,GAAG,SAAS,2DAA2D,QAAQ,kCAAkC,GAAG,+BAA+B,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,mBAAmB,QAAQ,4BAA4B,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,QAAQ,iCAAiC,GAAG,EAAE,SAAS,OAAO,QAAQ,mCAAmC,GAAG,EAAE,SAAS,OAAO,QAAQ,iCAAiC,GAAG,EAAE,SAAS,aAAa,QAAQ,qCAAqC,GAAG,EAAE,SAAS,OAAO,QAAQ,sCAAsC,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,0BAA0B,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,gCAAgC,EAAE,YAAY,CAAC,EAAE,SAAS,qBAAqB,QAAQ,mCAAmC,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,SAAS,oBAAoB,QAAQ,mCAAmC,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,0BAA0B,EAAE,SAAS,oCAAoC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,sCAAsC,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,4BAA4B,EAAE,SAAS,sCAAsC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,qCAAqC,YAAY,CAAC,EAAE,WAAW,kCAAkC,CAAC,EAAE,GAAG,0BAA0B,EAAE,SAAS,eAAe,QAAQ,mCAAmC,GAAG,2BAA2B,EAAE,SAAS,iEAAiE,QAAQ,mCAAmC,GAAG,0BAA0B,EAAE,YAAY,CAAC,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,sBAAsB,QAAQ,sCAAsC,GAAG,yBAAyB,EAAE,SAAS,yDAAyD,QAAQ,oCAAoC,GAAG,gBAAgB,EAAE,SAAS,uBAAuB,QAAQ,+BAA+B,GAAG,qBAAqB,EAAE,SAAS,mDAAmD,QAAQ,qCAAqC,GAAG,0BAA0B,EAAE,SAAS,sCAAsC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,wBAAwB,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,sCAAsC,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,4BAA4B,EAAE,SAAS,4CAA4C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,eAAe,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,qCAAqC,YAAY,CAAC,EAAE,WAAW,kCAAkC,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,4qBAA4qB,QAAQ,sBAAsB,GAAG,qBAAqB,EAAE,SAAS,QAAQ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,QAAQ,QAAQ,sCAAsC,CAAC,EAAE,GAAG,kCAAkC,EAAE,SAAS,kDAAkD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,mDAAmD,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,iCAAiC,EAAE,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,yCAAyC,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,0CAA0C,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gDAAgD,GAAG,EAAE,WAAW,kCAAkC,CAAC,EAAE,GAAG,gCAAgC,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,iFAAiF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,yCAAyC,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,0FAA0F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,mFAAmF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,0CAA0C,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,4FAA4F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,kCAAkC,EAAE,SAAS,qDAAqD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8EAA8E,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,4EAA4E,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,kCAAkC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wEAAwE,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,sEAAsE,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,gDAAgD,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sFAAsF,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,mCAAmC,EAAE,YAAY,CAAC,EAAE,SAAS,mCAAmC,GAAG,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mEAAmE,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,iEAAiE,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,gCAAgC,GAAG,EAAE,SAAS,UAAU,QAAQ,gCAAgC,CAAC,EAAE,CAAC,EAAE,GAAG,8BAA8B,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,kDAAkD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,mDAAmD,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,gCAAgC,EAAE,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,wCAAwC,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,yCAAyC,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,+CAA+C,GAAG,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,+BAA+B,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,iFAAiF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,wCAAwC,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,0FAA0F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,gCAAgC,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,mFAAmF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,yCAAyC,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,4FAA4F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,qDAAqD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8EAA8E,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,4EAA4E,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wEAAwE,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,sEAAsE,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,+CAA+C,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sFAAsF,EAAE,GAAG,OAAO,oCAAoC,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,oCAAoC,EAAE,SAAS,kDAAkD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,mDAAmD,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,mCAAmC,EAAE,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,qCAAqC,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,2CAA2C,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,4CAA4C,GAAG,EAAE,WAAW,oCAAoC,GAAG,EAAE,WAAW,kDAAkD,GAAG,EAAE,WAAW,oCAAoC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,kCAAkC,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,iFAAiF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,2CAA2C,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,0FAA0F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,mCAAmC,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,mFAAmF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,4CAA4C,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,4FAA4F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,oCAAoC,EAAE,SAAS,qDAAqD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8EAA8E,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,4EAA4E,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,oCAAoC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wEAAwE,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,sEAAsE,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,kDAAkD,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sFAAsF,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,qCAAqC,EAAE,YAAY,CAAC,EAAE,SAAS,mCAAmC,GAAG,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mEAAmE,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,iEAAiE,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,gCAAgC,GAAG,EAAE,SAAS,UAAU,QAAQ,gCAAgC,CAAC,EAAE,CAAC,EAAE,GAAG,gCAAgC,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,mCAAmC,EAAE,SAAS,kDAAkD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,mDAAmD,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,kCAAkC,EAAE,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,qCAAqC,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,0CAA0C,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,2CAA2C,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,iDAAiD,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,iFAAiF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,0CAA0C,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,0FAA0F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,kCAAkC,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,mFAAmF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,2CAA2C,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,4FAA4F,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,mCAAmC,EAAE,SAAS,qDAAqD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8EAA8E,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,4EAA4E,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,mCAAmC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wEAAwE,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,sEAAsE,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,iDAAiD,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sFAAsF,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,kGAAkG,QAAQ,6BAA6B,GAAG,qBAAqB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,iDAAiD,GAAG,2BAA2B,EAAE,YAAY,CAAC,EAAE,SAAS,qCAAqC,QAAQ,sCAAsC,GAAG,EAAE,SAAS,qCAAqC,QAAQ,sCAAsC,GAAG,EAAE,SAAS,uCAAuC,QAAQ,wCAAwC,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,SAAS,+BAA+B,QAAQ,+BAA+B,GAAG,EAAE,WAAW,6NAA6N,SAAS,iCAAiC,QAAQ,8BAA8B,GAAG,EAAE,WAAW,2CAA2C,SAAS,oBAAoB,QAAQ,gCAAgC,GAAG,EAAE,SAAS,yKAAyK,QAAQ,8BAA8B,GAAG,EAAE,SAAS,iDAAiD,QAAQ,sCAAsC,GAAG,EAAE,SAAS,yBAAyB,QAAQ,4BAA4B,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,oCAAoC,GAAG,EAAE,WAAW,qCAAqC,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,oCAAoC,GAAG,EAAE,WAAW,mCAAmC,GAAG,EAAE,WAAW,oCAAoC,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,kCAAkC,CAAC,EAAE,GAAG,gCAAgC,EAAE,SAAS,sBAAsB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,qCAAqC,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,wBAAwB,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,sCAAsC,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,2BAA2B,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qDAAqD,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAW96tC,QAAQ,2BAA2B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qDAAqD,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAWtO,QAAQ,2BAA2B,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,kBAAkB,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,SAAS,8GAA8G,QAAQ,6BAA6B,GAAG,4BAA4B,EAAE,SAAS,SAAS,QAAQ,2BAA2B,GAAG,0CAA0C,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,GAKjpB,WAAW,4BAA4B,OAAO,eAAe,YAAY,CAAC,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,8CAA8C,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,GAK/K,WAAW,4BAA4B,OAAO,eAAe,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,4BAA4B,CAAC,EAAE,GAAG,0CAA0C,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAQ5P,WAAW,oCAAoC,OAAO,eAAe,YAAY,CAAC,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,8CAA8C,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAQ5N,WAAW,oCAAoC,OAAO,eAAe,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,4BAA4B,EAAE,SAAS,0CAA0C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,8CAA8C,GAAG,EAAE,WAAW,8CAA8C,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,6BAA6B,EAAE,SAAS,yCAAyC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,wBAAwB,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,+CAA+C,GAAG,EAAE,WAAW,+CAA+C,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,CAAC,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,oCAAoC,EAAE,SAAS,iCAAiC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,yCAAyC,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,qCAAqC,EAAE,SAAS,gCAAgC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,wBAAwB,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,0CAA0C,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,2BAA2B,CAAC,EAAE,GAAG,gCAAgC,EAAE,SAAS,6BAA6B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,kCAAkC,YAAY,CAAC,EAAE,WAAW,0CAA0C,GAAG,EAAE,WAAW,0CAA0C,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,wBAAwB,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,2CAA2C,GAAG,EAAE,WAAW,2CAA2C,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,2CAA2C,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,GAKx2H,WAAW,4BAA4B,OAAO,6BAA6B,YAAY,CAAC,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,+CAA+C,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,GAK9L,WAAW,4BAA4B,OAAO,6BAA6B,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,4BAA4B,CAAC,EAAE,GAAG,2CAA2C,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAQ3Q,WAAW,oCAAoC,OAAO,6BAA6B,YAAY,CAAC,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,+CAA+C,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAQ3O,WAAW,oCAAoC,OAAO,6BAA6B,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,2BAA2B,CAAC,EAAE,EAAE,GAAG,aAAa,gBAAgB,WAAW,CAAC,IAAI,EAAE,CAAC;AACpY,IAAI,QAAQ;AAAA,EACV;AACF;", "names": []}