import {
  ClassDB,
  classDiagram_default,
  classRenderer_v3_unified_default,
  styles_default
} from "./chunk-EN2WPXP7.js";
import "./chunk-QZ7MSHUK.js";
import "./chunk-V2QRN2YP.js";
import "./chunk-VAYWTAUN.js";
import {
  __name
} from "./chunk-E47MS5QI.js";
import "./chunk-LNPCGL3T.js";
import "./chunk-SNBSRBFP.js";
import "./chunk-PT45IBK3.js";
import "./chunk-CO6MSXMG.js";
import "./chunk-2LSFTFF7.js";

// node_modules/.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/vue-element-plus-x/dist/classDiagram-M3E45YP4-CgPDRXoK.js
var diagram = {
  parser: classDiagram_default,
  get db() {
    return new ClassDB();
  },
  renderer: classRenderer_v3_unified_default,
  styles: styles_default,
  init: __name((cnf) => {
    if (!cnf.class) {
      cnf.class = {};
    }
    cnf.class.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;
  }, "init")
};
export {
  diagram
};
//# sourceMappingURL=classDiagram-M3E45YP4-CgPDRXoK-LD3D2IQX.js.map
