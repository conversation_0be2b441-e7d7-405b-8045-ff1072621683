{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/mermaid/dist/chunks/mermaid.core/chunk-E2GYISFI.mjs"], "sourcesContent": ["import {\n  __name\n} from \"./chunk-3XYRH5AP.mjs\";\n\n// src/diagrams/globalStyles.ts\nvar getIconStyles = /* @__PURE__ */ __name(() => `\n  /* Font Awesome icon styling - consolidated */\n  .label-icon {\n    display: inline-block;\n    height: 1em;\n    overflow: visible;\n    vertical-align: -0.125em;\n  }\n  \n  .node .label-icon path {\n    fill: currentColor;\n    stroke: revert;\n    stroke-width: revert;\n  }\n`, \"getIconStyles\");\n\nexport {\n  getIconStyles\n};\n"], "mappings": ";;;;;AAKG,IAAC,gBAAgC,OAAO,MAAM;;;;;;;;;;;;;;GAc9C,eAAe;", "names": []}