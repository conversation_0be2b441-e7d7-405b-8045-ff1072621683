{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/mermaid/dist/chunks/mermaid.core/chunk-AACKK3MU.mjs"], "sourcesContent": ["import {\n  __name\n} from \"./chunk-3XYRH5AP.mjs\";\n\n// src/utils/imperativeState.ts\nvar ImperativeState = class {\n  /**\n   * @param init - Function that creates the default state.\n   */\n  constructor(init) {\n    this.init = init;\n    this.records = this.init();\n  }\n  static {\n    __name(this, \"ImperativeState\");\n  }\n  reset() {\n    this.records = this.init();\n  }\n};\n\nexport {\n  ImperativeState\n};\n"], "mappings": ";;;;;;AAKG,IAAC,mBAAkB,KAAA,MAAM;;;;EAI1B,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,UAAU,KAAK,KAAI;EAC1B;EAIA,QAAQ;AACN,SAAK,UAAU,KAAK,KAAI;EAC1B;AACF,GALI,OAAO,IAAM,iBAAiB,GATZ;", "names": []}