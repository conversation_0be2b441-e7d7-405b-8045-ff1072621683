{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/mermaid/dist/chunks/mermaid.core/chunk-OW32GOEJ.mjs"], "sourcesContent": ["import {\n  getDiagramElement\n} from \"./chunk-BFAMUDN2.mjs\";\nimport {\n  setupViewPortForSVG\n} from \"./chunk-SKB7J2MH.mjs\";\nimport {\n  render\n} from \"./chunk-IWUHOULB.mjs\";\nimport {\n  generateId,\n  utils_default\n} from \"./chunk-55PJQP7W.mjs\";\nimport {\n  __name,\n  clear,\n  common_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-3XYRH5AP.mjs\";\n\n// src/diagrams/state/parser/stateDiagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 2], $V1 = [1, 3], $V2 = [1, 4], $V3 = [2, 4], $V4 = [1, 9], $V5 = [1, 11], $V6 = [1, 16], $V7 = [1, 17], $V8 = [1, 18], $V9 = [1, 19], $Va = [1, 33], $Vb = [1, 20], $Vc = [1, 21], $Vd = [1, 22], $Ve = [1, 23], $Vf = [1, 24], $Vg = [1, 26], $Vh = [1, 27], $Vi = [1, 28], $Vj = [1, 29], $Vk = [1, 30], $Vl = [1, 31], $Vm = [1, 32], $Vn = [1, 35], $Vo = [1, 36], $Vp = [1, 37], $Vq = [1, 38], $Vr = [1, 34], $Vs = [1, 4, 5, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 41, 45, 48, 51, 52, 53, 54, 57], $Vt = [1, 4, 5, 14, 15, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 39, 40, 41, 45, 48, 51, 52, 53, 54, 57], $Vu = [4, 5, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 41, 45, 48, 51, 52, 53, 54, 57];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SPACE\": 4, \"NL\": 5, \"SD\": 6, \"document\": 7, \"line\": 8, \"statement\": 9, \"classDefStatement\": 10, \"styleStatement\": 11, \"cssClassStatement\": 12, \"idStatement\": 13, \"DESCR\": 14, \"-->\": 15, \"HIDE_EMPTY\": 16, \"scale\": 17, \"WIDTH\": 18, \"COMPOSIT_STATE\": 19, \"STRUCT_START\": 20, \"STRUCT_STOP\": 21, \"STATE_DESCR\": 22, \"AS\": 23, \"ID\": 24, \"FORK\": 25, \"JOIN\": 26, \"CHOICE\": 27, \"CONCURRENT\": 28, \"note\": 29, \"notePosition\": 30, \"NOTE_TEXT\": 31, \"direction\": 32, \"acc_title\": 33, \"acc_title_value\": 34, \"acc_descr\": 35, \"acc_descr_value\": 36, \"acc_descr_multiline_value\": 37, \"CLICK\": 38, \"STRING\": 39, \"HREF\": 40, \"classDef\": 41, \"CLASSDEF_ID\": 42, \"CLASSDEF_STYLEOPTS\": 43, \"DEFAULT\": 44, \"style\": 45, \"STYLE_IDS\": 46, \"STYLEDEF_STYLEOPTS\": 47, \"class\": 48, \"CLASSENTITY_IDS\": 49, \"STYLECLASS\": 50, \"direction_tb\": 51, \"direction_bt\": 52, \"direction_rl\": 53, \"direction_lr\": 54, \"eol\": 55, \";\": 56, \"EDGE_STATE\": 57, \"STYLE_SEPARATOR\": 58, \"left_of\": 59, \"right_of\": 60, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SPACE\", 5: \"NL\", 6: \"SD\", 14: \"DESCR\", 15: \"-->\", 16: \"HIDE_EMPTY\", 17: \"scale\", 18: \"WIDTH\", 19: \"COMPOSIT_STATE\", 20: \"STRUCT_START\", 21: \"STRUCT_STOP\", 22: \"STATE_DESCR\", 23: \"AS\", 24: \"ID\", 25: \"FORK\", 26: \"JOIN\", 27: \"CHOICE\", 28: \"CONCURRENT\", 29: \"note\", 31: \"NOTE_TEXT\", 33: \"acc_title\", 34: \"acc_title_value\", 35: \"acc_descr\", 36: \"acc_descr_value\", 37: \"acc_descr_multiline_value\", 38: \"CLICK\", 39: \"STRING\", 40: \"HREF\", 41: \"classDef\", 42: \"CLASSDEF_ID\", 43: \"CLASSDEF_STYLEOPTS\", 44: \"DEFAULT\", 45: \"style\", 46: \"STYLE_IDS\", 47: \"STYLEDEF_STYLEOPTS\", 48: \"class\", 49: \"CLASSENTITY_IDS\", 50: \"STYLECLASS\", 51: \"direction_tb\", 52: \"direction_bt\", 53: \"direction_rl\", 54: \"direction_lr\", 56: \";\", 57: \"EDGE_STATE\", 58: \"STYLE_SEPARATOR\", 59: \"left_of\", 60: \"right_of\" },\n    productions_: [0, [3, 2], [3, 2], [3, 2], [7, 0], [7, 2], [8, 2], [8, 1], [8, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 2], [9, 3], [9, 4], [9, 1], [9, 2], [9, 1], [9, 4], [9, 3], [9, 6], [9, 1], [9, 1], [9, 1], [9, 1], [9, 4], [9, 4], [9, 1], [9, 2], [9, 2], [9, 1], [9, 5], [9, 5], [10, 3], [10, 3], [11, 3], [12, 3], [32, 1], [32, 1], [32, 1], [32, 1], [55, 1], [55, 1], [13, 1], [13, 1], [13, 3], [13, 3], [30, 1], [30, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.setRootDoc($$[$0]);\n          return $$[$0];\n          break;\n        case 4:\n          this.$ = [];\n          break;\n        case 5:\n          if ($$[$0] != \"nl\") {\n            $$[$0 - 1].push($$[$0]);\n            this.$ = $$[$0 - 1];\n          }\n          break;\n        case 6:\n        case 7:\n          this.$ = $$[$0];\n          break;\n        case 8:\n          this.$ = \"nl\";\n          break;\n        case 12:\n          this.$ = $$[$0];\n          break;\n        case 13:\n          const stateStmt = $$[$0 - 1];\n          stateStmt.description = yy.trimColon($$[$0]);\n          this.$ = stateStmt;\n          break;\n        case 14:\n          this.$ = { stmt: \"relation\", state1: $$[$0 - 2], state2: $$[$0] };\n          break;\n        case 15:\n          const relDescription = yy.trimColon($$[$0]);\n          this.$ = { stmt: \"relation\", state1: $$[$0 - 3], state2: $$[$0 - 1], description: relDescription };\n          break;\n        case 19:\n          this.$ = { stmt: \"state\", id: $$[$0 - 3], type: \"default\", description: \"\", doc: $$[$0 - 1] };\n          break;\n        case 20:\n          var id = $$[$0];\n          var description = $$[$0 - 2].trim();\n          if ($$[$0].match(\":\")) {\n            var parts = $$[$0].split(\":\");\n            id = parts[0];\n            description = [description, parts[1]];\n          }\n          this.$ = { stmt: \"state\", id, type: \"default\", description };\n          break;\n        case 21:\n          this.$ = { stmt: \"state\", id: $$[$0 - 3], type: \"default\", description: $$[$0 - 5], doc: $$[$0 - 1] };\n          break;\n        case 22:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"fork\" };\n          break;\n        case 23:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"join\" };\n          break;\n        case 24:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"choice\" };\n          break;\n        case 25:\n          this.$ = { stmt: \"state\", id: yy.getDividerId(), type: \"divider\" };\n          break;\n        case 26:\n          this.$ = { stmt: \"state\", id: $$[$0 - 1].trim(), note: { position: $$[$0 - 2].trim(), text: $$[$0].trim() } };\n          break;\n        case 29:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 30:\n        case 31:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 32:\n          this.$ = {\n            stmt: \"click\",\n            id: $$[$0 - 3],\n            url: $$[$0 - 2],\n            tooltip: $$[$0 - 1]\n          };\n          break;\n        case 33:\n          this.$ = {\n            stmt: \"click\",\n            id: $$[$0 - 3],\n            url: $$[$0 - 1],\n            tooltip: \"\"\n          };\n          break;\n        case 34:\n        case 35:\n          this.$ = { stmt: \"classDef\", id: $$[$0 - 1].trim(), classes: $$[$0].trim() };\n          break;\n        case 36:\n          this.$ = { stmt: \"style\", id: $$[$0 - 1].trim(), styleClass: $$[$0].trim() };\n          break;\n        case 37:\n          this.$ = { stmt: \"applyClass\", id: $$[$0 - 1].trim(), styleClass: $$[$0].trim() };\n          break;\n        case 38:\n          yy.setDirection(\"TB\");\n          this.$ = { stmt: \"dir\", value: \"TB\" };\n          break;\n        case 39:\n          yy.setDirection(\"BT\");\n          this.$ = { stmt: \"dir\", value: \"BT\" };\n          break;\n        case 40:\n          yy.setDirection(\"RL\");\n          this.$ = { stmt: \"dir\", value: \"RL\" };\n          break;\n        case 41:\n          yy.setDirection(\"LR\");\n          this.$ = { stmt: \"dir\", value: \"LR\" };\n          break;\n        case 44:\n        case 45:\n          this.$ = { stmt: \"state\", id: $$[$0].trim(), type: \"default\", description: \"\" };\n          break;\n        case 46:\n          this.$ = { stmt: \"state\", id: $$[$0 - 2].trim(), classes: [$$[$0].trim()], type: \"default\", description: \"\" };\n          break;\n        case 47:\n          this.$ = { stmt: \"state\", id: $$[$0 - 2].trim(), classes: [$$[$0].trim()], type: \"default\", description: \"\" };\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: $V0, 5: $V1, 6: $V2 }, { 1: [3] }, { 3: 5, 4: $V0, 5: $V1, 6: $V2 }, { 3: 6, 4: $V0, 5: $V1, 6: $V2 }, o([1, 4, 5, 16, 17, 19, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 41, 45, 48, 51, 52, 53, 54, 57], $V3, { 7: 7 }), { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 3], 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 41: $Vk, 45: $Vl, 48: $Vm, 51: $Vn, 52: $Vo, 53: $Vp, 54: $Vq, 57: $Vr }, o($Vs, [2, 5]), { 9: 39, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 41: $Vk, 45: $Vl, 48: $Vm, 51: $Vn, 52: $Vo, 53: $Vp, 54: $Vq, 57: $Vr }, o($Vs, [2, 7]), o($Vs, [2, 8]), o($Vs, [2, 9]), o($Vs, [2, 10]), o($Vs, [2, 11]), o($Vs, [2, 12], { 14: [1, 40], 15: [1, 41] }), o($Vs, [2, 16]), { 18: [1, 42] }, o($Vs, [2, 18], { 20: [1, 43] }), { 23: [1, 44] }, o($Vs, [2, 22]), o($Vs, [2, 23]), o($Vs, [2, 24]), o($Vs, [2, 25]), { 30: 45, 31: [1, 46], 59: [1, 47], 60: [1, 48] }, o($Vs, [2, 28]), { 34: [1, 49] }, { 36: [1, 50] }, o($Vs, [2, 31]), { 13: 51, 24: $Va, 57: $Vr }, { 42: [1, 52], 44: [1, 53] }, { 46: [1, 54] }, { 49: [1, 55] }, o($Vt, [2, 44], { 58: [1, 56] }), o($Vt, [2, 45], { 58: [1, 57] }), o($Vs, [2, 38]), o($Vs, [2, 39]), o($Vs, [2, 40]), o($Vs, [2, 41]), o($Vs, [2, 6]), o($Vs, [2, 13]), { 13: 58, 24: $Va, 57: $Vr }, o($Vs, [2, 17]), o($Vu, $V3, { 7: 59 }), { 24: [1, 60] }, { 24: [1, 61] }, { 23: [1, 62] }, { 24: [2, 48] }, { 24: [2, 49] }, o($Vs, [2, 29]), o($Vs, [2, 30]), { 39: [1, 63], 40: [1, 64] }, { 43: [1, 65] }, { 43: [1, 66] }, { 47: [1, 67] }, { 50: [1, 68] }, { 24: [1, 69] }, { 24: [1, 70] }, o($Vs, [2, 14], { 14: [1, 71] }), { 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 21: [1, 72], 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 41: $Vk, 45: $Vl, 48: $Vm, 51: $Vn, 52: $Vo, 53: $Vp, 54: $Vq, 57: $Vr }, o($Vs, [2, 20], { 20: [1, 73] }), { 31: [1, 74] }, { 24: [1, 75] }, { 39: [1, 76] }, { 39: [1, 77] }, o($Vs, [2, 34]), o($Vs, [2, 35]), o($Vs, [2, 36]), o($Vs, [2, 37]), o($Vt, [2, 46]), o($Vt, [2, 47]), o($Vs, [2, 15]), o($Vs, [2, 19]), o($Vu, $V3, { 7: 78 }), o($Vs, [2, 26]), o($Vs, [2, 27]), { 5: [1, 79] }, { 5: [1, 80] }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 21: [1, 81], 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 41: $Vk, 45: $Vl, 48: $Vm, 51: $Vn, 52: $Vo, 53: $Vp, 54: $Vq, 57: $Vr }, o($Vs, [2, 32]), o($Vs, [2, 33]), o($Vs, [2, 21])],\n    defaultActions: { 5: [2, 1], 6: [2, 2], 47: [2, 48], 48: [2, 49] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 38;\n            break;\n          case 1:\n            return 40;\n            break;\n          case 2:\n            return 39;\n            break;\n          case 3:\n            return 44;\n            break;\n          case 4:\n            return 51;\n            break;\n          case 5:\n            return 52;\n            break;\n          case 6:\n            return 53;\n            break;\n          case 7:\n            return 54;\n            break;\n          case 8:\n            break;\n          case 9:\n            {\n            }\n            break;\n          case 10:\n            return 5;\n            break;\n          case 11:\n            break;\n          case 12:\n            break;\n          case 13:\n            break;\n          case 14:\n            break;\n          case 15:\n            this.pushState(\"SCALE\");\n            return 17;\n            break;\n          case 16:\n            return 18;\n            break;\n          case 17:\n            this.popState();\n            break;\n          case 18:\n            this.begin(\"acc_title\");\n            return 33;\n            break;\n          case 19:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 20:\n            this.begin(\"acc_descr\");\n            return 35;\n            break;\n          case 21:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 22:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 23:\n            this.popState();\n            break;\n          case 24:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 25:\n            this.pushState(\"CLASSDEF\");\n            return 41;\n            break;\n          case 26:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return \"DEFAULT_CLASSDEF_ID\";\n            break;\n          case 27:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return 42;\n            break;\n          case 28:\n            this.popState();\n            return 43;\n            break;\n          case 29:\n            this.pushState(\"CLASS\");\n            return 48;\n            break;\n          case 30:\n            this.popState();\n            this.pushState(\"CLASS_STYLE\");\n            return 49;\n            break;\n          case 31:\n            this.popState();\n            return 50;\n            break;\n          case 32:\n            this.pushState(\"STYLE\");\n            return 45;\n            break;\n          case 33:\n            this.popState();\n            this.pushState(\"STYLEDEF_STYLES\");\n            return 46;\n            break;\n          case 34:\n            this.popState();\n            return 47;\n            break;\n          case 35:\n            this.pushState(\"SCALE\");\n            return 17;\n            break;\n          case 36:\n            return 18;\n            break;\n          case 37:\n            this.popState();\n            break;\n          case 38:\n            this.pushState(\"STATE\");\n            break;\n          case 39:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 25;\n            break;\n          case 40:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 26;\n            break;\n          case 41:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -10).trim();\n            return 27;\n            break;\n          case 42:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 25;\n            break;\n          case 43:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 26;\n            break;\n          case 44:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -10).trim();\n            return 27;\n            break;\n          case 45:\n            return 51;\n            break;\n          case 46:\n            return 52;\n            break;\n          case 47:\n            return 53;\n            break;\n          case 48:\n            return 54;\n            break;\n          case 49:\n            this.pushState(\"STATE_STRING\");\n            break;\n          case 50:\n            this.pushState(\"STATE_ID\");\n            return \"AS\";\n            break;\n          case 51:\n            this.popState();\n            return \"ID\";\n            break;\n          case 52:\n            this.popState();\n            break;\n          case 53:\n            return \"STATE_DESCR\";\n            break;\n          case 54:\n            return 19;\n            break;\n          case 55:\n            this.popState();\n            break;\n          case 56:\n            this.popState();\n            this.pushState(\"struct\");\n            return 20;\n            break;\n          case 57:\n            break;\n          case 58:\n            this.popState();\n            return 21;\n            break;\n          case 59:\n            break;\n          case 60:\n            this.begin(\"NOTE\");\n            return 29;\n            break;\n          case 61:\n            this.popState();\n            this.pushState(\"NOTE_ID\");\n            return 59;\n            break;\n          case 62:\n            this.popState();\n            this.pushState(\"NOTE_ID\");\n            return 60;\n            break;\n          case 63:\n            this.popState();\n            this.pushState(\"FLOATING_NOTE\");\n            break;\n          case 64:\n            this.popState();\n            this.pushState(\"FLOATING_NOTE_ID\");\n            return \"AS\";\n            break;\n          case 65:\n            break;\n          case 66:\n            return \"NOTE_TEXT\";\n            break;\n          case 67:\n            this.popState();\n            return \"ID\";\n            break;\n          case 68:\n            this.popState();\n            this.pushState(\"NOTE_TEXT\");\n            return 24;\n            break;\n          case 69:\n            this.popState();\n            yy_.yytext = yy_.yytext.substr(2).trim();\n            return 31;\n            break;\n          case 70:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 31;\n            break;\n          case 71:\n            return 6;\n            break;\n          case 72:\n            return 6;\n            break;\n          case 73:\n            return 16;\n            break;\n          case 74:\n            return 57;\n            break;\n          case 75:\n            return 24;\n            break;\n          case 76:\n            yy_.yytext = yy_.yytext.trim();\n            return 14;\n            break;\n          case 77:\n            return 15;\n            break;\n          case 78:\n            return 28;\n            break;\n          case 79:\n            return 58;\n            break;\n          case 80:\n            return 5;\n            break;\n          case 81:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:click\\b)/i, /^(?:href\\b)/i, /^(?:\"[^\"]*\")/i, /^(?:default\\b)/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:[\\s]+)/i, /^(?:((?!\\n)\\s)+)/i, /^(?:#[^\\n]*)/i, /^(?:%[^\\n]*)/i, /^(?:scale\\s+)/i, /^(?:\\d+)/i, /^(?:\\s+width\\b)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:classDef\\s+)/i, /^(?:DEFAULT\\s+)/i, /^(?:\\w+\\s+)/i, /^(?:[^\\n]*)/i, /^(?:class\\s+)/i, /^(?:(\\w+)+((,\\s*\\w+)*))/i, /^(?:[^\\n]*)/i, /^(?:style\\s+)/i, /^(?:[\\w,]+\\s+)/i, /^(?:[^\\n]*)/i, /^(?:scale\\s+)/i, /^(?:\\d+)/i, /^(?:\\s+width\\b)/i, /^(?:state\\s+)/i, /^(?:.*<<fork>>)/i, /^(?:.*<<join>>)/i, /^(?:.*<<choice>>)/i, /^(?:.*\\[\\[fork\\]\\])/i, /^(?:.*\\[\\[join\\]\\])/i, /^(?:.*\\[\\[choice\\]\\])/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:[\"])/i, /^(?:\\s*as\\s+)/i, /^(?:[^\\n\\{]*)/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[^\\n\\s\\{]+)/i, /^(?:\\n)/i, /^(?:\\{)/i, /^(?:%%(?!\\{)[^\\n]*)/i, /^(?:\\})/i, /^(?:[\\n])/i, /^(?:note\\s+)/i, /^(?:left of\\b)/i, /^(?:right of\\b)/i, /^(?:\")/i, /^(?:\\s*as\\s*)/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[^\\n]*)/i, /^(?:\\s*[^:\\n\\s\\-]+)/i, /^(?:\\s*:[^:\\n;]+)/i, /^(?:[\\s\\S]*?end note\\b)/i, /^(?:stateDiagram\\s+)/i, /^(?:stateDiagram-v2\\s+)/i, /^(?:hide empty description\\b)/i, /^(?:\\[\\*\\])/i, /^(?:[^:\\n\\s\\-\\{]+)/i, /^(?:\\s*:[^:\\n;]+)/i, /^(?:-->)/i, /^(?:--)/i, /^(?::::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"LINE\": { \"rules\": [12, 13], \"inclusive\": false }, \"struct\": { \"rules\": [12, 13, 25, 29, 32, 38, 45, 46, 47, 48, 57, 58, 59, 60, 74, 75, 76, 77, 78], \"inclusive\": false }, \"FLOATING_NOTE_ID\": { \"rules\": [67], \"inclusive\": false }, \"FLOATING_NOTE\": { \"rules\": [64, 65, 66], \"inclusive\": false }, \"NOTE_TEXT\": { \"rules\": [69, 70], \"inclusive\": false }, \"NOTE_ID\": { \"rules\": [68], \"inclusive\": false }, \"NOTE\": { \"rules\": [61, 62, 63], \"inclusive\": false }, \"STYLEDEF_STYLEOPTS\": { \"rules\": [], \"inclusive\": false }, \"STYLEDEF_STYLES\": { \"rules\": [34], \"inclusive\": false }, \"STYLE_IDS\": { \"rules\": [], \"inclusive\": false }, \"STYLE\": { \"rules\": [33], \"inclusive\": false }, \"CLASS_STYLE\": { \"rules\": [31], \"inclusive\": false }, \"CLASS\": { \"rules\": [30], \"inclusive\": false }, \"CLASSDEFID\": { \"rules\": [28], \"inclusive\": false }, \"CLASSDEF\": { \"rules\": [26, 27], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [23, 24], \"inclusive\": false }, \"acc_descr\": { \"rules\": [21], \"inclusive\": false }, \"acc_title\": { \"rules\": [19], \"inclusive\": false }, \"SCALE\": { \"rules\": [16, 17, 36, 37], \"inclusive\": false }, \"ALIAS\": { \"rules\": [], \"inclusive\": false }, \"STATE_ID\": { \"rules\": [51], \"inclusive\": false }, \"STATE_STRING\": { \"rules\": [52, 53], \"inclusive\": false }, \"FORK_STATE\": { \"rules\": [], \"inclusive\": false }, \"STATE\": { \"rules\": [12, 13, 39, 40, 41, 42, 43, 44, 49, 50, 54, 55, 56], \"inclusive\": false }, \"ID\": { \"rules\": [12, 13], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 18, 20, 22, 25, 29, 32, 35, 38, 56, 60, 71, 72, 73, 74, 75, 76, 77, 79, 80, 81], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar stateDiagram_default = parser;\n\n// src/diagrams/state/stateCommon.ts\nvar DEFAULT_DIAGRAM_DIRECTION = \"TB\";\nvar DEFAULT_NESTED_DOC_DIR = \"TB\";\nvar STMT_DIRECTION = \"dir\";\nvar STMT_STATE = \"state\";\nvar STMT_ROOT = \"root\";\nvar STMT_RELATION = \"relation\";\nvar STMT_CLASSDEF = \"classDef\";\nvar STMT_STYLEDEF = \"style\";\nvar STMT_APPLYCLASS = \"applyClass\";\nvar DEFAULT_STATE_TYPE = \"default\";\nvar DIVIDER_TYPE = \"divider\";\nvar G_EDGE_STYLE = \"fill:none\";\nvar G_EDGE_ARROWHEADSTYLE = \"fill: #333\";\nvar G_EDGE_LABELPOS = \"c\";\nvar G_EDGE_LABELTYPE = \"text\";\nvar G_EDGE_THICKNESS = \"normal\";\nvar SHAPE_STATE = \"rect\";\nvar SHAPE_STATE_WITH_DESC = \"rectWithTitle\";\nvar SHAPE_START = \"stateStart\";\nvar SHAPE_END = \"stateEnd\";\nvar SHAPE_DIVIDER = \"divider\";\nvar SHAPE_GROUP = \"roundedWithTitle\";\nvar SHAPE_NOTE = \"note\";\nvar SHAPE_NOTEGROUP = \"noteGroup\";\nvar CSS_DIAGRAM = \"statediagram\";\nvar CSS_STATE = \"state\";\nvar CSS_DIAGRAM_STATE = `${CSS_DIAGRAM}-${CSS_STATE}`;\nvar CSS_EDGE = \"transition\";\nvar CSS_NOTE = \"note\";\nvar CSS_NOTE_EDGE = \"note-edge\";\nvar CSS_EDGE_NOTE_EDGE = `${CSS_EDGE} ${CSS_NOTE_EDGE}`;\nvar CSS_DIAGRAM_NOTE = `${CSS_DIAGRAM}-${CSS_NOTE}`;\nvar CSS_CLUSTER = \"cluster\";\nvar CSS_DIAGRAM_CLUSTER = `${CSS_DIAGRAM}-${CSS_CLUSTER}`;\nvar CSS_CLUSTER_ALT = \"cluster-alt\";\nvar CSS_DIAGRAM_CLUSTER_ALT = `${CSS_DIAGRAM}-${CSS_CLUSTER_ALT}`;\nvar PARENT = \"parent\";\nvar NOTE = \"note\";\nvar DOMID_STATE = \"state\";\nvar DOMID_TYPE_SPACER = \"----\";\nvar NOTE_ID = `${DOMID_TYPE_SPACER}${NOTE}`;\nvar PARENT_ID = `${DOMID_TYPE_SPACER}${PARENT}`;\n\n// src/diagrams/state/stateRenderer-v3-unified.ts\nvar getDir = /* @__PURE__ */ __name((parsedItem, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  if (!parsedItem.doc) {\n    return defaultDir;\n  }\n  let dir = defaultDir;\n  for (const parsedItemDoc of parsedItem.doc) {\n    if (parsedItemDoc.stmt === \"dir\") {\n      dir = parsedItemDoc.value;\n    }\n  }\n  return dir;\n}, \"getDir\");\nvar getClasses = /* @__PURE__ */ __name(function(text, diagramObj) {\n  return diagramObj.db.getClasses();\n}, \"getClasses\");\nvar draw = /* @__PURE__ */ __name(async function(text, id, _version, diag) {\n  log.info(\"REF0:\");\n  log.info(\"Drawing state diagram (v2)\", id);\n  const { securityLevel, state: conf, layout } = getConfig();\n  diag.db.extract(diag.db.getRootDocV2());\n  const data4Layout = diag.db.getData();\n  const svg = getDiagramElement(id, securityLevel);\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = layout;\n  data4Layout.nodeSpacing = conf?.nodeSpacing || 50;\n  data4Layout.rankSpacing = conf?.rankSpacing || 50;\n  data4Layout.markers = [\"barb\"];\n  data4Layout.diagramId = id;\n  await render(data4Layout, svg);\n  const padding = 8;\n  try {\n    const links = typeof diag.db.getLinks === \"function\" ? diag.db.getLinks() : /* @__PURE__ */ new Map();\n    links.forEach((linkInfo, key) => {\n      const stateId = typeof key === \"string\" ? key : typeof key?.id === \"string\" ? key.id : \"\";\n      if (!stateId) {\n        log.warn(\"\\u26A0\\uFE0F Invalid or missing stateId from key:\", JSON.stringify(key));\n        return;\n      }\n      const allNodes = svg.node()?.querySelectorAll(\"g\");\n      let matchedElem;\n      allNodes?.forEach((g) => {\n        const text2 = g.textContent?.trim();\n        if (text2 === stateId) {\n          matchedElem = g;\n        }\n      });\n      if (!matchedElem) {\n        log.warn(\"\\u26A0\\uFE0F Could not find node matching text:\", stateId);\n        return;\n      }\n      const parent = matchedElem.parentNode;\n      if (!parent) {\n        log.warn(\"\\u26A0\\uFE0F Node has no parent, cannot wrap:\", stateId);\n        return;\n      }\n      const a = document.createElementNS(\"http://www.w3.org/2000/svg\", \"a\");\n      const cleanedUrl = linkInfo.url.replace(/^\"+|\"+$/g, \"\");\n      a.setAttributeNS(\"http://www.w3.org/1999/xlink\", \"xlink:href\", cleanedUrl);\n      a.setAttribute(\"target\", \"_blank\");\n      if (linkInfo.tooltip) {\n        const tooltip = linkInfo.tooltip.replace(/^\"+|\"+$/g, \"\");\n        a.setAttribute(\"title\", tooltip);\n      }\n      parent.replaceChild(a, matchedElem);\n      a.appendChild(matchedElem);\n      log.info(\"\\u{1F517} Wrapped node in <a> tag for:\", stateId, linkInfo.url);\n    });\n  } catch (err) {\n    log.error(\"\\u274C Error injecting clickable links:\", err);\n  }\n  utils_default.insertTitle(\n    svg,\n    \"statediagramTitleText\",\n    conf?.titleTopMargin ?? 25,\n    diag.db.getDiagramTitle()\n  );\n  setupViewPortForSVG(svg, padding, CSS_DIAGRAM, conf?.useMaxWidth ?? true);\n}, \"draw\");\nvar stateRenderer_v3_unified_default = {\n  getClasses,\n  draw,\n  getDir\n};\n\n// src/diagrams/state/dataFetcher.ts\nvar nodeDb = /* @__PURE__ */ new Map();\nvar graphItemCount = 0;\nfunction stateDomId(itemId = \"\", counter = 0, type = \"\", typeSpacer = DOMID_TYPE_SPACER) {\n  const typeStr = type !== null && type.length > 0 ? `${typeSpacer}${type}` : \"\";\n  return `${DOMID_STATE}-${itemId}${typeStr}-${counter}`;\n}\n__name(stateDomId, \"stateDomId\");\nvar setupDoc = /* @__PURE__ */ __name((parentParsedItem, doc, diagramStates, nodes, edges, altFlag, look, classes) => {\n  log.trace(\"items\", doc);\n  doc.forEach((item) => {\n    switch (item.stmt) {\n      case STMT_STATE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes, edges, altFlag, look, classes);\n        break;\n      case DEFAULT_STATE_TYPE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes, edges, altFlag, look, classes);\n        break;\n      case STMT_RELATION:\n        {\n          dataFetcher(\n            parentParsedItem,\n            item.state1,\n            diagramStates,\n            nodes,\n            edges,\n            altFlag,\n            look,\n            classes\n          );\n          dataFetcher(\n            parentParsedItem,\n            item.state2,\n            diagramStates,\n            nodes,\n            edges,\n            altFlag,\n            look,\n            classes\n          );\n          const edgeData = {\n            id: \"edge\" + graphItemCount,\n            start: item.state1.id,\n            end: item.state2.id,\n            arrowhead: \"normal\",\n            arrowTypeEnd: \"arrow_barb\",\n            style: G_EDGE_STYLE,\n            labelStyle: \"\",\n            label: common_default.sanitizeText(item.description ?? \"\", getConfig()),\n            arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n            labelpos: G_EDGE_LABELPOS,\n            labelType: G_EDGE_LABELTYPE,\n            thickness: G_EDGE_THICKNESS,\n            classes: CSS_EDGE,\n            look\n          };\n          edges.push(edgeData);\n          graphItemCount++;\n        }\n        break;\n    }\n  });\n}, \"setupDoc\");\nvar getDir2 = /* @__PURE__ */ __name((parsedItem, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  let dir = defaultDir;\n  if (parsedItem.doc) {\n    for (const parsedItemDoc of parsedItem.doc) {\n      if (parsedItemDoc.stmt === \"dir\") {\n        dir = parsedItemDoc.value;\n      }\n    }\n  }\n  return dir;\n}, \"getDir\");\nfunction insertOrUpdateNode(nodes, nodeData, classes) {\n  if (!nodeData.id || nodeData.id === \"</join></fork>\" || nodeData.id === \"</choice>\") {\n    return;\n  }\n  if (nodeData.cssClasses) {\n    if (!Array.isArray(nodeData.cssCompiledStyles)) {\n      nodeData.cssCompiledStyles = [];\n    }\n    nodeData.cssClasses.split(\" \").forEach((cssClass) => {\n      const classDef = classes.get(cssClass);\n      if (classDef) {\n        nodeData.cssCompiledStyles = [...nodeData.cssCompiledStyles ?? [], ...classDef.styles];\n      }\n    });\n  }\n  const existingNodeData = nodes.find((node) => node.id === nodeData.id);\n  if (existingNodeData) {\n    Object.assign(existingNodeData, nodeData);\n  } else {\n    nodes.push(nodeData);\n  }\n}\n__name(insertOrUpdateNode, \"insertOrUpdateNode\");\nfunction getClassesFromDbInfo(dbInfoItem) {\n  return dbInfoItem?.classes?.join(\" \") ?? \"\";\n}\n__name(getClassesFromDbInfo, \"getClassesFromDbInfo\");\nfunction getStylesFromDbInfo(dbInfoItem) {\n  return dbInfoItem?.styles ?? [];\n}\n__name(getStylesFromDbInfo, \"getStylesFromDbInfo\");\nvar dataFetcher = /* @__PURE__ */ __name((parent, parsedItem, diagramStates, nodes, edges, altFlag, look, classes) => {\n  const itemId = parsedItem.id;\n  const dbState = diagramStates.get(itemId);\n  const classStr = getClassesFromDbInfo(dbState);\n  const style = getStylesFromDbInfo(dbState);\n  const config = getConfig();\n  log.info(\"dataFetcher parsedItem\", parsedItem, dbState, style);\n  if (itemId !== \"root\") {\n    let shape = SHAPE_STATE;\n    if (parsedItem.start === true) {\n      shape = SHAPE_START;\n    } else if (parsedItem.start === false) {\n      shape = SHAPE_END;\n    }\n    if (parsedItem.type !== DEFAULT_STATE_TYPE) {\n      shape = parsedItem.type;\n    }\n    if (!nodeDb.get(itemId)) {\n      nodeDb.set(itemId, {\n        id: itemId,\n        shape,\n        description: common_default.sanitizeText(itemId, config),\n        cssClasses: `${classStr} ${CSS_DIAGRAM_STATE}`,\n        cssStyles: style\n      });\n    }\n    const newNode = nodeDb.get(itemId);\n    if (parsedItem.description) {\n      if (Array.isArray(newNode.description)) {\n        newNode.shape = SHAPE_STATE_WITH_DESC;\n        newNode.description.push(parsedItem.description);\n      } else {\n        if (newNode.description?.length && newNode.description.length > 0) {\n          newNode.shape = SHAPE_STATE_WITH_DESC;\n          if (newNode.description === itemId) {\n            newNode.description = [parsedItem.description];\n          } else {\n            newNode.description = [newNode.description, parsedItem.description];\n          }\n        } else {\n          newNode.shape = SHAPE_STATE;\n          newNode.description = parsedItem.description;\n        }\n      }\n      newNode.description = common_default.sanitizeTextOrArray(newNode.description, config);\n    }\n    if (newNode.description?.length === 1 && newNode.shape === SHAPE_STATE_WITH_DESC) {\n      if (newNode.type === \"group\") {\n        newNode.shape = SHAPE_GROUP;\n      } else {\n        newNode.shape = SHAPE_STATE;\n      }\n    }\n    if (!newNode.type && parsedItem.doc) {\n      log.info(\"Setting cluster for XCX\", itemId, getDir2(parsedItem));\n      newNode.type = \"group\";\n      newNode.isGroup = true;\n      newNode.dir = getDir2(parsedItem);\n      newNode.shape = parsedItem.type === DIVIDER_TYPE ? SHAPE_DIVIDER : SHAPE_GROUP;\n      newNode.cssClasses = `${newNode.cssClasses} ${CSS_DIAGRAM_CLUSTER} ${altFlag ? CSS_DIAGRAM_CLUSTER_ALT : \"\"}`;\n    }\n    const nodeData = {\n      labelStyle: \"\",\n      shape: newNode.shape,\n      label: newNode.description,\n      cssClasses: newNode.cssClasses,\n      cssCompiledStyles: [],\n      cssStyles: newNode.cssStyles,\n      id: itemId,\n      dir: newNode.dir,\n      domId: stateDomId(itemId, graphItemCount),\n      type: newNode.type,\n      isGroup: newNode.type === \"group\",\n      padding: 8,\n      rx: 10,\n      ry: 10,\n      look\n    };\n    if (nodeData.shape === SHAPE_DIVIDER) {\n      nodeData.label = \"\";\n    }\n    if (parent && parent.id !== \"root\") {\n      log.trace(\"Setting node \", itemId, \" to be child of its parent \", parent.id);\n      nodeData.parentId = parent.id;\n    }\n    nodeData.centerLabel = true;\n    if (parsedItem.note) {\n      const noteData = {\n        labelStyle: \"\",\n        shape: SHAPE_NOTE,\n        label: parsedItem.note.text,\n        cssClasses: CSS_DIAGRAM_NOTE,\n        // useHtmlLabels: false,\n        cssStyles: [],\n        cssCompiledStyles: [],\n        id: itemId + NOTE_ID + \"-\" + graphItemCount,\n        domId: stateDomId(itemId, graphItemCount, NOTE),\n        type: newNode.type,\n        isGroup: newNode.type === \"group\",\n        padding: config.flowchart?.padding,\n        look,\n        position: parsedItem.note.position\n      };\n      const parentNodeId = itemId + PARENT_ID;\n      const groupData = {\n        labelStyle: \"\",\n        shape: SHAPE_NOTEGROUP,\n        label: parsedItem.note.text,\n        cssClasses: newNode.cssClasses,\n        cssStyles: [],\n        id: itemId + PARENT_ID,\n        domId: stateDomId(itemId, graphItemCount, PARENT),\n        type: \"group\",\n        isGroup: true,\n        padding: 16,\n        //getConfig().flowchart.padding\n        look,\n        position: parsedItem.note.position\n      };\n      graphItemCount++;\n      groupData.id = parentNodeId;\n      noteData.parentId = parentNodeId;\n      insertOrUpdateNode(nodes, groupData, classes);\n      insertOrUpdateNode(nodes, noteData, classes);\n      insertOrUpdateNode(nodes, nodeData, classes);\n      let from = itemId;\n      let to = noteData.id;\n      if (parsedItem.note.position === \"left of\") {\n        from = noteData.id;\n        to = itemId;\n      }\n      edges.push({\n        id: from + \"-\" + to,\n        start: from,\n        end: to,\n        arrowhead: \"none\",\n        arrowTypeEnd: \"\",\n        style: G_EDGE_STYLE,\n        labelStyle: \"\",\n        classes: CSS_EDGE_NOTE_EDGE,\n        arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n        labelpos: G_EDGE_LABELPOS,\n        labelType: G_EDGE_LABELTYPE,\n        thickness: G_EDGE_THICKNESS,\n        look\n      });\n    } else {\n      insertOrUpdateNode(nodes, nodeData, classes);\n    }\n  }\n  if (parsedItem.doc) {\n    log.trace(\"Adding nodes children \");\n    setupDoc(parsedItem, parsedItem.doc, diagramStates, nodes, edges, !altFlag, look, classes);\n  }\n}, \"dataFetcher\");\nvar reset = /* @__PURE__ */ __name(() => {\n  nodeDb.clear();\n  graphItemCount = 0;\n}, \"reset\");\n\n// src/diagrams/state/stateDb.ts\nvar CONSTANTS = {\n  START_NODE: \"[*]\",\n  START_TYPE: \"start\",\n  END_NODE: \"[*]\",\n  END_TYPE: \"end\",\n  COLOR_KEYWORD: \"color\",\n  FILL_KEYWORD: \"fill\",\n  BG_FILL: \"bgFill\",\n  STYLECLASS_SEP: \",\"\n};\nvar newClassesList = /* @__PURE__ */ __name(() => /* @__PURE__ */ new Map(), \"newClassesList\");\nvar newDoc = /* @__PURE__ */ __name(() => ({\n  relations: [],\n  states: /* @__PURE__ */ new Map(),\n  documents: {}\n}), \"newDoc\");\nvar clone = /* @__PURE__ */ __name((o) => JSON.parse(JSON.stringify(o)), \"clone\");\nvar StateDB = class {\n  constructor(version) {\n    this.version = version;\n    this.nodes = [];\n    this.edges = [];\n    this.rootDoc = [];\n    this.classes = newClassesList();\n    this.documents = { root: newDoc() };\n    this.currentDocument = this.documents.root;\n    this.startEndCount = 0;\n    this.dividerCnt = 0;\n    this.links = /* @__PURE__ */ new Map();\n    this.getAccTitle = getAccTitle;\n    this.setAccTitle = setAccTitle;\n    this.getAccDescription = getAccDescription;\n    this.setAccDescription = setAccDescription;\n    this.setDiagramTitle = setDiagramTitle;\n    this.getDiagramTitle = getDiagramTitle;\n    this.clear();\n    this.setRootDoc = this.setRootDoc.bind(this);\n    this.getDividerId = this.getDividerId.bind(this);\n    this.setDirection = this.setDirection.bind(this);\n    this.trimColon = this.trimColon.bind(this);\n  }\n  static {\n    __name(this, \"StateDB\");\n  }\n  static {\n    this.relationType = {\n      AGGREGATION: 0,\n      EXTENSION: 1,\n      COMPOSITION: 2,\n      DEPENDENCY: 3\n    };\n  }\n  /**\n   * Convert all of the statements (stmts) that were parsed into states and relationships.\n   * This is done because a state diagram may have nested sections,\n   * where each section is a 'document' and has its own set of statements.\n   * Ex: the section within a fork has its own statements, and incoming and outgoing statements\n   * refer to the fork as a whole (document).\n   * See the parser grammar:  the definition of a document is a document then a 'line', where a line can be a statement.\n   * This will push the statement into the list of statements for the current document.\n   */\n  extract(statements) {\n    this.clear(true);\n    for (const item of Array.isArray(statements) ? statements : statements.doc) {\n      switch (item.stmt) {\n        case STMT_STATE:\n          this.addState(item.id.trim(), item.type, item.doc, item.description, item.note);\n          break;\n        case STMT_RELATION:\n          this.addRelation(item.state1, item.state2, item.description);\n          break;\n        case STMT_CLASSDEF:\n          this.addStyleClass(item.id.trim(), item.classes);\n          break;\n        case STMT_STYLEDEF:\n          this.handleStyleDef(item);\n          break;\n        case STMT_APPLYCLASS:\n          this.setCssClass(item.id.trim(), item.styleClass);\n          break;\n        case \"click\":\n          this.addLink(item.id, item.url, item.tooltip);\n          break;\n      }\n    }\n    const diagramStates = this.getStates();\n    const config = getConfig();\n    reset();\n    dataFetcher(\n      void 0,\n      this.getRootDocV2(),\n      diagramStates,\n      this.nodes,\n      this.edges,\n      true,\n      config.look,\n      this.classes\n    );\n    for (const node of this.nodes) {\n      if (!Array.isArray(node.label)) {\n        continue;\n      }\n      node.description = node.label.slice(1);\n      if (node.isGroup && node.description.length > 0) {\n        throw new Error(\n          `Group nodes can only have label. Remove the additional description for node [${node.id}]`\n        );\n      }\n      node.label = node.label[0];\n    }\n  }\n  handleStyleDef(item) {\n    const ids = item.id.trim().split(\",\");\n    const styles = item.styleClass.split(\",\");\n    for (const id of ids) {\n      let state = this.getState(id);\n      if (!state) {\n        const trimmedId = id.trim();\n        this.addState(trimmedId);\n        state = this.getState(trimmedId);\n      }\n      if (state) {\n        state.styles = styles.map((s) => s.replace(/;/g, \"\")?.trim());\n      }\n    }\n  }\n  setRootDoc(o) {\n    log.info(\"Setting root doc\", o);\n    this.rootDoc = o;\n    if (this.version === 1) {\n      this.extract(o);\n    } else {\n      this.extract(this.getRootDocV2());\n    }\n  }\n  docTranslator(parent, node, first) {\n    if (node.stmt === STMT_RELATION) {\n      this.docTranslator(parent, node.state1, true);\n      this.docTranslator(parent, node.state2, false);\n      return;\n    }\n    if (node.stmt === STMT_STATE) {\n      if (node.id === CONSTANTS.START_NODE) {\n        node.id = parent.id + (first ? \"_start\" : \"_end\");\n        node.start = first;\n      } else {\n        node.id = node.id.trim();\n      }\n    }\n    if (node.stmt !== STMT_ROOT && node.stmt !== STMT_STATE || !node.doc) {\n      return;\n    }\n    const doc = [];\n    let currentDoc = [];\n    for (const stmt of node.doc) {\n      if (stmt.type === DIVIDER_TYPE) {\n        const newNode = clone(stmt);\n        newNode.doc = clone(currentDoc);\n        doc.push(newNode);\n        currentDoc = [];\n      } else {\n        currentDoc.push(stmt);\n      }\n    }\n    if (doc.length > 0 && currentDoc.length > 0) {\n      const newNode = {\n        stmt: STMT_STATE,\n        id: generateId(),\n        type: \"divider\",\n        doc: clone(currentDoc)\n      };\n      doc.push(clone(newNode));\n      node.doc = doc;\n    }\n    node.doc.forEach((docNode) => this.docTranslator(node, docNode, true));\n  }\n  getRootDocV2() {\n    this.docTranslator(\n      { id: STMT_ROOT, stmt: STMT_ROOT },\n      { id: STMT_ROOT, stmt: STMT_ROOT, doc: this.rootDoc },\n      true\n    );\n    return { id: STMT_ROOT, doc: this.rootDoc };\n  }\n  /**\n   * Function called by parser when a node definition has been found.\n   *\n   * @param descr - description for the state. Can be a string or a list or strings\n   * @param classes - class styles to apply to this state. Can be a string (1 style) or an array of styles. If it's just 1 class, convert it to an array of that 1 class.\n   * @param styles - styles to apply to this state. Can be a string (1 style) or an array of styles. If it's just 1 style, convert it to an array of that 1 style.\n   * @param textStyles - text styles to apply to this state. Can be a string (1 text test) or an array of text styles. If it's just 1 text style, convert it to an array of that 1 text style.\n   */\n  addState(id, type = DEFAULT_STATE_TYPE, doc = void 0, descr = void 0, note = void 0, classes = void 0, styles = void 0, textStyles = void 0) {\n    const trimmedId = id?.trim();\n    if (!this.currentDocument.states.has(trimmedId)) {\n      log.info(\"Adding state \", trimmedId, descr);\n      this.currentDocument.states.set(trimmedId, {\n        stmt: STMT_STATE,\n        id: trimmedId,\n        descriptions: [],\n        type,\n        doc,\n        note,\n        classes: [],\n        styles: [],\n        textStyles: []\n      });\n    } else {\n      const state = this.currentDocument.states.get(trimmedId);\n      if (!state) {\n        throw new Error(`State not found: ${trimmedId}`);\n      }\n      if (!state.doc) {\n        state.doc = doc;\n      }\n      if (!state.type) {\n        state.type = type;\n      }\n    }\n    if (descr) {\n      log.info(\"Setting state description\", trimmedId, descr);\n      const descriptions = Array.isArray(descr) ? descr : [descr];\n      descriptions.forEach((des) => this.addDescription(trimmedId, des.trim()));\n    }\n    if (note) {\n      const doc2 = this.currentDocument.states.get(trimmedId);\n      if (!doc2) {\n        throw new Error(`State not found: ${trimmedId}`);\n      }\n      doc2.note = note;\n      doc2.note.text = common_default.sanitizeText(doc2.note.text, getConfig());\n    }\n    if (classes) {\n      log.info(\"Setting state classes\", trimmedId, classes);\n      const classesList = Array.isArray(classes) ? classes : [classes];\n      classesList.forEach((cssClass) => this.setCssClass(trimmedId, cssClass.trim()));\n    }\n    if (styles) {\n      log.info(\"Setting state styles\", trimmedId, styles);\n      const stylesList = Array.isArray(styles) ? styles : [styles];\n      stylesList.forEach((style) => this.setStyle(trimmedId, style.trim()));\n    }\n    if (textStyles) {\n      log.info(\"Setting state styles\", trimmedId, styles);\n      const textStylesList = Array.isArray(textStyles) ? textStyles : [textStyles];\n      textStylesList.forEach((textStyle) => this.setTextStyle(trimmedId, textStyle.trim()));\n    }\n  }\n  clear(saveCommon) {\n    this.nodes = [];\n    this.edges = [];\n    this.documents = { root: newDoc() };\n    this.currentDocument = this.documents.root;\n    this.startEndCount = 0;\n    this.classes = newClassesList();\n    if (!saveCommon) {\n      this.links = /* @__PURE__ */ new Map();\n      clear();\n    }\n  }\n  getState(id) {\n    return this.currentDocument.states.get(id);\n  }\n  getStates() {\n    return this.currentDocument.states;\n  }\n  logDocuments() {\n    log.info(\"Documents = \", this.documents);\n  }\n  getRelations() {\n    return this.currentDocument.relations;\n  }\n  /**\n   * Adds a clickable link to a state.\n   */\n  addLink(stateId, url, tooltip) {\n    this.links.set(stateId, { url, tooltip });\n    log.warn(\"Adding link\", stateId, url, tooltip);\n  }\n  /**\n   * Get all registered links.\n   */\n  getLinks() {\n    return this.links;\n  }\n  /**\n   * If the id is a start node ( [*] ), then return a new id constructed from\n   * the start node name and the current start node count.\n   * else return the given id\n   */\n  startIdIfNeeded(id = \"\") {\n    if (id === CONSTANTS.START_NODE) {\n      this.startEndCount++;\n      return `${CONSTANTS.START_TYPE}${this.startEndCount}`;\n    }\n    return id;\n  }\n  /**\n   * If the id is a start node ( [*] ), then return the start type ('start')\n   * else return the given type\n   */\n  startTypeIfNeeded(id = \"\", type = DEFAULT_STATE_TYPE) {\n    return id === CONSTANTS.START_NODE ? CONSTANTS.START_TYPE : type;\n  }\n  /**\n   * If the id is an end node ( [*] ), then return a new id constructed from\n   * the end node name and the current start_end node count.\n   * else return the given id\n   */\n  endIdIfNeeded(id = \"\") {\n    if (id === CONSTANTS.END_NODE) {\n      this.startEndCount++;\n      return `${CONSTANTS.END_TYPE}${this.startEndCount}`;\n    }\n    return id;\n  }\n  /**\n   * If the id is an end node ( [*] ), then return the end type\n   * else return the given type\n   *\n   */\n  endTypeIfNeeded(id = \"\", type = DEFAULT_STATE_TYPE) {\n    return id === CONSTANTS.END_NODE ? CONSTANTS.END_TYPE : type;\n  }\n  addRelationObjs(item1, item2, relationTitle = \"\") {\n    const id1 = this.startIdIfNeeded(item1.id.trim());\n    const type1 = this.startTypeIfNeeded(item1.id.trim(), item1.type);\n    const id2 = this.startIdIfNeeded(item2.id.trim());\n    const type2 = this.startTypeIfNeeded(item2.id.trim(), item2.type);\n    this.addState(\n      id1,\n      type1,\n      item1.doc,\n      item1.description,\n      item1.note,\n      item1.classes,\n      item1.styles,\n      item1.textStyles\n    );\n    this.addState(\n      id2,\n      type2,\n      item2.doc,\n      item2.description,\n      item2.note,\n      item2.classes,\n      item2.styles,\n      item2.textStyles\n    );\n    this.currentDocument.relations.push({\n      id1,\n      id2,\n      relationTitle: common_default.sanitizeText(relationTitle, getConfig())\n    });\n  }\n  /**\n   * Add a relation between two items.  The items may be full objects or just the string id of a state.\n   */\n  addRelation(item1, item2, title) {\n    if (typeof item1 === \"object\" && typeof item2 === \"object\") {\n      this.addRelationObjs(item1, item2, title);\n    } else if (typeof item1 === \"string\" && typeof item2 === \"string\") {\n      const id1 = this.startIdIfNeeded(item1.trim());\n      const type1 = this.startTypeIfNeeded(item1);\n      const id2 = this.endIdIfNeeded(item2.trim());\n      const type2 = this.endTypeIfNeeded(item2);\n      this.addState(id1, type1);\n      this.addState(id2, type2);\n      this.currentDocument.relations.push({\n        id1,\n        id2,\n        relationTitle: title ? common_default.sanitizeText(title, getConfig()) : void 0\n      });\n    }\n  }\n  addDescription(id, descr) {\n    const theState = this.currentDocument.states.get(id);\n    const _descr = descr.startsWith(\":\") ? descr.replace(\":\", \"\").trim() : descr;\n    theState?.descriptions?.push(common_default.sanitizeText(_descr, getConfig()));\n  }\n  cleanupLabel(label) {\n    return label.startsWith(\":\") ? label.slice(2).trim() : label.trim();\n  }\n  getDividerId() {\n    this.dividerCnt++;\n    return `divider-id-${this.dividerCnt}`;\n  }\n  /**\n   * Called when the parser comes across a (style) class definition\n   * @example classDef my-style fill:#f96;\n   *\n   * @param id - the id of this (style) class\n   * @param styleAttributes - the string with 1 or more style attributes (each separated by a comma)\n   */\n  addStyleClass(id, styleAttributes = \"\") {\n    if (!this.classes.has(id)) {\n      this.classes.set(id, { id, styles: [], textStyles: [] });\n    }\n    const foundClass = this.classes.get(id);\n    if (styleAttributes && foundClass) {\n      styleAttributes.split(CONSTANTS.STYLECLASS_SEP).forEach((attrib) => {\n        const fixedAttrib = attrib.replace(/([^;]*);/, \"$1\").trim();\n        if (RegExp(CONSTANTS.COLOR_KEYWORD).exec(attrib)) {\n          const newStyle1 = fixedAttrib.replace(CONSTANTS.FILL_KEYWORD, CONSTANTS.BG_FILL);\n          const newStyle2 = newStyle1.replace(CONSTANTS.COLOR_KEYWORD, CONSTANTS.FILL_KEYWORD);\n          foundClass.textStyles.push(newStyle2);\n        }\n        foundClass.styles.push(fixedAttrib);\n      });\n    }\n  }\n  getClasses() {\n    return this.classes;\n  }\n  /**\n   * Add a (style) class or css class to a state with the given id.\n   * If the state isn't already in the list of known states, add it.\n   * Might be called by parser when a style class or CSS class should be applied to a state\n   *\n   * @param itemIds - The id or a list of ids of the item(s) to apply the css class to\n   * @param cssClassName - CSS class name\n   */\n  setCssClass(itemIds, cssClassName) {\n    itemIds.split(\",\").forEach((id) => {\n      let foundState = this.getState(id);\n      if (!foundState) {\n        const trimmedId = id.trim();\n        this.addState(trimmedId);\n        foundState = this.getState(trimmedId);\n      }\n      foundState?.classes?.push(cssClassName);\n    });\n  }\n  /**\n   * Add a style to a state with the given id.\n   * @example style stateId fill:#f9f,stroke:#333,stroke-width:4px\n   *   where 'style' is the keyword\n   *   stateId is the id of a state\n   *   the rest of the string is the styleText (all of the attributes to be applied to the state)\n   *\n   * @param itemId - The id of item to apply the style to\n   * @param styleText - the text of the attributes for the style\n   */\n  setStyle(itemId, styleText) {\n    this.getState(itemId)?.styles?.push(styleText);\n  }\n  /**\n   * Add a text style to a state with the given id\n   *\n   * @param itemId - The id of item to apply the css class to\n   * @param cssClassName - CSS class name\n   */\n  setTextStyle(itemId, cssClassName) {\n    this.getState(itemId)?.textStyles?.push(cssClassName);\n  }\n  /**\n   * Finds the direction statement in the root document.\n   * @returns the direction statement if present\n   */\n  getDirectionStatement() {\n    return this.rootDoc.find((doc) => doc.stmt === STMT_DIRECTION);\n  }\n  getDirection() {\n    return this.getDirectionStatement()?.value ?? DEFAULT_DIAGRAM_DIRECTION;\n  }\n  setDirection(dir) {\n    const doc = this.getDirectionStatement();\n    if (doc) {\n      doc.value = dir;\n    } else {\n      this.rootDoc.unshift({ stmt: STMT_DIRECTION, value: dir });\n    }\n  }\n  trimColon(str) {\n    return str.startsWith(\":\") ? str.slice(1).trim() : str.trim();\n  }\n  getData() {\n    const config = getConfig();\n    return {\n      nodes: this.nodes,\n      edges: this.edges,\n      other: {},\n      config,\n      direction: getDir(this.getRootDocV2())\n    };\n  }\n  getConfig() {\n    return getConfig().state;\n  }\n};\n\n// src/diagrams/state/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `\ndefs #statediagram-barbEnd {\n    fill: ${options.transitionColor};\n    stroke: ${options.transitionColor};\n  }\ng.stateGroup text {\n  fill: ${options.nodeBorder};\n  stroke: none;\n  font-size: 10px;\n}\ng.stateGroup text {\n  fill: ${options.textColor};\n  stroke: none;\n  font-size: 10px;\n\n}\ng.stateGroup .state-title {\n  font-weight: bolder;\n  fill: ${options.stateLabelColor};\n}\n\ng.stateGroup rect {\n  fill: ${options.mainBkg};\n  stroke: ${options.nodeBorder};\n}\n\ng.stateGroup line {\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.transition {\n  stroke: ${options.transitionColor};\n  stroke-width: 1;\n  fill: none;\n}\n\n.stateGroup .composit {\n  fill: ${options.background};\n  border-bottom: 1px\n}\n\n.stateGroup .alt-composit {\n  fill: #e0e0e0;\n  border-bottom: 1px\n}\n\n.state-note {\n  stroke: ${options.noteBorderColor};\n  fill: ${options.noteBkgColor};\n\n  text {\n    fill: ${options.noteTextColor};\n    stroke: none;\n    font-size: 10px;\n  }\n}\n\n.stateLabel .box {\n  stroke: none;\n  stroke-width: 0;\n  fill: ${options.mainBkg};\n  opacity: 0.5;\n}\n\n.edgeLabel .label rect {\n  fill: ${options.labelBackgroundColor};\n  opacity: 0.5;\n}\n.edgeLabel {\n  background-color: ${options.edgeLabelBackground};\n  p {\n    background-color: ${options.edgeLabelBackground};\n  }\n  rect {\n    opacity: 0.5;\n    background-color: ${options.edgeLabelBackground};\n    fill: ${options.edgeLabelBackground};\n  }\n  text-align: center;\n}\n.edgeLabel .label text {\n  fill: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n.label div .edgeLabel {\n  color: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n\n.stateLabel text {\n  fill: ${options.stateLabelColor};\n  font-size: 10px;\n  font-weight: bold;\n}\n\n.node circle.state-start {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node .fork-join {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node circle.state-end {\n  fill: ${options.innerEndBackground};\n  stroke: ${options.background};\n  stroke-width: 1.5\n}\n.end-state-inner {\n  fill: ${options.compositeBackground || options.background};\n  // stroke: ${options.background};\n  stroke-width: 1.5\n}\n\n.node rect {\n  fill: ${options.stateBkg || options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n.node polygon {\n  fill: ${options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};;\n  stroke-width: 1px;\n}\n#statediagram-barbEnd {\n  fill: ${options.lineColor};\n}\n\n.statediagram-cluster rect {\n  fill: ${options.compositeTitleBackground};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n\n.cluster-label, .nodeLabel {\n  color: ${options.stateLabelColor};\n  // line-height: 1;\n}\n\n.statediagram-cluster rect.outer {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state .divider {\n  stroke: ${options.stateBorder || options.nodeBorder};\n}\n\n.statediagram-state .title-state {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-cluster.statediagram-cluster .inner {\n  fill: ${options.compositeBackground || options.background};\n}\n.statediagram-cluster.statediagram-cluster-alt .inner {\n  fill: ${options.altBackground ? options.altBackground : \"#efefef\"};\n}\n\n.statediagram-cluster .inner {\n  rx:0;\n  ry:0;\n}\n\n.statediagram-state rect.basic {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state rect.divider {\n  stroke-dasharray: 10,10;\n  fill: ${options.altBackground ? options.altBackground : \"#efefef\"};\n}\n\n.note-edge {\n  stroke-dasharray: 5;\n}\n\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n\n.statediagram-note text {\n  fill: ${options.noteTextColor};\n}\n\n.statediagram-note .nodeLabel {\n  color: ${options.noteTextColor};\n}\n.statediagram .edgeLabel {\n  color: red; // ${options.noteTextColor};\n}\n\n#dependencyStart, #dependencyEnd {\n  fill: ${options.lineColor};\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.statediagramTitleText {\n  text-anchor: middle;\n  font-size: 18px;\n  fill: ${options.textColor};\n}\n`, \"getStyles\");\nvar styles_default = getStyles;\n\nexport {\n  stateDiagram_default,\n  stateRenderer_v3_unified_default,\n  StateDB,\n  styles_default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,IAAI,SAAS,WAAW;AACtB,MAAI,IAAoB,OAAO,SAAS,GAAG,GAAG,IAAI,GAAG;AACnD,SAAK,KAAK,MAAM,CAAA,GAAI,IAAI,EAAE,QAAQ,KAAK,GAAG,EAAE,CAAC,CAAC,IAAI;AAAG;AACrD,WAAO;EACT,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AACzvB,MAAI,UAAU;IACZ,OAAuB,OAAO,SAAS,QAAQ;IAC/C,GAAG,OAAO;IACV,IAAI,CAAA;IACJ,UAAU,EAAE,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,MAAM,GAAG,MAAM,GAAG,YAAY,GAAG,QAAQ,GAAG,aAAa,GAAG,qBAAqB,IAAI,kBAAkB,IAAI,qBAAqB,IAAI,eAAe,IAAI,SAAS,IAAI,OAAO,IAAI,cAAc,IAAI,SAAS,IAAI,SAAS,IAAI,kBAAkB,IAAI,gBAAgB,IAAI,eAAe,IAAI,eAAe,IAAI,MAAM,IAAI,MAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI,UAAU,IAAI,cAAc,IAAI,QAAQ,IAAI,gBAAgB,IAAI,aAAa,IAAI,aAAa,IAAI,aAAa,IAAI,mBAAmB,IAAI,aAAa,IAAI,mBAAmB,IAAI,6BAA6B,IAAI,SAAS,IAAI,UAAU,IAAI,QAAQ,IAAI,YAAY,IAAI,eAAe,IAAI,sBAAsB,IAAI,WAAW,IAAI,SAAS,IAAI,aAAa,IAAI,sBAAsB,IAAI,SAAS,IAAI,mBAAmB,IAAI,cAAc,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,OAAO,IAAI,KAAK,IAAI,cAAc,IAAI,mBAAmB,IAAI,WAAW,IAAI,YAAY,IAAI,WAAW,GAAG,QAAQ,EAAC;IAC9/B,YAAY,EAAE,GAAG,SAAS,GAAG,SAAS,GAAG,MAAM,GAAG,MAAM,IAAI,SAAS,IAAI,OAAO,IAAI,cAAc,IAAI,SAAS,IAAI,SAAS,IAAI,kBAAkB,IAAI,gBAAgB,IAAI,eAAe,IAAI,eAAe,IAAI,MAAM,IAAI,MAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI,UAAU,IAAI,cAAc,IAAI,QAAQ,IAAI,aAAa,IAAI,aAAa,IAAI,mBAAmB,IAAI,aAAa,IAAI,mBAAmB,IAAI,6BAA6B,IAAI,SAAS,IAAI,UAAU,IAAI,QAAQ,IAAI,YAAY,IAAI,eAAe,IAAI,sBAAsB,IAAI,WAAW,IAAI,SAAS,IAAI,aAAa,IAAI,sBAAsB,IAAI,SAAS,IAAI,mBAAmB,IAAI,cAAc,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,KAAK,IAAI,cAAc,IAAI,mBAAmB,IAAI,WAAW,IAAI,WAAU;IACtyB,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACxa,eAA+B,OAAO,SAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAS,IAAI,IAAI;AACtG,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAO;QACb,KAAK;AACH,aAAG,WAAW,GAAG,EAAE,CAAC;AACpB,iBAAO,GAAG,EAAE;QAEd,KAAK;AACH,eAAK,IAAI,CAAA;AACT;QACF,KAAK;AACH,cAAI,GAAG,EAAE,KAAK,MAAM;AAClB,eAAG,KAAK,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;AACtB,iBAAK,IAAI,GAAG,KAAK,CAAC;UACpB;AACA;QACF,KAAK;QACL,KAAK;AACH,eAAK,IAAI,GAAG,EAAE;AACd;QACF,KAAK;AACH,eAAK,IAAI;AACT;QACF,KAAK;AACH,eAAK,IAAI,GAAG,EAAE;AACd;QACF,KAAK;AACH,gBAAM,YAAY,GAAG,KAAK,CAAC;AAC3B,oBAAU,cAAc,GAAG,UAAU,GAAG,EAAE,CAAC;AAC3C,eAAK,IAAI;AACT;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,YAAY,QAAQ,GAAG,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAC;AAC/D;QACF,KAAK;AACH,gBAAM,iBAAiB,GAAG,UAAU,GAAG,EAAE,CAAC;AAC1C,eAAK,IAAI,EAAE,MAAM,YAAY,QAAQ,GAAG,KAAK,CAAC,GAAG,QAAQ,GAAG,KAAK,CAAC,GAAG,aAAa,eAAc;AAChG;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,WAAW,aAAa,IAAI,KAAK,GAAG,KAAK,CAAC,EAAC;AAC3F;QACF,KAAK;AACH,cAAI,KAAK,GAAG,EAAE;AACd,cAAI,cAAc,GAAG,KAAK,CAAC,EAAE,KAAI;AACjC,cAAI,GAAG,EAAE,EAAE,MAAM,GAAG,GAAG;AACrB,gBAAI,QAAQ,GAAG,EAAE,EAAE,MAAM,GAAG;AAC5B,iBAAK,MAAM,CAAC;AACZ,0BAAc,CAAC,aAAa,MAAM,CAAC,CAAC;UACtC;AACA,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,MAAM,WAAW,YAAW;AAC1D;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,WAAW,aAAa,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,EAAC;AACnG;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,EAAE,GAAG,MAAM,OAAM;AAClD;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,EAAE,GAAG,MAAM,OAAM;AAClD;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,EAAE,GAAG,MAAM,SAAQ;AACpD;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,aAAY,GAAI,MAAM,UAAS;AAChE;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,EAAE,KAAI,GAAI,MAAM,EAAE,UAAU,GAAG,KAAK,CAAC,EAAE,KAAI,GAAI,MAAM,GAAG,EAAE,EAAE,KAAI,EAAE,EAAE;AAC3G;QACF,KAAK;AACH,eAAK,IAAI,GAAG,EAAE,EAAE,KAAI;AACpB,aAAG,YAAY,KAAK,CAAC;AACrB;QACF,KAAK;QACL,KAAK;AACH,eAAK,IAAI,GAAG,EAAE,EAAE,KAAI;AACpB,aAAG,kBAAkB,KAAK,CAAC;AAC3B;QACF,KAAK;AACH,eAAK,IAAI;YACP,MAAM;YACN,IAAI,GAAG,KAAK,CAAC;YACb,KAAK,GAAG,KAAK,CAAC;YACd,SAAS,GAAG,KAAK,CAAC;UAC9B;AACU;QACF,KAAK;AACH,eAAK,IAAI;YACP,MAAM;YACN,IAAI,GAAG,KAAK,CAAC;YACb,KAAK,GAAG,KAAK,CAAC;YACd,SAAS;UACrB;AACU;QACF,KAAK;QACL,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,YAAY,IAAI,GAAG,KAAK,CAAC,EAAE,KAAI,GAAI,SAAS,GAAG,EAAE,EAAE,KAAA,EAAM;AAC1E;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,EAAE,KAAI,GAAI,YAAY,GAAG,EAAE,EAAE,KAAA,EAAM;AAC1E;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,cAAc,IAAI,GAAG,KAAK,CAAC,EAAE,KAAI,GAAI,YAAY,GAAG,EAAE,EAAE,KAAA,EAAM;AAC/E;QACF,KAAK;AACH,aAAG,aAAa,IAAI;AACpB,eAAK,IAAI,EAAE,MAAM,OAAO,OAAO,KAAI;AACnC;QACF,KAAK;AACH,aAAG,aAAa,IAAI;AACpB,eAAK,IAAI,EAAE,MAAM,OAAO,OAAO,KAAI;AACnC;QACF,KAAK;AACH,aAAG,aAAa,IAAI;AACpB,eAAK,IAAI,EAAE,MAAM,OAAO,OAAO,KAAI;AACnC;QACF,KAAK;AACH,aAAG,aAAa,IAAI;AACpB,eAAK,IAAI,EAAE,MAAM,OAAO,OAAO,KAAI;AACnC;QACF,KAAK;QACL,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,EAAE,EAAE,KAAI,GAAI,MAAM,WAAW,aAAa,GAAE;AAC7E;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,EAAE,KAAI,GAAI,SAAS,CAAC,GAAG,EAAE,EAAE,KAAI,CAAE,GAAG,MAAM,WAAW,aAAa,GAAE;AAC3G;QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,EAAE,KAAI,GAAI,SAAS,CAAC,GAAG,EAAE,EAAE,KAAI,CAAE,GAAG,MAAM,WAAW,aAAa,GAAE;AAC3G;MACV;IACI,GAAG,WAAW;IACd,OAAO,CAAC,EAAE,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,IAAA,GAAO,EAAE,GAAG,CAAC,CAAC,EAAC,GAAI,EAAE,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,IAAG,GAAI,EAAE,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,IAAG,GAAI,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG,EAAC,CAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAA,GAAK,EAAE,GAAG,CAAC,GAAG,CAAC,EAAC,GAAI,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAA,GAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAG,GAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAC,CAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,CAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAA,GAAK,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAA,GAAK,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,KAAK,IAAI,IAAG,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAA,GAAK,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,CAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,CAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,KAAK,IAAI,IAAG,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,GAAE,CAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAA,GAAK,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAA,GAAK,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAA,GAAK,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAA,GAAK,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,CAAE,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAG,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAA,CAAG,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAA,GAAK,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,GAAA,CAAI,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAA,GAAK,EAAE,GAAG,CAAC,GAAG,EAAE,EAAC,GAAI,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAA,GAAO,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;IAC7tF,gBAAgB,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAC;IAChE,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,UAAI,KAAK,aAAa;AACpB,aAAK,MAAM,GAAG;MAChB,OAAO;AACL,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;MACR;IACF,GAAG,YAAY;IACf,OAAuB,OAAO,SAAS,MAAM,OAAO;AAC/C,UAAC,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAA,GAAI,SAAS,CAAC,IAAI,GAAG,SAAS,CAAA,GAAI,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAmB,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAI,SAAS,OAAO,OAAO,KAAK,KAAK;AACrC,UAAI,cAAc,EAAE,IAAI,CAAA,EAAE;AAC1B,eAAS,KAAK,KAAK,IAAI;AACrB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AACpD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;QAC/B;MACF;AACA,aAAO,SAAS,OAAO,YAAY,EAAE;AACrC,kBAAY,GAAG,QAAQ;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAO,OAAO,UAAU,aAAa;AACvC,eAAO,SAAS,CAAA;MAClB;AACA,UAAI,QAAQ,OAAO;AACnB,aAAO,KAAK,KAAK;AACjB,UAAI,SAAS,OAAO,WAAW,OAAO,QAAQ;AAC9C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACnD,aAAK,aAAa,YAAY,GAAG;MACnC,OAAO;AACL,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;MAChD;AACA,eAAS,SAAS,GAAG;AACnB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;MAClC;AACA,aAAO,UAAU,UAAU;AAC3B,eAAS,MAAM;AACb,YAAI;AACJ,gBAAQ,OAAO,IAAG,KAAM,OAAO,IAAG,KAAM;AACxC,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,iBAAiB,OAAO;AAC1B,qBAAS;AACT,oBAAQ,OAAO,IAAG;UACpB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;QAClC;AACA,eAAO;MACT;AACA,aAAO,KAAK,KAAK;AACd,UAAC,QAAwB,OAAO,QAAW,GAAG,QAAQ,CAAA,GAAI,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACX,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC9B,mBAAS,KAAK,eAAe,KAAK;QACpC,OAAO;AACL,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACnD,qBAAS,IAAG;UACd;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;QAC9C;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AACjE,cAAI,SAAS;AACb,qBAAW,CAAA;AACX,eAAK,KAAK,MAAM,KAAK,GAAG;AACtB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AACpC,uBAAS,KAAK,MAAM,KAAK,WAAW,CAAC,IAAI,GAAG;YAC9C;UACF;AACA,cAAI,OAAO,cAAc;AACvB,qBAAS,0BAA0B,WAAW,KAAK,QAAQ,OAAO,aAAY,IAAK,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAa,KAAK,WAAW,MAAM,KAAK,UAAU;UAC9K,OAAO;AACL,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAO,KAAK,WAAW,MAAM,KAAK,UAAU;UACrJ;AACA,eAAK,WAAW,QAAQ;YACtB,MAAM,OAAO;YACb,OAAO,KAAK,WAAW,MAAM,KAAK;YAClC,MAAM,OAAO;YACb,KAAK;YACL;UACZ,CAAW;QACH;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACnD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;QACpG;AACA,gBAAQ,OAAO,CAAC,GAAC;UACf,KAAK;AACH,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAK,OAAO,MAAM;AACzB,mBAAO,KAAK,OAAO,MAAM;AACzB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACY;AACnB,uBAAS,OAAO;AAChB,uBAAS,OAAO;AAChB,yBAAW,OAAO;AAClB,sBAAQ,OAAO;YAIjB;AAIA;UACF,KAAK;AACH,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;cACT,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;YACrD;AACY,gBAAI,QAAQ;AACV,oBAAM,GAAG,QAAQ;gBACf,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;cACjD;YACY;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;cAClC;cACA;cACA;cACA,YAAY;cACZ,OAAO,CAAC;cACR;cACA;YACd,EAAc,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC5B,qBAAO;YACT;AACA,gBAAI,KAAK;AACP,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;YACnC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;UACF,KAAK;AACH,mBAAO;QACnB;MACM;AACA,aAAO;IACT,GAAG,OAAO;EACd;AACE,MAAI,QAAwB,WAAW;AACrC,QAAI,SAAS;MACX,KAAK;MACL,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,YAAI,KAAK,GAAG,QAAQ;AAClB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;QACrC,OAAO;AACL,gBAAM,IAAI,MAAM,GAAG;QACrB;MACF,GAAG,YAAY;;MAEf,UAA0B,OAAO,SAAS,OAAO,IAAI;AACnD,aAAK,KAAK,MAAM,KAAK,MAAM,CAAA;AAC3B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;UACZ,YAAY;UACZ,cAAc;UACd,WAAW;UACX,aAAa;QACvB;AACQ,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,GAAG,CAAC;QAC3B;AACA,aAAK,SAAS;AACd,eAAO;MACT,GAAG,UAAU;;MAEb,OAAuB,OAAO,WAAW;AACvC,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACT,eAAK;AACL,eAAK,OAAO;QACd,OAAO;AACL,eAAK,OAAO;QACd;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,MAAM,CAAC;QACrB;AACA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;MACT,GAAG,OAAO;;MAEV,OAAuB,OAAO,SAAS,IAAI;AACzC,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AACpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAC5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAC7D,YAAI,MAAM,SAAS,GAAG;AACpB,eAAK,YAAY,MAAM,SAAS;QAClC;AACA,YAAI,IAAI,KAAK,OAAO;AACpB,aAAK,SAAS;UACZ,YAAY,KAAK,OAAO;UACxB,WAAW,KAAK,WAAW;UAC3B,cAAc,KAAK,OAAO;UAC1B,aAAa,SAAS,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAAK,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS,KAAK,OAAO,eAAe;QAClM;AACQ,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;QACrD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;MACT,GAAG,OAAO;;MAEV,MAAsB,OAAO,WAAW;AACtC,aAAK,QAAQ;AACb,eAAO;MACT,GAAG,MAAM;;MAET,QAAwB,OAAO,WAAW;AACxC,YAAI,KAAK,QAAQ,iBAAiB;AAChC,eAAK,aAAa;QACpB,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAA,GAAgB;YAChO,MAAM;YACN,OAAO;YACP,MAAM,KAAK;UACvB,CAAW;QACH;AACA,eAAO;MACT,GAAG,QAAQ;;MAEX,MAAsB,OAAO,SAAS,GAAG;AACvC,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;MAChC,GAAG,MAAM;;MAET,WAA2B,OAAO,WAAW;AAC3C,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAQ,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;MAC7E,GAAG,WAAW;;MAEd,eAA+B,OAAO,WAAW;AAC/C,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AACpB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAK,KAAK,MAAM;QAChD;AACA,gBAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;MACjF,GAAG,eAAe;;MAElB,cAA8B,OAAO,WAAW;AAC9C,YAAI,MAAM,KAAK,UAAS;AACxB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAa,IAAK,OAAO,IAAI;MACjD,GAAG,cAAc;;MAEjB,YAA4B,OAAO,SAAS,OAAO,cAAc;AAC/D,YAAI,OAAO,OAAO;AAClB,YAAI,KAAK,QAAQ,iBAAiB;AAChC,mBAAS;YACP,UAAU,KAAK;YACf,QAAQ;cACN,YAAY,KAAK,OAAO;cACxB,WAAW,KAAK;cAChB,cAAc,KAAK,OAAO;cAC1B,aAAa,KAAK,OAAO;YACvC;YACY,QAAQ,KAAK;YACb,OAAO,KAAK;YACZ,SAAS,KAAK;YACd,SAAS,KAAK;YACd,QAAQ,KAAK;YACb,QAAQ,KAAK;YACb,OAAO,KAAK;YACZ,QAAQ,KAAK;YACb,IAAI,KAAK;YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;YAC3C,MAAM,KAAK;UACvB;AACU,cAAI,KAAK,QAAQ,QAAQ;AACvB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;UACjD;QACF;AACA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACT,eAAK,YAAY,MAAM;QACzB;AACA,aAAK,SAAS;UACZ,YAAY,KAAK,OAAO;UACxB,WAAW,KAAK,WAAW;UAC3B,cAAc,KAAK,OAAO;UAC1B,aAAa,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAAS,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;QACvJ;AACQ,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;QAC9D;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC5B,eAAK,OAAO;QACd;AACA,YAAI,OAAO;AACT,iBAAO;QACT,WAAW,KAAK,YAAY;AAC1B,mBAAS,KAAK,QAAQ;AACpB,iBAAK,CAAC,IAAI,OAAO,CAAC;UACpB;AACA,iBAAO;QACT;AACA,eAAO;MACT,GAAG,YAAY;;MAEf,MAAsB,OAAO,WAAW;AACtC,YAAI,KAAK,MAAM;AACb,iBAAO,KAAK;QACd;AACA,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,OAAO;QACd;AACA,YAAI,OAAO,OAAO,WAAW;AAC7B,YAAI,CAAC,KAAK,OAAO;AACf,eAAK,SAAS;AACd,eAAK,QAAQ;QACf;AACA,YAAI,QAAQ,KAAK,cAAa;AAC9B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAClE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAChC,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACnB,uBAAO;cACT,WAAW,KAAK,YAAY;AAC1B,wBAAQ;AACR;cACF,OAAO;AACL,uBAAO;cACT;YACF,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC7B;YACF;UACF;QACF;AACA,YAAI,OAAO;AACT,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACnB,mBAAO;UACT;AACA,iBAAO;QACT;AACA,YAAI,KAAK,WAAW,IAAI;AACtB,iBAAO,KAAK;QACd,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAA,GAAgB;YACtH,MAAM;YACN,OAAO;YACP,MAAM,KAAK;UACvB,CAAW;QACH;MACF,GAAG,MAAM;;MAET,KAAqB,OAAO,SAAS,MAAM;AACzC,YAAI,IAAI,KAAK,KAAI;AACjB,YAAI,GAAG;AACL,iBAAO;QACT,OAAO;AACL,iBAAO,KAAK,IAAG;QACjB;MACF,GAAG,KAAK;;MAER,OAAuB,OAAO,SAAS,MAAM,WAAW;AACtD,aAAK,eAAe,KAAK,SAAS;MACpC,GAAG,OAAO;;MAEV,UAA0B,OAAO,SAAS,WAAW;AACnD,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACT,iBAAO,KAAK,eAAe,IAAG;QAChC,OAAO;AACL,iBAAO,KAAK,eAAe,CAAC;QAC9B;MACF,GAAG,UAAU;;MAEb,eAA+B,OAAO,SAAS,gBAAgB;AAC7D,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACrF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;QAC9E,OAAO;AACL,iBAAO,KAAK,WAAW,SAAS,EAAE;QACpC;MACF,GAAG,eAAe;;MAElB,UAA0B,OAAO,SAAS,SAAS,GAAG;AACpD,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACV,iBAAO,KAAK,eAAe,CAAC;QAC9B,OAAO;AACL,iBAAO;QACT;MACF,GAAG,UAAU;;MAEb,WAA2B,OAAO,SAAS,UAAU,WAAW;AAC9D,aAAK,MAAM,SAAS;MACtB,GAAG,WAAW;;MAEd,gBAAgC,OAAO,SAAS,iBAAiB;AAC/D,eAAO,KAAK,eAAe;MAC7B,GAAG,gBAAgB;MACnB,SAAS,EAAE,oBAAoB,KAAI;MACnC,eAA+B,OAAO,SAAS,UAAU,IAAI,KAAK,2BAA2B,UAAU;AAErG,gBAAQ,2BAAyB;UAC/B,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH;UACF,KAAK;AAGH;UACF,KAAK;AACH,mBAAO;UAET,KAAK;AACH;UACF,KAAK;AACH;UACF,KAAK;AACH;UACF,KAAK;AACH;UACF,KAAK;AACH,iBAAK,UAAU,OAAO;AACtB,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb;UACF,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,MAAM,qBAAqB;AAChC;UACF,KAAK;AACH,iBAAK,SAAQ;AACb;UACF,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,UAAU;AACzB,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,iBAAK,UAAU,YAAY;AAC3B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,iBAAK,UAAU,YAAY;AAC3B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,OAAO;AACtB,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,iBAAK,UAAU,aAAa;AAC5B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,OAAO;AACtB,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,iBAAK,UAAU,iBAAiB;AAChC,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,OAAO;AACtB,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb;UACF,KAAK;AACH,iBAAK,UAAU,OAAO;AACtB;UACF,KAAK;AACH,iBAAK,SAAQ;AACb,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAG,EAAE,EAAE,KAAI;AACzC,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAG,EAAE,EAAE,KAAI;AACzC,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAG,GAAG,EAAE,KAAI;AAC1C,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAG,EAAE,EAAE,KAAI;AACzC,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAG,EAAE,EAAE,KAAI;AACzC,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAG,GAAG,EAAE,KAAI;AAC1C,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,UAAU,cAAc;AAC7B;UACF,KAAK;AACH,iBAAK,UAAU,UAAU;AACzB,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb;UACF,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb;UACF,KAAK;AACH,iBAAK,SAAQ;AACb,iBAAK,UAAU,QAAQ;AACvB,mBAAO;UAET,KAAK;AACH;UACF,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH;UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,iBAAK,UAAU,SAAS;AACxB,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,iBAAK,UAAU,SAAS;AACxB,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,iBAAK,UAAU,eAAe;AAC9B;UACF,KAAK;AACH,iBAAK,SAAQ;AACb,iBAAK,UAAU,kBAAkB;AACjC,mBAAO;UAET,KAAK;AACH;UACF,KAAK;AACH,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,iBAAK,UAAU,WAAW;AAC1B,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,gBAAI,SAAS,IAAI,OAAO,OAAO,CAAC,EAAE,KAAI;AACtC,mBAAO;UAET,KAAK;AACH,iBAAK,SAAQ;AACb,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAG,EAAE,EAAE,KAAI;AACzC,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,gBAAI,SAAS,IAAI,OAAO,KAAI;AAC5B,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;UAET,KAAK;AACH,mBAAO;QAEnB;MACM,GAAG,WAAW;MACd,OAAO,CAAC,iBAAiB,gBAAgB,iBAAiB,mBAAmB,gCAAgC,gCAAgC,gCAAgC,gCAAgC,wBAAwB,uBAAuB,eAAe,eAAe,qBAAqB,iBAAiB,iBAAiB,kBAAkB,aAAa,oBAAoB,yBAAyB,yBAAyB,yBAAyB,yBAAyB,0BAA0B,cAAc,gBAAgB,qBAAqB,oBAAoB,gBAAgB,gBAAgB,kBAAkB,4BAA4B,gBAAgB,kBAAkB,mBAAmB,gBAAgB,kBAAkB,aAAa,oBAAoB,kBAAkB,oBAAoB,oBAAoB,sBAAsB,wBAAwB,wBAAwB,0BAA0B,gCAAgC,gCAAgC,gCAAgC,gCAAgC,aAAa,kBAAkB,kBAAkB,aAAa,eAAe,oBAAoB,YAAY,YAAY,wBAAwB,YAAY,cAAc,iBAAiB,mBAAmB,oBAAoB,WAAW,kBAAkB,aAAa,eAAe,gBAAgB,wBAAwB,sBAAsB,4BAA4B,yBAAyB,4BAA4B,kCAAkC,gBAAgB,uBAAuB,sBAAsB,aAAa,YAAY,aAAa,WAAW,SAAS;MAC7mD,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,UAAU,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,oBAAoB,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,iBAAiB,EAAE,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,aAAa,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,WAAW,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,QAAQ,EAAE,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,sBAAsB,EAAE,SAAS,CAAA,GAAI,aAAa,MAAK,GAAI,mBAAmB,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,aAAa,EAAE,SAAS,CAAA,GAAI,aAAa,MAAK,GAAI,SAAS,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,eAAe,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,SAAS,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,cAAc,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,YAAY,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,uBAAuB,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,aAAa,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,aAAa,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,SAAS,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,SAAS,EAAE,SAAS,CAAA,GAAI,aAAa,MAAK,GAAI,YAAY,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAK,GAAI,gBAAgB,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,cAAc,EAAE,SAAS,CAAA,GAAI,aAAa,MAAK,GAAI,SAAS,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,MAAM,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAK,GAAI,WAAW,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,KAAI,EAAE;IACxnD;AACI,WAAO;EACT,EAAC;AACD,UAAQ,QAAQ;AAChB,WAAS,SAAS;AAChB,SAAK,KAAK,CAAA;EACZ;AACA,SAAO,QAAQ,QAAQ;AACvB,SAAO,YAAY;AACnB,UAAQ,SAAS;AACjB,SAAO,IAAI,OAAM;AACnB,EAAC;AACD,OAAO,SAAS;AACb,IAAC,uBAAuB;AAG3B,IAAI,4BAA4B;AAChC,IAAI,yBAAyB;AAC7B,IAAI,iBAAiB;AACrB,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,qBAAqB;AACzB,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,wBAAwB;AAC5B,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,mBAAmB;AACvB,IAAI,cAAc;AAClB,IAAI,wBAAwB;AAC5B,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,kBAAkB;AACtB,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,oBAAoB,GAAG,WAAW,IAAI,SAAS;AACnD,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,gBAAgB;AACpB,IAAI,qBAAqB,GAAG,QAAQ,IAAI,aAAa;AACrD,IAAI,mBAAmB,GAAG,WAAW,IAAI,QAAQ;AACjD,IAAI,cAAc;AAClB,IAAI,sBAAsB,GAAG,WAAW,IAAI,WAAW;AACvD,IAAI,kBAAkB;AACtB,IAAI,0BAA0B,GAAG,WAAW,IAAI,eAAe;AAC/D,IAAI,SAAS;AACb,IAAI,OAAO;AACX,IAAI,cAAc;AAClB,IAAI,oBAAoB;AACxB,IAAI,UAAU,GAAG,iBAAiB,GAAG,IAAI;AACzC,IAAI,YAAY,GAAG,iBAAiB,GAAG,MAAM;AAG7C,IAAI,SAAyB,OAAO,CAAC,YAAY,aAAa,2BAA2B;AACvF,MAAI,CAAC,WAAW,KAAK;AACnB,WAAO;EACT;AACA,MAAI,MAAM;AACV,aAAW,iBAAiB,WAAW,KAAK;AAC1C,QAAI,cAAc,SAAS,OAAO;AAChC,YAAM,cAAc;IACtB;EACF;AACA,SAAO;AACT,GAAG,QAAQ;AACX,IAAI,aAA6B,OAAO,SAAS,MAAM,YAAY;AACjE,SAAO,WAAW,GAAG,WAAU;AACjC,GAAG,YAAY;AACf,IAAI,OAAuB,OAAO,eAAe,MAAM,IAAI,UAAU,MAAM;AACzE,MAAI,KAAK,OAAO;AAChB,MAAI,KAAK,8BAA8B,EAAE;AACzC,QAAM,EAAE,eAAe,OAAO,MAAM,OAAM,IAAKA,WAAS;AACxD,OAAK,GAAG,QAAQ,KAAK,GAAG,aAAY,CAAE;AACtC,QAAM,cAAc,KAAK,GAAG,QAAO;AACnC,QAAM,MAAM,kBAAkB,IAAI,aAAa;AAC/C,cAAY,OAAO,KAAK;AACxB,cAAY,kBAAkB;AAC9B,cAAY,eAAc,QAAA,OAAA,SAAA,KAAM,gBAAe;AAC/C,cAAY,eAAc,QAAA,OAAA,SAAA,KAAM,gBAAe;AAC/C,cAAY,UAAU,CAAC,MAAM;AAC7B,cAAY,YAAY;AACxB,QAAM,SAAO,aAAa,GAAG;AAC7B,QAAM,UAAU;AAChB,MAAI;AACF,UAAM,QAAQ,OAAO,KAAK,GAAG,aAAa,aAAa,KAAK,GAAG,SAAQ,IAAqB,oBAAI,IAAG;AACnG,UAAM,QAAQ,CAAC,UAAU,QAAQ;;AAC/B,YAAM,UAAU,OAAO,QAAQ,WAAW,MAAM,QAAO,OAAA,OAAA,SAAA,IAAK,QAAO,WAAW,IAAI,KAAK;AACvF,UAAI,CAAC,SAAS;AACZ,YAAI,KAAK,2CAAqD,KAAK,UAAU,GAAG,CAAC;AACjF;MACF;AACA,YAAM,YAAWC,MAAA,IAAI,KAAI,MAAR,OAAA,SAAAA,IAAY,iBAAiB,GAAA;AAC9C,UAAI;AACJ,kBAAA,OAAA,SAAA,SAAU,QAAQ,CAAC,MAAM;;AACvB,cAAM,SAAQA,MAAA,EAAE,gBAAF,OAAA,SAAAA,IAAe,KAAA;AAC7B,YAAI,UAAU,SAAS;AACrB,wBAAc;QAChB;MACF,CAAA;AACA,UAAI,CAAC,aAAa;AAChB,YAAI,KAAK,yCAAmD,OAAO;AACnE;MACF;AACA,YAAM,SAAS,YAAY;AAC3B,UAAI,CAAC,QAAQ;AACX,YAAI,KAAK,uCAAiD,OAAO;AACjE;MACF;AACA,YAAM,IAAI,SAAS,gBAAgB,8BAA8B,GAAG;AACpE,YAAM,aAAa,SAAS,IAAI,QAAQ,YAAY,EAAE;AACtD,QAAE,eAAe,gCAAgC,cAAc,UAAU;AACzE,QAAE,aAAa,UAAU,QAAQ;AACjC,UAAI,SAAS,SAAS;AACpB,cAAM,UAAU,SAAS,QAAQ,QAAQ,YAAY,EAAE;AACvD,UAAE,aAAa,SAAS,OAAO;MACjC;AACA,aAAO,aAAa,GAAG,WAAW;AAClC,QAAE,YAAY,WAAW;AACzB,UAAI,KAAK,mCAA0C,SAAS,SAAS,GAAG;IAC1E,CAAC;EACH,SAAS,KAAK;AACZ,QAAI,MAAM,sCAA2C,GAAG;EAC1D;AACA,gBAAc;IACZ;IACA;KACA,QAAA,OAAA,SAAA,KAAM,mBAAkB;IACxB,KAAK,GAAG,gBAAe;EAC3B;AACE,sBAAoB,KAAK,SAAS,cAAa,QAAA,OAAA,SAAA,KAAM,gBAAe,IAAI;AAC1E,GAAG,MAAM;AACN,IAAC,mCAAmC;EACrC;EACA;EACA;AACF;AAGA,IAAI,SAAyB,oBAAI,IAAG;AACpC,IAAI,iBAAiB;AACrB,SAAS,WAAW,SAAS,IAAI,UAAU,GAAG,OAAO,IAAI,aAAa,mBAAmB;AACvF,QAAM,UAAU,SAAS,QAAQ,KAAK,SAAS,IAAI,GAAG,UAAU,GAAG,IAAI,KAAK;AAC5E,SAAO,GAAG,WAAW,IAAI,MAAM,GAAG,OAAO,IAAI,OAAO;AACtD;AACA,OAAO,YAAY,YAAY;AAC/B,IAAI,WAA2B,OAAO,CAAC,kBAAkB,KAAK,eAAe,OAAO,OAAO,SAAS,MAAM,YAAY;AACpH,MAAI,MAAM,SAAS,GAAG;AACtB,MAAI,QAAQ,CAAC,SAAS;AACpB,YAAQ,KAAK,MAAI;MACf,KAAK;AACH,oBAAY,kBAAkB,MAAM,eAAe,OAAO,OAAO,SAAS,MAAM,OAAO;AACvF;MACF,KAAK;AACH,oBAAY,kBAAkB,MAAM,eAAe,OAAO,OAAO,SAAS,MAAM,OAAO;AACvF;MACF,KAAK;AACH;AACE;YACE;YACA,KAAK;YACL;YACA;YACA;YACA;YACA;YACA;UACZ;AACU;YACE;YACA,KAAK;YACL;YACA;YACA;YACA;YACA;YACA;UACZ;AACU,gBAAM,WAAW;YACf,IAAI,SAAS;YACb,OAAO,KAAK,OAAO;YACnB,KAAK,KAAK,OAAO;YACjB,WAAW;YACX,cAAc;YACd,OAAO;YACP,YAAY;YACZ,OAAO,eAAe,aAAa,KAAK,eAAe,IAAID,WAAAA,CAAW;YACtE,gBAAgB;YAChB,UAAU;YACV,WAAW;YACX,WAAW;YACX,SAAS;YACT;UACZ;AACU,gBAAM,KAAK,QAAQ;AACnB;QACF;AACA;IACR;EACE,CAAC;AACH,GAAG,UAAU;AACb,IAAI,UAA0B,OAAO,CAAC,YAAY,aAAa,2BAA2B;AACxF,MAAI,MAAM;AACV,MAAI,WAAW,KAAK;AAClB,eAAW,iBAAiB,WAAW,KAAK;AAC1C,UAAI,cAAc,SAAS,OAAO;AAChC,cAAM,cAAc;MACtB;IACF;EACF;AACA,SAAO;AACT,GAAG,QAAQ;AACX,SAAS,mBAAmB,OAAO,UAAU,SAAS;AACpD,MAAI,CAAC,SAAS,MAAM,SAAS,OAAO,oBAAoB,SAAS,OAAO,aAAa;AACnF;EACF;AACA,MAAI,SAAS,YAAY;AACvB,QAAI,CAAC,MAAM,QAAQ,SAAS,iBAAiB,GAAG;AAC9C,eAAS,oBAAoB,CAAA;IAC/B;AACA,aAAS,WAAW,MAAM,GAAG,EAAE,QAAQ,CAAC,aAAa;AACnD,YAAM,WAAW,QAAQ,IAAI,QAAQ;AACrC,UAAI,UAAU;AACZ,iBAAS,oBAAoB,CAAC,GAAG,SAAS,qBAAqB,CAAA,GAAI,GAAG,SAAS,MAAM;MACvF;IACF,CAAC;EACH;AACA,QAAM,mBAAmB,MAAM,KAAK,CAAC,SAAS,KAAK,OAAO,SAAS,EAAE;AACrE,MAAI,kBAAkB;AACpB,WAAO,OAAO,kBAAkB,QAAQ;EAC1C,OAAO;AACL,UAAM,KAAK,QAAQ;EACrB;AACF;AACA,OAAO,oBAAoB,oBAAoB;AAC/C,SAAS,qBAAqB,YAAY;;AACxC,WAAOC,MAAA,cAAA,OAAA,SAAA,WAAY,YAAZ,OAAA,SAAAA,IAAqB,KAAK,GAAA,MAAQ;AAC3C;AACA,OAAO,sBAAsB,sBAAsB;AACnD,SAAS,oBAAoB,YAAY;AACvC,UAAO,cAAA,OAAA,SAAA,WAAY,WAAU,CAAA;AAC/B;AACA,OAAO,qBAAqB,qBAAqB;AACjD,IAAI,cAA8B,OAAO,CAAC,QAAQ,YAAY,eAAe,OAAO,OAAO,SAAS,MAAM,YAAY;;AACpH,QAAM,SAAS,WAAW;AAC1B,QAAM,UAAU,cAAc,IAAI,MAAM;AACxC,QAAM,WAAW,qBAAqB,OAAO;AAC7C,QAAM,QAAQ,oBAAoB,OAAO;AACzC,QAAM,SAASD,WAAS;AACxB,MAAI,KAAK,0BAA0B,YAAY,SAAS,KAAK;AAC7D,MAAI,WAAW,QAAQ;AACrB,QAAI,QAAQ;AACZ,QAAI,WAAW,UAAU,MAAM;AAC7B,cAAQ;IACV,WAAW,WAAW,UAAU,OAAO;AACrC,cAAQ;IACV;AACA,QAAI,WAAW,SAAS,oBAAoB;AAC1C,cAAQ,WAAW;IACrB;AACA,QAAI,CAAC,OAAO,IAAI,MAAM,GAAG;AACvB,aAAO,IAAI,QAAQ;QACjB,IAAI;QACJ;QACA,aAAa,eAAe,aAAa,QAAQ,MAAM;QACvD,YAAY,GAAG,QAAQ,IAAI,iBAAiB;QAC5C,WAAW;MACnB,CAAO;IACH;AACA,UAAM,UAAU,OAAO,IAAI,MAAM;AACjC,QAAI,WAAW,aAAa;AAC1B,UAAI,MAAM,QAAQ,QAAQ,WAAW,GAAG;AACtC,gBAAQ,QAAQ;AAChB,gBAAQ,YAAY,KAAK,WAAW,WAAW;MACjD,OAAO;AACL,cAAIC,MAAA,QAAQ,gBAAR,OAAA,SAAAA,IAAqB,WAAU,QAAQ,YAAY,SAAS,GAAG;AACjE,kBAAQ,QAAQ;AAChB,cAAI,QAAQ,gBAAgB,QAAQ;AAClC,oBAAQ,cAAc,CAAC,WAAW,WAAW;UAC/C,OAAO;AACL,oBAAQ,cAAc,CAAC,QAAQ,aAAa,WAAW,WAAW;UACpE;QACF,OAAO;AACL,kBAAQ,QAAQ;AAChB,kBAAQ,cAAc,WAAW;QACnC;MACF;AACA,cAAQ,cAAc,eAAe,oBAAoB,QAAQ,aAAa,MAAM;IACtF;AACA,UAAI,KAAA,QAAQ,gBAAR,OAAA,SAAA,GAAqB,YAAW,KAAK,QAAQ,UAAU,uBAAuB;AAChF,UAAI,QAAQ,SAAS,SAAS;AAC5B,gBAAQ,QAAQ;MAClB,OAAO;AACL,gBAAQ,QAAQ;MAClB;IACF;AACA,QAAI,CAAC,QAAQ,QAAQ,WAAW,KAAK;AACnC,UAAI,KAAK,2BAA2B,QAAQ,QAAQ,UAAU,CAAC;AAC/D,cAAQ,OAAO;AACf,cAAQ,UAAU;AAClB,cAAQ,MAAM,QAAQ,UAAU;AAChC,cAAQ,QAAQ,WAAW,SAAS,eAAe,gBAAgB;AACnE,cAAQ,aAAa,GAAG,QAAQ,UAAU,IAAI,mBAAmB,IAAI,UAAU,0BAA0B,EAAE;IAC7G;AACA,UAAM,WAAW;MACf,YAAY;MACZ,OAAO,QAAQ;MACf,OAAO,QAAQ;MACf,YAAY,QAAQ;MACpB,mBAAmB,CAAA;MACnB,WAAW,QAAQ;MACnB,IAAI;MACJ,KAAK,QAAQ;MACb,OAAO,WAAW,QAAQ,cAAc;MACxC,MAAM,QAAQ;MACd,SAAS,QAAQ,SAAS;MAC1B,SAAS;MACT,IAAI;MACJ,IAAI;MACJ;IACN;AACI,QAAI,SAAS,UAAU,eAAe;AACpC,eAAS,QAAQ;IACnB;AACA,QAAI,UAAU,OAAO,OAAO,QAAQ;AAClC,UAAI,MAAM,iBAAiB,QAAQ,+BAA+B,OAAO,EAAE;AAC3E,eAAS,WAAW,OAAO;IAC7B;AACA,aAAS,cAAc;AACvB,QAAI,WAAW,MAAM;AACnB,YAAM,WAAW;QACf,YAAY;QACZ,OAAO;QACP,OAAO,WAAW,KAAK;QACvB,YAAY;;QAEZ,WAAW,CAAA;QACX,mBAAmB,CAAA;QACnB,IAAI,SAAS,UAAU,MAAM;QAC7B,OAAO,WAAW,QAAQ,gBAAgB,IAAI;QAC9C,MAAM,QAAQ;QACd,SAAS,QAAQ,SAAS;QAC1B,UAAS,KAAA,OAAO,cAAP,OAAA,SAAA,GAAkB;QAC3B;QACA,UAAU,WAAW,KAAK;MAClC;AACM,YAAM,eAAe,SAAS;AAC9B,YAAM,YAAY;QAChB,YAAY;QACZ,OAAO;QACP,OAAO,WAAW,KAAK;QACvB,YAAY,QAAQ;QACpB,WAAW,CAAA;QACX,IAAI,SAAS;QACb,OAAO,WAAW,QAAQ,gBAAgB,MAAM;QAChD,MAAM;QACN,SAAS;QACT,SAAS;;QAET;QACA,UAAU,WAAW,KAAK;MAClC;AACM;AACA,gBAAU,KAAK;AACf,eAAS,WAAW;AACpB,yBAAmB,OAAO,WAAW,OAAO;AAC5C,yBAAmB,OAAO,UAAU,OAAO;AAC3C,yBAAmB,OAAO,UAAU,OAAO;AAC3C,UAAI,OAAO;AACX,UAAI,KAAK,SAAS;AAClB,UAAI,WAAW,KAAK,aAAa,WAAW;AAC1C,eAAO,SAAS;AAChB,aAAK;MACP;AACA,YAAM,KAAK;QACT,IAAI,OAAO,MAAM;QACjB,OAAO;QACP,KAAK;QACL,WAAW;QACX,cAAc;QACd,OAAO;QACP,YAAY;QACZ,SAAS;QACT,gBAAgB;QAChB,UAAU;QACV,WAAW;QACX,WAAW;QACX;MACR,CAAO;IACH,OAAO;AACL,yBAAmB,OAAO,UAAU,OAAO;IAC7C;EACF;AACA,MAAI,WAAW,KAAK;AAClB,QAAI,MAAM,wBAAwB;AAClC,aAAS,YAAY,WAAW,KAAK,eAAe,OAAO,OAAO,CAAC,SAAS,MAAM,OAAO;EAC3F;AACF,GAAG,aAAa;AAChB,IAAI,QAAwB,OAAO,MAAM;AACvC,SAAO,MAAK;AACZ,mBAAiB;AACnB,GAAG,OAAO;AAGV,IAAI,YAAY;EACd,YAAY;EACZ,YAAY;EACZ,UAAU;EACV,UAAU;EACV,eAAe;EACf,cAAc;EACd,SAAS;EACT,gBAAgB;AAClB;AACA,IAAI,iBAAiC,OAAO,MAAsB,oBAAI,IAAG,GAAI,gBAAgB;AAC7F,IAAI,SAAyB,OAAO,OAAO;EACzC,WAAW,CAAA;EACX,QAAwB,oBAAI,IAAG;EAC/B,WAAW,CAAA;AACb,IAAI,QAAQ;AACZ,IAAI,QAAwB,OAAO,CAAC,MAAM,KAAK,MAAM,KAAK,UAAU,CAAC,CAAC,GAAG,OAAO;AAC7E,IAAC,WAAU,KAAA,MAAM;EAClB,YAAY,SAAS;AACnB,SAAK,UAAU;AACf,SAAK,QAAQ,CAAA;AACb,SAAK,QAAQ,CAAA;AACb,SAAK,UAAU,CAAA;AACf,SAAK,UAAU,eAAc;AAC7B,SAAK,YAAY,EAAE,MAAM,OAAM,EAAE;AACjC,SAAK,kBAAkB,KAAK,UAAU;AACtC,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,QAAwB,oBAAI,IAAG;AACpC,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,MAAK;AACV,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;EAC3C;;;;;;;;;;EAqBA,QAAQ,YAAY;AAClB,SAAK,MAAM,IAAI;AACf,eAAW,QAAQ,MAAM,QAAQ,UAAU,IAAI,aAAa,WAAW,KAAK;AAC1E,cAAQ,KAAK,MAAI;QACf,KAAK;AACH,eAAK,SAAS,KAAK,GAAG,KAAI,GAAI,KAAK,MAAM,KAAK,KAAK,KAAK,aAAa,KAAK,IAAI;AAC9E;QACF,KAAK;AACH,eAAK,YAAY,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;AAC3D;QACF,KAAK;AACH,eAAK,cAAc,KAAK,GAAG,KAAI,GAAI,KAAK,OAAO;AAC/C;QACF,KAAK;AACH,eAAK,eAAe,IAAI;AACxB;QACF,KAAK;AACH,eAAK,YAAY,KAAK,GAAG,KAAI,GAAI,KAAK,UAAU;AAChD;QACF,KAAK;AACH,eAAK,QAAQ,KAAK,IAAI,KAAK,KAAK,KAAK,OAAO;AAC5C;MACV;IACI;AACA,UAAM,gBAAgB,KAAK,UAAS;AACpC,UAAM,SAASD,WAAS;AACxB,UAAK;AACL;MACE;MACA,KAAK,aAAY;MACjB;MACA,KAAK;MACL,KAAK;MACL;MACA,OAAO;MACP,KAAK;IACX;AACI,eAAW,QAAQ,KAAK,OAAO;AAC7B,UAAI,CAAC,MAAM,QAAQ,KAAK,KAAK,GAAG;AAC9B;MACF;AACA,WAAK,cAAc,KAAK,MAAM,MAAM,CAAC;AACrC,UAAI,KAAK,WAAW,KAAK,YAAY,SAAS,GAAG;AAC/C,cAAM,IAAI;UACR,gFAAgF,KAAK,EAAE;QACjG;MACM;AACA,WAAK,QAAQ,KAAK,MAAM,CAAC;IAC3B;EACF;EACA,eAAe,MAAM;AACnB,UAAM,MAAM,KAAK,GAAG,KAAI,EAAG,MAAM,GAAG;AACpC,UAAM,SAAS,KAAK,WAAW,MAAM,GAAG;AACxC,eAAW,MAAM,KAAK;AACpB,UAAI,QAAQ,KAAK,SAAS,EAAE;AAC5B,UAAI,CAAC,OAAO;AACV,cAAM,YAAY,GAAG,KAAI;AACzB,aAAK,SAAS,SAAS;AACvB,gBAAQ,KAAK,SAAS,SAAS;MACjC;AACA,UAAI,OAAO;AACT,cAAM,SAAS,OAAO,IAAI,CAAC,MAAC;;AAAK,kBAAAC,MAAA,EAAE,QAAQ,MAAM,EAAE,MAAlB,OAAA,SAAAA,IAAqB,KAAA;QAAA,CAAM;MAC9D;IACF;EACF;EACA,WAAW,GAAG;AACZ,QAAI,KAAK,oBAAoB,CAAC;AAC9B,SAAK,UAAU;AACf,QAAI,KAAK,YAAY,GAAG;AACtB,WAAK,QAAQ,CAAC;IAChB,OAAO;AACL,WAAK,QAAQ,KAAK,aAAA,CAAc;IAClC;EACF;EACA,cAAc,QAAQ,MAAM,OAAO;AACjC,QAAI,KAAK,SAAS,eAAe;AAC/B,WAAK,cAAc,QAAQ,KAAK,QAAQ,IAAI;AAC5C,WAAK,cAAc,QAAQ,KAAK,QAAQ,KAAK;AAC7C;IACF;AACA,QAAI,KAAK,SAAS,YAAY;AAC5B,UAAI,KAAK,OAAO,UAAU,YAAY;AACpC,aAAK,KAAK,OAAO,MAAM,QAAQ,WAAW;AAC1C,aAAK,QAAQ;MACf,OAAO;AACL,aAAK,KAAK,KAAK,GAAG,KAAI;MACxB;IACF;AACA,QAAI,KAAK,SAAS,aAAa,KAAK,SAAS,cAAc,CAAC,KAAK,KAAK;AACpE;IACF;AACA,UAAM,MAAM,CAAA;AACZ,QAAI,aAAa,CAAA;AACjB,eAAW,QAAQ,KAAK,KAAK;AAC3B,UAAI,KAAK,SAAS,cAAc;AAC9B,cAAM,UAAU,MAAM,IAAI;AAC1B,gBAAQ,MAAM,MAAM,UAAU;AAC9B,YAAI,KAAK,OAAO;AAChB,qBAAa,CAAA;MACf,OAAO;AACL,mBAAW,KAAK,IAAI;MACtB;IACF;AACA,QAAI,IAAI,SAAS,KAAK,WAAW,SAAS,GAAG;AAC3C,YAAM,UAAU;QACd,MAAM;QACN,IAAI,WAAU;QACd,MAAM;QACN,KAAK,MAAM,UAAU;MAC7B;AACM,UAAI,KAAK,MAAM,OAAO,CAAC;AACvB,WAAK,MAAM;IACb;AACA,SAAK,IAAI,QAAQ,CAAC,YAAY,KAAK,cAAc,MAAM,SAAS,IAAI,CAAC;EACvE;EACA,eAAe;AACb,SAAK;MACH,EAAE,IAAI,WAAW,MAAM,UAAS;MAChC,EAAE,IAAI,WAAW,MAAM,WAAW,KAAK,KAAK,QAAO;MACnD;IACN;AACI,WAAO,EAAE,IAAI,WAAW,KAAK,KAAK,QAAO;EAC3C;;;;;;;;;EASA,SAAS,IAAI,OAAO,oBAAoB,MAAM,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,UAAU,QAAQ,SAAS,QAAQ,aAAa,QAAQ;AAC3I,UAAM,YAAY,MAAA,OAAA,SAAA,GAAI,KAAA;AACtB,QAAI,CAAC,KAAK,gBAAgB,OAAO,IAAI,SAAS,GAAG;AAC/C,UAAI,KAAK,iBAAiB,WAAW,KAAK;AAC1C,WAAK,gBAAgB,OAAO,IAAI,WAAW;QACzC,MAAM;QACN,IAAI;QACJ,cAAc,CAAA;QACd;QACA;QACA;QACA,SAAS,CAAA;QACT,QAAQ,CAAA;QACR,YAAY,CAAA;MACpB,CAAO;IACH,OAAO;AACL,YAAM,QAAQ,KAAK,gBAAgB,OAAO,IAAI,SAAS;AACvD,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,oBAAoB,SAAS,EAAE;MACjD;AACA,UAAI,CAAC,MAAM,KAAK;AACd,cAAM,MAAM;MACd;AACA,UAAI,CAAC,MAAM,MAAM;AACf,cAAM,OAAO;MACf;IACF;AACA,QAAI,OAAO;AACT,UAAI,KAAK,6BAA6B,WAAW,KAAK;AACtD,YAAM,eAAe,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC1D,mBAAa,QAAQ,CAAC,QAAQ,KAAK,eAAe,WAAW,IAAI,KAAI,CAAE,CAAC;IAC1E;AACA,QAAI,MAAM;AACR,YAAM,OAAO,KAAK,gBAAgB,OAAO,IAAI,SAAS;AACtD,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,oBAAoB,SAAS,EAAE;MACjD;AACA,WAAK,OAAO;AACZ,WAAK,KAAK,OAAO,eAAe,aAAa,KAAK,KAAK,MAAMD,WAAAA,CAAW;IAC1E;AACA,QAAI,SAAS;AACX,UAAI,KAAK,yBAAyB,WAAW,OAAO;AACpD,YAAM,cAAc,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAC/D,kBAAY,QAAQ,CAAC,aAAa,KAAK,YAAY,WAAW,SAAS,KAAI,CAAE,CAAC;IAChF;AACA,QAAI,QAAQ;AACV,UAAI,KAAK,wBAAwB,WAAW,MAAM;AAClD,YAAM,aAAa,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AAC3D,iBAAW,QAAQ,CAAC,UAAU,KAAK,SAAS,WAAW,MAAM,KAAI,CAAE,CAAC;IACtE;AACA,QAAI,YAAY;AACd,UAAI,KAAK,wBAAwB,WAAW,MAAM;AAClD,YAAM,iBAAiB,MAAM,QAAQ,UAAU,IAAI,aAAa,CAAC,UAAU;AAC3E,qBAAe,QAAQ,CAAC,cAAc,KAAK,aAAa,WAAW,UAAU,KAAI,CAAE,CAAC;IACtF;EACF;EACA,MAAM,YAAY;AAChB,SAAK,QAAQ,CAAA;AACb,SAAK,QAAQ,CAAA;AACb,SAAK,YAAY,EAAE,MAAM,OAAM,EAAE;AACjC,SAAK,kBAAkB,KAAK,UAAU;AACtC,SAAK,gBAAgB;AACrB,SAAK,UAAU,eAAc;AAC7B,QAAI,CAAC,YAAY;AACf,WAAK,QAAwB,oBAAI,IAAG;AACpC,cAAK;IACP;EACF;EACA,SAAS,IAAI;AACX,WAAO,KAAK,gBAAgB,OAAO,IAAI,EAAE;EAC3C;EACA,YAAY;AACV,WAAO,KAAK,gBAAgB;EAC9B;EACA,eAAe;AACb,QAAI,KAAK,gBAAgB,KAAK,SAAS;EACzC;EACA,eAAe;AACb,WAAO,KAAK,gBAAgB;EAC9B;;;;EAIA,QAAQ,SAAS,KAAK,SAAS;AAC7B,SAAK,MAAM,IAAI,SAAS,EAAE,KAAK,QAAA,CAAS;AACxC,QAAI,KAAK,eAAe,SAAS,KAAK,OAAO;EAC/C;;;;EAIA,WAAW;AACT,WAAO,KAAK;EACd;;;;;;EAMA,gBAAgB,KAAK,IAAI;AACvB,QAAI,OAAO,UAAU,YAAY;AAC/B,WAAK;AACL,aAAO,GAAG,UAAU,UAAU,GAAG,KAAK,aAAa;IACrD;AACA,WAAO;EACT;;;;;EAKA,kBAAkB,KAAK,IAAI,OAAO,oBAAoB;AACpD,WAAO,OAAO,UAAU,aAAa,UAAU,aAAa;EAC9D;;;;;;EAMA,cAAc,KAAK,IAAI;AACrB,QAAI,OAAO,UAAU,UAAU;AAC7B,WAAK;AACL,aAAO,GAAG,UAAU,QAAQ,GAAG,KAAK,aAAa;IACnD;AACA,WAAO;EACT;;;;;;EAMA,gBAAgB,KAAK,IAAI,OAAO,oBAAoB;AAClD,WAAO,OAAO,UAAU,WAAW,UAAU,WAAW;EAC1D;EACA,gBAAgB,OAAO,OAAO,gBAAgB,IAAI;AAChD,UAAM,MAAM,KAAK,gBAAgB,MAAM,GAAG,KAAA,CAAM;AAChD,UAAM,QAAQ,KAAK,kBAAkB,MAAM,GAAG,KAAI,GAAI,MAAM,IAAI;AAChE,UAAM,MAAM,KAAK,gBAAgB,MAAM,GAAG,KAAA,CAAM;AAChD,UAAM,QAAQ,KAAK,kBAAkB,MAAM,GAAG,KAAI,GAAI,MAAM,IAAI;AAChE,SAAK;MACH;MACA;MACA,MAAM;MACN,MAAM;MACN,MAAM;MACN,MAAM;MACN,MAAM;MACN,MAAM;IACZ;AACI,SAAK;MACH;MACA;MACA,MAAM;MACN,MAAM;MACN,MAAM;MACN,MAAM;MACN,MAAM;MACN,MAAM;IACZ;AACI,SAAK,gBAAgB,UAAU,KAAK;MAClC;MACA;MACA,eAAe,eAAe,aAAa,eAAeA,WAAS,CAAE;IAC3E,CAAK;EACH;;;;EAIA,YAAY,OAAO,OAAO,OAAO;AAC/B,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AAC1D,WAAK,gBAAgB,OAAO,OAAO,KAAK;IAC1C,WAAW,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AACjE,YAAM,MAAM,KAAK,gBAAgB,MAAM,KAAI,CAAE;AAC7C,YAAM,QAAQ,KAAK,kBAAkB,KAAK;AAC1C,YAAM,MAAM,KAAK,cAAc,MAAM,KAAI,CAAE;AAC3C,YAAM,QAAQ,KAAK,gBAAgB,KAAK;AACxC,WAAK,SAAS,KAAK,KAAK;AACxB,WAAK,SAAS,KAAK,KAAK;AACxB,WAAK,gBAAgB,UAAU,KAAK;QAClC;QACA;QACA,eAAe,QAAQ,eAAe,aAAa,OAAOA,WAAS,CAAE,IAAI;MACjF,CAAO;IACH;EACF;EACA,eAAe,IAAI,OAAO;;AACxB,UAAM,WAAW,KAAK,gBAAgB,OAAO,IAAI,EAAE;AACnD,UAAM,SAAS,MAAM,WAAW,GAAG,IAAI,MAAM,QAAQ,KAAK,EAAE,EAAE,KAAI,IAAK;AACvE,KAAAC,MAAA,YAAA,OAAA,SAAA,SAAU,iBAAV,OAAA,SAAAA,IAAwB,KAAK,eAAe,aAAa,QAAQD,WAAS,CAAE,CAAA;EAC9E;EACA,aAAa,OAAO;AAClB,WAAO,MAAM,WAAW,GAAG,IAAI,MAAM,MAAM,CAAC,EAAE,KAAA,IAAS,MAAM,KAAI;EACnE;EACA,eAAe;AACb,SAAK;AACL,WAAO,cAAc,KAAK,UAAU;EACtC;;;;;;;;EAQA,cAAc,IAAI,kBAAkB,IAAI;AACtC,QAAI,CAAC,KAAK,QAAQ,IAAI,EAAE,GAAG;AACzB,WAAK,QAAQ,IAAI,IAAI,EAAE,IAAI,QAAQ,CAAA,GAAI,YAAY,CAAA,EAAA,CAAI;IACzD;AACA,UAAM,aAAa,KAAK,QAAQ,IAAI,EAAE;AACtC,QAAI,mBAAmB,YAAY;AACjC,sBAAgB,MAAM,UAAU,cAAc,EAAE,QAAQ,CAAC,WAAW;AAClE,cAAM,cAAc,OAAO,QAAQ,YAAY,IAAI,EAAE,KAAI;AACzD,YAAI,OAAO,UAAU,aAAa,EAAE,KAAK,MAAM,GAAG;AAChD,gBAAM,YAAY,YAAY,QAAQ,UAAU,cAAc,UAAU,OAAO;AAC/E,gBAAM,YAAY,UAAU,QAAQ,UAAU,eAAe,UAAU,YAAY;AACnF,qBAAW,WAAW,KAAK,SAAS;QACtC;AACA,mBAAW,OAAO,KAAK,WAAW;MACpC,CAAC;IACH;EACF;EACA,aAAa;AACX,WAAO,KAAK;EACd;;;;;;;;;EASA,YAAY,SAAS,cAAc;AACjC,YAAQ,MAAM,GAAG,EAAE,QAAQ,CAAC,OAAO;;AACjC,UAAI,aAAa,KAAK,SAAS,EAAE;AACjC,UAAI,CAAC,YAAY;AACf,cAAM,YAAY,GAAG,KAAI;AACzB,aAAK,SAAS,SAAS;AACvB,qBAAa,KAAK,SAAS,SAAS;MACtC;AACA,OAAAC,MAAA,cAAA,OAAA,SAAA,WAAY,YAAZ,OAAA,SAAAA,IAAqB,KAAK,YAAA;IAC5B,CAAC;EACH;;;;;;;;;;;EAWA,SAAS,QAAQ,WAAW;;AAC1B,KAAA,MAAAA,MAAA,KAAK,SAAS,MAAM,MAApB,OAAA,SAAAA,IAAuB,WAAvB,OAAA,SAAA,GAA+B,KAAK,SAAA;EACtC;;;;;;;EAOA,aAAa,QAAQ,cAAc;;AACjC,KAAA,MAAAA,MAAA,KAAK,SAAS,MAAM,MAApB,OAAA,SAAAA,IAAuB,eAAvB,OAAA,SAAA,GAAmC,KAAK,YAAA;EAC1C;;;;;EAKA,wBAAwB;AACtB,WAAO,KAAK,QAAQ,KAAK,CAAC,QAAQ,IAAI,SAAS,cAAc;EAC/D;EACA,eAAe;;AACb,aAAOA,MAAA,KAAK,sBAAA,MAAL,OAAA,SAAAA,IAA8B,UAAS;EAChD;EACA,aAAa,KAAK;AAChB,UAAM,MAAM,KAAK,sBAAqB;AACtC,QAAI,KAAK;AACP,UAAI,QAAQ;IACd,OAAO;AACL,WAAK,QAAQ,QAAQ,EAAE,MAAM,gBAAgB,OAAO,IAAA,CAAK;IAC3D;EACF;EACA,UAAU,KAAK;AACb,WAAO,IAAI,WAAW,GAAG,IAAI,IAAI,MAAM,CAAC,EAAE,KAAA,IAAS,IAAI,KAAI;EAC7D;EACA,UAAU;AACR,UAAM,SAASD,WAAS;AACxB,WAAO;MACL,OAAO,KAAK;MACZ,OAAO,KAAK;MACZ,OAAO,CAAA;MACP;MACA,WAAW,OAAO,KAAK,aAAY,CAAE;IAC3C;EACE;EACA,YAAY;AACV,WAAOA,WAAS,EAAG;EACrB;AACF,GA/bI,OAAO,IAAM,SAAS,GAGtB,GAAK,eAAe;EAClB,aAAa;EACb,WAAW;EACX,aAAa;EACb,YAAY;AAClB,GAjCc;AA2dd,IAAI,YAA4B,OAAO,CAAC,YAAY;;YAExC,QAAQ,eAAe;cACrB,QAAQ,eAAe;;;UAG3B,QAAQ,UAAU;;;;;UAKlB,QAAQ,SAAS;;;;;;;UAOjB,QAAQ,eAAe;;;;UAIvB,QAAQ,OAAO;YACb,QAAQ,UAAU;;;;YAIlB,QAAQ,SAAS;;;;;YAKjB,QAAQ,eAAe;;;;;;UAMzB,QAAQ,UAAU;;;;;;;;;;YAUhB,QAAQ,eAAe;UACzB,QAAQ,YAAY;;;YAGlB,QAAQ,aAAa;;;;;;;;;UASvB,QAAQ,OAAO;;;;;UAKf,QAAQ,oBAAoB;;;;sBAIhB,QAAQ,mBAAmB;;wBAEzB,QAAQ,mBAAmB;;;;wBAI3B,QAAQ,mBAAmB;YACvC,QAAQ,mBAAmB;;;;;UAK7B,QAAQ,wBAAwB,QAAQ,iBAAiB;;;WAGxD,QAAQ,wBAAwB,QAAQ,iBAAiB;;;;UAI1D,QAAQ,eAAe;;;;;;UAMvB,QAAQ,iBAAiB;YACvB,QAAQ,iBAAiB;;;;UAI3B,QAAQ,iBAAiB;YACvB,QAAQ,iBAAiB;;;;UAI3B,QAAQ,kBAAkB;YACxB,QAAQ,UAAU;;;;UAIpB,QAAQ,uBAAuB,QAAQ,UAAU;eAC5C,QAAQ,UAAU;;;;;UAKvB,QAAQ,YAAY,QAAQ,OAAO;YACjC,QAAQ,eAAe,QAAQ,UAAU;;;;UAI3C,QAAQ,OAAO;YACb,QAAQ,eAAe,QAAQ,UAAU;;;;UAI3C,QAAQ,SAAS;;;;UAIjB,QAAQ,wBAAwB;YAC9B,QAAQ,eAAe,QAAQ,UAAU;;;;;WAK1C,QAAQ,eAAe;;;;;;;;;YAStB,QAAQ,eAAe,QAAQ,UAAU;;;;;;;;UAQ3C,QAAQ,uBAAuB,QAAQ,UAAU;;;UAGjD,QAAQ,gBAAgB,QAAQ,gBAAgB,SAAS;;;;;;;;;;;;;;UAczD,QAAQ,gBAAgB,QAAQ,gBAAgB,SAAS;;;;;;;;UAQzD,QAAQ,YAAY;YAClB,QAAQ,eAAe;;;;;;UAMzB,QAAQ,YAAY;YAClB,QAAQ,eAAe;;;;;;;UAOzB,QAAQ,aAAa;;;;WAIpB,QAAQ,aAAa;;;mBAGb,QAAQ,aAAa;;;;UAI9B,QAAQ,SAAS;YACf,QAAQ,SAAS;;;;;;;UAOnB,QAAQ,SAAS;;GAExB,WAAW;AACX,IAAC,iBAAiB;", "names": ["getConfig", "_a"]}